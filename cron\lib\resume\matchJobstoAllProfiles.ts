import { PrismaClient } from "@prisma/client";
import { scoreAllJobsForResume } from "../lib/scoreAllJobsForResume";
import { saveJobSearchResults } from "../lib/saveJobSearchResults";
import { logger } from "../utils/logger";

const prisma = new PrismaClient();

export async function matchJobsToAllProfiles() {
  logger.info("🧠 Starting auto-matching process...");

  const profiles = await prisma.profile.findMany({
    where: { parsedResume: { not: null } },
    select: {
      id: true,
      userId: true,
      parsedResume: true,
    },
  });

  for (const profile of profiles) {
    try {
      const scored = await scoreAllJobsForResume(profile.parsedResume!);
      const top = scored.filter((j) => j.matchScore >= 0.6).slice(0, 50);

      await saveJobSearchResults({
        scoredJobs: top.map((j) => ({ id: j.id, matchScore: j.matchScore })),
        userId: profile.userId,
        profileId: profile.id,
      });

      logger.info(`✅ Matched ${top.length} jobs for profile ${profile.id}`);
    } catch (err) {
      logger.error(`❌ Failed for profile ${profile.id}:`, err);
    }
  }

  logger.info("🏁 Finished matching all profiles.");
}
