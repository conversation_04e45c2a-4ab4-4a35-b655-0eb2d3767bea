import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';

/**
 * Get all AI interview coaching sessions for the current user
 */
export const GET: RequestHandler = async ({ cookies, locals }) => {
  try {
    // Verify authentication
    const token = cookies.get('auth_token');
    if (!token && !locals.user) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user ID from token or locals
    const userId = locals.user?.id || (token ? verifySessionToken(token)?.id : null);

    if (!userId) {
      return json({ error: 'Invalid token or user not found' }, { status: 401 });
    }

    // Get all interview coaching sessions for the user
    const sessions = await prisma.interviewCoachingSession.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Transform the data for the frontend
    const transformedSessions = sessions.map((session) => ({
      id: session.id,
      jobTitle: session.jobTitle,
      company: session.company,
      status: session.status,
      questions: session.questions,
      responses: session.responses,
      feedback: session.feedback,
      sessionDate: session.createdAt,
      updatedAt: session.updatedAt,
    }));

    return json({ sessions: transformedSessions });
  } catch (error) {
    console.error('Error fetching interview sessions:', error);
    return json({ error: 'Failed to fetch interview sessions' }, { status: 500 });
  }
};

/**
 * Create a new AI interview coaching session
 *
 * This endpoint creates a new interview coaching session with AI-generated questions.
 */
export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    // Verify authentication
    const token = cookies.get('auth_token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tokenData = verifySessionToken(token);
    if (!tokenData?.id) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // Get user ID from token
    const userId = tokenData.id;

    // Parse request body
    const body = await request.json();
    const { applicationId, jobTitle, industry } = body;

    // Validate required fields
    if (!applicationId || !jobTitle) {
      return json(
        { error: 'Missing required fields', required: ['applicationId', 'jobTitle'] },
        { status: 400 }
      );
    }

    // Check if application exists and belongs to the user
    const application = await prisma.application.findFirst({
      where: {
        id: applicationId,
        userId,
      },
    });

    if (!application) {
      return json({ error: 'Application not found or access denied' }, { status: 404 });
    }

    // Check if user has access to the ai_interview_coach feature
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscriptions: {
          where: { status: 'active' },
          include: {
            plan: {
              include: {
                features: {
                  where: { featureId: 'ai_interview_coach' },
                },
              },
            },
          },
        },
        featureUsage: {
          where: {
            featureId: 'ai_interview_coach',
            limitId: 'ai_interview_sessions_monthly',
          },
        },
      },
    });

    const hasAccess = user?.subscriptions.some((sub) =>
      sub.plan.features.some((feature) => feature.featureId === 'ai_interview_coach')
    );

    if (!hasAccess && process.env.NODE_ENV === 'production') {
      return json({ error: 'Feature not available in your plan' }, { status: 403 });
    }

    // Check usage limits
    const usageLimit = user?.subscriptions
      .flatMap((sub) => sub.plan.features)
      .find((feature) => feature.featureId === 'ai_interview_coach')
      ?.limits?.find((limit) => limit.limitId === 'ai_interview_sessions_monthly')?.value;

    const currentUsage =
      user?.featureUsage.find(
        (usage) =>
          usage.featureId === 'ai_interview_coach' &&
          usage.limitId === 'ai_interview_sessions_monthly'
      )?.usage ?? 0;

    if (
      usageLimit &&
      currentUsage >= parseInt(usageLimit) &&
      process.env.NODE_ENV === 'production'
    ) {
      return json({ error: 'Monthly usage limit reached' }, { status: 403 });
    }

    // Check if application exists
    if (applicationId) {
      const application = await prisma.application.findFirst({
        where: {
          id: applicationId,
          userId,
        },
      });

      if (!application) {
        return json({ error: 'Application not found or access denied' }, { status: 404 });
      }
    }

    // Generate questions based on job title and industry
    const questions = generateInterviewQuestions(jobTitle, industry);

    // Create AI coaching session
    const session = await prisma.interviewCoachingSession.create({
      data: {
        userId,
        applicationId,
        jobTitle,
        company: body.company ?? null,
        status: 'in_progress',
        questions: questions,
      },
    });

    // Update feature usage
    await prisma.featureUsage.upsert({
      where: {
        userId_featureId_limitId: {
          userId,
          featureId: 'ai_interview_coach',
          limitId: 'ai_interview_sessions_monthly',
        },
      },
      update: {
        usage: { increment: 1 },
      },
      create: {
        userId,
        featureId: 'ai_interview_coach',
        limitId: 'ai_interview_sessions_monthly',
        usage: 1,
      },
    });

    return json({ success: true, session });
  } catch (error) {
    console.error('Error creating interview coaching session:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

/**
 * Generate interview questions based on job title and industry
 *
 * In a real implementation, this would call an AI service
 */
function generateInterviewQuestions(jobTitle: string, industry?: string) {
  // Base questions that apply to most roles
  const baseQuestions = [
    {
      question: 'Tell me about yourself and your background.',
      category: 'general',
      difficulty: 3,
    },
    {
      question: 'What are your greatest strengths and weaknesses?',
      category: 'general',
      difficulty: 4,
    },
    {
      question: 'Why are you interested in this position?',
      category: 'general',
      difficulty: 3,
    },
    {
      question: 'Describe a challenging situation at work and how you handled it.',
      category: 'behavioral',
      difficulty: 6,
    },
    {
      question: 'Where do you see yourself in 5 years?',
      category: 'general',
      difficulty: 4,
    },
  ];

  // Technical questions based on job title
  let technicalQuestions = [];

  if (
    jobTitle.toLowerCase().includes('software') ||
    jobTitle.toLowerCase().includes('developer') ||
    jobTitle.toLowerCase().includes('engineer')
  ) {
    technicalQuestions = [
      {
        question: 'Explain the difference between a stack and a queue.',
        category: 'technical',
        difficulty: 5,
      },
      {
        question: 'What is your experience with version control systems?',
        category: 'technical',
        difficulty: 4,
      },
      {
        question:
          'Describe a project where you had to optimize performance. What techniques did you use?',
        category: 'technical',
        difficulty: 7,
      },
      {
        question: 'How do you approach debugging a complex issue?',
        category: 'technical',
        difficulty: 6,
      },
      {
        question: 'Explain the concept of dependency injection.',
        category: 'technical',
        difficulty: 8,
      },
    ];
  } else if (
    jobTitle.toLowerCase().includes('manager') ||
    jobTitle.toLowerCase().includes('lead')
  ) {
    technicalQuestions = [
      {
        question: 'How do you prioritize tasks for your team?',
        category: 'leadership',
        difficulty: 6,
      },
      {
        question: 'Describe your approach to managing underperforming team members.',
        category: 'leadership',
        difficulty: 7,
      },
      {
        question: 'How do you handle conflicts within your team?',
        category: 'leadership',
        difficulty: 6,
      },
      {
        question: 'What metrics do you use to measure team success?',
        category: 'leadership',
        difficulty: 7,
      },
      {
        question: 'How do you ensure your team meets deadlines?',
        category: 'leadership',
        difficulty: 5,
      },
    ];
  } else if (jobTitle.toLowerCase().includes('marketing')) {
    technicalQuestions = [
      {
        question: 'How do you measure the success of a marketing campaign?',
        category: 'marketing',
        difficulty: 5,
      },
      {
        question: 'Describe a successful marketing campaign you developed.',
        category: 'marketing',
        difficulty: 6,
      },
      {
        question: 'How do you stay updated with the latest marketing trends?',
        category: 'marketing',
        difficulty: 4,
      },
      {
        question: 'What tools do you use for marketing analytics?',
        category: 'marketing',
        difficulty: 5,
      },
      {
        question: 'How would you approach marketing to a new demographic?',
        category: 'marketing',
        difficulty: 7,
      },
    ];
  }

  // Combine and return questions
  return [...baseQuestions, ...technicalQuestions];
}
