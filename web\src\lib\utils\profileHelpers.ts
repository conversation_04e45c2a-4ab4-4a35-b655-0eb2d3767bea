import type { ProfileData, WorkExperience } from '$lib/types/profile';
import { v4 as uuidv4 } from 'uuid';

/**
 * Parse profile data from ProfileData model
 *
 * This function handles different formats of profile data:
 * 1. String: Parses JSON string into ProfileData object
 * 2. Object with data property: Extracts and parses data property
 * 3. Direct ProfileData object: Returns as is
 */
export function parseProfileData(data: any): ProfileData {
  try {
    if (!data) return {} as ProfileData;

    // If data is a string, parse it
    if (typeof data === 'string') {
      return JSON.parse(data) as ProfileData;
    }

    // If data.data exists and is a string, parse it
    if (data.data && typeof data.data === 'string') {
      return JSON.parse(data.data) as ProfileData;
    }

    // If data.data exists and is an object, return it
    if (data.data && typeof data.data === 'object') {
      return data.data as ProfileData;
    }

    // If data is already an object with the expected structure, return it
    return data as ProfileData;
  } catch (err) {
    console.error('Error parsing profile data:', err);
    return {} as ProfileData;
  }
}

/**
 * Migrate legacy profile data to the new structure
 *
 * This function ensures that a ProfileData object has all the required fields
 * and migrates any legacy data to the new structure.
 */
export function migrateProfileData(data: ProfileData): ProfileData {
  const migratedData: ProfileData = { ...data };

  // Migrate personal info
  if (!migratedData.personalInfo) {
    migratedData.personalInfo = {
      fullName: data.fullName,
      email: data.email,
      phone: data.phone,
      location: data.location,
      website: data.website,
      summary: data.summary,
      jobTitle: data.jobType,
    };
  }

  // Migrate skills
  if (!migratedData.skillsData && data.skills) {
    let skillsArray = [];

    if (Array.isArray(data.skills)) {
      skillsArray = data.skills;
    } else if (typeof data.skills === 'string') {
      skillsArray = data.skills
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean);
    }

    migratedData.skillsData = {
      list: skillsArray,
      technical: skillsArray,
    };
  }

  // Initialize empty arrays for collections
  if (!migratedData.workExperience) migratedData.workExperience = [];
  if (!migratedData.education) migratedData.education = [];
  if (!migratedData.projects) migratedData.projects = [];
  if (!migratedData.certifications) migratedData.certifications = [];
  if (!migratedData.languages) migratedData.languages = [];
  if (!migratedData.achievements) migratedData.achievements = [];

  // Initialize job preferences
  if (!migratedData.jobPreferences) {
    migratedData.jobPreferences = {
      valueInRole: [],
      interestedRoles: [],
      roleSpecializations: [],
      preferredLocations: [],
      experienceLevel: '',
      companySize: '',
      desiredIndustries: [],
      avoidIndustries: [],
      preferredSkills: [],
      avoidSkills: [],
      minimumSalary: '',
      securityClearance: '',
      jobSearchStatus: '',
    };
  }

  return migratedData;
}

/**
 * Add an ID to an item if it doesn't have one
 */
export function ensureItemId<T extends { id?: string }>(item: T): T {
  if (!item.id) {
    return { ...item, id: uuidv4() };
  }
  return item;
}

/**
 * Add a work experience item to profile data
 */
export function addWorkExperience(
  profileData: ProfileData,
  experience: WorkExperience
): ProfileData {
  const updatedData = { ...profileData };
  if (!updatedData.workExperience) updatedData.workExperience = [];

  updatedData.workExperience = [ensureItemId(experience), ...updatedData.workExperience];

  return updatedData;
}

/**
 * Update a work experience item in profile data
 */
export function updateWorkExperience(
  profileData: ProfileData,
  experience: WorkExperience
): ProfileData {
  if (!experience.id || !profileData.workExperience) return profileData;

  const updatedData = { ...profileData };
  updatedData.workExperience = updatedData.workExperience.map((item) =>
    item.id === experience.id ? experience : item
  );

  return updatedData;
}

/**
 * Remove a work experience item from profile data
 */
export function removeWorkExperience(profileData: ProfileData, experienceId: string): ProfileData {
  if (!profileData.workExperience) return profileData;

  const updatedData = { ...profileData };
  updatedData.workExperience = updatedData.workExperience.filter(
    (item) => item.id !== experienceId
  );

  return updatedData;
}

/**
 * Calculate profile completion percentage
 *
 * This function calculates the completion percentage of a profile based on
 * the presence of key information sections. It handles both ProfileData structure
 * and CompleteProfileSchema structure for consistency across the app.
 */
export function calculateProfileCompletion(profileData: ProfileData | any): number {
  let completed = 0;
  const total = 7; // Personal info, summary, experience, education, skills, projects, job preferences

  // Check personal info (handle both ProfileData and CompleteProfileSchema structures)
  const hasPersonalInfo =
    profileData.personalInfo?.fullName || profileData.fullName || profileData.header?.fullName;
  if (hasPersonalInfo) completed++;

  // Check summary (handle both structures)
  const hasSummary =
    profileData.personalInfo?.summary ||
    profileData.summary ||
    profileData.personalInfo?.headline ||
    profileData.header?.headline;
  if (hasSummary) completed++;

  // Check experience (handle both workExperience and workExperiences)
  const hasExperience =
    (profileData.workExperience && profileData.workExperience.length > 0) ||
    (profileData.workExperiences && profileData.workExperiences.length > 0);
  if (hasExperience) completed++;

  // Check education (handle both education and educations)
  const hasEducation =
    (profileData.education && profileData.education.length > 0) ||
    (profileData.educations && profileData.educations.length > 0);
  if (hasEducation) completed++;

  // Check skills (handle multiple skill structures)
  const hasSkills =
    (profileData.skillsData?.list && profileData.skillsData.list.length > 0) ||
    (Array.isArray(profileData.skills) && profileData.skills.length > 0) ||
    (profileData.skills?.skills && profileData.skills.skills.length > 0);
  if (hasSkills) completed++;

  // Check projects
  const hasProjects = profileData.projects && profileData.projects.length > 0;
  if (hasProjects) completed++;

  // Check job preferences
  const hasJobPreferences =
    (profileData.jobPreferences?.preferredLocations &&
      profileData.jobPreferences.preferredLocations.length > 0) ||
    (profileData.jobPreferences?.interestedRoles &&
      profileData.jobPreferences.interestedRoles.length > 0);
  if (hasJobPreferences) completed++;

  return Math.round((completed / total) * 100);
}

/**
 * Check if a profile is eligible for automation
 *
 * @param profile - The profile object with data and documents
 * @param minCompletionPercentage - Minimum completion percentage required (default: 70)
 * @returns Object with eligibility status and reasons
 */
export function checkAutomationEligibility(
  profile: any,
  minCompletionPercentage: number = 70
): {
  isEligible: boolean;
  completionPercentage: number;
  missingRequirements: string[];
  hasResume: boolean;
} {
  const missingRequirements: string[] = [];

  // Check if profile has data
  if (!profile.data?.data) {
    return {
      isEligible: false,
      completionPercentage: 0,
      missingRequirements: ['Profile data is missing'],
      hasResume: false,
    };
  }

  // Parse profile data properly - it might be a JSON string
  let profileData = profile.data.data;
  if (typeof profileData === 'string') {
    try {
      profileData = JSON.parse(profileData);
    } catch (e) {
      console.error('Error parsing profile data JSON:', e);
      return {
        isEligible: false,
        completionPercentage: 0,
        missingRequirements: ['Profile data is corrupted'],
        hasResume: false,
      };
    }
  }

  const completionPercentage = calculateProfileCompletion(profileData);

  // Check minimum completion percentage
  if (completionPercentage < minCompletionPercentage) {
    missingRequirements.push(
      `Profile completion is ${completionPercentage}%, minimum required is ${minCompletionPercentage}%`
    );
  }

  // Check for resume
  const hasResume = profile.documents && profile.documents.length > 0;
  if (!hasResume) {
    missingRequirements.push('At least one resume is required');
  }

  // Check essential fields for automation
  if (!profileData.personalInfo?.fullName && !profileData.fullName) {
    missingRequirements.push('Full name is required');
  }

  if (!profileData.personalInfo?.email && !profileData.email) {
    missingRequirements.push('Email address is required');
  }

  // Check for skills (essential for job matching)
  const hasSkills =
    (profileData.skillsData?.list && profileData.skillsData.list.length > 0) ||
    (Array.isArray(profileData.skills) && profileData.skills.length > 0);
  if (!hasSkills) {
    missingRequirements.push('Skills are required for job matching');
  }

  // Check for work experience (essential for job matching)
  if (!profileData.workExperience || profileData.workExperience.length === 0) {
    missingRequirements.push('Work experience is required for job matching');
  }

  const isEligible = missingRequirements.length === 0;

  return {
    isEligible,
    completionPercentage,
    missingRequirements,
    hasResume,
  };
}
