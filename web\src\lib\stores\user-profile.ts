import { writable } from 'svelte/store';

// Define the user profile store type
type UserProfile = {
  image: string | null;
  name: string | null;
  id: string | null;
  email: string | null;
  role: string | null;
};

// Initialize with default values
const initialProfile: UserProfile = {
  image: null,
  name: null,
  id: null,
  email: null,
  role: null,
};

// Create the writable store
const userProfileStore = writable<UserProfile>(initialProfile);

// Export a function to update the profile image
export function updateProfileImage(imageUrl: string | null) {
  console.log('updateProfileImage called with:', imageUrl);
  userProfileStore.update((profile) => {
    console.log('Current profile before update:', profile);
    const updatedProfile = {
      ...profile,
      image: imageUrl,
    };
    console.log('Updated profile:', updatedProfile);
    return updatedProfile;
  });
}

// Export a function to initialize the store with user data
export function initUserProfile(userData: any) {
  if (!userData) return;

  userProfileStore.set({
    image: userData.image || null,
    name: userData.name || null,
    id: userData.id || null,
    email: userData.email || null,
    role: userData.role || null,
  });
}

// No subscriptions - they can cause issues

// Export the store
export default userProfileStore;
