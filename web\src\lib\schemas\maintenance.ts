import { z } from 'zod';

// Define the maintenance event schema
export const maintenanceSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, { message: 'Title is required' }),
  description: z.string().min(1, { message: 'Description is required' }),
  startTime: z.string().min(1, { message: 'Start time is required' }),
  endTime: z.string().min(1, { message: 'End time is required' }),
  status: z.enum(['scheduled', 'in-progress', 'completed', 'cancelled'], {
    required_error: 'Status is required',
  }),
  severity: z.enum(['info', 'maintenance', 'minor', 'major', 'critical'], {
    required_error: 'Severity is required',
  }),
  affectedServices: z
    .array(z.string())
    .min(1, { message: 'At least one affected service is required' }),
  sendNotification: z.boolean().default(false),
  comment: z.string().optional(),
  commentStatus: z
    .enum(['investigating', 'identified', 'in-progress', 'monitoring', 'resolved'])
    .optional(),
});

// Type for the maintenance event
export type MaintenanceEvent = z.infer<typeof maintenanceSchema>;

// Define the maintenance event history schema
export const maintenanceHistorySchema = z.object({
  eventId: z.string(),
  changeType: z.enum(['status_change', 'update', 'comment']),
  previousStatus: z.string().optional(),
  newStatus: z.string().optional(),
  comment: z.string().optional(),
  metadata: z.any().optional(),
});

// Type for the maintenance event history
export type MaintenanceEventHistory = z.infer<typeof maintenanceHistorySchema>;
