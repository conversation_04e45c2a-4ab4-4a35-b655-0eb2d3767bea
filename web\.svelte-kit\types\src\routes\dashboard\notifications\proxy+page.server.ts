// @ts-nocheck
// src/routes/dashboard/notifications/+page.server.ts
import { redirect } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import type { PageServerLoad } from './$types';

export const load = async ({ locals, url }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  // Get query parameters
  const page = parseInt(url.searchParams.get('page') || '1');
  const limit = parseInt(url.searchParams.get('limit') || '20');
  const type = url.searchParams.get('type') || undefined;
  const includeRead = url.searchParams.get('includeRead') === 'true';

  // Calculate offset
  const offset = (page - 1) * limit;

  try {
    // Build the where clause
    const whereClause: any = {
      OR: [{ userId: user.id }, { global: true }],
    };

    // Filter by read status if needed
    if (!includeRead) {
      whereClause.read = false;
    }

    // Filter by type if specified
    if (type) {
      whereClause.type = type;
    }

    // Get notifications with pagination
    const notifications = await prisma.Notification.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
    });

    // Get total count for pagination
    const totalCount = await prisma.Notification.count({
      where: whereClause,
    });

    // Get notification preferences
    const preferences = await prisma.notificationSettings.findUnique({
      where: { userId: user.id },
    });

    // If no preferences exist yet, create default preferences
    const userPreferences =
      preferences ||
      (await prisma.notificationSettings.create({
        data: {
          userId: user.id,
          emailEnabled: true,
          pushEnabled: true,
          browserEnabled: true,
          jobMatchEnabled: true,
          applicationStatusEnabled: true,
          marketingEnabled: false,
          productUpdatesEnabled: true,
        },
      }));

    // Get unread count
    const unreadCount = await prisma.Notification.count({
      where: {
        OR: [{ userId: user.id }, { global: true }],
        read: false,
      },
    });

    return {
      notifications,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
      filters: {
        type,
        includeRead,
      },
      preferences: userPreferences,
      unreadCount,
    };
  } catch (error) {
    console.error('Error loading notifications:', error);
    return {
      notifications: [],
      pagination: {
        page: 1,
        limit,
        totalCount: 0,
        totalPages: 0,
      },
      filters: {
        type,
        includeRead,
      },
      preferences: {
        email: true,
        push: true,
        inApp: true,
        jobAlerts: true,
        applicationUpdates: true,
        marketingUpdates: false,
        systemAnnouncements: true,
      },
      unreadCount: 0,
      error: 'Failed to load notifications',
    };
  }
};
