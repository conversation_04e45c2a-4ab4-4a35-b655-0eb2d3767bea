// cron/scripts/loadLanguages.ts

import { PrismaClient } from "@prisma/client";
import fs from "fs/promises";
import path from "path";
import { logger } from "../utils/logger";

const prisma = new PrismaClient();
const LANG_FILE = path.resolve("../cron/utils/json/languages.json");

async function loadLanguages() {
  try {
    logger.info("🔁 Starting language taxonomy sync...");

    const fileExists = await fs.stat(LANG_FILE).catch(() => null);
    if (!fileExists) {
      logger.error(`❌ Missing file: ${LANG_FILE}`);
      return;
    }

    const raw = JSON.parse(await fs.readFile(LANG_FILE, "utf-8")) as Record<
      string,
      string
    >;
    const languages = Object.entries(raw).map(([code, name]) => ({
      code,
      name,
    }));

    for (const lang of languages) {
      await prisma.language.upsert({
        where: { code: lang.code },
        update: {},
        create: {
          code: lang.code,
          name: lang.name,
        },
      });
      logger.debug(`✅ Inserted language: ${lang.name} (${lang.code})`);
    }

    logger.info(`✅ Loaded ${languages.length} languages.`);
  } catch (err) {
    logger.error("❌ Failed to sync language taxonomy:", err);
  } finally {
    await prisma.$disconnect();
  }
}

await loadLanguages();
