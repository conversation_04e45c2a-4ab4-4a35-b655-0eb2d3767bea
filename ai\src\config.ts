import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const config = {
  server: {
    port: process.env.PORT ?? 3100,
    host: process.env.HOST ?? "localhost",
  },
  llm: {
    provider: process.env.LLM_PROVIDER ?? "ollama",
    ollamaUrl: process.env.OLLAMA_URL ?? "http://localhost:11434",
    model: process.env.LLM_MODEL ?? "mistral",
    timeout: parseInt(process.env.LLM_TIMEOUT ?? "30000", 10),
  },
  health: {
    checkInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL ?? "300000", 10), // 5 minutes
    metricsInterval: parseInt(process.env.METRICS_INTERVAL ?? "60000", 10), // 1 minute
    circuitBreaker: {
      memoryThreshold: parseInt(process.env.MEMORY_THRESHOLD ?? "85", 10), // 85%
      cpuThreshold: parseInt(process.env.CPU_THRESHOLD ?? "85", 10), // 85%
      resetThreshold: parseInt(process.env.RESET_THRESHOLD ?? "70", 10), // 70%
      checkInterval: parseInt(
        process.env.CIRCUIT_CHECK_INTERVAL ?? "10000",
        10
      ), // 10 seconds
    },
  },
  logging: {
    level: process.env.LOG_LEVEL ?? "info",
    prettyPrint: process.env.NODE_ENV !== "production",
  },
  modelPath: process.env.MODEL_PATH ?? path.join(process.cwd(), "models"),
};

export default config;
