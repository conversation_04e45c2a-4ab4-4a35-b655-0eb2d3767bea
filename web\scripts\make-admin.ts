/**
 * Make Admin Script
 *
 * This script makes a user an admin by email address.
 *
 * Usage:
 * npx tsx scripts/make-admin.ts <EMAIL>
 */

import { PrismaClient } from '@prisma/client';

// Define interfaces for type safety
interface User {
  id: string;
  email: string;
  name: string | null;
  isAdmin: boolean;
}

const prisma = new PrismaClient();

async function makeAdmin(): Promise<void> {
  // Get email from command line arguments
  const email = process.argv[2];

  if (!email) {
    console.error('Error: Email is required');
    console.error('Usage: npx tsx scripts/make-admin.ts <EMAIL>');
    process.exit(1);
  }

  try {
    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true, email: true, name: true, isAdmin: true },
    });

    if (!user) {
      console.error(`Error: User with email ${email} not found`);
      process.exit(1);
    }

    if (user.isAdmin) {
      console.log(`User ${user.email} is already an admin`);
      process.exit(0);
    }

    // Update user to be an admin
    const updatedUser = await prisma.user.update({
      where: { email },
      data: { isAdmin: true },
      select: { id: true, email: true, name: true, isAdmin: true },
    });

    console.log(`User ${updatedUser.email} is now an admin`);
  } catch (error) {
    console.error('Error making user an admin:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

makeAdmin();
