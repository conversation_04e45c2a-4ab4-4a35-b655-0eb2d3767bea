// cron/scripts/reset-circuit-breaker-if-idle.ts
// <PERSON><PERSON><PERSON> to reset the circuit breaker if no jobs are running

import { logger } from "../utils/logger";
import {
  ImprovedImprovedCircuitBreaker,
  CircuitState,
} from "../utils/improvedImprovedCircuitBreaker";
import {
  areJobsRunning,
  getRunningJobCount,
  closePrismaConnection,
} from "../utils/jobChecker";
import { getContainerMetrics } from "../utils/containerMetrics";

/**
 * Reset the circuit breaker if no jobs are running
 */
async function resetCircuitBreakerIfIdle() {
  logger.info("🔍 Checking if circuit breaker needs to be reset");

  // Check if any jobs are running with error handling
  let jobsRunning = false;
  let runningJobCount = 0;

  try {
    jobsRunning = await areJobsRunning();
  } catch (error) {
    logger.error(`Error checking if jobs are running: ${error}`);
    // Default to false if we can't check
    jobsRunning = false;
  }

  try {
    runningJobCount = await getRunningJobCount();
    // Ensure we have a valid number
    if (typeof runningJobCount !== "number" || isNaN(runningJobCount)) {
      logger.warn(
        `Invalid running job count: ${runningJobCount}, defaulting to 0`
      );
      runningJobCount = 0;
    }
  } catch (error) {
    logger.error(`Error getting running job count: ${error}`);
    // Default to 0 if we can't check
    runningJobCount = 0;
  }

  logger.info(
    `ℹ️ Job status: ${jobsRunning ? "Jobs are running" : "No jobs running"}`
  );
  logger.info(`ℹ️ Running job count: ${runningJobCount}`);

  if (jobsRunning) {
    logger.info("⚠️ Jobs are still running, not resetting circuit breaker");
    return;
  }

  // Check current system resources
  const containerMetrics = getContainerMetrics();
  let memoryUsage = 0;
  let cpuUsage = 0;

  if (containerMetrics) {
    memoryUsage = containerMetrics.memoryUsagePercent;
    cpuUsage = containerMetrics.cpuUsagePercent;
    logger.info("📊 Container metrics from cgroups:");
  } else {
    // Fall back to basic OS metrics
    const os = await import("os");
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    memoryUsage = (usedMemory / totalMemory) * 100;

    // CPU usage is harder to get accurately without node-os-utils
    // Just use a placeholder value that won't block reset
    cpuUsage = 50;
    logger.info("📊 Basic system metrics:");
  }

  logger.info(`  • Memory usage: ${memoryUsage.toFixed(2)}%`);
  logger.info(`  • CPU usage: ${cpuUsage.toFixed(2)}%`);

  // Create a circuit breaker instance
  const circuitBreaker = new ImprovedImprovedCircuitBreaker({
    memoryThresholdPercent: 90,
    cpuThresholdPercent: 90,
    degradedMemoryThresholdPercent: 85,
    degradedCpuThresholdPercent: 85,
    onStateChange: (oldState, newState) => {
      logger.info(
        `🔄 Circuit breaker state changed from ${oldState} to ${newState}`
      );
    },
  });

  // Check current state
  const currentState = circuitBreaker.getState();
  logger.info(`🔄 Current circuit breaker state: ${currentState}`);

  // If circuit breaker is not closed and resources are below thresholds, reset it
  if (currentState !== CircuitState.CLOSED) {
    if (memoryUsage < 85 && cpuUsage < 85) {
      logger.info(
        "✅ No jobs running and resources are good, resetting circuit breaker"
      );
      circuitBreaker.closeCircuit();
      logger.info(
        `🔄 Circuit breaker state after reset: ${circuitBreaker.getState()}`
      );
    } else {
      logger.warn(
        "⚠️ Resources still constrained, not resetting circuit breaker"
      );
      logger.info(`  • Memory: ${memoryUsage.toFixed(2)}% (threshold: 85%)`);
      logger.info(`  • CPU: ${cpuUsage.toFixed(2)}% (threshold: 85%)`);
    }
  } else {
    logger.info("✅ Circuit breaker is already closed, no reset needed");
  }

  // Stop monitoring
  circuitBreaker.stopMonitoring();
}

// Run the reset function
resetCircuitBreakerIfIdle()
  .then(async () => {
    logger.info("✅ Circuit breaker check completed");
    await closePrismaConnection();
    process.exit(0);
  })
  .catch(async (error) => {
    logger.error(`❌ Error checking circuit breaker: ${error}`);
    await closePrismaConnection();
    process.exit(1);
  });
