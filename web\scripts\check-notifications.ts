// web/scripts/check-notifications.ts
// <PERSON>ript to check notifications in the database

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkNotifications() {
  try {
    console.log('Checking notifications in the database...');
    
    // Get all notifications
    const notifications = await prisma.notification.findMany({
      orderBy: { createdAt: 'desc' },
      take: 100,
    });
    
    console.log(`Found ${notifications.length} notifications`);
    
    if (notifications.length > 0) {
      console.log('\nNotification details:');
      notifications.forEach((notification, index) => {
        console.log(`\n--- Notification ${index + 1} ---`);
        console.log(`ID: ${notification.id}`);
        console.log(`User ID: ${notification.userId || 'Global'}`);
        console.log(`Title: ${notification.title}`);
        console.log(`Message: ${notification.message}`);
        console.log(`Type: ${notification.type}`);
        console.log(`Priority: ${notification.priority}`);
        console.log(`Read: ${notification.read}`);
        console.log(`Global: ${notification.global}`);
        console.log(`Created At: ${notification.createdAt}`);
      });
    } else {
      console.log('No notifications found in the database.');
    }
    
    // Check notification settings
    const notificationSettings = await prisma.notificationSettings.findMany();
    console.log(`\nFound ${notificationSettings.length} notification settings records`);
    
    // Create a test notification
    console.log('\nCreating a test notification...');
    const testNotification = await prisma.notification.create({
      data: {
        title: 'Test Notification',
        message: 'This is a test notification created by the check-notifications script',
        type: 'info',
        priority: 'medium',
        global: true,
      },
    });
    
    console.log(`Created test notification with ID: ${testNotification.id}`);
    
  } catch (error) {
    console.error('Error checking notifications:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
checkNotifications()
  .then(() => console.log('Done!'))
  .catch((error) => console.error('Script error:', error));
