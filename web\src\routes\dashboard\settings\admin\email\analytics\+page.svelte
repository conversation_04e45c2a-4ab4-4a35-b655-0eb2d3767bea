<script lang="ts">
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import * as Card from '$lib/components/ui/card/index.js';
  import { toast } from 'svelte-sonner';
  import { onMount } from 'svelte';
  import { subDays, startOfDay, endOfDay } from 'date-fns';
  import { CalendarDate, getLocalTimeZone } from '@internationalized/date';
  import type { DateRange } from 'bits-ui';
  import OverviewTab from './OverviewTab.svelte';
  import EventsTab from './EventsTab.svelte';

  // TypeScript interfaces
  interface EmailStats {
    total: number;
    delivered: number;
    opened: number;
    clicked: number;
    bounced: number;
    complained: number;
    unsubscribed: number;
  }

  interface ChartDataPoint {
    day: string;
    delivered: number;
    opened: number;
    clicked: number;
    bounced: number;
    complained: number;
    unsubscribed: number;
  }

  interface TopEmail {
    template: string;
    subject: string;
    sent: number;
    openRate: number;
    clickRate: number;
  }

  interface EmailEvent {
    id: string;
    email: string;
    templateName: string | null;
    type: string;
    timestamp: string;
    data?: any;
  }

  interface Template {
    name: string;
    label: string;
    category?: string;
    description?: string;
  }

  interface CustomDateRange {
    startDate: Date | undefined;
    endDate: Date | undefined;
  }

  // State
  let isLoading = $state(true);
  let emailEvents = $state<EmailEvent[]>([]);
  let emailStats = $state<EmailStats>({
    total: 0,
    delivered: 0,
    opened: 0,
    clicked: 0,
    bounced: 0,
    complained: 0,
    unsubscribed: 0,
  });
  // We're using the DateRangePicker directly now, no need for timeRange
  let dateRange = $state<CustomDateRange>({
    startDate: subDays(new Date(), 7),
    endDate: new Date(),
  });

  // Fixed date range - last 7 days
  // These variables are used to display the date range in the UI
  const initialStartDate = subDays(new Date(), 7);
  const initialEndDate = new Date();

  // Create a calendar date range that will be updated when the user selects dates
  let calendarDateRange = $state<DateRange>({
    start: new CalendarDate(
      initialStartDate.getFullYear(),
      initialStartDate.getMonth() + 1,
      initialStartDate.getDate()
    ),
    end: new CalendarDate(
      initialEndDate.getFullYear(),
      initialEndDate.getMonth() + 1,
      initialEndDate.getDate()
    ),
  });
  let eventType = $state('all');
  let templateFilter = $state('all');
  let templates = $state<Template[]>([]);
  let chartData = $state<ChartDataPoint[]>([]);
  let topEmails = $state<TopEmail[]>([]);
  let isExporting = $state(false);
  let currentPage = $state(1);
  let itemsPerPage = $state(100);
  let totalEvents = $state(0);
  let totalPages = $state(1);

  // We're using the DateRangePicker directly now, no need for time range options

  // Event type options
  const eventTypeOptions = [
    { value: 'all', label: 'All Events' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'opened', label: 'Opened' },
    { value: 'clicked', label: 'Clicked' },
    { value: 'bounced', label: 'Bounced' },
    { value: 'complained', label: 'Complained' },
    { value: 'unsubscribed', label: 'Unsubscribed' },
  ];

  // Load data on mount
  onMount(async () => {
    await loadTemplates();
    // Load initial data
    await loadEmailStats();
    await loadEmailEvents();
    isLoading = false;
  });

  // Load email stats
  async function loadEmailStats() {
    isLoading = true;

    try {
      // Build query params
      const params = new URLSearchParams();

      // Use date range for filtering
      if (dateRange.startDate && dateRange.endDate) {
        params.append('startDate', startOfDay(dateRange.startDate).toISOString());
        params.append('endDate', endOfDay(dateRange.endDate).toISOString());
      }

      if (templateFilter !== 'all') params.append('template', templateFilter);

      const queryString = params.toString() ? `?${params.toString()}` : '';
      const response = await fetch(`/api/email/analytics/stats${queryString}`);

      if (response.ok) {
        const data = await response.json();

        // Update stats from the API response
        emailStats = data.stats || {
          total: 0,
          delivered: 0,
          opened: 0,
          clicked: 0,
          bounced: 0,
          complained: 0,
          unsubscribed: 0,
        };

        // Update chart data
        chartData = data.chartData || [];

        // Update top emails
        topEmails =
          data.topEmails?.map((template: any) => ({
            template: template.template,
            subject: template.subject || 'No Subject',
            sent: template.sent || 0,
            openRate: template.openRate || 0,
            clickRate: template.clickRate || 0,
          })) || [];
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to load email stats');
      }
    } catch (error) {
      console.error('Error loading email stats:', error);
      toast.error('Failed to load email stats');
    } finally {
      isLoading = false;
    }
  }

  // Load email events
  async function loadEmailEvents() {
    isLoading = true;

    try {
      // Build query params
      const params = new URLSearchParams();

      // Use date range for filtering
      if (dateRange.startDate && dateRange.endDate) {
        params.append('startDate', startOfDay(dateRange.startDate).toISOString());
        params.append('endDate', endOfDay(dateRange.endDate).toISOString());
      }

      if (eventType !== 'all') params.append('type', eventType);
      if (templateFilter !== 'all') params.append('template', templateFilter);
      params.append('page', currentPage.toString());
      params.append('limit', itemsPerPage.toString());

      // Get events count for pagination
      const countParams = new URLSearchParams(params);
      countParams.append('count', 'true');

      // First get the count
      const countResponse = await fetch(`/api/email/analytics/events?${countParams.toString()}`);

      if (countResponse.ok) {
        const countData = await countResponse.json();
        totalEvents = countData.count || 0;
        totalPages = Math.ceil(totalEvents / itemsPerPage) || 1;
      }

      // Then get the events for the current page
      const response = await fetch(`/api/email/analytics/events?${params.toString()}`);

      if (response.ok) {
        const events = await response.json();

        // Map the events to our format
        emailEvents = events.map((event: any) => ({
          id: event.id,
          email: event.email,
          templateName: event.templateName,
          type: event.type,
          timestamp: event.timestamp,
          data: event.data,
        }));
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to load email events');
      }
    } catch (error) {
      console.error('Error loading email events:', error);
      toast.error('Failed to load email events');
    } finally {
      isLoading = false;
    }
  }

  // Handle pagination
  function goToPage(page: number) {
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
      loadEmailEvents();
    }
  }

  // Load templates
  async function loadTemplates() {
    try {
      // Since we're using the worker API for templates, we need to use the worker endpoint
      const response = await fetch('/api/email/templates/list');

      if (response.ok) {
        const data = await response.json();

        // Map the templates to our format
        if (data.allTemplates && Array.isArray(data.allTemplates)) {
          templates = [
            { name: 'all', label: 'All Templates' },
            ...data.allTemplates.map((template: any) => ({
              name: template.name,
              label: template.label || template.name,
              category: template.category || 'Uncategorized',
              description: template.description || '',
            })),
          ];
        } else {
          // Fallback to sample templates if the API doesn't return the expected format
          templates = [
            { name: 'all', label: 'All Templates' },
            {
              name: 'welcome',
              label: 'Welcome Email',
              category: 'Transactional',
              description: 'Sent to new users',
            },
            {
              name: 'verification',
              label: 'Email Verification',
              category: 'Transactional',
              description: 'Verify email address',
            },
            {
              name: 'password-reset',
              label: 'Password Reset',
              category: 'Transactional',
              description: 'Reset password',
            },
          ];
        }
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to load templates');

        // Fallback to sample templates
        templates = [
          { name: 'all', label: 'All Templates' },
          {
            name: 'welcome',
            label: 'Welcome Email',
            category: 'Transactional',
            description: 'Sent to new users',
          },
          {
            name: 'verification',
            label: 'Email Verification',
            category: 'Transactional',
            description: 'Verify email address',
          },
          {
            name: 'password-reset',
            label: 'Password Reset',
            category: 'Transactional',
            description: 'Reset password',
          },
        ];
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      toast.error('Failed to load templates');

      // Fallback to sample templates
      templates = [
        { name: 'all', label: 'All Templates' },
        {
          name: 'welcome',
          label: 'Welcome Email',
          category: 'Transactional',
          description: 'Sent to new users',
        },
        {
          name: 'verification',
          label: 'Email Verification',
          category: 'Transactional',
          description: 'Verify email address',
        },
        {
          name: 'password-reset',
          label: 'Password Reset',
          category: 'Transactional',
          description: 'Reset password',
        },
      ];
    }
  }

  // Handle filter changes
  function handleFilterChange() {
    loadEmailStats();
    if (eventType !== 'all') {
      loadEmailEvents();
    }
  }

  // Handle date range changes from calendar component
  function handleCalendarDateChange(event: CustomEvent) {
    const { startDate, endDate, calendarValue } = event.detail;

    // Only update if we have valid dates
    if (startDate && endDate) {
      console.log('Date range changed:', { startDate, endDate });

      // Update the date range state
      dateRange = {
        startDate,
        endDate,
      };

      // Update the calendar date range if provided
      if (calendarValue) {
        calendarDateRange = calendarValue;
      }

      // Reset to first page when changing date range
      currentPage = 1;

      // Trigger data loading
      loadEmailStats();
      loadEmailEvents();
    }
  }

  // Date range is fixed to last 7 days to prevent infinite loops with calendar component

  // Format date
  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleString();
  }

  // Get event type badge class
  function getEventTypeBadgeClass(type: string): string {
    switch (type) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'opened':
        return 'bg-blue-100 text-blue-800';
      case 'clicked':
        return 'bg-purple-100 text-purple-800';
      case 'bounced':
        return 'bg-red-100 text-red-800';
      case 'complained':
        return 'bg-orange-100 text-orange-800';
      case 'unsubscribed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Export data
  async function exportData() {
    isExporting = true;

    try {
      // Build query params
      const params = new URLSearchParams();

      // Use date range for filtering
      if (dateRange.startDate && dateRange.endDate) {
        params.append('startDate', startOfDay(dateRange.startDate).toISOString());
        params.append('endDate', endOfDay(dateRange.endDate).toISOString());
      }

      if (eventType !== 'all') params.append('type', eventType);
      if (templateFilter !== 'all') params.append('template', templateFilter);

      const response = await fetch(`/api/email/analytics/export?${params.toString()}`);

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `email-analytics-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        toast.success('Data exported successfully');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to export data');
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      toast.error('Failed to export data');
    } finally {
      isExporting = false;
    }
  }

  // Pagination is handled directly in the UI when needed
</script>

<Card.Root>
  <Card.Header>
    <Card.Title>Email Analytics</Card.Title>
    <Card.Description>Track and analyze email performance metrics.</Card.Description>
  </Card.Header>

  <Card.Content>
    <Tabs.Root value="overview" class="w-full">
      <Tabs.List class="mb-4">
        <Tabs.Trigger value="overview">Overview</Tabs.Trigger>
        <Tabs.Trigger value="events">Event Log</Tabs.Trigger>
      </Tabs.List>

      <!-- Overview Tab -->
      <Tabs.Content value="overview">
        <OverviewTab
          {dateRange}
          {calendarDateRange}
          {templateFilter}
          {templates}
          {emailStats}
          {chartData}
          {topEmails}
          {isLoading}
          {isExporting}
          {handleCalendarDateChange}
          {handleFilterChange}
          {loadEmailStats}
          {exportData} />
      </Tabs.Content>

      <!-- Events Tab -->
      <Tabs.Content value="events">
        <EventsTab
          {dateRange}
          {calendarDateRange}
          {templateFilter}
          {eventType}
          {templates}
          {eventTypeOptions}
          {emailEvents}
          {isLoading}
          {currentPage}
          {totalPages}
          {totalEvents}
          {itemsPerPage}
          {handleCalendarDateChange}
          {loadEmailEvents}
          {goToPage}
          {getEventTypeBadgeClass}
          {formatDate} />
      </Tabs.Content>
    </Tabs.Root>
  </Card.Content>
</Card.Root>
