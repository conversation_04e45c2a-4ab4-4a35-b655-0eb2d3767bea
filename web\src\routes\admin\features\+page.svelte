<script lang="ts">
  import FeatureControlPanel from '$components/admin/FeatureControlPanel.svelte';
  import SEO from '$components/shared/SEO.svelte';
  import { Button } from '$lib/components/ui/button';
  import * as Alert from '$lib/components/ui/alert';
  import { AlertTriangle } from 'lucide-svelte';

  // Simple admin check (you can enhance this with proper auth)
  const isAdmin = true; // Replace with actual admin check
</script>

<SEO 
  title="Feature Control Panel | Admin"
  description="Manage feature flags and access controls"
/>

<div class="container mx-auto py-8">
  <div class="mb-8">
    <h1 class="text-3xl font-bold">Feature Control Panel</h1>
    <p class="text-muted-foreground mt-2">
      Manage feature flags and access controls across the application
    </p>
  </div>

  {#if !isAdmin}
    <Alert.Root variant="destructive">
      <AlertTriangle class="h-4 w-4" />
      <Alert.Title>Access Denied</Alert.Title>
      <Alert.Description>
        You don't have permission to access this page.
      </Alert.Description>
    </Alert.Root>
  {:else}
    <FeatureControlPanel />
    
    <div class="mt-8 space-y-4">
      <h2 class="text-xl font-semibold">Quick Actions</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="rounded-lg border p-4">
          <h3 class="font-medium mb-2">Development Mode</h3>
          <p class="text-sm text-muted-foreground mb-3">
            Set environment variables to control features globally
          </p>
          <div class="space-y-2 text-xs font-mono">
            <div>VITE_DISABLE_FEATURE_LIMITS=true</div>
            <div>VITE_ENABLE_ALL_FEATURES=true</div>
            <div>VITE_DISABLED_FEATURES=automation,ai</div>
          </div>
        </div>
        
        <div class="rounded-lg border p-4">
          <h3 class="font-medium mb-2">Runtime Control</h3>
          <p class="text-sm text-muted-foreground mb-3">
            Toggle features on/off without restarting the application
          </p>
          <Button variant="outline" size="sm" onclick={() => {
            // Example: Toggle automation feature
            import('$lib/config/feature-flags').then(({ toggleFeature }) => {
              toggleFeature('automation', false);
              alert('Automation feature disabled');
            });
          }}>
            Disable Automation
          </Button>
        </div>
        
        <div class="rounded-lg border p-4">
          <h3 class="font-medium mb-2">Debug Mode</h3>
          <p class="text-sm text-muted-foreground mb-3">
            Enable debug mode to see feature check details
          </p>
          <Button variant="outline" size="sm" onclick={() => {
            localStorage.setItem('feature-debug', 'true');
            alert('Debug mode enabled. Refresh the page to see debug info.');
          }}>
            Enable Debug
          </Button>
        </div>
      </div>
    </div>
  {/if}
</div>
