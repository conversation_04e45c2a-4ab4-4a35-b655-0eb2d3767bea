// File: web/src/routes/api/worker-process/[id]/+server.ts
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { prisma } from '$lib/server/prisma';
import { logger } from '$lib/server/logger';

/**
 * GET handler to retrieve a specific worker process by ID
 */
export const GET: RequestHandler = async ({ params, locals }) => {
  try {
    // Get the user from locals
    const user = locals.user;
    if (!user) {
      return new Response('Unauthorized', { status: 401 });
    }

    const id = params.id;
    if (!id) {
      return json({ error: 'Worker process ID is required' }, { status: 400 });
    }

    // Get the worker process
    const workerProcess = await prisma.workerProcess.findUnique({
      where: { id },
    });

    if (!workerProcess) {
      return json({ error: 'Worker process not found' }, { status: 404 });
    }

    // Parse the data field if it's a string
    let parsedData = workerProcess.data;
    if (typeof parsedData === 'string') {
      try {
        parsedData = JSON.parse(parsedData);
      } catch (parseError) {
        logger.warn(`Failed to parse worker process data for ${id}:`, parseError);
        // Keep the original string if parsing fails
      }
    }

    return json({
      success: true,
      workerProcess: {
        ...workerProcess,
        data: parsedData,
      },
    });
  } catch (error) {
    logger.error('Error retrieving worker process:', error);
    return json(
      { 
        success: false, 
        error: 'Failed to retrieve worker process',
        message: error instanceof Error ? error.message : String(error)
      }, 
      { status: 500 }
    );
  }
};

/**
 * PATCH handler to update a specific worker process by ID
 */
export const PATCH: RequestHandler = async ({ params, request, locals }) => {
  try {
    // Get the user from locals
    const user = locals.user;
    if (!user) {
      return new Response('Unauthorized', { status: 401 });
    }

    const id = params.id;
    if (!id) {
      return json({ error: 'Worker process ID is required' }, { status: 400 });
    }

    // Parse the request body
    const body = await request.json();
    
    // Build the update data
    const updateData: any = {};
    
    if (body.status) {
      updateData.status = body.status;
      
      // Set timestamps based on status
      if (body.status === 'PROCESSING' && !body.startedAt) {
        updateData.startedAt = new Date();
      } else if (body.status === 'COMPLETED' || body.status === 'FAILED') {
        updateData.completedAt = new Date();
      }
    }
    
    if (body.data !== undefined) {
      updateData.data = typeof body.data === 'string' ? body.data : JSON.stringify(body.data || {});
    }
    
    if (body.error !== undefined) {
      updateData.error = body.error;
    }

    // Update the worker process
    const workerProcess = await prisma.workerProcess.update({
      where: { id },
      data: updateData,
    });

    return json({
      success: true,
      workerProcess,
      message: `Worker process ${id} updated`,
    });
  } catch (error) {
    logger.error('Error updating worker process:', error);
    return json(
      { 
        success: false, 
        error: 'Failed to update worker process',
        message: error instanceof Error ? error.message : String(error)
      }, 
      { status: 500 }
    );
  }
};

/**
 * DELETE handler to delete a specific worker process by ID
 */
export const DELETE: RequestHandler = async ({ params, locals }) => {
  try {
    // Get the user from locals
    const user = locals.user;
    if (!user) {
      return new Response('Unauthorized', { status: 401 });
    }

    const id = params.id;
    if (!id) {
      return json({ error: 'Worker process ID is required' }, { status: 400 });
    }

    // Delete the worker process
    await prisma.workerProcess.delete({
      where: { id },
    });

    return json({
      success: true,
      message: `Worker process ${id} deleted`,
    });
  } catch (error) {
    logger.error('Error deleting worker process:', error);
    return json(
      { 
        success: false, 
        error: 'Failed to delete worker process',
        message: error instanceof Error ? error.message : String(error)
      }, 
      { status: 500 }
    );
  }
};
