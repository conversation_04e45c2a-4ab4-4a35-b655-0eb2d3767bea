// ws-server.js
import http from 'node:http';
import { handler } from './build/handler.js';
import { WebSocketServer } from 'ws';
import { createClient } from 'redis';
// Note: We can't directly import from $lib here since this file runs outside of SvelteKit
// The websocket-server.ts module will be used by SvelteKit server-side code

const PORT = process.env.PORT || 3000;

// Create an HTTP server that routes all normal requests to SvelteKit
const server = http.createServer(handler);

// Store connected clients
const clients = new Set();

// Redis channels to subscribe to
const REDIS_CHANNELS = [
  'resume-parsing::status',
  'resume-parsing::stream::status',
  'automation:status',
  'automation:jobs',
  'notifications',
  'websocket::broadcast',
];

// Redis client and subscriber
let redis;
let subscriber;

/**
 * Connect to Redis
 */
async function connectToRedis() {
  try {
    // Determine Redis URL based on environment
    const redisUrl =
      process.env.NODE_ENV === 'production'
        ? process.env.REDIS_URL ||
          'rediss://red-cvmu1me3jp1c738ve7ig:<EMAIL>:6379'
        : process.env.REDIS_URL || 'redis://localhost:6379';

    console.log(`Connecting to Redis at ${redisUrl.replace(/:[^:]*@/, ':***@')}`);

    // Create Redis client
    redis = createClient({
      url: redisUrl,
    });

    // Create subscriber client
    subscriber = redis.duplicate();

    // Handle Redis errors
    redis.on('error', (error) => {
      console.error('Redis client error:', error);
    });

    subscriber.on('error', (error) => {
      console.error('Redis subscriber error:', error);
    });

    // Connect to Redis
    await redis.connect();
    await subscriber.connect();

    console.log('Connected to Redis');

    // Subscribe to channels
    for (const channel of REDIS_CHANNELS) {
      await subscriber.subscribe(channel, (message) => {
        try {
          const data = JSON.parse(message);

          // Filter out heartbeat and non-essential messages
          if (data.type === 'heartbeat' || data.type === 'ping') {
            return; // Skip broadcasting these message types
          }

          // For resume parsing status updates
          if (channel.includes('resume-parsing')) {
            console.log(`[Redis] Received resume parsing update on ${channel}:`, data);

            // Ensure the message has the correct type for resume parsing
            // If it doesn't have a type, set it to resume_parsing_status
            if (!data.type) {
              if (data.status === 'completed') {
                data.type = 'resume_parsing_completed';
              } else {
                data.type = 'resume_parsing_status';
              }
              console.log(`[Redis] Set message type to ${data.type}`);
            }

            // Always broadcast resume parsing messages
            // This ensures all clients receive the updates
            broadcastToClients(data);
            return;
          }

          // For other messages, broadcast to all clients
          broadcastToClients(data);
        } catch (error) {
          console.error(`Error processing Redis message from ${channel}:`, error);
        }
      });
      console.log(`Subscribed to Redis channel: ${channel}`);
    }

    return true;
  } catch (error) {
    console.error('Failed to connect to Redis:', error);
    return false;
  }
}

// Create WebSocket server
const wss = new WebSocketServer({
  server,
  path: '/ws',
});

// Handle WebSocket connections
wss.on('connection', (ws) => {
  // Generate a unique client ID
  const clientId = Math.random().toString(36).substring(2, 15);

  // Store client info
  ws.clientId = clientId;
  ws.connectedAt = new Date();

  console.log(`[WebSocket] Client connected (ID: ${clientId})`);

  // Send welcome message with connection info
  ws.send(
    JSON.stringify({
      type: 'welcome',
      message: 'Connected to WebSocket server',
      clientId: clientId,
      timestamp: new Date().toISOString(),
    })
  );

  // Add client to the set
  clients.add(ws);
  console.log(`[WebSocket] Total connected clients: ${clients.size}`);

  // Handle messages from client
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      console.log(`[WebSocket] Received message from client ${clientId}:`, data);

      // If client sends a subscribe message, store their subscriptions
      if (data.type === 'subscribe') {
        ws.subscriptions = data.channels || [];
        console.log(`[WebSocket] Client ${clientId} subscribed to:`, ws.subscriptions);
      }

      // Echo the message back to the client
      ws.send(
        JSON.stringify({
          type: 'echo',
          data,
          timestamp: new Date().toISOString(),
        })
      );
    } catch (error) {
      console.error(`[WebSocket] Error processing message from client ${clientId}:`, error);
    }
  });

  // Set up ping interval to keep connection alive
  const pingInterval = setInterval(() => {
    if (ws.readyState === 1) {
      ws.send(JSON.stringify({ type: 'ping', timestamp: new Date().toISOString() }));
    } else {
      clearInterval(pingInterval);
    }
  }, 30000); // Send ping every 30 seconds

  // Handle client disconnection
  ws.on('close', () => {
    console.log(`[WebSocket] Client disconnected (ID: ${clientId})`);
    clients.delete(ws);
    clearInterval(pingInterval);
    console.log(`[WebSocket] Total connected clients: ${clients.size}`);
  });

  // Handle errors
  ws.on('error', (error) => {
    console.error(`[WebSocket] Error for client ${clientId}:`, error);
    clients.delete(ws);
    clearInterval(pingInterval);
  });
});

/**
 * Broadcast a message to all connected WebSocket clients
 */
function broadcastToClients(data) {
  // Determine the message type
  let messageType = data.type || 'notification';

  // Special handling for resume parsing messages
  if (!data.type && data.resumeId && data.status) {
    // This looks like a resume parsing message without a type
    if (data.status === 'completed') {
      messageType = 'resume_parsing_completed';
    } else {
      messageType = 'resume_parsing_status';
    }
    console.log(`[WebSocket] Detected resume parsing message, setting type to ${messageType}`);

    // Add the type to the data object as well
    data.type = messageType;
  }

  const payload = JSON.stringify({
    type: messageType,
    data,
    timestamp: new Date().toISOString(),
  });

  let sentCount = 0;
  const totalClients = clients.size;

  clients.forEach((client) => {
    // WebSocket.OPEN is 1
    if (client.readyState === 1) {
      try {
        client.send(payload);
        sentCount++;
      } catch (error) {
        console.error(`[WebSocket] Error sending message to client:`, error);
      }
    }
  });

  console.log(
    `[WebSocket] Broadcast message of type "${messageType}" to ${sentCount}/${totalClients} clients`
  );

  // If no clients received the message, log a warning
  if (sentCount === 0 && totalClients > 0) {
    console.warn(
      `[WebSocket] No clients received the "${messageType}" message despite ${totalClients} being connected`
    );
  }

  // Log the full payload for debugging if it's a resume parsing message
  if (messageType.includes('resume_parsing')) {
    console.log(`[WebSocket] Resume parsing message payload:`, JSON.parse(payload));
  }
}

// Connect to Redis before starting the server
connectToRedis().then((connected) => {
  if (connected) {
    console.log('Redis connection established');
  } else {
    console.warn('Failed to connect to Redis, continuing without Redis integration');
  }

  // Start the server
  server.listen(PORT, () => {
    console.log(`⚡️ SvelteKit server with WebSockets listening on http://localhost:${PORT}`);
    console.log(`WebSocket server available at ws://localhost:${PORT}/ws`);
  });
});
