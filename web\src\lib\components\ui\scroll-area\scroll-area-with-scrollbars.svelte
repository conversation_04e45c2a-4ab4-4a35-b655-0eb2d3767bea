<script lang="ts">
  import { ScrollArea as ScrollAreaPrimitive } from 'bits-ui';
  import { Scrollbar } from './index.js';
  import { cn } from '$lib/utils.js';
  import { onMount } from 'svelte';

  let {
    class: className,
    orientation = 'vertical',
    scrollbarXClasses = '',
    scrollbarYClasses = '',
    children,
    ...restProps
  } = $props<{
    class?: string;
    orientation?: 'vertical' | 'horizontal' | 'both';
    scrollbarXClasses?: string;
    scrollbarYClasses?: string;
    children?: any;
  }>();

  let viewportElement: any; // Using any to avoid type issues

  // This function removes the inline styles that hide scrollbars
  onMount(() => {
    // We'll use a timeout to ensure the element is mounted
    setTimeout(() => {
      if (viewportElement) {
        try {
          // Remove the data attribute that's targeted by the CSS
          viewportElement.removeAttribute('data-melt-scroll-area-viewport');

          // Override the inline styles that hide scrollbars
          if ('scrollbarWidth' in viewportElement.style) {
            viewportElement.style.scrollbarWidth = 'auto';
          }

          // Find and remove the style element that hides scrollbars
          const parentElement = viewportElement.parentElement;
          if (parentElement) {
            const styleElements = parentElement.querySelectorAll('style');
            styleElements.forEach((styleEl) => {
              if (styleEl.innerHTML.includes('[data-melt-scroll-area-viewport]')) {
                styleEl.remove();
              }
            });
          }
        } catch (error) {
          console.warn('Error setting scrollbar styles:', error);
        }
      }
    }, 0);
  });
</script>

<style>
  /* Override the scrollbar hiding styles */
  :global([data-melt-scroll-area-viewport]) {
    scrollbar-width: auto !important;
    -ms-overflow-style: auto !important;
    -webkit-overflow-scrolling: touch;
  }

  :global([data-melt-scroll-area-viewport]::-webkit-scrollbar) {
    display: block !important;
    width: 8px !important;
  }

  /* Style the scrollbars for better appearance */
  :global(::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
  }

  :global(::-webkit-scrollbar-track) {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  :global(::-webkit-scrollbar-thumb) {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  :global(::-webkit-scrollbar-thumb:hover) {
    background: rgba(0, 0, 0, 0.3);
  }
</style>

<ScrollAreaPrimitive.Root {...restProps} class={cn('relative overflow-auto', className)}>
  <ScrollAreaPrimitive.Viewport
    bind:this={viewportElement}
    class="h-full w-full overflow-auto rounded-[inherit]">
    {@render children?.()}
  </ScrollAreaPrimitive.Viewport>
  {#if orientation === 'vertical' || orientation === 'both'}
    <Scrollbar orientation="vertical" class={scrollbarYClasses} />
  {/if}
  {#if orientation === 'horizontal' || orientation === 'both'}
    <Scrollbar orientation="horizontal" class={scrollbarXClasses} />
  {/if}
  <ScrollAreaPrimitive.Corner />
</ScrollAreaPrimitive.Root>
