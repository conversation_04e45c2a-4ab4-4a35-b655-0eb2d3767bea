// scripts/seed-features.ts
import { PrismaClient } from '@prisma/client';

// Define interfaces for type safety
enum FeatureCategory {
  Core = 'core',
  JobSearch = 'job_search',
  Resume = 'resume',
  Applications = 'applications',
  Analytics = 'analytics',
  Team = 'team',
  Integration = 'integration',
}

enum LimitType {
  Monthly = 'monthly',
  Total = 'total',
  Concurrent = 'concurrent',
  Unlimited = 'unlimited',
}

interface FeatureLimit {
  id: string;
  name: string;
  description: string;
  defaultValue: number;
  type: LimitType;
  unit?: string;
  resetDay?: number;
}

interface Feature {
  id: string;
  name: string;
  description?: string;
  category?: FeatureCategory;
  icon?: string;
  beta?: boolean;
  limits?: string[];
}

const prisma = new PrismaClient();

// Feature limits
const FEATURE_LIMITS: Record<string, FeatureLimit> = {
  // Resume limits
  resume_scans_per_month: {
    id: 'resume_scans_per_month',
    name: 'Resume Scans',
    description: 'Number of resumes you can scan per month',
    defaultValue: 10,
    type: LimitType.Monthly,
    unit: 'scans',
    resetDay: 1,
  },
  resume_versions: {
    id: 'resume_versions',
    name: 'Resume Versions',
    description: 'Number of different resume versions you can create',
    defaultValue: 3,
    type: LimitType.Total,
    unit: 'versions',
  },
  resume_templates: {
    id: 'resume_templates',
    name: 'Resume Templates',
    description: 'Number of resume templates you can access',
    defaultValue: 5,
    type: LimitType.Total,
    unit: 'templates',
  },

  // Job search limits
  saved_jobs: {
    id: 'saved_jobs',
    name: 'Saved Jobs',
    description: 'Number of jobs you can save',
    defaultValue: 25,
    type: LimitType.Total,
    unit: 'jobs',
  },
  job_search_profiles: {
    id: 'job_search_profiles',
    name: 'Job Search Profiles',
    description: 'Number of job search profiles you can create',
    defaultValue: 3,
    type: LimitType.Total,
    unit: 'profiles',
  },
  job_alerts: {
    id: 'job_alerts',
    name: 'Job Alerts',
    description: 'Number of job alerts you can create',
    defaultValue: 5,
    type: LimitType.Total,
    unit: 'alerts',
  },

  // Application limits
  applications_per_month: {
    id: 'applications_per_month',
    name: 'Applications',
    description: 'Number of job applications you can submit per month',
    defaultValue: 25,
    type: LimitType.Monthly,
    unit: 'applications',
    resetDay: 1,
  },
  cover_letters_per_month: {
    id: 'cover_letters_per_month',
    name: 'Cover Letters',
    description: 'Number of cover letters you can generate per month',
    defaultValue: 10,
    type: LimitType.Monthly,
    unit: 'letters',
    resetDay: 1,
  },
};

// Features
const FEATURES: Feature[] = [
  // Core features
  {
    id: 'dashboard',
    name: 'Dashboard',
    description: 'Access to the main dashboard',
    category: FeatureCategory.Core,
    icon: 'layout-dashboard',
  },
  {
    id: 'profile',
    name: 'User Profile',
    description: 'Manage your user profile',
    category: FeatureCategory.Core,
    icon: 'user',
  },

  // Resume features
  {
    id: 'resume_scanner',
    name: 'Resume Scanner',
    description: 'Scan and analyze your resume against job descriptions',
    category: FeatureCategory.Resume,
    icon: 'file-scan',
    limits: ['resume_scans_per_month'],
  },
  {
    id: 'resume_builder',
    name: 'Resume Builder',
    description: 'Create and edit professional resumes',
    category: FeatureCategory.Resume,
    icon: 'file-edit',
    limits: ['resume_versions', 'resume_templates'],
  },

  // Job search features
  {
    id: 'job_search',
    name: 'Job Search',
    description: 'Search for jobs across multiple platforms',
    category: FeatureCategory.JobSearch,
    icon: 'search',
  },
  {
    id: 'job_save',
    name: 'Save Jobs',
    description: 'Save jobs for later review',
    category: FeatureCategory.JobSearch,
    icon: 'bookmark',
    limits: ['saved_jobs'],
  },
];

async function main(): Promise<void> {
  console.log('Seeding features...');
  let featuresCreated = 0;
  let limitsCreated = 0;

  // Seed features
  for (const feature of FEATURES) {
    try {
      const existingFeature = await prisma.feature.findUnique({
        where: { id: feature.id },
      });

      if (!existingFeature) {
        await prisma.feature.create({
          data: {
            id: feature.id,
            name: feature.name,
            description: feature.description || '',
            category: feature.category || 'general',
            icon: feature.icon || null,
            beta: feature.beta || false,
          },
        });
        console.log(`Created feature: ${feature.name}`);
        featuresCreated++;
      } else {
        console.log(`Feature already exists: ${feature.name}`);
      }

      // Seed feature limits
      if (feature.limits) {
        for (const limitId of feature.limits) {
          const limit = FEATURE_LIMITS[limitId];
          if (!limit) continue;

          const existingLimit = await prisma.featureLimit.findUnique({
            where: { id: limitId },
          });

          if (!existingLimit) {
            await prisma.featureLimit.create({
              data: {
                id: limitId,
                featureId: feature.id,
                name: limit.name,
                description: limit.description || '',
                defaultValue: limit.defaultValue.toString(),
                type: limit.type,
                unit: limit.unit || null,
                resetDay: limit.resetDay || null,
              },
            });
            console.log(`Created feature limit: ${limit.name}`);
            limitsCreated++;
          } else {
            console.log(`Feature limit already exists: ${limit.name}`);
          }
        }
      }
    } catch (error) {
      console.error(`Error creating feature ${feature.name}:`, error);
    }
  }

  console.log(
    `Seeding completed! Created ${featuresCreated} features and ${limitsCreated} limits.`
  );
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
