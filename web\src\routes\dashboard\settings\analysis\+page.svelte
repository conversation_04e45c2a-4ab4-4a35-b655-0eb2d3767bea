<script lang="ts">
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import * as Card from '$lib/components/ui/card/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import {
    Activity,
    TrendingUp,
    FileText,
    BriefcaseBusiness,
    Globe,
    AlertTriangle,
    Lightbulb,
    CheckCircle,
  } from 'lucide-svelte';
  import SEO from '$components/shared/SEO.svelte';
  import { goto } from '$app/navigation';
  import { FeatureGuardSimple } from '$lib/components/features';

  // Get user data from props
  let { data } = $props<{
    data: {
      user: any;
      applications?: any[];
      resumes?: any[];
    };
  }>();

  // Create reactive variables for the data with safe defaults
  let userData = data?.user || {};
  let applications = data?.applications || [];
  let resumes = data?.resumes || [];

  // State for active tab
  let activeTab = 'skills';

  // State for loading indicator
  let isLoadingSkills = false;

  // Handle tab change
  function handleTabChange(value: string) {
    activeTab = value;
  }
</script>

<SEO
  title="Career Analysis | Hirli"
  description="Analyze your career progress, skills, and application performance"
  keywords="career analysis, skill gap, job applications, resume effectiveness"
  url="https://hirli.com/dashboard/settings/analysis" />

<div class="border-border flex flex-col justify-between border-b p-6">
  <h2 class="text-lg font-semibold">Career Analysis</h2>
  <p class="text-muted-foreground text-foreground/80">
    Gain insights into your skills, applications, and career trajectory.
  </p>
</div>

<div class="p-6">
  <Tabs.Root value={activeTab} onValueChange={handleTabChange}>
    <Tabs.List>
      <Tabs.Trigger value="skills" class="flex items-center gap-2">
        <Activity class="h-4 w-4" />
        <span>Skill Analysis</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="career" class="flex items-center gap-2">
        <TrendingUp class="h-4 w-4" />
        <span>Career Path</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="applications" class="flex items-center gap-2">
        <BriefcaseBusiness class="h-4 w-4" />
        <span>Application Performance</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="resume" class="flex items-center gap-2">
        <FileText class="h-4 w-4" />
        <span>Resume Effectiveness</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="market" class="flex items-center gap-2">
        <Globe class="h-4 w-4" />
        <span>Market Insights</span>
      </Tabs.Trigger>
    </Tabs.List>

    <!-- Skill Analysis Tab -->
    <Tabs.Content value="skills" class="mt-6">
      <FeatureGuardSimple {userData} featureId="skill_gap_analysis">
        <div class="space-y-6">
          <Card.Root>
            <Card.Header>
              <Card.Title>Skill Gap Analysis</Card.Title>
              <Card.Description>
                Compare your skills against job requirements to identify areas for growth
              </Card.Description>
            </Card.Header>
            <Card.Content>
              {#if isLoadingSkills}
                <div class="flex items-center justify-center p-8">
                  <div class="text-primary h-8 w-8 animate-spin"></div>
                </div>
              {:else if applications.length === 0 || resumes.length === 0}
                <div class="flex items-center justify-center p-6 text-center">
                  <div class="max-w-md">
                    <AlertTriangle class="text-warning mx-auto h-12 w-12" />
                    <h3 class="mt-4 text-lg font-medium">Insufficient Data</h3>
                    <p class="text-muted-foreground mt-2">
                      We need more information about your skills and job applications to provide an
                      analysis. Update your resume or apply to more jobs to see insights here.
                    </p>
                    <div class="mt-6 flex justify-center gap-4">
                      <Button variant="outline" onclick={() => goto('/dashboard/documents')}>
                        Update Resume
                      </Button>
                      <Button variant="outline" onclick={() => goto('/dashboard/jobs')}>
                        Browse Jobs
                      </Button>
                    </div>
                  </div>
                </div>
              {:else}
                <p class="text-muted-foreground mb-6">
                  This feature is coming soon. We're working on analyzing your skills against job
                  requirements to provide personalized insights and recommendations.
                </p>
                <div class="rounded-md border p-4">
                  <div class="flex gap-3">
                    <Lightbulb class="text-primary h-5 w-5" />
                    <div>
                      <h4 class="font-medium">What you'll see here soon:</h4>
                      <ul class="text-muted-foreground mt-2 space-y-2 text-sm">
                        <li class="flex items-start gap-2">
                          <CheckCircle class="mt-0.5 h-4 w-4" />
                          <span>Skill match percentage against your target jobs</span>
                        </li>
                        <li class="flex items-start gap-2">
                          <CheckCircle class="mt-0.5 h-4 w-4" />
                          <span>Identification of missing skills in your profile</span>
                        </li>
                        <li class="flex items-start gap-2">
                          <CheckCircle class="mt-0.5 h-4 w-4" />
                          <span>Personalized recommendations for skill development</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              {/if}
            </Card.Content>
          </Card.Root>
        </div>
      </FeatureGuardSimple>
    </Tabs.Content>

    <!-- Career Path Tab -->
    <Tabs.Content value="career" class="mt-6">
      <FeatureGuardSimple {userData} featureId="career_trajectory">
        <div class="space-y-6">
          <Card.Root>
            <Card.Header>
              <Card.Title>Career Trajectory Analysis</Card.Title>
              <Card.Description>
                Visualize your career progression options based on your experience
              </Card.Description>
            </Card.Header>
            <Card.Content>
              <p class="text-muted-foreground mb-6">
                This feature is coming soon. We're working on analyzing career paths based on your
                experience and target roles to help you plan your next career move.
              </p>
              <div class="rounded-md border p-4">
                <div class="flex gap-3">
                  <Lightbulb class="text-primary h-5 w-5" />
                  <div>
                    <h4 class="font-medium">What you'll see here soon:</h4>
                    <ul class="text-muted-foreground mt-2 space-y-2 text-sm">
                      <li class="flex items-start gap-2">
                        <CheckCircle class="mt-0.5 h-4 w-4" />
                        <span>Visualization of potential career paths</span>
                      </li>
                      <li class="flex items-start gap-2">
                        <CheckCircle class="mt-0.5 h-4 w-4" />
                        <span>Recommended next roles based on your experience</span>
                      </li>
                      <li class="flex items-start gap-2">
                        <CheckCircle class="mt-0.5 h-4 w-4" />
                        <span>Skill requirements for career advancement</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </Card.Content>
          </Card.Root>
        </div>
      </FeatureGuardSimple>
    </Tabs.Content>

    <!-- Application Performance Tab -->
    <Tabs.Content value="applications" class="mt-6">
      <FeatureGuardSimple {userData} featureId="application_analytics">
        <div class="space-y-6">
          <Card.Root>
            <Card.Header>
              <Card.Title>Application Performance</Card.Title>
              <Card.Description>
                Track and analyze your job application success rates
              </Card.Description>
            </Card.Header>
            <Card.Content>
              <p class="text-muted-foreground mb-6">
                This feature is coming soon. We're working on analyzing your application performance
                to help you optimize your job search strategy.
              </p>
              <div class="rounded-md border p-4">
                <div class="flex gap-3">
                  <Lightbulb class="text-primary h-5 w-5" />
                  <div>
                    <h4 class="font-medium">What you'll see here soon:</h4>
                    <ul class="text-muted-foreground mt-2 space-y-2 text-sm">
                      <li class="flex items-start gap-2">
                        <CheckCircle class="mt-0.5 h-4 w-4" />
                        <span>Application success rates by job type and industry</span>
                      </li>
                      <li class="flex items-start gap-2">
                        <CheckCircle class="mt-0.5 h-4 w-4" />
                        <span>Response and interview conversion metrics</span>
                      </li>
                      <li class="flex items-start gap-2">
                        <CheckCircle class="mt-0.5 h-4 w-4" />
                        <span>Recommendations to improve application outcomes</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </Card.Content>
          </Card.Root>
        </div>
      </FeatureGuardSimple>
    </Tabs.Content>

    <!-- Resume Effectiveness Tab -->
    <Tabs.Content value="resume" class="mt-6">
      <FeatureGuardSimple {userData} featureId="resume_effectiveness">
        <div class="space-y-6">
          <Card.Root>
            <Card.Header>
              <Card.Title>Resume Effectiveness</Card.Title>
              <Card.Description>
                Measure how well your resume performs against job requirements
              </Card.Description>
            </Card.Header>
            <Card.Content>
              <p class="text-muted-foreground mb-6">
                This feature is coming soon. We're working on analyzing your resume effectiveness to
                help you optimize your resume for better job matches.
              </p>
              <div class="rounded-md border p-4">
                <div class="flex gap-3">
                  <Lightbulb class="text-primary h-5 w-5" />
                  <div>
                    <h4 class="font-medium">What you'll see here soon:</h4>
                    <ul class="text-muted-foreground mt-2 space-y-2 text-sm">
                      <li class="flex items-start gap-2">
                        <CheckCircle class="mt-0.5 h-4 w-4" />
                        <span>Keyword match analysis against target jobs</span>
                      </li>
                      <li class="flex items-start gap-2">
                        <CheckCircle class="mt-0.5 h-4 w-4" />
                        <span>Resume optimization score and suggestions</span>
                      </li>
                      <li class="flex items-start gap-2">
                        <CheckCircle class="mt-0.5 h-4 w-4" />
                        <span>Performance comparison of different resume versions</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </Card.Content>
          </Card.Root>
        </div>
      </FeatureGuardSimple>
    </Tabs.Content>

    <!-- Market Insights Tab -->
    <Tabs.Content value="market" class="mt-6">
      <FeatureGuardSimple {userData} featureId="market_intelligence">
        <div class="space-y-6">
          <Card.Root>
            <Card.Header>
              <Card.Title>Market Insights</Card.Title>
              <Card.Description>
                Get insights on job market trends relevant to your career
              </Card.Description>
            </Card.Header>
            <Card.Content>
              <p class="text-muted-foreground mb-6">
                This feature is coming soon. We're working on gathering market intelligence to help
                you make informed career decisions.
              </p>
              <div class="rounded-md border p-4">
                <div class="flex gap-3">
                  <Lightbulb class="text-primary h-5 w-5" />
                  <div>
                    <h4 class="font-medium">What you'll see here soon:</h4>
                    <ul class="text-muted-foreground mt-2 space-y-2 text-sm">
                      <li class="flex items-start gap-2">
                        <CheckCircle class="mt-0.5 h-4 w-4" />
                        <span>Industry hiring trends and demand forecasts</span>
                      </li>
                      <li class="flex items-start gap-2">
                        <CheckCircle class="mt-0.5 h-4 w-4" />
                        <span>Salary benchmarks for your target roles</span>
                      </li>
                      <li class="flex items-start gap-2">
                        <CheckCircle class="mt-0.5 h-4 w-4" />
                        <span>Emerging skill requirements in your field</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </Card.Content>
          </Card.Root>
        </div>
      </FeatureGuardSimple>
    </Tabs.Content>
  </Tabs.Root>
</div>
