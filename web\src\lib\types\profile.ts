// Profile data type definitions

export interface PersonalInfo {
  fullName?: string;
  email?: string;
  phone?: string;
  location?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  website?: string;
  dateOfBirth?: string;
  nationality?: string;
  gender?: string;
  profileImage?: string;
  summary?: string;
  headline?: string;
  jobTitle?: string;
}

export interface WorkExperience {
  id?: string;
  company?: string;
  title?: string;
  jobTitle?: string;
  location?: string;
  startDate?: string;
  endDate?: string;
  current?: boolean;
  description?: string;
  achievements?: string[];
  skills?: string[];
  industry?: string;
  companySize?: string;
  companyType?: string;
}

export interface Education {
  id?: string;
  school?: string;
  institution?: string;
  degree?: string;
  field?: string;
  location?: string;
  startDate?: string;
  endDate?: string;
  current?: boolean;
  description?: string;
  achievements?: string[];
  gpa?: string;
}

export interface Project {
  id?: string;
  name?: string;
  description?: string;
  startDate?: string;
  endDate?: string;
  current?: boolean;
  url?: string;
  skills?: string[];
  role?: string;
  achievements?: string[];
}

export interface PortfolioItem {
  id?: string;
  title?: string;
  description?: string;
  url?: string;
  imageUrl?: string;
  category?: string;
  date?: string;
}

export interface Link {
  id?: string;
  type?: string;
  url?: string;
  title?: string;
  description?: string;
}

export interface Certification {
  id?: string;
  name?: string;
  issuer?: string;
  date?: string;
  expiryDate?: string;
  url?: string;
  description?: string;
}

export interface Language {
  id?: string;
  language?: string;
  proficiency?: string;
}

export interface Achievement {
  id?: string;
  title?: string;
  date?: string;
  description?: string;
  issuer?: string;
}

export interface Publication {
  id?: string;
  title?: string;
  publisher?: string;
  date?: string;
  url?: string;
  description?: string;
  authors?: string[];
}

export interface Reference {
  id?: string;
  name?: string;
  company?: string;
  title?: string;
  relationship?: string;
  email?: string;
  phone?: string;
  reference?: string;
}

export interface JobPreferences {
  valueInRole?: string[];
  interestedRoles?: string[];
  roleSpecializations?: string[];
  preferredLocations?: string[];
  experienceLevel?: string;
  companySize?: string;
  desiredIndustries?: string[];
  avoidIndustries?: string[];
  preferredSkills?: string[];
  avoidSkills?: string[];
  minimumSalary?: string;
  securityClearance?: string;
  jobSearchStatus?: string;
  remotePreference?: string;
  travelPreference?: string;
  workSchedule?: string;
  visaSponsorship?: boolean;
  relocationPreference?: string;
}

export interface EmploymentInfo {
  ethnicity?: string;
  authorizedUS?: boolean;
  authorizedCanada?: boolean;
  authorizedUK?: boolean;
  requireSponsorship?: boolean;
  disability?: boolean;
  lgbtq?: boolean;
  gender?: string;
  veteran?: boolean;
}

export interface Skills {
  technical?: string[];
  soft?: string[];
  languages?: string[];
  tools?: string[];
  frameworks?: string[];
  methodologies?: string[];
  list?: string[]; // For backward compatibility
}

export interface ProfileData {
  // Basic metadata
  jobType?: string;
  industry?: string;

  // Legacy fields for backward compatibility
  fullName?: string;
  email?: string;
  phone?: string;
  location?: string;
  website?: string;
  summary?: string;
  skills?: string[] | string;

  // Structured data
  personalInfo?: PersonalInfo;
  workExperience?: WorkExperience[];
  education?: Education[];
  projects?: Project[];
  portfolio?: PortfolioItem[];
  links?: Link[];
  skillsData?: Skills;
  certifications?: Certification[];
  languages?: Language[];
  achievements?: Achievement[];
  publications?: Publication[];
  references?: Reference[];
  jobPreferences?: JobPreferences;
  employmentInfo?: EmploymentInfo;

  // Additional fields
  image?: string;
  lastUpdated?: string;
}

export interface Profile {
  id: string;
  name: string;
  userId?: string;
  teamId?: string;
  createdAt: string;
  updatedAt: string;
  defaultDocumentId?: string;
  data?: {
    id: string;
    profileId: string;
    data: string | ProfileData;
    createdAt: string;
    updatedAt: string;
  };
  defaultDocument?: any;
}
