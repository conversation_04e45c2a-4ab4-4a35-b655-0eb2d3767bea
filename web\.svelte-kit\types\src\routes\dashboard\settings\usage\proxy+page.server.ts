// @ts-nocheck
import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { prisma } from '$lib/server/prisma';
import { getFeatureUsage } from '$lib/server/feature-usage';
import { FEATURES } from '$lib/models/features';
import { FeatureAccessLevel } from '$lib/models/features/features';

export const load = async ({ locals }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  // Get basic user data
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
    select: {
      id: true,
      email: true,
      name: true,
      role: true,
      image: true,
    },
  });

  if (!userData) {
    throw redirect(302, '/auth/sign-in');
  }

  // Create a simplified user object without plan information
  const simplifiedUser = {
    id: userData.id,
    email: userData.email,
    name: userData.name,
    role: userData.role,
    image: userData.image,
  };

  // Get feature usage for the current month and previous month
  let featureUsage = [];
  let usageTrends = {
    currentMonthTotal: 0,
    previousMonthTotal: 0,
    trendPercent: 0,
    trendDirection: 'stable',
  };

  try {
    // Get all feature usage for the user
    const usageData = await getFeatureUsage(userData.id);
    console.log(`Loaded ${usageData.length} feature usage records for user ${userData.id}`);

    // Get current and previous month
    const now = new Date();
    const currentMonth = now.toISOString().substring(0, 7); // YYYY-MM format

    // Calculate previous month
    const prevMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      .toISOString()
      .substring(0, 7);

    // Calculate usage trends
    const currentMonthUsage = usageData.filter((u) => u.period === currentMonth);
    const prevMonthUsage = usageData.filter((u) => u.period === prevMonth);

    // Calculate total usage for current and previous months
    const currentTotalUsed = currentMonthUsage.reduce((sum, u) => sum + u.used, 0);
    const prevTotalUsed = prevMonthUsage.reduce((sum, u) => sum + u.used, 0);

    // Calculate usage trend percentage
    let usageTrendPercent = 0;
    if (prevTotalUsed > 0) {
      usageTrendPercent = Math.round(((currentTotalUsed - prevTotalUsed) / prevTotalUsed) * 100);
    }

    // Map features to include usage data
    featureUsage = FEATURES.map((feature) => {
      // Find usage data for this feature
      const featureUsageData = usageData.filter((usage) => usage.featureId === feature.id);

      // Map usage data to a more usable format
      const usage =
        featureUsageData.length > 0
          ? featureUsageData.map((usage) => ({
              limitId: usage.limitId,
              limitName: usage.limitName,
              used: usage.used,
              limit: usage.limit === null ? 'unlimited' : usage.limit,
              remaining: usage.remaining,
              percentUsed: usage.percentUsed,
              period: usage.period,
              description: usage.description || '',
            }))
          : feature.limits?.map((limit) => ({
              limitId: limit.id,
              limitName: limit.name,
              used: 0,
              limit: limit.defaultValue || 'unlimited',
              remaining: limit.defaultValue || 'unlimited',
              percentUsed: 0,
              period: currentMonth,
              description: limit.description || '',
              placeholder: true, // Mark as placeholder
            })) || [];

      // Determine access level based on usage
      let accessLevel = FeatureAccessLevel.NotIncluded;

      if (usage.length > 0) {
        // If any limit is unlimited, the feature is unlimited
        if (usage.some((u) => u.limit === 'unlimited')) {
          accessLevel = FeatureAccessLevel.Unlimited;
        }
        // If all limits have a value > 0, the feature is limited
        else if (
          usage.every(
            (u) => u.limit === 'unlimited' || (typeof u.limit === 'number' && u.limit > 0)
          )
        ) {
          accessLevel = FeatureAccessLevel.Limited;
        }
        // Otherwise, the feature is included
        else {
          accessLevel = FeatureAccessLevel.Included;
        }
      }

      // Calculate feature-specific trend
      const currentFeatureUsage = currentMonthUsage
        .filter((u) => u.featureId === feature.id)
        .reduce((sum, u) => sum + u.used, 0);

      const prevFeatureUsage = prevMonthUsage
        .filter((u) => u.featureId === feature.id)
        .reduce((sum, u) => sum + u.used, 0);

      let featureTrendPercent = 0;
      if (prevFeatureUsage > 0) {
        featureTrendPercent = Math.round(
          ((currentFeatureUsage - prevFeatureUsage) / prevFeatureUsage) * 100
        );
      }

      return {
        ...feature,
        usage,
        accessLevel,
        currentUsage: currentFeatureUsage,
        previousUsage: prevFeatureUsage,
        trendPercent: featureTrendPercent,
      };
    });

    // Update usage trends
    usageTrends = {
      currentMonthTotal: currentTotalUsed,
      previousMonthTotal: prevTotalUsed,
      trendPercent: usageTrendPercent,
      trendDirection: usageTrendPercent > 0 ? 'up' : usageTrendPercent < 0 ? 'down' : 'stable',
    };

    console.log(`Mapped ${featureUsage.length} features with usage data`);
  } catch (error) {
    console.error('Error loading feature usage:', error);

    // Even if there's an error, provide default feature data
    featureUsage = FEATURES.map((feature) => ({
      ...feature,
      usage:
        feature.limits?.map((limit) => ({
          limitId: limit.id,
          limitName: limit.name,
          used: 0,
          limit: limit.defaultValue || 'unlimited',
          remaining: limit.defaultValue || 'unlimited',
          percentUsed: 0,
          period: new Date().toISOString().substring(0, 7),
          description: limit.description || '',
          placeholder: true,
        })) || [],
      accessLevel: FeatureAccessLevel.NotIncluded,
      currentUsage: 0,
      previousUsage: 0,
      trendPercent: 0,
    }));
  }

  // Return the data
  return {
    user: simplifiedUser,
    featureUsage,
    usageTrends,
  };
};
