<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import * as Table from '$lib/components/ui/table/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  // Select component is replaced with native select
  import { Switch } from '$lib/components/ui/switch/index.js';
  import { Checkbox } from '$lib/components/ui/checkbox/index.js';
  import { toast } from 'svelte-sonner';
  import { onMount } from 'svelte';
  import {
    Bell,
    Send,
    RefreshCw,
    Search,
    Briefcase,
    MessageSquare,
    AlertTriangle,
    Info,
    CheckCircle,
  } from 'lucide-svelte';
  import SEO from '$components/shared/SEO.svelte';
  import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';

  export let data;
  const { users, currentUser } = data;

  // Form data
  let title = '';
  let message = '';
  let url = '';
  let type = 'info';
  let global = false;
  let loading = false;
  let selectedUsers: string[] = [];
  let searchQuery = '';
  let sentNotifications: any[] = [];
  let loadingSentNotifications = false;
  let activeTab = 'send';
  let testLogs: string[] = [];

  // Handle tab changes
  $: if (activeTab === 'test') {
    // Initialize test tab
    if (testLogs.length === 0) {
      testLogs = ['Test tab initialized. Create a notification to test the system.'];
    }
  }

  // Filter users based on search query
  $: filteredUsers = users.filter(
    (user) =>
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (user.name && user.name.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Toggle user selection
  function toggleUserSelection(userId: string) {
    if (selectedUsers.includes(userId)) {
      selectedUsers = selectedUsers.filter((id) => id !== userId);
    } else {
      selectedUsers = [...selectedUsers, userId];
    }
  }

  // Select all visible users
  function selectAllUsers() {
    if (selectedUsers.length === filteredUsers.length) {
      // If all are selected, deselect all
      selectedUsers = [];
    } else {
      // Otherwise, select all filtered users
      selectedUsers = filteredUsers.map((user) => user.id);
    }
  }

  // Send notification
  async function sendNotification() {
    if (!title || !message) {
      toast.error('Title and message are required');
      return;
    }

    if (!global && selectedUsers.length === 0) {
      toast.error('Please select at least one user or enable global notification');
      return;
    }

    loading = true;

    try {
      if (global) {
        // Send global notification
        const response = await fetch('/api/notifications/send', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            title,
            message,
            url: url || undefined,
            type,
            global: true,
          }),
        });

        const data = await response.json();

        if (response.ok) {
          toast.success('Global notification sent successfully');
          // Reset form
          resetForm();
        } else {
          toast.error(data.error || 'Failed to send notification');
        }
      } else {
        // Send to selected users
        const promises = selectedUsers.map((userId) =>
          fetch('/api/notifications/send', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              title,
              message,
              url: url || undefined,
              type,
              userId,
            }),
          })
        );

        const results = await Promise.allSettled(promises);
        const successCount = results.filter((result) => result.status === 'fulfilled').length;

        if (successCount > 0) {
          toast.success(`Sent notifications to ${successCount} users`);
          // Reset form
          resetForm();
        } else {
          toast.error('Failed to send notifications');
        }
      }

      // Refresh sent notifications
      await loadSentNotifications();
    } catch (error) {
      toast.error('An error occurred while sending notifications');
      console.error('Error sending notifications:', error);
    } finally {
      loading = false;
    }
  }

  // Reset form
  function resetForm() {
    title = '';
    message = '';
    url = '';
    type = 'info';
    global = false;
    selectedUsers = [];
    searchQuery = '';
  }

  // Load sent notifications
  async function loadSentNotifications() {
    loadingSentNotifications = true;
    try {
      // Fetch notification history from the API
      const response = await fetch('/api/notifications/history');

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to load notification history');
      }

      const data = await response.json();
      sentNotifications = data.notifications;
    } catch (error) {
      console.error('Error loading sent notifications:', error);
      toast.error('Failed to load notification history');

      // Fallback to empty array if there's an error
      sentNotifications = [];
    } finally {
      loadingSentNotifications = false;
    }
  }

  // Get icon for notification type
  function getTypeIcon(type: string) {
    switch (type) {
      case 'message':
        return MessageSquare;
      case 'job':
        return Briefcase;
      case 'error':
        return AlertTriangle;
      case 'success':
        return CheckCircle;
      case 'info':
      default:
        return Info;
    }
  }

  // Function to add a log entry
  function addTestLog(entry: string) {
    testLogs = [entry, ...testLogs].slice(0, 50); // Keep only the last 50 logs
    console.log(entry);
  }

  // Function to refresh notifications
  async function refreshNotifications() {
    if (loading) return;

    try {
      loading = true;
      addTestLog('Refreshing notifications from server...');

      // Fetch notifications from the API
      const response = await fetch('/api/notifications');

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to refresh notifications');
      }

      const data = await response.json();
      addTestLog(`Successfully refreshed ${data.notifications.length} notifications`);
      addTestLog(`Unread count: ${data.unreadCount}`);

      // Also refresh the notification history
      await loadSentNotifications();
      addTestLog('Notification history refreshed');
    } catch (error) {
      console.error('Error refreshing notifications:', error);
      addTestLog(`Error refreshing notifications: ${error}`);
    } finally {
      loading = false;
    }
  }

  // Function to create a test notification
  async function createTestNotification() {
    if (loading) return;

    try {
      loading = true;
      addTestLog('Creating test notification...');

      // Create notification data
      const notificationData = {
        title,
        message,
        url: url || undefined,
        type,
      };

      addTestLog(`Notification data: ${JSON.stringify(notificationData)}`);

      // Send request to server
      const response = await fetch('/api/admin/test-notification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notificationData),
      });

      // Parse response
      const result = await response.json();

      if (response.ok && result.success) {
        addTestLog(`Test notification created successfully`);
        addTestLog(`Database ID: ${result.databaseId}`);
        addTestLog('The notification should now appear in the dropdown');

        // Refresh notification history
        await loadSentNotifications();
      } else {
        addTestLog(`Error from server: ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error creating test notification:', error);
      addTestLog(`Error creating test notification: ${error}`);
    } finally {
      loading = false;
    }
  }

  // Load sent notifications on mount
  onMount(() => {
    loadSentNotifications();
  });
</script>

<SEO title="Admin Notifications - Hirli" />

<div class="border-border flex flex-col gap-1 border-b p-4">
  <div class="flex items-center justify-between">
    <h1 class="text-2xl font-bold">Admin Notifications</h1>
  </div>
</div>

<Tabs.Root bind:value={activeTab} class="w-full">
  <div class="border-border border-b p-0">
    <Tabs.List class="flex flex-row gap-2 divide-x">
      <Tabs.Trigger value="send" class="flex-1 border-none">Send Notifications</Tabs.Trigger>
      <Tabs.Trigger value="history" class="flex-1 border-none">Notification History</Tabs.Trigger>
      <Tabs.Trigger value="test" class="flex-1 border-none">Test Notifications</Tabs.Trigger>
    </Tabs.List>
  </div>
  <!-- Send Notifications Tab -->
  <Tabs.Content value="send">
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
      <!-- Notification Form -->
      <Card.Root>
        <Card.Header>
          <Card.Title>Send Notification</Card.Title>
          <Card.Description>Create and send notifications to users or globally</Card.Description>
        </Card.Header>
        <Card.Content>
          <form class="space-y-4">
            <div class="space-y-2">
              <Label for="title">Title</Label>
              <Input id="title" bind:value={title} placeholder="Notification title" />
            </div>

            <div class="space-y-2">
              <Label for="message">Message</Label>
              <Textarea bind:value={message} />
            </div>

            <div class="space-y-2">
              <Label for="url">URL (optional)</Label>
              <Input id="url" bind:value={url} placeholder="https://example.com" />
            </div>

            <div class="space-y-2">
              <Label for="type">Type</Label>
              <select
                bind:value={type}
                class="border-input bg-background ring-offset-background w-full rounded-md border px-3 py-2 text-sm">
                <option value="info">Info</option>
                <option value="success">Success</option>
                <option value="error">Error</option>
                <option value="job">Job</option>
                <option value="message">Message</option>
              </select>
            </div>

            <div class="flex items-center space-x-2">
              <Switch id="global" bind:checked={global} />
              <Label for="global">Send to all users (global notification)</Label>
            </div>
          </form>
        </Card.Content>
        <Card.Footer>
          <Button variant="outline" onclick={sendNotification} disabled={loading}>
            {#if loading}
              <RefreshCw class="mr-2 h-4 w-4 animate-spin" />
            {:else}
              <Send class="mr-2 h-4 w-4" />
            {/if}
            {loading ? 'Sending...' : 'Send Notification'}
          </Button>
        </Card.Footer>
      </Card.Root>

      <!-- User Selection -->
      <Card.Root class={global ? 'opacity-50' : ''}>
        <Card.Header>
          <Card.Title>Select Recipients</Card.Title>
          <Card.Description>Choose which users will receive the notification</Card.Description>
        </Card.Header>
        <Card.Content>
          <div class="mb-4 flex items-center space-x-2">
            <div class="relative flex-1">
              <Search class="text-muted-foreground absolute left-2 top-2.5 h-4 w-4" />
              <Input placeholder="Search users..." bind:value={searchQuery} class="pl-8" />
            </div>
            <Button variant="outline" onclick={selectAllUsers} disabled={global}>
              {selectedUsers.length === filteredUsers.length ? 'Deselect All' : 'Select All'}
            </Button>
          </div>

          <div class="max-h-[400px] overflow-y-auto rounded-md border">
            {#if filteredUsers.length === 0}
              <div class="flex h-20 items-center justify-center">
                <p class="text-muted-foreground">No users found</p>
              </div>
            {:else}
              <Table.Root>
                <Table.Header>
                  <Table.Row>
                    <Table.Head class="w-[50px]"></Table.Head>
                    <Table.Head>User</Table.Head>
                    <Table.Head>Role</Table.Head>
                  </Table.Row>
                </Table.Header>
                <Table.Body>
                  {#each filteredUsers as user (user.id)}
                    <Table.Row>
                      <Table.Cell>
                        <Checkbox
                          checked={selectedUsers.includes(user.id)}
                          onCheckedChange={() => toggleUserSelection(user.id)}
                          disabled={global} />
                      </Table.Cell>
                      <Table.Cell>
                        <div class="flex items-center gap-2">
                          <Avatar class="h-8 w-8">
                            <AvatarImage src={user.image || ''} alt={user.name || user.email} />
                            <AvatarFallback>
                              {user.name
                                ? user.name
                                    .split(' ')
                                    .map((n) => n[0])
                                    .join('')
                                    .toUpperCase()
                                : user.email.substring(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p class="text-sm font-medium">{user.name || 'Unnamed User'}</p>
                            <p class="text-muted-foreground text-xs">{user.email}</p>
                          </div>
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <Badge variant={user.role === 'admin' ? 'default' : 'outline'}>
                          {user.role || 'user'}
                        </Badge>
                      </Table.Cell>
                    </Table.Row>
                  {/each}
                </Table.Body>
              </Table.Root>
            {/if}
          </div>

          <div class="mt-4 flex items-center justify-between">
            <p class="text-muted-foreground text-sm">
              {selectedUsers.length} of {users.length} users selected
            </p>
          </div>
        </Card.Content>
      </Card.Root>
    </div>
  </Tabs.Content>

  <!-- Notification History Tab -->
  <Tabs.Content value="history">
    <Card.Root>
      <Card.Header>
        <div class="flex items-center justify-between">
          <Card.Title>Notification History</Card.Title>
          <Button variant="outline" size="sm" onclick={loadSentNotifications}>
            <RefreshCw class={`mr-2 h-4 w-4 ${loadingSentNotifications ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
        <Card.Description>View previously sent notifications</Card.Description>
      </Card.Header>
      <Card.Content>
        {#if loadingSentNotifications}
          <div class="flex h-40 items-center justify-center">
            <RefreshCw class="text-muted-foreground h-8 w-8 animate-spin" />
          </div>
        {:else if sentNotifications.length === 0}
          <div class="flex h-40 flex-col items-center justify-center">
            <Bell class="text-muted-foreground mb-2 h-12 w-12 opacity-20" />
            <p class="text-muted-foreground">No notifications have been sent yet</p>
          </div>
        {:else}
          <Table.Root>
            <Table.Header>
              <Table.Row>
                <Table.Head>Type</Table.Head>
                <Table.Head>Title</Table.Head>
                <Table.Head>Recipients</Table.Head>
                <Table.Head>Sent By</Table.Head>
                <Table.Head>Sent At</Table.Head>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {#each sentNotifications as notification (notification.id)}
                <Table.Row>
                  <Table.Cell>
                    <div class="flex items-center gap-2">
                      <svelte:component
                        this={getTypeIcon(notification.type)}
                        class="text-muted-foreground h-4 w-4" />
                      <span class="capitalize">{notification.type}</span>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div>
                      <p class="font-medium">{notification.title}</p>
                      <p class="text-muted-foreground text-xs">{notification.message}</p>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <Badge variant={notification.global ? 'default' : 'outline'}>
                      {notification.global ? 'All Users' : notification.recipients}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>{notification.sentBy || currentUser.email}</Table.Cell>
                  <Table.Cell>
                    {new Date(notification.sentAt).toLocaleString()}
                  </Table.Cell>
                </Table.Row>
              {/each}
            </Table.Body>
          </Table.Root>
        {/if}
      </Card.Content>
    </Card.Root>
  </Tabs.Content>

  <!-- Test Notifications Tab -->
  <Tabs.Content value="test">
    <Card.Root>
      <Card.Header>
        <Card.Title>Test Notification System</Card.Title>
        <Card.Description>
          Test creating notifications in the database and displaying them in the UI
        </Card.Description>
      </Card.Header>
      <Card.Content>
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <!-- Test Form -->
          <div>
            <form class="space-y-4">
              <div class="space-y-2">
                <Label for="test-title">Title</Label>
                <Input id="test-title" bind:value={title} />
              </div>

              <div class="space-y-2">
                <Label for="test-message">Message</Label>
                <Textarea bind:value={message} />
              </div>

              <div class="space-y-2">
                <Label for="test-url">URL (optional)</Label>
                <Input id="test-url" bind:value={url} />
              </div>

              <div class="space-y-2">
                <Label for="test-type">Type</Label>
                <select
                  id="test-type"
                  bind:value={type}
                  class="border-input bg-background ring-offset-background w-full rounded-md border px-3 py-2 text-sm">
                  <option value="info">Info</option>
                  <option value="success">Success</option>
                  <option value="error">Error</option>
                  <option value="job">Job</option>
                  <option value="message">Message</option>
                </select>
              </div>
            </form>

            <div class="mt-4 flex gap-2">
              <Button variant="outline" onclick={refreshNotifications} disabled={loading}>
                <RefreshCw class={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                Refresh Notifications
              </Button>
              <Button onclick={createTestNotification} disabled={loading}>
                {#if loading}
                  <RefreshCw class="mr-2 h-4 w-4 animate-spin" />
                {:else}
                  <Bell class="mr-2 h-4 w-4" />
                {/if}
                Create Notification
              </Button>
            </div>
          </div>

          <!-- Test Logs -->
          <div>
            <div class="mb-2 font-medium">Test Logs</div>
            <div
              class="h-[400px] overflow-y-auto rounded-md border bg-gray-50 p-2 dark:bg-gray-900">
              {#if testLogs.length === 0}
                <div class="flex h-full items-center justify-center text-gray-400">
                  No logs yet. Create a notification to see logs.
                </div>
              {:else}
                {#each testLogs as log}
                  <div class="mb-1 border-b pb-1 font-mono text-sm">{log}</div>
                {/each}
              {/if}
            </div>
          </div>
        </div>
      </Card.Content>
    </Card.Root>
  </Tabs.Content>
</Tabs.Root>
