<script lang="ts">
  import { goto } from '$app/navigation';
  import * as Accordion from '$lib/components/ui/accordion/index.js';
  import { ChevronLeft, ChevronRight } from 'lucide-svelte';
  import MaintenanceAccordion from './MaintenanceAccordion.svelte';
  import type { Incident } from './types';

  // Props
  const { 
    maintenance, 
    currentMonth, 
    currentYear, 
    hasNextMonth, 
    hasPrevMonth 
  } = $props<{
    maintenance: Incident[];
    currentMonth: number;
    currentYear: number;
    hasNextMonth: boolean;
    hasPrevMonth: boolean;
  }>();

  // Format month name
  function formatMonth(month: number, year: number): string {
    return new Intl.DateTimeFormat('en-US', {
      month: 'long',
      year: 'numeric',
    }).format(new Date(year, month - 1, 1));
  }

  // Navigate to previous month
  function goToPrevMonth() {
    let prevMonth = currentMonth - 1;
    let prevYear = currentYear;
    
    if (prevMonth < 1) {
      prevMonth = 12;
      prevYear--;
    }
    
    goto(`/system-status/history?month=${prevMonth}&year=${prevYear}`);
  }

  // Navigate to next month
  function goToNextMonth() {
    let nextMonth = currentMonth + 1;
    let nextYear = currentYear;
    
    if (nextMonth > 12) {
      nextMonth = 1;
      nextYear++;
    }
    
    goto(`/system-status/history?month=${nextMonth}&year=${nextYear}`);
  }
</script>

<div class="flex flex-col gap-4">
  <!-- Month Navigation -->
  <div class="flex items-center justify-between px-4">
    <button 
      class="flex items-center gap-1 rounded-md p-2 hover:bg-gray-100 dark:hover:bg-gray-800"
      on:click={goToPrevMonth}
      disabled={!hasPrevMonth}
      aria-label="Previous month">
      <ChevronLeft class="h-4 w-4" />
      <span>Previous</span>
    </button>
    
    <h2 class="text-xl font-semibold">{formatMonth(currentMonth, currentYear)}</h2>
    
    <button 
      class="flex items-center gap-1 rounded-md p-2 hover:bg-gray-100 dark:hover:bg-gray-800"
      on:click={goToNextMonth}
      disabled={!hasNextMonth}
      aria-label="Next month">
      <span>Next</span>
      <ChevronRight class="h-4 w-4" />
    </button>
  </div>

  <!-- Maintenance Events List -->
  <div class="px-4 pb-8">
    {#if maintenance && maintenance.length > 0}
      <Accordion.Root type="multiple" class="w-full space-y-4">
        {#each maintenance as incident, i}
          <MaintenanceAccordion {incident} index={i} />
        {/each}
      </Accordion.Root>
    {:else}
      <div class="mt-8 rounded-lg border p-8 text-center">
        <p class="text-muted-foreground">No notices or maintenance events for {formatMonth(currentMonth, currentYear)}</p>
      </div>
    {/if}
  </div>
</div>
