<script lang="ts">
  import { Sun, Moon, Laptop } from 'lucide-svelte';
  import { Button } from '$lib/components/ui/button';
  import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
  } from '$lib/components/ui/dropdown-menu';
  import { store, setTheme, type ThemeMode } from '$lib/stores/store';

  // Accept props for styling
  const { variant = 'default', size = 'default' } = $props<{
    variant?: 'default' | 'outline' | 'ghost';
    size?: 'default' | 'sm' | 'lg' | 'icon';
  }>();

  // Get current theme from store
  const currentTheme = $derived($store?.theme || 'system');

  // Handle theme change
  function handleThemeChange(theme: ThemeMode) {
    setTheme(theme);
  }
</script>

<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button {variant} {size} class="gap-2">
      {#if currentTheme === 'light'}
        <Sun class="h-4 w-4" />
        {#if size !== 'icon'}
          <span>Light</span>
        {/if}
      {:else if currentTheme === 'dark'}
        <Moon class="h-4 w-4" />
        {#if size !== 'icon'}
          <span>Dark</span>
        {/if}
      {:else}
        <Laptop class="h-4 w-4" />
        {#if size !== 'icon'}
          <span>System</span>
        {/if}
      {/if}
      <span class="sr-only">Toggle theme</span>
    </Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent align="end">
    <DropdownMenuItem on:click={() => handleThemeChange('light')}>
      <Sun class="mr-2 h-4 w-4" />
      <span>Light</span>
    </DropdownMenuItem>
    <DropdownMenuItem on:click={() => handleThemeChange('dark')}>
      <Moon class="mr-2 h-4 w-4" />
      <span>Dark</span>
    </DropdownMenuItem>
    <DropdownMenuItem on:click={() => handleThemeChange('system')}>
      <Laptop class="mr-2 h-4 w-4" />
      <span>System</span>
    </DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
