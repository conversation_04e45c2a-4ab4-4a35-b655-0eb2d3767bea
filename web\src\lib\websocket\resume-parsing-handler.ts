/**
 * Resume Parsing WebSocket Handler
 *
 * This module handles WebSocket messages related to resume parsing status updates.
 */

import { toast } from 'svelte-sonner';
import websocket from '$lib/websocket/websocket-singleton';

// Event listeners for resume parsing status updates
const parsingListeners = new Map<
  string,
  {
    onCompleted: (resumeId: string, parsedData: any) => void;
    onFailed: (resumeId: string, error: string) => void;
  }
>();

// Track recently processed notifications to avoid duplicates
// Format: Map<resumeId+jobId+status, timestamp>
const recentNotifications = new Map<string, number>();

// How long to consider a notification as "recent" (in milliseconds)
const NOTIFICATION_DEDUPLICATION_WINDOW = 3000; // 3 seconds - reduced to avoid missing important updates

// Track processed message IDs to avoid duplicates
// This is more reliable than just tracking resumeId+status
const processedMessageIds = new Set<string>();

/**
 * Register a listener for resume parsing status updates
 */
export function registerParsingListener(
  resumeId: string,
  onCompleted: (resumeId: string, parsedData: any) => void,
  onFailed: (resumeId: string, error: string) => void
): void {
  parsingListeners.set(resumeId, { onCompleted, onFailed });
  console.log(`Registered parsing listener for resume ${resumeId}`);
}

/**
 * Unregister a listener for resume parsing status updates
 */
export function unregisterParsingListener(resumeId: string): void {
  parsingListeners.delete(resumeId);
  console.log(`Unregistered parsing listener for resume ${resumeId}`);
}

/**
 * Generate a unique key for a notification
 */
function getNotificationKey(resumeId: string, status: string, jobId?: string): string {
  return `${resumeId}:${status}:${jobId || 'no-job-id'}`;
}

/**
 * Check if a notification has been recently processed
 */
function isRecentNotification(resumeId: string, status: string, jobId?: string): boolean {
  const now = Date.now();
  const key = getNotificationKey(resumeId, status, jobId);

  // Check if we have this notification key
  if (!recentNotifications.has(key)) {
    return false;
  }

  // Check if the notification is recent
  const timestamp = recentNotifications.get(key);
  return now - timestamp! < NOTIFICATION_DEDUPLICATION_WINDOW;
}

// These utility functions are kept for future use if needed

/**
 * Mark a notification as recently processed
 */
function markNotificationAsProcessed(resumeId: string, status: string, jobId?: string): void {
  const now = Date.now();
  const key = getNotificationKey(resumeId, status, jobId);

  // Set the timestamp for this notification
  recentNotifications.set(key, now);

  // Clean up old notifications after a while
  setTimeout(() => {
    recentNotifications.delete(key);
  }, NOTIFICATION_DEDUPLICATION_WINDOW * 2);
}

/**
 * Handle a resume parsing status update message
 */
export function handleParsingStatusUpdate(message: any): void {
  const { resumeId, status, parsedData, error, profileId, jobId, messageId } = message;

  if (!resumeId) {
    console.warn('Received parsing status update without resumeId:', message);
    return;
  }

  // Dispatch a custom event for parsing status updates (pending/processing)
  // This ensures the UI is updated even if there's no registered listener
  if (status === 'pending' || status === 'processing') {
    if (typeof window !== 'undefined') {
      const resumeEvent = new CustomEvent('resume-parsing-status', {
        detail: {
          resumeId,
          profileId,
          status: 'parsing',
          isParsing: true,
          message: 'Resume parsing in progress',
          timestamp: new Date().toISOString(),
        },
      });
      window.dispatchEvent(resumeEvent);
      console.log('Dispatched resume-parsing-status event with isParsing=true');

      // Also check if we need to update the worker process in the database
      try {
        fetch(`/api/resume/${resumeId}/parsing-status`)
          .then((response) => response.json())
          .then((data) => {
            if (!data.isParsing) {
              console.log(
                `Resume ${resumeId} is not marked as parsing in the database, creating worker process`
              );
              // Create a worker process if one doesn't exist
              fetch(`/api/worker-process`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  type: 'resume-parsing',
                  status: 'PENDING',
                  data: {
                    resumeId,
                    profileId,
                    userId: null, // Will be set by the server
                    timestamp: new Date().toISOString(),
                  },
                }),
              }).catch((error) => {
                console.error('Error creating worker process:', error);
              });
            }
          })
          .catch((error) => {
            console.error('Error checking parsing status:', error);
          });
      } catch (error) {
        console.error('Error ensuring worker process exists:', error);
      }
    }
  }

  // If we have a profileId, ensure the document is associated with the profile
  if (profileId) {
    try {
      // Call the API to get the document for this resume
      fetch(`/api/resume/${resumeId}`)
        .then((response) => response.json())
        .then((resumeData) => {
          if (resumeData && resumeData.document && resumeData.document.id) {
            const documentId = resumeData.document.id;

            // Check if the document is already associated with the profile
            if (resumeData.document.profileId !== profileId) {
              console.log(`Associating document ${documentId} with profile ${profileId}`);

              // Update the document to associate it with the profile
              fetch(`/api/document/${documentId}`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  profileId: profileId,
                }),
              })
                .then((response) => {
                  if (response.ok) {
                    console.log(`Document ${documentId} associated with profile ${profileId}`);
                  } else {
                    console.warn(
                      `Failed to associate document ${documentId} with profile ${profileId}`
                    );
                  }
                })
                .catch((error) => {
                  console.error('Error associating document with profile:', error);
                });
            } else {
              console.log(`Document ${documentId} is already associated with profile ${profileId}`);
            }
          }
        })
        .catch((error) => {
          console.error('Error fetching resume data:', error);
        });
    } catch (error) {
      console.error('Error ensuring document-profile association:', error);
    }
  }

  // Generate a unique message ID if not provided
  const uniqueMessageId = messageId ?? `${resumeId}:${status}:${jobId ?? ''}:${Date.now()}`;

  // For completed status, we want to process it even if it's a duplicate
  // This ensures we don't miss important completion notifications
  if (status !== 'completed') {
    // Skip if we've already processed this notification recently
    if (isRecentNotification(resumeId, status, jobId)) {
      console.log(`Skipping duplicate notification for resume ${resumeId} with status ${status}`);
      return;
    }

    // Skip if we've already processed this exact message ID
    if (processedMessageIds.has(uniqueMessageId)) {
      console.log(`Skipping already processed message ID: ${uniqueMessageId}`);
      return;
    }
  } else {
    console.log(`Processing completion notification for resume ${resumeId} even if duplicate`);

    // For completed status, we want to ensure the UI is updated
    // Dispatch a custom event to update UI components
    if (typeof window !== 'undefined') {
      const resumeEvent = new CustomEvent('resume-parsing-completed', {
        detail: {
          resumeId,
          profileId,
          message: 'Resume parsing completed successfully',
          timestamp: new Date().toISOString(),
        },
      });
      window.dispatchEvent(resumeEvent);
      console.log('Dispatched resume-parsing-completed event from resume-parsing-handler');
    }
  }

  // Mark this notification and message ID as processed
  markNotificationAsProcessed(resumeId, status, jobId);
  processedMessageIds.add(uniqueMessageId);

  // Clean up message ID after a while
  setTimeout(() => {
    processedMessageIds.delete(uniqueMessageId);
  }, NOTIFICATION_DEDUPLICATION_WINDOW * 2);

  console.log(`Processing parsing status update for resume ${resumeId}:`, status);
  console.log('Parsed data:', parsedData);
  console.log('Profile ID:', profileId);
  console.log('Job ID:', jobId);
  console.log('Message ID:', uniqueMessageId);

  // Get the listener for this resume
  const listener = parsingListeners.get(resumeId);

  if (!listener) {
    console.log(`No listener registered for resume ${resumeId}`);

    // Show a toast notification anyway
    if (status === 'completed') {
      // Verify the resume is actually parsed by checking the database
      if (typeof window !== 'undefined') {
        fetch(`/api/resume/${resumeId}/status`)
          .then((response) => response.json())
          .then((data) => {
            console.log(`Fetched latest resume status for ${resumeId}:`, data);

            // If the data doesn't match the notification, show an error
            if (!data.isParsed) {
              console.warn(
                'Resume notification indicates parsing complete, but database shows isParsed=false'
              );
              toast.error('Resume parsing failed: Database not updated properly');
              return;
            }

            // Only show success and update profile if database confirms parsing
            toast.success('Resume parsing completed');

            // If we have a profileId, update the ProfileData record with the parsed resume data
            if (profileId) {
              console.log(
                `Updating ProfileData record for profile ${profileId} with parsed resume data from resume ${resumeId}`
              );

              // First, fetch the current profile data
              fetch(`/api/profile/${profileId}/data`)
                .then((response) => {
                  if (!response.ok) {
                    throw new Error('Failed to update profile data');
                  }
                  return response.json();
                })
                .then((result) => {
                  console.log('Profile data updated successfully:', result);
                  toast.success('Profile updated with resume data');

                  // Refresh the page after a short delay to show the updated profile
                  if (typeof window !== 'undefined') {
                    setTimeout(() => {
                      window.location.reload();
                    }, 1500);
                  }
                })
                .catch((error) => {
                  console.error('Error updating profile data:', error);
                  toast.error('Failed to update profile with resume data');
                });
            }
          })
          .catch((err) => {
            console.error('Error fetching resume status:', err);
            toast.error('Error verifying resume parsing status');
          });
      } else {
        toast.success('Resume parsing completed');
      }
    } else if (status === 'failed') {
      toast.error(`Resume parsing failed: ${error ?? 'Unknown error'}`);
    }

    return;
  }

  // Make window.parsingListeners available for debugging
  if (typeof window !== 'undefined') {
    (window as any).parsingListeners = parsingListeners;
  }

  // For registered listeners, verify the database state before calling callbacks
  if (status === 'completed') {
    // Verify the resume is actually parsed by checking the database
    if (typeof window !== 'undefined') {
      // Function to check database status with retry
      const checkDatabaseStatus = async (retryCount = 0, maxRetries = 3, delay = 1000) => {
        try {
          console.log(
            `Checking database status for resume ${resumeId} (attempt ${retryCount + 1}/${maxRetries + 1})`
          );

          // Fetch resume status
          const response = await fetch(`/api/resume/${resumeId}/status`);
          const data = await response.json();
          console.log(`Fetched latest resume status for ${resumeId}:`, data);

          // If the resume is parsed in the database, call success callback
          if (data.isParsed) {
            console.log(`Resume ${resumeId} is marked as parsed in the database`);
            listener.onCompleted(resumeId, parsedData ?? data.parsedData);
            return;
          }

          // If we have parsed data from the notification but database isn't updated yet
          if (!data.isParsed && parsedData) {
            console.warn(
              'Resume notification indicates parsing complete, but database shows isParsed=false. ' +
                'Updating database and using the parsed data from the notification.'
            );

            try {
              // Update the resume in the database to ensure consistency
              await fetch(`/api/resume/${resumeId}/update-status`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  isParsed: true,
                  parsedData: parsedData,
                }),
              });

              console.log('Updated resume status in database to match notification');
              // Call success callback with the parsed data from the notification
              listener.onCompleted(resumeId, parsedData);
            } catch (updateErr) {
              console.error('Failed to update resume status in database:', updateErr);
              // Still call success callback with the parsed data from the notification
              listener.onCompleted(resumeId, parsedData);
            }
            return;
          }

          // If database isn't updated yet and we don't have parsed data, try to force update the database
          if (!data.isParsed && !parsedData && retryCount < maxRetries) {
            console.warn(
              `Resume notification indicates parsing complete, but database shows isParsed=false. ` +
                `Attempting to force update the database (attempt ${retryCount + 1}/${maxRetries})`
            );

            try {
              // Try to force update the database
              const updateResponse = await fetch(`/api/resume/${resumeId}/update-status`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  isParsed: true,
                  forceUpdate: true,
                }),
              });

              if (updateResponse.ok) {
                console.log(
                  `Successfully forced update of resume ${resumeId} status to isParsed=true`
                );

                // Check the status again after a short delay
                await new Promise((resolve) => setTimeout(resolve, 500));
                await checkDatabaseStatus(retryCount + 1, maxRetries, delay * 1.5);
                return;
              } else {
                console.warn(`Failed to force update resume ${resumeId} status`);
              }
            } catch (updateError) {
              console.error(`Error forcing update of resume status:`, updateError);
            }

            // If force update failed or wasn't attempted, retry after delay with exponential backoff
            await new Promise((resolve) => setTimeout(resolve, delay));
            await checkDatabaseStatus(retryCount + 1, maxRetries, delay * 1.5);
            return;
          }

          // If we've exhausted retries and still no success, report failure
          if (!data.isParsed && !parsedData) {
            console.warn(
              'Resume notification indicates parsing complete, but database shows isParsed=false ' +
                'and no parsed data was provided after multiple retries'
            );
            listener.onFailed(resumeId, 'Database not updated properly');
            return;
          }

          // Fallback - call success callback with whatever data we have
          listener.onCompleted(resumeId, parsedData ?? data.parsedData);
        } catch (err) {
          console.error('Error fetching resume status:', err);

          // Retry on error if we haven't exhausted retries
          if (retryCount < maxRetries) {
            console.warn(`Error checking resume status, retrying in ${delay}ms`);
            await new Promise((resolve) => setTimeout(resolve, delay));
            await checkDatabaseStatus(retryCount + 1, maxRetries, delay * 1.5);
            return;
          }

          // If we've exhausted retries, report failure
          listener.onFailed(resumeId, 'Error verifying resume parsing status');
        }
      };

      // Start checking database status with retries
      (async () => {
        await checkDatabaseStatus();
      })();
    } else {
      // If we can't verify (server-side), just call the success callback
      listener.onCompleted(resumeId, parsedData);
    }
  } else if (status === 'failed') {
    listener.onFailed(resumeId, error ?? 'Unknown error');
  }
}

/**
 * Initialize the resume parsing WebSocket handler
 */
export function initResumeParsingHandler(): () => void {
  console.log('Initializing resume parsing WebSocket handler with improved deduplication...');

  // Clear any existing data in the deduplication maps
  recentNotifications.clear();
  processedMessageIds.clear();

  console.log('Deduplication caches cleared');

  // Keep track of the last processed message timestamp to avoid reprocessing
  let lastProcessedMessageTimestamp = 0;

  // Subscribe to WebSocket messages
  const unsubscribe = websocket.messages.subscribe((messages) => {
    if (messages.length === 0) return;

    // Find the newest messages we haven't processed yet
    const newMessages = messages.filter((msg) => {
      // Convert timestamp to number for comparison
      const msgTimestamp = msg.timestamp ? new Date(msg.timestamp).getTime() : Date.now();
      return msgTimestamp > lastProcessedMessageTimestamp;
    });

    if (newMessages.length === 0) {
      // No new messages to process
      return;
    }

    // Update the last processed timestamp to the newest message
    const newestMessage = newMessages[0]; // Messages are in reverse chronological order
    if (newestMessage.timestamp) {
      lastProcessedMessageTimestamp = new Date(newestMessage.timestamp).getTime();
    }

    // Process only new messages
    for (const message of newMessages) {
      // Generate a unique message ID for deduplication
      const messageId = `ws:${message.type}:${message.timestamp ?? Date.now()}`;

      // Skip if we've already processed this exact message
      if (processedMessageIds.has(messageId)) {
        continue;
      }

      // Mark this message as processed
      processedMessageIds.add(messageId);

      // Check if it's a resume parsing status message
      if (message.type === 'resume_parsing_status' || message.type === 'resume_parsing_completed') {
        console.log(`Received resume parsing message (${message.type}):`, message);

        // Extract the actual data - handle both formats
        let messageData: any;

        // Handle different message formats
        if (message.data) {
          // Format: { data: { resumeId, status, ... } }
          messageData = {
            ...message.data,
            messageId,
            status: message.type === 'resume_parsing_completed' ? 'completed' : message.data.status,
          };
        } else {
          // Format: { resumeId, status, ... }
          messageData = {
            ...message,
            messageId,
            status: message.type === 'resume_parsing_completed' ? 'completed' : message.status,
          };
        }

        // Ensure status is set correctly for completed messages
        if (message.type === 'resume_parsing_completed' && messageData.status !== 'completed') {
          messageData.status = 'completed';
        }

        console.log('Processed message data:', messageData);
        handleParsingStatusUpdate(messageData);
      }
      // Also check for notification messages that might contain resume parsing data
      else if (message.type === 'notification') {
        console.log('Checking notification message for resume parsing data:', message);

        // Check if the data contains resumeId and status fields
        if (message.data?.resumeId && message.data?.status) {
          console.log('Found resume parsing data in notification message:', message.data);
          // Add the messageId to the data for better deduplication
          const notificationData = { ...message.data, messageId };
          handleParsingStatusUpdate(notificationData);
        }
      }
    }
  });

  // Also listen for the custom websocket-message event, but with improved deduplication
  const handleWebSocketMessage = (event: any) => {
    const { type, data } = event.detail;

    // Generate a unique message ID for deduplication
    const messageId = `event:${type}:${data?.timestamp ?? Date.now()}`;

    // Skip if we've already processed this exact message
    if (processedMessageIds.has(messageId)) {
      console.log(`Skipping already processed event message: ${messageId}`);
      return;
    }

    // Mark this message as processed
    processedMessageIds.add(messageId);

    if (type === 'resume_parsing_status' || type === 'resume_parsing_completed') {
      console.log(`Received resume parsing event (${type}):`, data);

      // Extract the actual data - handle both formats
      let messageData: any;

      // Handle different message formats
      if (data.data) {
        // Format: { data: { resumeId, status, ... } }
        messageData = {
          ...data.data,
          messageId,
          status: type === 'resume_parsing_completed' ? 'completed' : data.data.status,
        };
      } else {
        // Format: { resumeId, status, ... }
        messageData = {
          ...data,
          messageId,
          status: type === 'resume_parsing_completed' ? 'completed' : data.status,
        };
      }

      // Ensure status is set correctly for completed messages
      if (type === 'resume_parsing_completed' && messageData.status !== 'completed') {
        messageData.status = 'completed';
      }

      // For resume_parsing_status with pending/processing status, dispatch a custom event
      if (
        type === 'resume_parsing_status' &&
        (messageData.status === 'pending' || messageData.status === 'processing')
      ) {
        // Dispatch a custom event for resume parsing status updates
        if (typeof window !== 'undefined') {
          const resumeEvent = new CustomEvent('resume-parsing-status', {
            detail: {
              resumeId: messageData.resumeId,
              profileId: messageData.profileId,
              userId: messageData.userId,
              isParsing: true,
              status: messageData.status,
              message: 'Resume parsing in progress',
              timestamp: new Date().toISOString(),
            },
          });
          window.dispatchEvent(resumeEvent);
          console.log(
            'Dispatched resume-parsing-status event from event handler with isParsing=true'
          );
        }
      }

      console.log('Processed message data:', messageData);
      handleParsingStatusUpdate(messageData);
    } else if (type === 'notification') {
      console.log('Checking notification event for resume parsing data:', data);

      // Check if the data contains resumeId and status fields
      if (data?.resumeId && data?.status) {
        console.log('Found resume parsing data in notification event:', data);
        // Add the messageId to the data for better deduplication
        const notificationData = { ...(data.data ?? data), messageId };
        handleParsingStatusUpdate(notificationData);
      }
    }
  };

  // Add event listener for custom websocket messages
  if (typeof window !== 'undefined') {
    window.addEventListener('websocket-message', handleWebSocketMessage);
  }

  // We don't need to connect here - connection is handled at the application level
  console.log('Resume parsing handler using existing WebSocket connection');

  // Add a debug function to the window object for testing
  if (typeof window !== 'undefined') {
    (window as any).debugResumeParser = {
      listeners: parsingListeners,
      handleMessage: (data: any) => handleParsingStatusUpdate(data),
      registerListener: registerParsingListener,
      unregisterListener: unregisterParsingListener,
      testConnection: () => {
        // Test the WebSocket connection
        const currentStatus = websocket.getStatus();
        console.log(`Current WebSocket status: ${currentStatus}`);

        if (currentStatus !== 'connected') {
          console.log('WebSocket not connected');
          return false;
        }

        // Send a test message
        const success = websocket.send({
          type: 'ping',
          data: { time: Date.now() },
        });

        console.log(`Test ping sent: ${success}`);
        return success;
      },
      forceReconnect: () => {
        console.log('Forcing WebSocket reconnection...');
        websocket.disconnect();
        setTimeout(() => {
          websocket.initialize();
        }, 500);
      },
    };
  }

  // No periodic checks - rely entirely on WebSocket notifications

  console.log('Resume parsing WebSocket handler initialized');

  // Return cleanup function
  return () => {
    unsubscribe();
    if (typeof window !== 'undefined') {
      window.removeEventListener('websocket-message', handleWebSocketMessage);
    }

    // Clear deduplication caches on cleanup
    recentNotifications.clear();
    processedMessageIds.clear();

    console.log('Resume parsing WebSocket handler cleaned up and caches cleared');
  };
}
