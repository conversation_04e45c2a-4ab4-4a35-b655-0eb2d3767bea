// @ts-nocheck
import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';

export const load = async ({ locals, url }: Parameters<LayoutServerLoad>[0]) => {
  // If user is already authenticated, redirect to dashboard
  if (locals.user) {
    throw redirect(302, '/dashboard');
  }

  // Otherwise, pass the user data (which will be null) to the client
  return {
    user: locals.user,
    currentPath: url.pathname,
  };
};
