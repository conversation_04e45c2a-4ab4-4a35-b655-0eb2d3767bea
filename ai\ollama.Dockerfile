FROM ollama/ollama:latest

# Set environment variables
ENV OLLAMA_HOST=0.0.0.0
ENV OLLAMA_MODELS_PATH=/root/.ollama/models

# Expose the Ollama API port
EXPOSE 11434

# Create a script to pull the model and start Ollama
RUN echo '#!/bin/sh \n\
ollama serve & \n\
sleep 10 \n\
ollama pull mistral \n\
# Keep the container running \n\
tail -f /dev/null' > /start.sh && chmod +x /start.sh

# Run the script when the container starts
CMD ["/start.sh"]
