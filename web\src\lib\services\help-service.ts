// src/lib/services/help-service.ts
import { browser } from '$app/environment';
import {
  getHelpArticles,
  getFeaturedHelpArticles,
  getHelpArticlesByCategory,
  getHelpArticleBySlug,
  searchHelpArticles as searchHelpArticlesFromSanity,
} from '$lib/sanity/client';

/**
 * Fetches all help articles with optional filtering
 * @param options Filter options
 * @returns Promise with articles and pagination data
 */
export async function fetchHelpArticles(
  options: {
    category?: string;
    tag?: string;
    limit?: number;
    page?: number;
    featured?: boolean;
  } = {}
) {
  if (!browser) return { articles: [], pagination: { total: 0, page: 1, limit: 20, pages: 0 } };

  const { category, limit = 20, page = 1, featured = false } = options;

  try {
    let articles: any[] = [];

    if (featured) {
      articles = await getFeaturedHelpArticles(limit);
    } else if (category) {
      articles = await getHelpArticlesByCategory(category);
      // Apply pagination manually
      const startIndex = (page - 1) * limit;
      articles = articles.slice(startIndex, startIndex + limit);
    } else {
      articles = await getHelpArticles();
      // Apply pagination manually
      const startIndex = (page - 1) * limit;
      articles = articles.slice(startIndex, startIndex + limit);
    }

    // Format articles to match the expected structure
    const formattedArticles = articles.map((article: any) => ({
      ...article,
      id: article._id,
      slug: article.slug.current,
      excerpt: article.description,
      category: {
        id: article.category,
        name: getCategoryName(article.category),
        slug: article.category,
        icon: getCategoryIcon(article.category),
      },
      tags:
        article.tags?.map((tag: string) => ({
          id: tag,
          name: tag,
          slug: tag.toLowerCase().replace(/\s+/g, '-'),
        })) || [],
    }));

    return {
      articles: formattedArticles,
      pagination: {
        total: articles.length,
        page,
        limit,
        pages: Math.ceil(articles.length / limit),
      },
    };
  } catch (error) {
    console.error('Error fetching help articles:', error);
    return { articles: [], pagination: { total: 0, page: 1, limit: 20, pages: 0 } };
  }
}

/**
 * Fetches a single help article by slug
 * @param slug The article slug
 * @returns Promise with the article data
 */
export async function fetchHelpArticle(slug: string) {
  if (!browser) return null;

  try {
    const article = await getHelpArticleBySlug(slug);

    if (!article) return null;

    // Format article to match the expected structure
    return {
      ...article,
      id: article._id,
      slug: article.slug.current,
      excerpt: article.description,
      category: {
        id: article.category,
        name: getCategoryName(article.category),
        slug: article.category,
        icon: getCategoryIcon(article.category),
      },
      tags:
        article.tags?.map((tag: string) => ({
          id: tag,
          name: tag,
          slug: tag.toLowerCase().replace(/\s+/g, '-'),
        })) || [],
      relatedArticles:
        article.relatedArticles?.map((related: any) => ({
          ...related,
          id: related._id,
          slug: related.slug.current,
          excerpt: related.description,
          category: {
            id: related.category,
            name: getCategoryName(related.category),
            slug: related.category,
            icon: getCategoryIcon(related.category),
          },
        })) || [],
    };
  } catch (error) {
    console.error(`Error fetching help article ${slug}:`, error);
    return null;
  }
}

/**
 * Fetches all help categories
 * @param options Options for fetching categories
 * @returns Promise with categories data
 */
export async function fetchHelpCategories(
  options: {
    includeArticleCount?: boolean;
  } = {}
) {
  if (!browser) return [];

  try {
    // Get all articles
    const articles = await getHelpArticles();

    // Group articles by category and count them
    const categoryCounts = articles.reduce((acc: Record<string, any>, article: any) => {
      if (!acc[article.category]) {
        acc[article.category] = {
          count: 0,
          name: getCategoryName(article.category),
          slug: article.category,
          icon: getCategoryIcon(article.category),
        };
      }
      acc[article.category].count += 1;
      return acc;
    }, {});

    // Convert to array and format
    const categories = Object.entries(categoryCounts).map(([slug, data]: [string, any]) => ({
      id: slug,
      name: data.name,
      slug: slug,
      icon: data.icon,
      articleCount: options.includeArticleCount ? data.count : undefined,
    }));

    return categories;
  } catch (error) {
    console.error('Error fetching help categories:', error);
    return [];
  }
}

/**
 * Fetches all help tags
 * @param includeArticleCount Whether to include article count
 * @returns Promise with tags data
 */
export async function fetchHelpTags(includeArticleCount = false) {
  if (!browser) return [];

  try {
    // Get all articles
    const articles = await getHelpArticles();

    // Extract all unique tags and count occurrences
    const tagCounts: Record<string, number> = {};

    articles.forEach((article: any) => {
      if (article.tags && article.tags.length) {
        article.tags.forEach((tag: string) => {
          if (!tagCounts[tag]) {
            tagCounts[tag] = 0;
          }
          tagCounts[tag] += 1;
        });
      }
    });

    // Convert to array and format
    const tags = Object.entries(tagCounts).map(([tag, count]) => ({
      id: tag,
      name: tag,
      slug: tag.toLowerCase().replace(/\s+/g, '-'),
      articleCount: includeArticleCount ? count : undefined,
    }));

    return tags;
  } catch (error) {
    console.error('Error fetching help tags:', error);
    return [];
  }
}

/**
 * Searches help articles
 * @param query The search query
 * @param options Search options
 * @returns Promise with search results
 */
export async function searchHelpArticles(
  query: string,
  options: {
    limit?: number;
    page?: number;
  } = {}
) {
  if (!browser) return { articles: [], pagination: { total: 0, page: 1, limit: 10, pages: 0 } };
  if (!query || query.length < 2) {
    return { articles: [], pagination: { total: 0, page: 1, limit: 10, pages: 0 } };
  }

  const { limit = 10, page = 1 } = options;

  try {
    const searchResults = await searchHelpArticlesFromSanity(query, limit);

    // Format articles to match the expected structure
    const formattedArticles = searchResults.map((article: any) => ({
      ...article,
      id: article._id,
      slug: article.slug.current,
      excerpt: article.description,
      category: {
        id: article.category,
        name: getCategoryName(article.category),
        slug: article.category,
        icon: getCategoryIcon(article.category),
      },
      tags:
        article.tags?.map((tag: string) => ({
          id: tag,
          name: tag,
          slug: tag.toLowerCase().replace(/\s+/g, '-'),
        })) || [],
    }));

    return {
      articles: formattedArticles,
      pagination: {
        total: searchResults.length,
        page,
        limit,
        pages: Math.ceil(searchResults.length / limit),
      },
    };
  } catch (error) {
    console.error('Error searching help articles:', error);
    return { articles: [], pagination: { total: 0, page: 1, limit: 10, pages: 0 } };
  }
}

// Helper function to get category name from slug
function getCategoryName(slug: string): string {
  const categoryMap = {
    'getting-started': 'Getting Started',
    'auto-apply': 'Using Auto Apply',
    'account-billing': 'Account & Billing',
    troubleshooting: 'Troubleshooting',
    'privacy-security': 'Privacy & Security',
  };

  return categoryMap[slug as keyof typeof categoryMap] || slug;
}

// Helper function to get category icon from slug
function getCategoryIcon(slug: string): string {
  const iconMap = {
    'getting-started': 'BookOpen',
    'auto-apply': 'FileText',
    'account-billing': 'CreditCard',
    troubleshooting: 'HelpCircle',
    'privacy-security': 'Shield',
  };

  return iconMap[slug as keyof typeof iconMap] || 'HelpCircle';
}
