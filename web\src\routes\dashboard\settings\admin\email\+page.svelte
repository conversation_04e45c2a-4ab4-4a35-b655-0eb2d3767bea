<script lang="ts">
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import Seo from '$components/shared/SEO.svelte';
  import { onMount } from 'svelte';

  // Import components for each tab
  import Audiences from './audiences/+page.svelte';
  import Broadcast from './broadcast/+page.svelte';
  import Analytics from './analytics/+page.svelte';

  // Get the active tab from the URL or default to 'test'
  let activeTab = $state('analytics');

  // Check if Resend API is configured
  let isResendConfigured = $state(true);
  let isCheckingConfig = $state(true);

  // Check if Resend API is configured
  async function checkResendConfig() {
    isCheckingConfig = true;
    try {
      const response = await fetch('/api/email/config');
      if (response.ok) {
        const data = await response.json();
        isResendConfigured = data.resendConfigured === true;
      } else {
        isResendConfigured = false;
      }
    } catch (error) {
      console.error('Error checking Resend API configuration:', error);
      isResendConfigured = false;
    } finally {
      isCheckingConfig = false;
    }
  }

  // Add onMount to check configuration
  onMount(() => {
    // Check Resend API configuration
    checkResendConfig();
  });

  // Define tabs
  const tabs = [
    {
      id: 'analytics',
      label: 'Analytics',
      component: Analytics,
    },
    {
      id: 'audiences',
      label: 'Audiences',
      component: Audiences,
    },
    {
      id: 'broadcast',
      label: 'Broadcast',
      component: Broadcast,
    },
  ];
</script>

<Seo title="Email Management - Hirli" />

<div class="border-border flex items-center justify-between border-b px-4 py-2">
  <h2 class="text-lg font-semibold">Email Settings</h2>
</div>

<Tabs.Root
  value={activeTab}
  onValueChange={(value) => {
    activeTab = value;
  }}>
  <div class="border-border border-b p-0">
    <Tabs.List class="flex flex-row divide-x">
      {#each tabs as tab}
        <Tabs.Trigger value={tab.id} class="no-border flex items-center rounded-none p-2">
          {tab.label}
        </Tabs.Trigger>
      {/each}
    </Tabs.List>
  </div>
  {#each tabs as tab}
    <Tabs.Content value={tab.id} class="space-y-4">
      {#if tab.component}
        <tab.component />
      {/if}
    </Tabs.Content>
  {/each}
</Tabs.Root>
