// src/routes/api/help/categories/+server.ts
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { prisma } from '$lib/server/prisma';

// Get all help categories
export const GET: RequestHandler = async ({ url }) => {
  try {
    const includeArticleCount = url.searchParams.get('includeArticleCount') === 'true';
    const parentOnly = url.searchParams.get('parentOnly') === 'true';

    // Build the query filters
    const filters: any = {};

    if (parentOnly) {
      filters.parentId = null;
    }

    // Get categories from the database using Prisma models
    const categories = await prisma.helpCategory.findMany({
      where: filters,
      orderBy: [
        {
          order: 'asc',
        },
        {
          name: 'asc',
        },
      ],
      include: {
        children: true,
        _count: includeArticleCount ? {
          select: {
            articles: true,
          },
        } : undefined,
      },
    });

    // Format the response
    const formattedCategories = categories.map((category) => ({
      ...category,
      articleCount: includeArticleCount ? category._count.articles : undefined,
      _count: undefined,
    }));

    return json(formattedCategories);
  } catch (error) {
    console.error('Error fetching help categories:', error);
    return json({ error: 'Failed to fetch help categories' }, { status: 500 });
  }
};

// Create a new help category (admin only)
export const POST: RequestHandler = async ({ request, locals }) => {
  const user = locals.user;
  if (!user || user.role !== 'ADMIN') {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { name, slug, description, icon, parentId, order } = await request.json();

    // Create the category
    const category = await prisma.helpCategory.create({
      data: {
        name,
        slug,
        description,
        icon,
        parentId,
        order: order || 0,
      },
    });

    return json(category);
  } catch (error) {
    console.error('Error creating help category:', error);
    return json({ error: 'Failed to create help category' }, { status: 500 });
  }
};
