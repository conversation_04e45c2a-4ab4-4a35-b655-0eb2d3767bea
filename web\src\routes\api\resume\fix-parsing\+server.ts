// File: web/src/routes/api/resume/fix-parsing/+server.ts
import { json } from '@sveltejs/kit';
import { getRedisClient } from '$lib/server/redis';
import { prisma } from '$lib/server/prisma';
import type { <PERSON>quest<PERSON>and<PERSON> } from './$types';

/**
 * POST handler to fix resume parsing by sending to the correct stream
 */
export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    // Get the user from locals
    const user = locals.user;
    if (!user) return new Response('Unauthorized', { status: 401 });

    // Get the resume ID from the request body
    const { resumeId } = await request.json();

    if (!resumeId) {
      return json({ error: 'Resume ID is required' }, { status: 400 });
    }

    console.log(`Fixing resume parsing for resume ${resumeId}`);

    // Check if the resume exists and belongs to the user
    const resume = await prisma.resume.findUnique({
      where: { id: resumeId },
      include: {
        document: true,
      },
    });

    if (!resume) {
      return json({ error: 'Resume not found' }, { status: 404 });
    }

    if (resume.document.userId !== user.id) {
      return json({ error: 'Unauthorized access to resume' }, { status: 403 });
    }

    // Get the profile ID if the document is associated with a profile
    const profileId = resume.document.profileId;

    // Reset the resume parsing status
    await prisma.resume.update({
      where: { id: resumeId },
      data: {
        isParsed: false,
        parsedAt: null,
      },
    });

    // Add to Redis queue with the CORRECT stream name (the one the worker is actually looking for)
    try {
      const redis = await getRedisClient();
      if (!redis) {
        throw new Error('Redis client not available');
      }

      // The worker is looking for messages in resume-parsing::stream
      const streamName = 'resume-parsing::stream';

      // Create stream group if it doesn't exist
      try {
        await redis.xgroup('CREATE', streamName, 'resume-parsing::group', '$', 'MKSTREAM');
        console.log(`Created ${streamName} stream group`);
      } catch (err: any) {
        // Ignore BUSYGROUP error (group already exists)
        if (!err.message.includes('BUSYGROUP')) {
          console.error(`Error creating stream group for ${streamName}:`, err);
        }
      }

      // Add job to Redis stream
      await redis.xadd(
        streamName,
        '*',
        'job',
        JSON.stringify({
          resumeId,
          fileUrl: resume.document?.fileUrl ?? null,
          filePath: resume.document?.filePath ?? null,
          userId: user.id,
          profileId: profileId || null,
          timestamp: new Date().toISOString(),
        })
      );
      console.log(`Added job to ${streamName}`);

      // Disconnect from Redis
      redis.disconnect();

      return json({
        success: true,
        message: 'Resume parsing job added to the correct stream',
        resumeId,
        profileId: profileId || null,
        streamName,
      });
    } catch (redisError) {
      console.error('Redis error:', redisError);
      return json(
        { error: 'Failed to add job to Redis', details: String(redisError) },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error fixing resume parsing:', error);
    return json({ error: 'Failed to fix resume parsing', details: String(error) }, { status: 500 });
  }
};
