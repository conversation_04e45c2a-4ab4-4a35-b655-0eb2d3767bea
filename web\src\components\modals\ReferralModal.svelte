<script lang="ts">
  import { toast } from 'svelte-sonner';
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { Separator } from '$lib/components/ui/separator/index.js';
  import { Copy, Share2, Users, ExternalLink } from 'lucide-svelte';

  let { open = $bindable(false), onClose = () => {} } = $props<{
    open?: boolean;
    onClose?: () => void;
  }>();

  let referralData = $state<any>(null);
  let loading = $state(true);
  let copying = $state(false);

  // Load referral data
  const loadReferralData = async () => {
    try {
      const response = await fetch('/api/referrals');
      if (response.ok) {
        referralData = await response.json();
      } else {
        toast.error('Failed to load referral data');
      }
    } catch (error) {
      console.error('Error loading referral data:', error);
      toast.error('Failed to load referral data');
    } finally {
      loading = false;
    }
  };

  // Copy referral link to clipboard
  const copyReferralLink = async () => {
    if (!referralData?.referralLink) return;

    copying = true;
    try {
      await navigator.clipboard.writeText(referralData.referralLink);
      toast.success('Referral link copied to clipboard!');
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast.error('Failed to copy referral link');
    } finally {
      copying = false;
    }
  };

  // Share referral link
  const shareReferralLink = async () => {
    if (!referralData?.referralLink) return;

    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Join Hirli with my referral link',
          text: 'Sign up for Hirli using my referral link and get started with job automation!',
          url: referralData.referralLink,
        });
      } catch (error) {
        console.error('Error sharing:', error);
        // Fallback to copying
        copyReferralLink();
      }
    } else {
      // Fallback to copying
      copyReferralLink();
    }
  };

  // Load data when modal opens
  $effect(() => {
    if (open) {
      loadReferralData();
    }
  });
</script>

<Dialog.Root bind:open>
  <Dialog.Content class="max-w-2xl">
    <Dialog.Header>
      <Dialog.Title class="flex items-center gap-2">
        <Share2 class="h-5 w-5" />
        Referral Program
      </Dialog.Title>
      <Dialog.Description>
        Share Hirli with friends and earn rewards for successful referrals.
      </Dialog.Description>
    </Dialog.Header>

    <div class="space-y-6">
      {#if loading}
        <div class="flex items-center justify-center py-8">
          <div class="text-muted-foreground">Loading referral data...</div>
        </div>
      {:else if referralData}
        <!-- Quick Stats -->
        <div class="grid grid-cols-3 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold">{referralData.referralCount || 0}</div>
            <div class="text-muted-foreground text-sm">Total Referrals</div>
          </div>
          <div class="text-center">
            <div class="font-mono text-2xl font-bold">{referralData.referralCode}</div>
            <div class="text-muted-foreground text-sm">Your Code</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold">$0</div>
            <div class="text-muted-foreground text-sm">Rewards (Soon)</div>
          </div>
        </div>

        <Separator />

        <!-- Referral Link -->
        <div class="space-y-3">
          <h3 class="font-semibold">Your Referral Link</h3>
          <div class="flex gap-2">
            <input
              value={referralData.referralLink}
              readonly
              class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 font-mono text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
            <Button variant="outline" size="sm" onclick={copyReferralLink} disabled={copying}>
              <Copy class="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onclick={shareReferralLink}>
              <Share2 class="h-4 w-4" />
            </Button>
          </div>
          <p class="text-muted-foreground text-sm">
            Share this link with friends to earn referral rewards when they sign up.
          </p>
        </div>

        <!-- Recent Referrals -->
        {#if referralData.referrals && referralData.referrals.length > 0}
          <div class="space-y-3">
            <h3 class="font-semibold">Recent Referrals</h3>
            <div class="max-h-40 space-y-2 overflow-y-auto">
              {#each referralData.referrals.slice(0, 3) as referral}
                <div class="flex items-center justify-between rounded border p-2">
                  <div>
                    <p class="text-sm font-medium">
                      {referral.referred?.name || referral.referred?.email || 'Unknown User'}
                    </p>
                    <p class="text-muted-foreground text-xs">
                      Joined {new Date(referral.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <Badge
                    variant={referral.status === 'completed' ? 'default' : 'secondary'}
                    class="text-xs">
                    {referral.status || 'pending'}
                  </Badge>
                </div>
              {/each}
            </div>
            {#if referralData.referrals.length > 3}
              <p class="text-muted-foreground text-center text-sm">
                And {referralData.referrals.length - 3} more...
              </p>
            {/if}
          </div>
        {:else}
          <div class="py-6 text-center">
            <Users class="text-muted-foreground mx-auto mb-2 h-12 w-12" />
            <p class="text-muted-foreground">No referrals yet</p>
            <p class="text-muted-foreground text-sm">Start sharing your link to earn rewards!</p>
          </div>
        {/if}

        <!-- Referred By -->
        {#if referralData.referredBy}
          <div class="space-y-2">
            <h3 class="text-sm font-semibold">You Were Referred By</h3>
            <div class="flex items-center gap-2 rounded border p-2">
              <p class="text-sm font-medium">
                {referralData.referredBy.name || referralData.referredBy.email}
              </p>
              <Badge variant="outline" class="text-xs">Referrer</Badge>
            </div>
          </div>
        {/if}
      {:else}
        <div class="flex items-center justify-center py-8">
          <div class="text-muted-foreground">Failed to load referral data</div>
        </div>
      {/if}
    </div>

    <Dialog.Footer class="flex justify-between">
      <Button variant="outline" href="/dashboard/settings/referrals">
        <ExternalLink class="mr-2 h-4 w-4" />
        Full Settings
      </Button>
      <Button variant="outline" onclick={onClose}>Close</Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
