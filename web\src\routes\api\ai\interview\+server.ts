import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';
import { OpenAI } from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { applicationId, jobTitle, company, jobDescription } = await request.json();

    if (!jobTitle) {
      return json({ error: 'Job title is required' }, { status: 400 });
    }

    // Generate interview questions based on job details
    const questions = await generateInterviewQuestions(jobTitle, company, jobDescription);

    // Create a new interview coaching session
    const session = await prisma.interviewCoachingSession.create({
      data: {
        userId: locals.user.id,
        applicationId,
        jobTitle,
        company,
        status: 'in_progress',
        questions: questions.map(q => ({ question: q, type: 'behavioral' })),
        responses: [],
        feedback: []
      }
    });

    // Update feature usage
    await prisma.featureUsage.upsert({
      where: {
        userId_featureId_limitId: {
          userId: locals.user.id,
          featureId: 'ai_interview_coach',
          limitId: 'ai_interview_sessions_monthly'
        }
      },
      update: {
        usage: {
          increment: 1
        }
      },
      create: {
        userId: locals.user.id,
        featureId: 'ai_interview_coach',
        limitId: 'ai_interview_sessions_monthly',
        usage: 1
      }
    });

    return json({ session });
  } catch (error) {
    console.error('Error creating interview coaching session:', error);
    return json({ error: 'Failed to create interview coaching session' }, { status: 500 });
  }
};

export const GET: RequestHandler = async ({ locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Get user's interview coaching sessions
    const sessions = await prisma.interviewCoachingSession.findMany({
      where: {
        userId: locals.user.id
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    return json({ sessions });
  } catch (error) {
    console.error('Error fetching interview coaching sessions:', error);
    return json({ error: 'Failed to fetch interview coaching sessions' }, { status: 500 });
  }
};

// Helper function to generate interview questions
async function generateInterviewQuestions(jobTitle: string, company?: string, jobDescription?: string): Promise<string[]> {
  try {
    const prompt = `Generate 5 behavioral interview questions for a ${jobTitle} position${company ? ` at ${company}` : ''}. 
    ${jobDescription ? `The job description is: ${jobDescription}` : ''}
    
    Focus on questions that assess the candidate's experience, skills, and fit for the role.
    Return only the questions as a numbered list, without any additional text.`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: 'You are an expert interview coach helping to prepare candidates for job interviews.' },
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 1000
    });

    // Parse the response to extract questions
    const content = response.choices[0]?.message?.content || '';
    const questions = content
      .split('\n')
      .filter(line => line.trim().match(/^\d+\.\s/)) // Match lines starting with a number and period
      .map(line => line.replace(/^\d+\.\s/, '').trim()); // Remove the numbering

    // If we couldn't parse any questions, return default ones
    if (questions.length === 0) {
      return [
        'Tell me about a time when you faced a challenging situation at work and how you handled it.',
        `What experience do you have that makes you a good fit for this ${jobTitle} role?`,
        'Describe a time when you had to work under pressure to meet a deadline.',
        'Give an example of a time when you had to adapt to a significant change at work.',
        'Tell me about a time when you had to resolve a conflict with a colleague or client.'
      ];
    }

    return questions;
  } catch (error) {
    console.error('Error generating interview questions:', error);
    
    // Return default questions if OpenAI API fails
    return [
      'Tell me about a time when you faced a challenging situation at work and how you handled it.',
      `What experience do you have that makes you a good fit for this ${jobTitle} role?`,
      'Describe a time when you had to work under pressure to meet a deadline.',
      'Give an example of a time when you had to adapt to a significant change at work.',
      'Tell me about a time when you had to resolve a conflict with a colleague or client.'
    ];
  }
}
