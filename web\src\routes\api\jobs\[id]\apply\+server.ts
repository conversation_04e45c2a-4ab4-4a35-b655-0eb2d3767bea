import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth.js';

export async function POST({ request, params, cookies }) {
  try {
    const token = cookies.get('auth_token');
    const user = token && verifySessionToken(token);

    if (!user) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get the job details
    const job = await prisma.job_listing.findUnique({
      where: { id },
    });

    if (!job) {
      return json({ error: 'Job not found' }, { status: 404 });
    }

    // Check if the job is already applied to
    const existingApplication = await prisma.application.findFirst({
      where: {
        userId: user.id,
        url: job.url,
      },
    });

    if (existingApplication) {
      return json({ 
        message: 'Already applied to this job',
        application: existingApplication
      });
    }

    // Create a new application
    const application = await prisma.application.create({
      data: {
        userId: user.id,
        company: job.company || 'Unknown Company',
        position: job.title,
        location: job.location,
        appliedDate: new Date(),
        status: 'Applied',
        url: job.url,
        jobType: job.employmentType || 'Full-time',
        resumeUploaded: false,
      },
    });

    // Update the job match result if it exists
    if (job.id) {
      try {
        await prisma.job_match_result.updateMany({
          where: {
            userId: user.id,
            jobId: job.id,
          },
          data: {
            applied: true,
          },
        });
      } catch (error) {
        console.error('Error updating job match result:', error);
        // Continue even if this fails
      }
    }

    return json({
      success: true,
      message: 'Application created successfully',
      application,
    });
  } catch (error) {
    console.error('Error applying to job:', error);
    return json({ error: 'Failed to apply to job' }, { status: 500 });
  }
}
