// Script to add storage_gb limit to document_storage feature
import { PrismaClient } from '@prisma/client';

// Define LimitType enum
enum LimitType {
  Total = 'total',
  Monthly = 'monthly',
  Yearly = 'yearly',
  Concurrent = 'concurrent',
}

// Define interfaces for type safety
interface FeatureLimit {
  id: string;
  name: string;
  description: string;
  defaultValue: string;
  type: LimitType;
  unit: string;
}

interface PlanTierDefaults {
  [key: string]: {
    [limitId: string]: string;
  };
}

const prisma = new PrismaClient();

async function addStorageLimit(): Promise<void> {
  try {
    console.log('Adding limits to document_storage feature...');

    // Check if the document_storage feature exists
    const feature = await prisma.feature.findUnique({
      where: { id: 'document_storage' },
    });

    if (!feature) {
      console.log('document_storage feature not found. Creating it...');

      // Create the document_storage feature if it doesn't exist
      await prisma.feature.create({
        data: {
          id: 'document_storage',
          name: 'Document Storage',
          description: 'Securely store and manage your resumes, cover letters, and other documents',
          category: 'resume',
          icon: 'folder',
          beta: false,
        },
      });

      console.log('document_storage feature created successfully.');
    }

    // Define all the limits we want to add
    const limits: FeatureLimit[] = [
      {
        id: 'storage_gb',
        name: 'Storage',
        description: 'Amount of storage space for your documents',
        defaultValue: '1',
        type: LimitType.Total,
        unit: 'GB',
      },
      {
        id: 'document_count',
        name: 'Document Count',
        description: 'Maximum number of documents you can upload',
        defaultValue: '20',
        type: LimitType.Total,
        unit: 'documents',
      },
      {
        id: 'document_size_mb',
        name: 'Document Size',
        description: 'Maximum size of individual documents',
        defaultValue: '10',
        type: LimitType.Total,
        unit: 'MB',
      },
      {
        id: 'document_sharing',
        name: 'Document Sharing',
        description: 'Number of documents you can share with others',
        defaultValue: '5',
        type: LimitType.Total,
        unit: 'documents',
      },
    ];

    // Add or update each limit
    for (const limit of limits) {
      // Check if the limit already exists
      const existingLimit = await prisma.featureLimit.findUnique({
        where: { id: limit.id },
      });

      if (existingLimit) {
        console.log(`${limit.id} limit already exists. Updating it...`);

        // Update the existing limit
        await prisma.featureLimit.update({
          where: { id: limit.id },
          data: {
            featureId: 'document_storage',
            name: limit.name,
            description: limit.description,
            defaultValue: limit.defaultValue,
            type: limit.type,
            unit: limit.unit,
          },
        });

        console.log(`${limit.id} limit updated successfully.`);
      } else {
        console.log(`${limit.id} limit not found. Creating it...`);

        // Create the limit
        await prisma.featureLimit.create({
          data: {
            id: limit.id,
            featureId: 'document_storage',
            name: limit.name,
            description: limit.description,
            defaultValue: limit.defaultValue,
            type: limit.type,
            unit: limit.unit,
          },
        });

        console.log(`${limit.id} limit created successfully.`);
      }
    }

    // Add the limits to all plans
    const plans = await prisma.plan.findMany({
      include: {
        features: true,
      },
    });

    // Define default values for each plan tier
    const planTierDefaults: PlanTierDefaults = {
      free: {
        storage_gb: '1',
        document_count: '10',
        document_size_mb: '5',
        document_sharing: '2',
      },
      casual: {
        storage_gb: '5',
        document_count: '30',
        document_size_mb: '10',
        document_sharing: '10',
      },
      active: {
        storage_gb: '10',
        document_count: '50',
        document_size_mb: '20',
        document_sharing: '20',
      },
      startup: {
        storage_gb: '50',
        document_count: '100',
        document_size_mb: '50',
        document_sharing: '50',
      },
      default: {
        storage_gb: '1',
        document_count: '20',
        document_size_mb: '10',
        document_sharing: '5',
      },
    };

    for (const plan of plans) {
      // Check if the plan has the document_storage feature
      let planFeature = await prisma.planFeature.findFirst({
        where: {
          planId: plan.id,
          featureId: 'document_storage',
        },
      });

      if (!planFeature) {
        console.log(`Adding document_storage feature to plan ${plan.name}...`);

        // Create the plan feature
        planFeature = await prisma.planFeature.create({
          data: {
            planId: plan.id,
            featureId: 'document_storage',
            accessLevel: 'limited',
          },
        });

        console.log(`document_storage feature added to plan ${plan.name}.`);
      }

      // Get the default values for this plan tier
      const defaults = planTierDefaults[plan.id.toLowerCase()] || planTierDefaults.default;

      // Add each limit to the plan
      for (const limit of limits) {
        // Check if the plan feature already has this limit
        const existingPlanLimit = await prisma.planFeatureLimit.findFirst({
          where: {
            planFeatureId: planFeature.id,
            limitId: limit.id,
          },
        });

        if (!existingPlanLimit) {
          console.log(`Adding ${limit.id} limit to plan ${plan.name}...`);

          // Get the appropriate value for this plan tier
          const value = defaults[limit.id] || limit.defaultValue;

          // Create the plan feature limit
          await prisma.planFeatureLimit.create({
            data: {
              planFeatureId: planFeature.id,
              limitId: limit.id,
              value: value,
            },
          });

          console.log(`${limit.id} limit added to plan ${plan.name}.`);
        }
      }
    }

    console.log('Storage limit setup completed successfully!');
  } catch (error) {
    console.error('Error adding storage limit:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
addStorageLimit()
  .then(() => console.log('Script completed.'))
  .catch((error) => console.error('Script failed:', error));
