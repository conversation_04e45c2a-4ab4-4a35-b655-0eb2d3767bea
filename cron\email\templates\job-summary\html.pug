extends ../layouts/base.pug

block content
  h1 #{reportTitle || 'Job Processing Summary'}

  p This report covers the period ending #{reportDate || new Date().toLocaleDateString()}.

  if message
    .card
      .card-header Message
      p(style="padding: 12px 16px; margin: 0;") #{message}

  .card
    .card-header Summary
    table(cellspacing="0" cellpadding="0" border="0" width="100%" style="width: 100%; border-collapse: collapse; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);")
      tr(style="background-color: #f8f9fa;")
        th(width="60%" align="left" style="padding: 12px 16px; text-align: left; border-bottom: 2px solid #dee2e6; font-weight: 600;") Metric
        th(width="40%" align="center" style="padding: 12px 16px; text-align: center; border-bottom: 2px solid #dee2e6; font-weight: 600;") Value
      tr(style="border-bottom: 1px solid #eee;")
        td(width="60%" align="left" style="padding: 12px 16px; font-weight: 500;") Jobs Processed
        td(width="40%" align="center" style="padding: 12px 16px; text-align: center; font-weight: bold;") #{jobsProcessed || 0}
      tr(style="border-bottom: 1px solid #eee;")
        td(width="60%" align="left" style="padding: 12px 16px; font-weight: 500;") Jobs Succeeded
        td(width="40%" align="center" style="padding: 12px 16px; text-align: center; font-weight: bold; color: #28a745;") #{jobsSucceeded || 0}
      tr(style="border-bottom: 1px solid #eee;")
        td(width="60%" align="left" style="padding: 12px 16px; font-weight: 500;") Jobs Failed
        td(width="40%" align="center" style="padding: 12px 16px; text-align: center; font-weight: bold; color: #{(jobsFailed && jobsFailed > 0) ? '#dc3545' : '#6c757d'};") #{jobsFailed || 0}
      tr(style="border-bottom: 1px solid #eee;")
        td(width="60%" align="left" style="padding: 12px 16px; font-weight: 500;") Processing Time
        td(width="40%" align="center" style="padding: 12px 16px; text-align: center; font-weight: bold;") #{processingTime || 'N/A'}

  if jobTypes && jobTypes.length
    .card
      .card-header Job Types
      table(cellspacing="0" cellpadding="0" border="0" width="100%" style="width: 100%; border-collapse: collapse; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);")
        tr(style="background-color: #f8f9fa;")
          th(width="40%" align="left" style="padding: 12px 16px; text-align: left; border-bottom: 2px solid #dee2e6; font-weight: 600;") Type
          th(width="20%" align="center" style="padding: 12px 16px; text-align: center; border-bottom: 2px solid #dee2e6; font-weight: 600;") Count
          th(width="20%" align="center" style="padding: 12px 16px; text-align: center; border-bottom: 2px solid #dee2e6; font-weight: 600;") Success
          th(width="20%" align="center" style="padding: 12px 16px; text-align: center; border-bottom: 2px solid #dee2e6; font-weight: 600;") Failure
        each jobType in jobTypes
          tr(style="border-bottom: 1px solid #eee;")
            td(width="40%" align="left" style="padding: 12px 16px; font-weight: 500;") #{jobType.type}
            td(width="20%" align="center" style="padding: 12px 16px; text-align: center;") #{jobType.count}
            td(width="20%" align="center" style="padding: 12px 16px; text-align: center; color: #28a745;") #{jobType.success}
            td(width="20%" align="center" style="padding: 12px 16px; text-align: center; color: #{(jobType.failure > 0) ? '#dc3545' : '#6c757d'};") #{jobType.failure}

  if failureReasons && failureReasons.length
    .card
      .card-header Failure Reasons
      table(cellspacing="0" cellpadding="0" border="0" width="100%" style="width: 100%; border-collapse: collapse; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);")
        tr(style="background-color: #f8f9fa;")
          th(width="80%" align="left" style="padding: 12px 16px; text-align: left; border-bottom: 2px solid #dee2e6; font-weight: 600;") Reason
          th(width="20%" align="center" style="padding: 12px 16px; text-align: center; border-bottom: 2px solid #dee2e6; font-weight: 600;") Count
        each reason in failureReasons
          tr(style="border-bottom: 1px solid #eee;")
            td(width="80%" align="left" style="padding: 12px 16px;") #{reason.reason}
            td(width="20%" align="center" style="padding: 12px 16px; text-align: center; font-weight: bold; color: #dc3545;") #{reason.count}

  p(style="margin-top: 24px; color: #6c757d;") This is an automated report. Please do not reply to this email.

  p Regards,
  p.signature The #{appName || 'Hirli'} Team
