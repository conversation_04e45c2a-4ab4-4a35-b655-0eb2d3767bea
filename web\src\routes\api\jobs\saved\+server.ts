import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth.js';
import type { RequestHandler } from './$types';

/**
 * GET /api/jobs/saved
 * Get all saved jobs for the current user
 */
export const GET: RequestHandler = async ({ cookies }) => {
  try {
    // Get the user from the session
    const token = cookies.get('auth_token');
    const user = token && verifySessionToken(token);

    if (!user) {
      return json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get all saved jobs for the user
    const savedJobs = await prisma.savedJob.findMany({
      where: {
        userId: user.id,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return json({
      success: true,
      savedJobs,
    });
  } catch (error) {
    console.error('Error getting saved jobs:', error);
    return json({ error: 'Failed to get saved jobs' }, { status: 500 });
  }
};
