<script lang="ts">
  import SEO from '$components/shared/SEO.svelte';
  import * as Card from '$lib/components/ui/card';
  import * as Tabs from '$lib/components/ui/tabs';
  import * as Dialog from '$lib/components/ui/dialog';
  import { Badge } from '$lib/components/ui/badge';
  import { Input } from '$lib/components/ui/input';
  import { Separator } from '$lib/components/ui/separator';
  import { Button } from '$lib/components/ui/button';
  import { MessageSquare, Clock, Plus, Search, Building, Sparkles } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import InterviewCoachSession from '$components/ai/InterviewCoachSession.svelte';
  import { formatDate } from '$lib/utils';

  // No props needed for this component

  // State
  let activeTab = $state('sessions');
  let showNewSessionModal = $state(false);
  let selectedApplication = $state<any>(null);
  let searchTerm = $state('');
  let sessions = $state<any[]>([]);
  let applications = $state<any[]>([]);
  let isLoading = $state(true);

  // Load sessions and applications
  $effect(() => {
    loadData();
  });

  // Filter sessions based on search term
  const filteredSessions = $derived(
    sessions.filter((session) => session.jobTitle.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Load data function
  async function loadData() {
    try {
      // Load coaching sessions
      const sessionsResponse = await fetch('/api/ai/interview/sessions');
      if (sessionsResponse.ok) {
        const responseData = await sessionsResponse.json();
        sessions = responseData.sessions || [];
      }

      // Load applications
      const applicationsResponse = await fetch('/api/applications?status=applied,interview');
      if (applicationsResponse.ok) {
        const responseData = await applicationsResponse.json();
        applications = responseData.applications || [];
      }
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    } finally {
      isLoading = false;
    }
  }

  // Start a new coaching session
  function startNewSession() {
    showNewSessionModal = true;
  }

  // Handle application selection
  function selectApplication(application: any) {
    selectedApplication = application;
    showNewSessionModal = false;
    toast.success('Starting new coaching session');
  }

  // Format session status
  function formatStatus(status: string): string {
    switch (status) {
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }

  // Get status color
  function getStatusColor(status: string): string {
    switch (status) {
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
</script>

<SEO
  title="AI Interview Coach | Auto Apply"
  description="Practice for your interviews with AI-powered coaching" />

<div class="flex h-full flex-col">
  <div class="border-border flex flex-col justify-between border-b p-6">
    <div class="flex items-center">
      <Sparkles class="mr-2 h-5 w-5 text-blue-500" />
      <h2 class="text-lg font-semibold">AI Interview Coach</h2>
    </div>
    <p class="text-muted-foreground">Practice for your interviews with AI-powered coaching.</p>
  </div>

  <div class="flex-1 p-6">
    <Tabs.Root value={activeTab} onValueChange={(value) => (activeTab = value)}>
      <div class="flex items-center justify-between">
        <Tabs.List>
          <Tabs.Trigger value="sessions" class="flex items-center gap-2">
            <MessageSquare class="h-4 w-4" />
            <span>Coaching Sessions</span>
          </Tabs.Trigger>
          <Tabs.Trigger value="history" class="flex items-center gap-2">
            <Clock class="h-4 w-4" />
            <span>History</span>
          </Tabs.Trigger>
        </Tabs.List>

        <Button onclick={startNewSession}>
          <Plus class="mr-2 h-4 w-4" />
          New Session
        </Button>
      </div>

      <Tabs.Content value="sessions" class="mt-6">
        <div class="mb-4">
          <div class="relative">
            <Search class="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search sessions..."
              class="pl-8"
              bind:value={searchTerm} />
          </div>
        </div>

        {#if isLoading}
          <div class="flex h-64 items-center justify-center">
            <p class="text-gray-500">Loading sessions...</p>
          </div>
        {:else if filteredSessions.length === 0}
          <Card.Root>
            <Card.Content class="flex flex-col items-center justify-center p-6">
              <div
                class="bg-primary/10 mb-4 flex h-12 w-12 items-center justify-center rounded-full">
                <MessageSquare class="text-primary h-6 w-6" />
              </div>
              <h3 class="mb-2 text-lg font-medium">No coaching sessions found</h3>
              <p class="text-muted-foreground mb-4 text-center">
                Start a new coaching session to practice for your interviews.
              </p>
              <Button onclick={startNewSession}>
                <Plus class="mr-2 h-4 w-4" />
                New Session
              </Button>
            </Card.Content>
          </Card.Root>
        {:else}
          <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {#each filteredSessions as session}
              <Card.Root class="overflow-hidden">
                <Card.Header class="pb-3">
                  <div class="flex items-center justify-between">
                    <Badge class={getStatusColor(session.status)}>
                      {formatStatus(session.status)}
                    </Badge>
                    <span class="text-muted-foreground text-xs">
                      {formatDate(session.sessionDate)}
                    </span>
                  </div>
                  <Card.Title class="line-clamp-1">{session.jobTitle}</Card.Title>
                  {#if session.industry}
                    <Card.Description>{session.industry}</Card.Description>
                  {/if}
                </Card.Header>
                <Card.Content class="pb-3">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <MessageSquare class="mr-2 h-4 w-4 text-gray-500" />
                      <span class="text-sm">{session.questions?.length || 0} Questions</span>
                    </div>
                    <div>
                      <Button variant="outline" size="sm" onclick={() => {}}>Continue</Button>
                    </div>
                  </div>
                </Card.Content>
              </Card.Root>
            {/each}
          </div>
        {/if}
      </Tabs.Content>

      <Tabs.Content value="history" class="mt-6">
        <div class="mb-4">
          <div class="relative">
            <Search class="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search history..."
              class="pl-8"
              bind:value={searchTerm} />
          </div>
        </div>

        {#if isLoading}
          <div class="flex h-64 items-center justify-center">
            <p class="text-gray-500">Loading history...</p>
          </div>
        {:else if sessions.filter((s) => s.status === 'completed').length === 0}
          <Card.Root>
            <Card.Content class="flex flex-col items-center justify-center p-6">
              <div
                class="bg-primary/10 mb-4 flex h-12 w-12 items-center justify-center rounded-full">
                <Clock class="text-primary h-6 w-6" />
              </div>
              <h3 class="mb-2 text-lg font-medium">No completed sessions</h3>
              <p class="text-muted-foreground mb-4 text-center">
                Complete a coaching session to see it in your history.
              </p>
              <Button onclick={startNewSession}>
                <Plus class="mr-2 h-4 w-4" />
                New Session
              </Button>
            </Card.Content>
          </Card.Root>
        {:else}
          <div class="rounded-md border">
            <div class="grid grid-cols-[1fr_auto_auto] gap-4 p-4 font-medium">
              <div>Job Title</div>
              <div>Date</div>
              <div>Actions</div>
            </div>
            <Separator />
            {#each sessions.filter((s) => s.status === 'completed') as session}
              <div class="grid grid-cols-[1fr_auto_auto] items-center gap-4 p-4">
                <div>
                  <div class="font-medium">{session.jobTitle}</div>
                  {#if session.industry}
                    <div class="text-muted-foreground text-sm">{session.industry}</div>
                  {/if}
                </div>
                <div class="text-muted-foreground text-sm">
                  {formatDate(session.sessionDate)}
                </div>
                <div>
                  <Button variant="outline" size="sm">View</Button>
                </div>
              </div>
              <Separator />
            {/each}
          </div>
        {/if}
      </Tabs.Content>
    </Tabs.Root>
  </div>
</div>

<Dialog.Root bind:open={showNewSessionModal}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="sm:max-w-md">
      <Dialog.Header>
        <Dialog.Title>Select an Application</Dialog.Title>
        <Dialog.Description>
          Choose a job application to practice for an interview.
        </Dialog.Description>
      </Dialog.Header>

      {#if applications.length === 0}
        <div class="mb-4 rounded-md bg-blue-50 p-4 text-sm text-blue-700">
          <p>No job applications found. Add a job application first.</p>
        </div>
      {:else}
        <div class="mb-4 max-h-64 overflow-y-auto rounded-md border">
          {#each applications as application}
            <div
              class="flex cursor-pointer items-center justify-between border-b p-3 hover:bg-gray-50"
              onclick={() => selectApplication(application)}
              onkeydown={(e) => e.key === 'Enter' && selectApplication(application)}
              role="button"
              tabindex="0">
              <div>
                <div class="font-medium">{application.jobTitle}</div>
                <div class="flex items-center text-sm text-gray-500">
                  <Building class="mr-1 h-3 w-3" />
                  {application.company}
                </div>
              </div>
              <Badge variant={application.status === 'interview' ? 'default' : 'outline'}>
                {application.status}
              </Badge>
            </div>
          {/each}
        </div>
      {/if}

      <Dialog.Footer>
        <Button variant="outline" onclick={() => (showNewSessionModal = false)}>Cancel</Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>

{#if selectedApplication}
  <InterviewCoachSession
    applicationId={selectedApplication.id}
    jobTitle={selectedApplication.jobTitle}
    company={selectedApplication.company}
    onClose={() => (selectedApplication = null)} />
{/if}
