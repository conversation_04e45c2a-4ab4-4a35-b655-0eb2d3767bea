{"name": "auto-apply-scraper", "version": "1.0.0", "description": "Dedicated job scraping service for auto-apply platform", "main": "index.ts", "type": "module", "scripts": {"start": "tsx index.ts", "dev": "tsx --watch index.ts", "build": "tsc", "parallel": "tsx jobs/parallelJobScraper.ts", "details": "tsx jobs/scrapeJobDetails.ts", "enrich": "tsx jobs/enrichJobDetails.ts"}, "dependencies": {"@prisma/client": "^6.8.2", "playwright": "^1.52.0", "redis": "^4.7.0", "ioredis": "^5.4.1", "tsx": "^4.19.2", "typescript": "^5.7.2", "node-os-utils": "^1.3.7", "cheerio": "^1.0.0", "axios": "^1.7.9", "dotenv": "^16.4.7", "uuid": "^10.0.0", "cron": "^3.1.7", "puppeteer": "^23.10.4", "resend": "^4.0.1"}, "devDependencies": {"@types/node": "^22.10.2", "@types/uuid": "^10.0.0", "prisma": "^6.8.2"}}