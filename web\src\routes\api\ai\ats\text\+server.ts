import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';

// Worker API URL - fallback to localhost for development
const WORKER_API_URL = process.env.WORKER_API_URL ?? 'http://localhost:3002';

/**
 * Direct resume text analysis endpoint
 * This endpoint accepts raw resume text and job description for analysis
 * It doesn't require a saved resume document
 */
export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { resumeText, jobDescription } = await request.json();

    if (!resumeText) {
      return json({ error: 'Resume text is required' }, { status: 400 });
    }

    // Generate ATS analysis
    const analysis = await generateATSAnalysis(resumeText, jobDescription);

    // Track feature usage using the centralized system
    try {
      const { trackFeatureUsage } = await import('$lib/server/feature-usage');
      await trackFeatureUsage(locals.user.id, 'ats_optimization', 'ats_scans_monthly', 1);
    } catch (usageError) {
      console.warn('Failed to track feature usage:', usageError);
      // Don't fail the request if usage tracking fails
    }

    return json({ analysis });
  } catch (error) {
    console.error('Error generating ATS analysis:', error);
    return json({ error: 'Failed to generate ATS analysis' }, { status: 500 });
  }
};

// Helper function to generate ATS analysis
async function generateATSAnalysis(resumeText: string, jobDescription?: string): Promise<any> {
  try {
    // Prepare the request body
    const requestBody: any = {
      resumeText,
    };

    // Add job description if available
    if (jobDescription) {
      requestBody.jobDescription = jobDescription;
    }

    console.log('Calling worker API for resume analysis with:', {
      resumeTextLength: resumeText.length,
      hasJobDescription: !!jobDescription,
    });

    // Call the worker API for AI analysis
    const response = await fetch(`${WORKER_API_URL}/api/ai/resume-analysis`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`Worker API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.success || !data.analysis) {
      throw new Error('Worker API returned invalid response format');
    }

    console.log('Received analysis from worker API');

    // Return the analysis
    return {
      overallScore: data.analysis.overallScore ?? 0,
      keywordScore: data.analysis.keywordScore ?? 0,
      formatScore: data.analysis.formatScore ?? 0,
      contentScore: data.analysis.contentScore ?? 0,
      readabilityScore: data.analysis.readabilityScore ?? 0,
      keywordMatches: data.analysis.keywordMatches ?? [],
      missingKeywords: data.analysis.missingKeywords ?? [],
      formatIssues: data.analysis.formatIssues ?? [],
      contentSuggestions: data.analysis.contentSuggestions ?? [],
      readabilitySuggestions: data.analysis.readabilitySuggestions ?? [],
    };
  } catch (error) {
    console.error('Error in ATS analysis generation:', error);

    // Return default values if analysis fails
    return {
      overallScore: 70,
      keywordScore: 65,
      formatScore: 75,
      contentScore: 70,
      readabilityScore: 80,
      keywordMatches: ['resume', 'experience', 'skills'],
      missingKeywords: ['specific technical skills', 'certifications'],
      formatIssues: ['Consider using a more ATS-friendly format'],
      contentSuggestions: ['Add more quantifiable achievements'],
      readabilitySuggestions: ['Use more bullet points for better readability'],
    };
  }
}
