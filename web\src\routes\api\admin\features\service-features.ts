import { FeatureCategory, LimitType } from '$lib/models/features/features';
import type { Feature, FeatureLimit } from '$lib/models/features/types';

/**
 * Feature limits for service features
 */
export const SERVICE_FEATURE_LIMITS: Record<string, FeatureLimit> = {
  // Auto-Apply Limits
  auto_apply_jobs_monthly: {
    id: 'auto_apply_jobs_monthly',
    name: 'Auto-Apply Jobs',
    description: 'Number of jobs you can auto-apply to per month',
    defaultValue: 50,
    type: LimitType.Monthly,
    unit: 'applications',
    resetDay: 1,
  },

  job_search_monthly: {
    id: 'job_search_monthly',
    name: 'Job Searches',
    description: 'Number of job searches you can perform per month',
    defaultValue: 100,
    type: LimitType.Monthly,
    unit: 'searches',
    resetDay: 1,
  },

  saved_jobs_total: {
    id: 'saved_jobs_total',
    name: 'Saved Jobs',
    description: 'Maximum number of jobs you can save',
    defaultValue: 200,
    type: LimitType.Total,
    unit: 'jobs',
  },

  // Co-Pilot Limits
  ai_interview_sessions_monthly: {
    id: 'ai_interview_sessions_monthly',
    name: 'AI Interview Sessions',
    description: 'Number of AI interview coaching sessions per month',
    defaultValue: 5,
    type: LimitType.Monthly,
    unit: 'sessions',
    resetDay: 1,
  },

  career_advice_requests_monthly: {
    id: 'career_advice_requests_monthly',
    name: 'Career Advice Requests',
    description: 'Number of personalized career advice requests per month',
    defaultValue: 10,
    type: LimitType.Monthly,
    unit: 'requests',
    resetDay: 1,
  },

  job_match_analysis_monthly: {
    id: 'job_match_analysis_monthly',
    name: 'Job Match Analysis',
    description: 'Number of job match analyses you can perform per month',
    defaultValue: 20,
    type: LimitType.Monthly,
    unit: 'analyses',
    resetDay: 1,
  },

  // Job Tracker Limits
  tracked_applications_total: {
    id: 'tracked_applications_total',
    name: 'Tracked Applications',
    description: 'Maximum number of job applications you can track',
    defaultValue: 100,
    type: LimitType.Total,
    unit: 'applications',
  },

  application_notes_per_job: {
    id: 'application_notes_per_job',
    name: 'Notes Per Application',
    description: 'Maximum number of notes you can add per job application',
    defaultValue: 20,
    type: LimitType.Total,
    unit: 'notes',
  },

  // Resume Builder Limits
  resume_versions_total: {
    id: 'resume_versions_total',
    name: 'Resume Versions',
    description: 'Maximum number of resume versions you can create',
    defaultValue: 5,
    type: LimitType.Total,
    unit: 'versions',
  },

  resume_templates_total: {
    id: 'resume_templates_total',
    name: 'Resume Templates',
    description: 'Number of premium resume templates available',
    defaultValue: 10,
    type: LimitType.Total,
    unit: 'templates',
  },

  // Automation Limits
  automation_runs_monthly: {
    id: 'automation_runs_monthly',
    name: 'Automation Runs',
    description: 'Number of automation workflows you can run per month',
    defaultValue: 5,
    type: LimitType.Monthly,
    unit: 'runs',
    resetDay: 1,
  },

  automation_tasks_per_run: {
    id: 'automation_tasks_per_run',
    name: 'Tasks Per Automation',
    description: 'Maximum number of tasks per automation workflow',
    defaultValue: 10,
    type: LimitType.Total,
    unit: 'tasks',
  },
};

/**
 * Auto-Apply features
 */
export const AUTO_APPLY_FEATURES: Feature[] = [
  // One-Click Apply
  {
    id: 'one_click_apply',
    name: 'One-Click Apply',
    description: 'Apply to jobs with a single click using your saved profile and resume',
    category: FeatureCategory.JobSearch,
    icon: 'mouse-pointer-click',
    beta: false,
    limits: [SERVICE_FEATURE_LIMITS.auto_apply_jobs_monthly],
  },

  // Advanced Job Search
  {
    id: 'advanced_job_search',
    name: 'Advanced Job Search',
    description: 'Access advanced filters and search options to find the perfect job match',
    category: FeatureCategory.JobSearch,
    icon: 'search',
    beta: false,
    limits: [SERVICE_FEATURE_LIMITS.job_search_monthly],
  },

  // Job Matching
  {
    id: 'job_matching',
    name: 'AI Job Matching',
    description: 'Get AI-powered job recommendations based on your skills and experience',
    category: FeatureCategory.JobSearch,
    icon: 'sparkles',
    beta: false,
  },

  // Job Bookmarking
  {
    id: 'job_bookmarking',
    name: 'Job Bookmarking',
    description: "Save and organize jobs you're interested in for later review",
    category: FeatureCategory.JobSearch,
    icon: 'bookmark',
    beta: false,
    limits: [SERVICE_FEATURE_LIMITS.saved_jobs_total],
  },
];

/**
 * Co-Pilot features
 */
export const CO_PILOT_FEATURES: Feature[] = [
  // AI Interview Coach
  {
    id: 'ai_interview_coach',
    name: 'AI Interview Coach',
    description: 'Practice interviews with AI that provides feedback and improvement suggestions',
    category: FeatureCategory.Advanced,
    icon: 'message-square',
    beta: true,
    limits: [SERVICE_FEATURE_LIMITS.ai_interview_sessions_monthly],
  },

  // Career Advisor
  {
    id: 'career_advisor',
    name: 'Career Advisor',
    description: 'Get personalized career advice and guidance based on your goals',
    category: FeatureCategory.Advanced,
    icon: 'compass',
    beta: true,
    limits: [SERVICE_FEATURE_LIMITS.career_advice_requests_monthly],
  },

  // Job Match Analyzer
  {
    id: 'job_match_analyzer',
    name: 'Job Match Analyzer',
    description: 'Analyze how well your profile matches specific job requirements',
    category: FeatureCategory.Advanced,
    icon: 'target',
    beta: true,
    limits: [SERVICE_FEATURE_LIMITS.job_match_analysis_monthly],
  },

  // Career Roadmap
  {
    id: 'career_roadmap',
    name: 'Career Roadmap',
    description: 'Create a strategic career development plan with milestones and goals',
    category: FeatureCategory.Advanced,
    icon: 'map',
    beta: true,
  },
];

/**
 * Job Tracker features
 */
export const JOB_TRACKER_FEATURES: Feature[] = [
  // Application Tracking
  {
    id: 'application_tracking',
    name: 'Application Tracking',
    description: 'Track the status and progress of all your job applications',
    category: FeatureCategory.Applications,
    icon: 'list-checks',
    beta: false,
    limits: [SERVICE_FEATURE_LIMITS.tracked_applications_total],
  },

  // Application Notes
  {
    id: 'application_notes',
    name: 'Application Notes',
    description: 'Add detailed notes and reminders to your job applications',
    category: FeatureCategory.Applications,
    icon: 'clipboard',
    beta: false,
    limits: [SERVICE_FEATURE_LIMITS.application_notes_per_job],
  },

  // Interview Scheduler
  {
    id: 'interview_scheduler',
    name: 'Interview Scheduler',
    description: 'Schedule and manage your job interviews with reminders',
    category: FeatureCategory.Applications,
    icon: 'calendar',
    beta: false,
  },

  // Application Analytics
  {
    id: 'application_analytics',
    name: 'Application Analytics',
    description: 'View insights and statistics about your job application performance',
    category: FeatureCategory.Applications,
    icon: 'bar-chart',
    beta: false,
  },
];

/**
 * Resume Builder features
 */
export const RESUME_BUILDER_FEATURES: Feature[] = [
  // Resume Creator
  {
    id: 'resume_creator',
    name: 'Resume Creator',
    description: 'Create professional resumes with our easy-to-use builder',
    category: FeatureCategory.Resume,
    icon: 'file-edit',
    beta: false,
    limits: [SERVICE_FEATURE_LIMITS.resume_versions_total],
  },

  // ATS Optimization
  {
    id: 'ats_optimization',
    name: 'ATS Optimization',
    description: 'Optimize your resume to pass through Applicant Tracking Systems',
    category: FeatureCategory.Resume,
    icon: 'check-circle',
    beta: false,
  },

  // Premium Templates
  {
    id: 'premium_templates',
    name: 'Premium Templates',
    description: 'Access to premium, professionally designed resume templates',
    category: FeatureCategory.Resume,
    icon: 'layout-template',
    beta: false,
    limits: [SERVICE_FEATURE_LIMITS.resume_templates_total],
  },

  // Resume Export
  {
    id: 'resume_export',
    name: 'Resume Export',
    description: 'Export your resume in multiple formats (PDF, DOCX, TXT)',
    category: FeatureCategory.Resume,
    icon: 'download',
    beta: false,
  },
];

/**
 * Automation features
 */
export const AUTOMATION_FEATURES: Feature[] = [
  // Workflow Automation
  {
    id: 'workflow_automation',
    name: 'Workflow Automation',
    description: 'Create automated workflows for your job search process',
    category: FeatureCategory.Automation,
    icon: 'workflow',
    beta: true,
    limits: [SERVICE_FEATURE_LIMITS.automation_runs_monthly],
  },

  // Job Alerts
  {
    id: 'job_alerts',
    name: 'Job Alerts',
    description: 'Receive customized job alerts based on your preferences',
    category: FeatureCategory.Automation,
    icon: 'bell',
    beta: false,
  },

  // Auto Resume Tailoring
  {
    id: 'auto_resume_tailoring',
    name: 'Auto Resume Tailoring',
    description: 'Automatically tailor your resume for specific job applications',
    category: FeatureCategory.Automation,
    icon: 'scissors',
    beta: true,
  },

  // Scheduled Applications
  {
    id: 'scheduled_applications',
    name: 'Scheduled Applications',
    description: 'Schedule job applications to be submitted at optimal times',
    category: FeatureCategory.Automation,
    icon: 'clock',
    beta: true,
    limits: [SERVICE_FEATURE_LIMITS.automation_tasks_per_run],
  },
];

/**
 * Matches features
 */
export const MATCHES_FEATURES: Feature[] = [
  // AI Job Matching
  {
    id: 'ai_job_matching',
    name: 'AI Job Matching',
    description: 'Get AI-powered job recommendations based on your skills and experience',
    category: FeatureCategory.JobSearch,
    icon: 'sparkles',
    beta: false,
    limits: [SERVICE_FEATURE_LIMITS.job_match_analysis_monthly],
  },

  // Match Scoring
  {
    id: 'match_scoring',
    name: 'Match Scoring',
    description: 'See detailed compatibility scores for each job match',
    category: FeatureCategory.JobSearch,
    icon: 'percent',
    beta: false,
  },

  // Skill Gap Analysis
  {
    id: 'skill_gap_analysis_matches',
    name: 'Skill Gap Analysis',
    description: 'Identify missing skills for your matched job opportunities',
    category: FeatureCategory.JobSearch,
    icon: 'puzzle',
    beta: true,
  },
];

/**
 * Jobs features
 */
export const JOBS_FEATURES: Feature[] = [
  // Job Board
  {
    id: 'job_board',
    name: 'Job Board',
    description: 'Browse and search through thousands of job listings',
    category: FeatureCategory.JobSearch,
    icon: 'briefcase',
    beta: false,
  },

  // Job Filtering
  {
    id: 'job_filtering',
    name: 'Advanced Job Filtering',
    description: 'Filter jobs by salary, location, experience level, and more',
    category: FeatureCategory.JobSearch,
    icon: 'filter',
    beta: false,
  },

  // Salary Insights
  {
    id: 'salary_insights_jobs',
    name: 'Salary Insights',
    description: 'View salary ranges and compensation details for job listings',
    category: FeatureCategory.JobSearch,
    icon: 'dollar-sign',
    beta: false,
  },
];

/**
 * Documents features
 */
export const DOCUMENTS_FEATURES: Feature[] = [
  // Document Storage
  {
    id: 'document_storage',
    name: 'Document Storage',
    description: 'Securely store and manage your resumes, cover letters, and other documents',
    category: FeatureCategory.Resume,
    icon: 'folder',
    beta: false,
    limits: [
      {
        id: 'storage_gb',
        name: 'Storage',
        description: 'Amount of storage space for your documents',
        defaultValue: 1,
        type: LimitType.Total,
        unit: 'GB',
      },
      {
        id: 'document_count',
        name: 'Document Count',
        description: 'Maximum number of documents you can upload',
        defaultValue: 20,
        type: LimitType.Total,
        unit: 'documents',
      },
      {
        id: 'document_size_mb',
        name: 'Document Size',
        description: 'Maximum size of individual documents',
        defaultValue: 10,
        type: LimitType.Total,
        unit: 'MB',
      },
      {
        id: 'document_sharing',
        name: 'Document Sharing',
        description: 'Number of documents you can share with others',
        defaultValue: 5,
        type: LimitType.Total,
        unit: 'documents',
      },
    ],
  },

  // Document Sharing
  {
    id: 'document_sharing',
    name: 'Document Sharing',
    description: 'Share your documents with recruiters or team members',
    category: FeatureCategory.Resume,
    icon: 'share',
    beta: false,
  },

  // Document Versioning
  {
    id: 'document_versioning',
    name: 'Document Versioning',
    description: 'Track changes and maintain multiple versions of your documents',
    category: FeatureCategory.Resume,
    icon: 'history',
    beta: false,
  },
];

/**
 * Analysis features (additional to the ones in analysis-features.ts)
 */
export const SERVICE_ANALYSIS_FEATURES: Feature[] = [
  // Resume Analysis
  {
    id: 'resume_analysis',
    name: 'Resume Analysis',
    description: 'Get detailed feedback and improvement suggestions for your resume',
    category: FeatureCategory.Analytics,
    icon: 'file-text',
    beta: false,
  },

  // Job Market Analysis
  {
    id: 'job_market_analysis',
    name: 'Job Market Analysis',
    description: 'Analyze trends and insights in your target job market',
    category: FeatureCategory.Analytics,
    icon: 'trending-up',
    beta: true,
  },

  // Application Performance Analysis
  {
    id: 'application_performance_analysis',
    name: 'Application Performance Analysis',
    description: 'Analyze your application success rates and identify improvement areas',
    category: FeatureCategory.Analytics,
    icon: 'activity',
    beta: true,
  },
];

/**
 * All service features combined
 */
export const SERVICE_FEATURES: Feature[] = [
  ...AUTO_APPLY_FEATURES,
  ...CO_PILOT_FEATURES,
  ...JOB_TRACKER_FEATURES,
  ...RESUME_BUILDER_FEATURES,
  ...AUTOMATION_FEATURES,
  ...MATCHES_FEATURES,
  ...JOBS_FEATURES,
  ...DOCUMENTS_FEATURES,
  ...SERVICE_ANALYSIS_FEATURES,
];
