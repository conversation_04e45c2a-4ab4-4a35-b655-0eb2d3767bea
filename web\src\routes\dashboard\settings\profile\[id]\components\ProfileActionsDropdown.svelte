<script lang="ts">
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
  import { MoreHorizontal, Trash2, Eye, EyeOff } from 'lucide-svelte';
  import { Button } from '$lib/components/ui/button/index.js';
  import { goto } from '$app/navigation';
  import { toast } from 'svelte-sonner';

  // Props
  const { profile, isVisible, onToggleVisibility } = $props<{
    profile: any;
    isVisible: boolean;
    onToggleVisibility: () => Promise<void>;
  }>();

  // Delete profile function
  async function deleteProfile() {
    if (confirm('Are you sure you want to delete this profile? This action cannot be undone.')) {
      try {
        const res = await fetch(`/api/profile/${profile.id}`, {
          method: 'DELETE',
        });
        
        if (res.ok) {
          toast.success('Profile deleted successfully');
          goto('/dashboard/settings/profile');
        } else {
          const data = await res.json();
          toast.error(data.error || 'Failed to delete profile');
        }
      } catch (err) {
        console.error('Error deleting profile:', err);
        toast.error('Failed to delete profile');
      }
    }
  }
</script>

<DropdownMenu.Root>
  <DropdownMenu.Trigger asChild let:builder>
    <Button 
      variant="ghost" 
      size="icon" 
      class="h-8 w-8 rounded-full" 
      builders={[builder]}>
      <MoreHorizontal class="h-4 w-4" />
      <span class="sr-only">Open menu</span>
    </Button>
  </DropdownMenu.Trigger>
  <DropdownMenu.Content align="end" class="w-48">
    <DropdownMenu.Label>Profile Actions</DropdownMenu.Label>
    <DropdownMenu.Separator />
    
    <DropdownMenu.Item 
      on:click={onToggleVisibility}
      class="cursor-pointer">
      {#if isVisible}
        <EyeOff class="mr-2 h-4 w-4" />
        <span>Hide from Recruiters</span>
      {:else}
        <Eye class="mr-2 h-4 w-4" />
        <span>Show to Recruiters</span>
      {/if}
    </DropdownMenu.Item>
    
    <DropdownMenu.Separator />
    
    <DropdownMenu.Item 
      on:click={deleteProfile}
      class="cursor-pointer text-destructive focus:text-destructive">
      <Trash2 class="mr-2 h-4 w-4" />
      <span>Delete Profile</span>
    </DropdownMenu.Item>
  </DropdownMenu.Content>
</DropdownMenu.Root>
