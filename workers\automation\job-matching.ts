/**
 * Job Matching Utility for Automation System
 * 
 * Implements a two-tier matching system:
 * Tier 1: Fast database filtering using indexed fields
 * Tier 2: Detailed scoring for pre-filtered jobs
 */

export interface JobMatchCriteria {
  profileId: string;
  skills: string[];
  experienceYears: number;
  jobTitles: string[];
  salaryMin?: number;
  salaryMax?: number;
  remotePreference?: string;
  companySizePreference?: string[];
  excludeCompanies?: string[];
  preferredCompanies?: string[];
}

export interface JobListing {
  id: string;
  title: string;
  company: string;
  location: string;
  description: string;
  salary?: string;
  salaryMin?: number;
  salaryMax?: number;
  jobType?: string;
  experienceYears?: number;
  requiredSkills?: string[];
  isRemote?: boolean;
  remoteType?: string;
  companySize?: string;
  postedAt: Date;
  
  // Enhanced fields for fast matching
  normalizedTitle?: string;
  seniorityLevel?: string;
  techStack?: string[];
  isRemoteFriendly?: boolean;
}

export interface JobMatchResult {
  job: JobListing;
  matchScore: number;
  skillsMatch: number;
  experienceMatch: number;
  titleMatch: number;
  salaryMatch: number;
  locationMatch: number;
  matchReasons: string[];
  missingSkills: string[];
}

/**
 * Tier 1: Fast Database Filtering
 * Pre-filters jobs using indexed database fields
 */
export function buildDatabaseFilter(criteria: JobMatchCriteria): any {
  const filter: any = {
    isActive: true,
  };

  // Title/Seniority matching
  if (criteria.jobTitles.length > 0) {
    const normalizedTitles = criteria.jobTitles.map(title => 
      normalizeJobTitle(title)
    );
    filter.OR = [
      {
        normalizedTitle: {
          in: normalizedTitles
        }
      },
      {
        title: {
          contains: criteria.jobTitles[0],
          mode: 'insensitive'
        }
      }
    ];
  }

  // Skills matching (array overlap)
  if (criteria.skills.length > 0) {
    filter.techStack = {
      hasSome: criteria.skills.map(skill => skill.toLowerCase())
    };
  }

  // Salary filtering
  if (criteria.salaryMin || criteria.salaryMax) {
    filter.AND = filter.AND || [];
    
    if (criteria.salaryMin) {
      filter.AND.push({
        OR: [
          { salaryMax: { gte: criteria.salaryMin } },
          { salaryMin: { gte: criteria.salaryMin } }
        ]
      });
    }
    
    if (criteria.salaryMax) {
      filter.AND.push({
        OR: [
          { salaryMin: { lte: criteria.salaryMax } },
          { salaryMax: { lte: criteria.salaryMax } }
        ]
      });
    }
  }

  // Remote preference filtering
  if (criteria.remotePreference && criteria.remotePreference !== 'any') {
    if (criteria.remotePreference === 'remote') {
      filter.isRemoteFriendly = true;
    } else if (criteria.remotePreference === 'onsite') {
      filter.isRemoteFriendly = false;
    }
  }

  // Company size filtering
  if (criteria.companySizePreference && criteria.companySizePreference.length > 0) {
    filter.companySize = {
      in: criteria.companySizePreference
    };
  }

  // Exclude companies
  if (criteria.excludeCompanies && criteria.excludeCompanies.length > 0) {
    filter.company = {
      notIn: criteria.excludeCompanies
    };
  }

  // Preferred companies (boost in scoring, not filter)
  // This will be handled in Tier 2 scoring

  return filter;
}

/**
 * Tier 2: Detailed Job Scoring
 * Calculates detailed match scores for pre-filtered jobs
 */
export function calculateJobMatchScore(
  job: JobListing,
  criteria: JobMatchCriteria
): JobMatchResult {
  const reasons: string[] = [];
  const missingSkills: string[] = [];
  let totalScore = 0;

  // Skills matching (40% weight)
  const jobSkills = job.requiredSkills || job.techStack || [];
  const matchedSkills = criteria.skills.filter(skill =>
    jobSkills.some(jobSkill =>
      jobSkill.toLowerCase().includes(skill.toLowerCase()) ||
      skill.toLowerCase().includes(jobSkill.toLowerCase())
    )
  );

  const skillsMatchPercentage = jobSkills.length > 0 
    ? (matchedSkills.length / jobSkills.length) * 100 
    : 50; // Neutral if no skills listed

  const skillsScore = Math.min(100, skillsMatchPercentage);
  totalScore += skillsScore * 0.4;

  if (matchedSkills.length > 0) {
    reasons.push(`Matched ${matchedSkills.length} required skills: ${matchedSkills.join(', ')}`);
  }

  // Find missing skills
  jobSkills.forEach(jobSkill => {
    if (!criteria.skills.some(skill => 
      skill.toLowerCase().includes(jobSkill.toLowerCase()) ||
      jobSkill.toLowerCase().includes(skill.toLowerCase())
    )) {
      missingSkills.push(jobSkill);
    }
  });

  // Experience matching (25% weight)
  const requiredYears = job.experienceYears || 0;
  const experienceMatch = criteria.experienceYears >= requiredYears - 1;
  const experienceScore = experienceMatch ? 100 : Math.max(0, 100 - (requiredYears - criteria.experienceYears) * 20);
  totalScore += experienceScore * 0.25;

  if (experienceMatch) {
    reasons.push(`Experience requirement met (${criteria.experienceYears} years)`);
  }

  // Title matching (20% weight)
  const titleMatch = criteria.jobTitles.some(title =>
    job.title.toLowerCase().includes(title.toLowerCase()) ||
    title.toLowerCase().includes(job.title.toLowerCase())
  );
  const titleScore = titleMatch ? 100 : 30;
  totalScore += titleScore * 0.2;

  if (titleMatch) {
    reasons.push('Job title matches profile experience');
  }

  // Salary matching (10% weight)
  let salaryScore = 50; // Default neutral score
  if (job.salaryMin && job.salaryMax && criteria.salaryMin) {
    const jobSalaryMid = (job.salaryMin + job.salaryMax) / 2;
    const salaryDiff = Math.abs(jobSalaryMid - criteria.salaryMin) / criteria.salaryMin;
    salaryScore = Math.max(0, 100 - salaryDiff * 100);
    
    if (salaryScore > 70) {
      reasons.push('Salary range matches expectations');
    }
  }
  totalScore += salaryScore * 0.1;

  // Location/Remote matching (5% weight)
  let locationScore = 50; // Default neutral score
  if (criteria.remotePreference === 'remote' && job.isRemoteFriendly) {
    locationScore = 100;
    reasons.push('Remote work available');
  } else if (criteria.remotePreference === 'onsite' && !job.isRemoteFriendly) {
    locationScore = 100;
    reasons.push('On-site position as preferred');
  }
  totalScore += locationScore * 0.05;

  // Company preference bonus (not weighted in main score)
  if (criteria.preferredCompanies && criteria.preferredCompanies.includes(job.company)) {
    totalScore += 10; // Bonus points
    reasons.push('Preferred company');
  }

  return {
    job,
    matchScore: Math.round(Math.min(100, totalScore)),
    skillsMatch: Math.round(skillsScore),
    experienceMatch: Math.round(experienceScore),
    titleMatch: Math.round(titleScore),
    salaryMatch: Math.round(salaryScore),
    locationMatch: Math.round(locationScore),
    matchReasons: reasons,
    missingSkills
  };
}

/**
 * Normalize job titles for consistent matching
 */
export function normalizeJobTitle(title: string): string {
  return title
    .toLowerCase()
    .replace(/\b(senior|sr|junior|jr|lead|principal|staff)\b/g, '')
    .replace(/\b(engineer|developer|dev)\b/g, 'engineer')
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Extract seniority level from job title
 */
export function extractSeniorityLevel(title: string): string {
  const titleLower = title.toLowerCase();
  
  if (titleLower.includes('principal') || titleLower.includes('staff')) {
    return 'principal';
  } else if (titleLower.includes('senior') || titleLower.includes('sr')) {
    return 'senior';
  } else if (titleLower.includes('lead')) {
    return 'lead';
  } else if (titleLower.includes('junior') || titleLower.includes('jr')) {
    return 'junior';
  }
  
  return 'mid';
}

/**
 * Extract tech stack from job description
 */
export function extractTechStack(description: string, title: string): string[] {
  const techKeywords = [
    'javascript', 'typescript', 'python', 'java', 'react', 'angular', 'vue',
    'node.js', 'express', 'django', 'flask', 'spring', 'aws', 'azure', 'gcp',
    'docker', 'kubernetes', 'postgresql', 'mysql', 'mongodb', 'redis',
    'git', 'jenkins', 'terraform', 'ansible', 'linux', 'windows'
  ];

  const text = (description + ' ' + title).toLowerCase();
  const foundTech = techKeywords.filter(tech => 
    text.includes(tech) || text.includes(tech.replace('.', ''))
  );

  return [...new Set(foundTech)]; // Remove duplicates
}
