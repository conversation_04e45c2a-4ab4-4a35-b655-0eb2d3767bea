/**
 * Email Analytics Check API Endpoint
 *
 * This endpoint checks if the EmailEvent table exists and is accessible.
 */

import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { logger } from '$lib/server/logger';

export async function GET() {
  try {
    // Try to count the number of records in the EmailEvent table
    const count = await prisma.emailEvent.count();

    return json({
      exists: true,
      count,
      message: 'EmailEvent table exists and is accessible',
    });
  } catch (error) {
    logger.error('Error checking EmailEvent table:', error);

    // Create the EmailEvent table if it doesn't exist
    try {
      // This will run the necessary migrations to create the table
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS "web"."EmailEvent" (
          "id" TEXT NOT NULL,
          "email" TEXT NOT NULL,
          "type" TEXT NOT NULL,
          "timestamp" TIMESTAMP(3) NOT NULL,
          "templateName" TEXT,
          "category" TEXT,
          "data" JSONB,
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          CONSTRAINT "EmailEvent_pkey" PRIMARY KEY ("id")
        )
      `;

      // Create indexes
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "EmailEvent_email_idx" ON "web"."EmailEvent"("email")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "EmailEvent_type_idx" ON "web"."EmailEvent"("type")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "EmailEvent_timestamp_idx" ON "web"."EmailEvent"("timestamp")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "EmailEvent_templateName_idx" ON "web"."EmailEvent"("templateName")`;

      return json({
        exists: true,
        count: 0,
        message: 'EmailEvent table created successfully',
      });
    } catch (createError) {
      logger.error('Error creating EmailEvent table:', createError);

      return json(
        {
          exists: false,
          error: createError instanceof Error ? createError.message : 'Unknown error',
          message: 'Failed to create EmailEvent table',
        },
        { status: 500 }
      );
    }
  }
}
