import type { PageServerLoad } from './$types';
import { prisma } from '$lib/server/prisma';
import { redirect } from '@sveltejs/kit';

// Generate a unique referral code
function generateReferralCode(name?: string, email?: string): string {
  const prefix = name
    ? name
        .replace(/[^a-zA-Z]/g, '')
        .substring(0, 3)
        .toUpperCase()
    : email?.substring(0, 3).toUpperCase() || 'REF';

  const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `${prefix}${randomSuffix}`;
}

export const load: PageServerLoad = async ({ locals }) => {
  const user = locals.user;

  if (!user?.email) {
    throw redirect(302, '/auth/sign-in');
  }

  try {
    // Get user with referral data
    const userData = await prisma.user.findUnique({
      where: { email: user.email },
      include: {
        referralsMade: {
          include: {
            referred: {
              select: {
                id: true,
                name: true,
                email: true,
                createdAt: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        referrals: {
          select: {
            id: true,
            name: true,
            email: true,
            createdAt: true,
          },
        },
        referredBy: {
          select: {
            id: true,
            name: true,
            email: true,
            referralCode: true,
          },
        },
      },
    });

    if (!userData) {
      throw redirect(302, '/auth/sign-in');
    }

    // Generate referral code if user doesn't have one
    let referralCode = userData.referralCode;
    if (!referralCode) {
      referralCode = generateReferralCode(userData.name, userData.email);

      // Ensure uniqueness
      let attempts = 0;
      while (attempts < 5) {
        const existing = await prisma.user.findUnique({
          where: { referralCode },
        });

        if (!existing) break;

        referralCode = generateReferralCode(userData.name, userData.email);
        attempts++;
      }

      // Create initial referral code (no history until it has data)
      await prisma.user.update({
        where: { id: userData.id },
        data: { referralCode },
      });
    }

    const baseUrl = process.env.PUBLIC_BASE_URL || 'http://localhost:5173';
    const referralLink = `${baseUrl}/auth/sign-up?ref=${referralCode}`;

    const referralData = {
      referralCode,
      referralLink,
      referralCount: userData.referralCount ?? 0,
      referralRewards: userData.referralRewards ?? 0,
      referrals: userData.referralsMade ?? [],
      referredBy: userData.referredBy,
    };

    return {
      referralData,
    };
  } catch (error) {
    console.error('Error loading referral data:', error);
    throw redirect(302, '/auth/sign-in');
  }
};
