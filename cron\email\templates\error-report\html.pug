extends ../layouts/base.pug

block content
  h1(style="color: #dc3545;") #{errorTitle || 'Error Report'}

  p(style="font-size: 18px; margin-bottom: 20px;") #{errorMessage || 'An error has occurred in the application.'}

  .card
    .card-header Error Details
    table(style="width: 100%; table-layout: fixed; border-collapse: collapse; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);")
      colgroup
        col(style="width: 30%;")
        col(style="width: 70%;")
      thead
        tr(style="background-color: #f8f9fa;")
          th(style="padding: 12px 16px; text-align: left; border-bottom: 2px solid #dee2e6; font-weight: 600;") Property
          th(style="padding: 12px 16px; text-align: left; border-bottom: 2px solid #dee2e6; font-weight: 600;") Value
      tbody
        tr(style="border-bottom: 1px solid #eee;")
          td(style="padding: 12px 16px; font-weight: 500;") Timestamp
          td(style="padding: 12px 16px;") #{timestamp || new Date().toISOString()}
        if userId
          tr(style="border-bottom: 1px solid #eee;")
            td(style="padding: 12px 16px; font-weight: 500;") User ID
            td(style="padding: 12px 16px;") #{userId}
        if userEmail
          tr(style="border-bottom: 1px solid #eee;")
            td(style="padding: 12px 16px; font-weight: 500;") User Email
            td(style="padding: 12px 16px;") #{userEmail}
        if url
          tr(style="border-bottom: 1px solid #eee;")
            td(style="padding: 12px 16px; font-weight: 500;") URL
            td(style="padding: 12px 16px; word-break: break-all;") #{url}
        if userAgent
          tr(style="border-bottom: 1px solid #eee;")
            td(style="padding: 12px 16px; font-weight: 500;") User Agent
            td(style="padding: 12px 16px; font-size: 14px; word-break: break-all;") #{userAgent}

  if errorStack
    .card
      .card-header Stack Trace
      pre(style="white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 14px; background-color: #f8f9fa; padding: 16px; border-radius: 8px; overflow: auto; border-left: 4px solid #dc3545;") #{errorStack}

  if details
    .card
      .card-header Additional Details
      table(style="width: 100%; table-layout: fixed; border-collapse: collapse; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);")
        colgroup
          col(style="width: 30%;")
          col(style="width: 70%;")
        thead
          tr(style="background-color: #f8f9fa;")
            th(style="padding: 12px 16px; text-align: left; border-bottom: 2px solid #dee2e6; font-weight: 600;") Property
            th(style="padding: 12px 16px; text-align: left; border-bottom: 2px solid #dee2e6; font-weight: 600;") Value
        tbody
          each value, key in details
            tr(style="border-bottom: 1px solid #eee;")
              td(style="padding: 12px 16px; font-weight: 500;") #{key}
              td(style="padding: 12px 16px; word-break: break-all;") #{value}

  p(style="margin-top: 24px;")
    a.button(href=dashboardUrl || (appUrl + '/admin/errors') style="background-color: #dc3545;") View in Dashboard

  p(style="margin-top: 24px; color: #6c757d;") This is an automated error report. Please investigate and take appropriate action.

  p Regards,
  p.signature The #{appName || 'Hirli'} Team
