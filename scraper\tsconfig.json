{"compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "./dist", "rootDir": "./", "baseUrl": ".", "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist"]}