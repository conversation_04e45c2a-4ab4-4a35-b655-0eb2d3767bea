import { logger } from "../../utils/logger";

export interface SalaryInfo {
  salary?: string;
  salaryMin?: number;
  salaryMax?: number;
  salaryCurrency?: string;
}

/**
 * Extract salary information from job description
 */
export function extractSalaryInfo(text: string): SalaryInfo {
  logger.info(`🔍 Extracting salary information from job description`);

  if (!text) {
    logger.info(`⚠️ No description provided for salary extraction`);
    return {};
  }

  // Clean the text by removing HTML tags
  const cleanText = text
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/\s+/g, ' ')
    .trim();

  const lowerCaseText = cleanText.toLowerCase();
  
  // Initialize result
  const result: SalaryInfo = {};
  
  // Common patterns for salary ranges
  const salaryPatterns = [
    // $X-$Y per hour/year
    /\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*-\s*\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*(?:per|\/|an?)?\s*(hour|hr|year|yr|month|mo|week|wk|day)/gi,
    
    // $X to $Y per hour/year
    /\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*to\s*\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*(?:per|\/|an?)?\s*(hour|hr|year|yr|month|mo|week|wk|day)/gi,
    
    // $X - $Y
    /\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*-\s*\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)/gi,
    
    // $X to $Y
    /\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*to\s*\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)/gi,
    
    // $X+ per hour/year
    /\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\+\s*(?:per|\/|an?)?\s*(hour|hr|year|yr|month|mo|week|wk|day)/gi,
    
    // Up to $X per hour/year
    /up\s*to\s*\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*(?:per|\/|an?)?\s*(hour|hr|year|yr|month|mo|week|wk|day)/gi,
    
    // From $X per hour/year
    /from\s*\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*(?:per|\/|an?)?\s*(hour|hr|year|yr|month|mo|week|wk|day)/gi,
    
    // Starting at $X per hour/year
    /starting\s*(?:at|from)?\s*\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*(?:per|\/|an?)?\s*(hour|hr|year|yr|month|mo|week|wk|day)/gi,
    
    // Salary: $X-$Y
    /salary:?\s*\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*-\s*\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)/gi,
    
    // Compensation: $X-$Y
    /compensation:?\s*\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*-\s*\$(\d{1,3}(?:,\d{3})*(?:\.\d+)?)/gi,
  ];
  
  // Try each pattern
  for (const pattern of salaryPatterns) {
    const matches = [...lowerCaseText.matchAll(pattern)];
    
    if (matches.length > 0) {
      const match = matches[0];
      
      // Check if it's a range pattern
      if (match.length >= 3) {
        // It's a range pattern with min and max
        const min = parseFloat(match[1].replace(/,/g, ''));
        const max = parseFloat(match[2].replace(/,/g, ''));
        
        // Get the time period if available
        let timePeriod = '';
        if (match.length >= 4) {
          timePeriod = match[3]?.toLowerCase() || '';
        }
        
        // Convert to annual salary if needed
        let annualMin = min;
        let annualMax = max;
        
        if (timePeriod.startsWith('hour') || timePeriod === 'hr') {
          // Assuming 40 hours per week, 52 weeks per year
          annualMin = min * 40 * 52;
          annualMax = max * 40 * 52;
        } else if (timePeriod.startsWith('week') || timePeriod === 'wk') {
          // 52 weeks per year
          annualMin = min * 52;
          annualMax = max * 52;
        } else if (timePeriod.startsWith('month') || timePeriod === 'mo') {
          // 12 months per year
          annualMin = min * 12;
          annualMax = max * 12;
        } else if (timePeriod.startsWith('day')) {
          // Assuming 5 days per week, 52 weeks per year
          annualMin = min * 5 * 52;
          annualMax = max * 5 * 52;
        }
        
        result.salaryMin = annualMin;
        result.salaryMax = annualMax;
        result.salaryCurrency = 'USD';
        result.salary = `$${min.toLocaleString()}-$${max.toLocaleString()}${timePeriod ? ' per ' + timePeriod : ''}`;
        
        logger.info(`💰 Extracted salary range: $${min.toLocaleString()}-$${max.toLocaleString()}${timePeriod ? ' per ' + timePeriod : ''}`);
        logger.info(`💰 Converted to annual: $${annualMin.toLocaleString()}-$${annualMax.toLocaleString()}`);
        
        break;
      } else if (match.length >= 2) {
        // It's a single value pattern
        const value = parseFloat(match[1].replace(/,/g, ''));
        
        // Get the time period if available
        let timePeriod = '';
        if (match.length >= 3) {
          timePeriod = match[2]?.toLowerCase() || '';
        }
        
        // Convert to annual salary if needed
        let annualValue = value;
        
        if (timePeriod.startsWith('hour') || timePeriod === 'hr') {
          // Assuming 40 hours per week, 52 weeks per year
          annualValue = value * 40 * 52;
        } else if (timePeriod.startsWith('week') || timePeriod === 'wk') {
          // 52 weeks per year
          annualValue = value * 52;
        } else if (timePeriod.startsWith('month') || timePeriod === 'mo') {
          // 12 months per year
          annualValue = value * 12;
        } else if (timePeriod.startsWith('day')) {
          // Assuming 5 days per week, 52 weeks per year
          annualValue = value * 5 * 52;
        }
        
        // If it's a "up to" or "starting at" pattern
        if (pattern.toString().includes('up\\s*to')) {
          result.salaryMax = annualValue;
          result.salary = `Up to $${value.toLocaleString()}${timePeriod ? ' per ' + timePeriod : ''}`;
          logger.info(`💰 Extracted maximum salary: $${value.toLocaleString()}${timePeriod ? ' per ' + timePeriod : ''}`);
        } else {
          result.salaryMin = annualValue;
          result.salary = `From $${value.toLocaleString()}${timePeriod ? ' per ' + timePeriod : ''}`;
          logger.info(`💰 Extracted minimum salary: $${value.toLocaleString()}${timePeriod ? ' per ' + timePeriod : ''}`);
        }
        
        result.salaryCurrency = 'USD';
        
        logger.info(`💰 Converted to annual: $${annualValue.toLocaleString()}`);
        
        break;
      }
    }
  }
  
  if (!result.salary) {
    logger.info(`⚠️ Could not extract salary information`);
  }
  
  return result;
}
