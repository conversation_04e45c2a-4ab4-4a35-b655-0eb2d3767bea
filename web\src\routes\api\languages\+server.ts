// src/routes/api/languages/+server.ts
import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON>ler } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';

export const GET: RequestHandler = async ({ url }) => {
  try {
    // If prisma is null (during build), return empty array
    if (!prisma) {
      console.log('Prisma client not initialized during build');
      return json([]);
    }

    const search = url.searchParams.get('search') || '';
    const limit = parseInt(url.searchParams.get('limit') || '100');

    // Build the query filters
    const filters: any = {};

    if (search) {
      filters.name = {
        contains: search,
        mode: 'insensitive',
      };
    }

    // Get languages from the database using Prisma models
    const languages = await prisma.language.findMany({
      where: filters,
      orderBy: {
        name: 'asc',
      },
      take: limit,
    });

    // Format the response
    const formattedLanguages = languages.map((language) => ({
      id: language.id,
      name: language.name,
      code: language.code,
    }));

    return json(formattedLanguages);
  } catch (error) {
    console.error('Error fetching languages:', error);
    return json({ error: 'Failed to fetch languages' }, { status: 500 });
  }
};
