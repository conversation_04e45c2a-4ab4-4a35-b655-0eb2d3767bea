<script lang="ts">
  import { goto } from '$app/navigation';
  import { Button } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card';
  import { Badge } from '$lib/components/ui/badge';
  import { ScrollArea } from '$lib/components/ui/scroll-area';
  import { createEventDispatcher } from 'svelte';

  export let matches: any[] = [];
  export let pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasMore: boolean;
  } = {
    page: 1,
    limit: 20,
    totalCount: 0,
    totalPages: 0,
    hasMore: false,
  };
  export let selectedProfileId: string | null = null;

  const dispatch = createEventDispatcher();

  // Format match score as percentage
  const formatScore = (score: number) => {
    return `${Math.round(score * 100)}%`;
  };

  // Handle job selection
  const handleJobSelect = (jobId: string, index: number) => {
    dispatch('select', { jobId, index });
  };

  // Get color class based on match score
  const getScoreColorClass = (score: number) => {
    if (score >= 0.8) return 'bg-green-100 text-green-800';
    if (score >= 0.6) return 'bg-blue-100 text-blue-800';
    if (score >= 0.4) return 'bg-yellow-100 text-yellow-800';
    return 'bg-gray-100 text-gray-800';
  };
</script>

<div>
  <h2 class="mb-4 text-lg font-semibold">Matched Jobs</h2>
  <ScrollArea class="h-[calc(100vh-250px)]">
    <div class="space-y-3 pr-3">
      {#each matches as match, index}
        <Card.Root
          class="cursor-pointer transition-colors hover:border-blue-500"
          onclick={() => handleJobSelect(match.job_listing.id, index)}>
          <Card.Header class="p-4">
            <div class="flex items-start justify-between">
              <Card.Title class="text-base">{match.job_listing.title}</Card.Title>
              <Badge class={getScoreColorClass(match.matchScore)}>
                {formatScore(match.matchScore)}
              </Badge>
            </div>
            <Card.Description>{match.job_listing.company}</Card.Description>
          </Card.Header>
          <Card.Content class="p-4 pt-0">
            <p class="text-sm text-gray-500">{match.job_listing.location}</p>
            {#if match.job_listing.salary}
              <p class="mt-1 text-sm text-gray-500">{match.job_listing.salary}</p>
            {/if}
          </Card.Content>
        </Card.Root>
      {/each}
    </div>
  </ScrollArea>

  <!-- Pagination -->
  {#if pagination.totalPages > 1}
    <div class="mt-4 flex items-center justify-between">
      <Button
        variant="outline"
        disabled={pagination.page === 1}
        onclick={() =>
          goto(`/dashboard/matches?profileId=${selectedProfileId}&page=${pagination.page - 1}`)}>
        Previous
      </Button>
      <span class="text-sm text-gray-500">
        Page {pagination.page} of {pagination.totalPages}
      </span>
      <Button
        variant="outline"
        disabled={!pagination.hasMore}
        onclick={() =>
          goto(`/dashboard/matches?profileId=${selectedProfileId}&page=${pagination.page + 1}`)}>
        Next
      </Button>
    </div>
  {/if}
</div>
