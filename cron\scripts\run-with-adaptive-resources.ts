#!/usr/bin/env node

/**
 * This script runs a specified command with adaptive resource management.
 * It will monitor memory usage and adjust worker counts dynamically.
 * Usage: tsx run-with-adaptive-resources.ts <command>
 * Example: tsx run-with-adaptive-resources.ts jobs/parallelJobScraper.ts
 */

import { spawn, exec } from "child_process";
import os from "os";
import { AdaptiveResourceManager } from "../utils/adaptiveResourceManager";

// Get the command to run from command line arguments
const args = process.argv.slice(2);

if (args.length === 0) {
  console.error("Error: No command specified");
  console.error("Usage: tsx run-with-adaptive-resources.ts <command>");
  console.error(
    "Example: tsx run-with-adaptive-resources.ts jobs/parallelJobScraper.ts"
  );
  process.exit(1);
}

// Set environment variables for worker configuration
const totalMemoryMB = Math.floor(os.totalmem() / 1024 / 1024);
const cpuCount = os.cpus().length;

// Calculate initial worker count based on system resources
// Use 1 worker per 2GB of RAM, but no more than CPU count - 1
const initialWorkerCount = Math.min(
  Math.max(1, Math.floor(totalMemoryMB / 2048)),
  Math.max(1, cpuCount - 1)
);

// Set environment variables for the child process
process.env.SCRAPER_MAX_WORKERS = initialWorkerCount.toString();
process.env.SCRAPER_BATCH_SIZE = Math.max(
  1,
  Math.floor(initialWorkerCount / 2)
).toString();
process.env.SCRAPER_CONCURRENCY = initialWorkerCount.toString();

console.log(`Total system memory: ${totalMemoryMB} MB`);
console.log(`CPU count: ${cpuCount}`);
console.log(`Setting initial worker count to: ${initialWorkerCount}`);
console.log(`Setting batch size to: ${process.env.SCRAPER_BATCH_SIZE}`);
console.log(`Setting concurrency to: ${process.env.SCRAPER_CONCURRENCY}`);

// Determine if we're in production
const isProduction = process.env.NODE_ENV === "production";

// In production, don't try to use --expose-gc since it's not allowed on Render.com
const command = "tsx";
const commandArgs = isProduction ? [...args] : ["--expose-gc", ...args];

console.log(
  `Running: ${command} ${commandArgs.join(" ")} (GC flag: ${!isProduction})`
);

// Create adaptive resource manager for the parent process
const resourceManager = new AdaptiveResourceManager({
  initialWorkerCount,
  minWorkerCount: 1,
  memoryThresholdPercent: 70, // Start adapting at 70% memory usage
  criticalMemoryThresholdPercent: 85, // Critical at 85%
  onWorkerCountChange: (newCount) => {
    console.log(
      `Adaptive resource manager adjusted worker count to: ${newCount}`
    );
    // Update environment variables for child process
    process.env.SCRAPER_MAX_WORKERS = newCount.toString();
    process.env.SCRAPER_BATCH_SIZE = Math.max(
      1,
      Math.floor(newCount / 2)
    ).toString();
    process.env.SCRAPER_CONCURRENCY = newCount.toString();
  },
});

// Memory monitoring interval in milliseconds
const RESOURCE_CHECK_INTERVAL = 30000; // 30 seconds

// Spawn the process
const child = spawn(command, commandArgs, {
  stdio: "inherit",
  shell: true,
  env: process.env,
});

// Function to check for other running jobs
async function checkForOtherJobs(): Promise<boolean> {
  try {
    // Determine the command based on the operating system
    const platform = process.platform;
    let command = "";

    if (platform === "win32") {
      // Windows
      command = 'tasklist /FI "IMAGENAME eq node.exe" /FO CSV';
    } else {
      // Linux/Unix/macOS
      command = "ps -e | grep node | wc -l";
    }

    // Get a list of running processes using the imported exec function
    // We're already importing exec at the top of the file
    const { stdout } = await new Promise<{ stdout: string; stderr: string }>(
      (resolve, reject) => {
        exec(command, (error: Error | null, stdout: string, stderr: string) => {
          if (error) {
            reject(error);
            return;
          }
          resolve({ stdout, stderr });
        });
      }
    );

    // Count the number of node processes (excluding the current one)
    let nodeProcessCount = 0;

    if (platform === "win32") {
      // Windows: Count lines in CSV output (subtract header and current process)
      nodeProcessCount = stdout.split("\n").length - 2;
    } else {
      // Linux/Unix/macOS: The command already gives us a count
      nodeProcessCount = parseInt(stdout.trim(), 10);
      // Subtract 2 for the current process and the grep process itself
      nodeProcessCount = Math.max(0, nodeProcessCount - 2);
    }

    // If there are more than 1 node processes, other jobs might be running
    return nodeProcessCount > 1;
  } catch (error) {
    console.error("Error checking for other jobs:", error);
    return false; // Assume no other jobs on error
  }
}

// Set up resource monitoring
const resourceMonitorInterval = setInterval(async () => {
  try {
    // Check if other jobs are running
    const otherJobsRunning = await checkForOtherJobs();

    if (otherJobsRunning) {
      console.log(
        "⚠️ Detected other jobs running - adjusting resource usage accordingly"
      );
    }

    // Check and adjust resources, passing the otherJobsRunning flag
    resourceManager.checkAndAdjustResources(otherJobsRunning);
  } catch (error) {
    console.error("Error in resource monitoring:", error);
  }
}, RESOURCE_CHECK_INTERVAL);

// Add more frequent garbage collection
const gcInterval = setInterval(() => {
  try {
    if (typeof global !== "undefined" && global.gc) {
      console.log("Running manual garbage collection...");
      global.gc();
    }
  } catch (error) {
    console.error("Error running garbage collection:", error);
  }
}, 60000); // Run GC every minute

// Make sure to clear the interval when the process exits
child.on("close", (code) => {
  clearInterval(resourceMonitorInterval);
  clearInterval(gcInterval);
  console.log(`Process exited with code ${code}`);
  process.exit(code || 0);
});

// Handle process errors
child.on("error", (err) => {
  clearInterval(resourceMonitorInterval);
  console.error("Failed to start process:", err);
  process.exit(1);
});
