// src/lib/server/notification-worker.ts
import { getRedisClient } from '$lib/server/redis';
import {
  sendNotificationToUser,
  sendGlobalNotification,
  sendJobNotification,
  NotificationType,
  NotificationPriority,
} from '$lib/server/notification-triggers/notification-service';
import { logger } from '$lib/server/logger';
import { dev } from '$app/environment';

// Define queue keys
const NOTIFICATION_QUEUE = 'notifications:job-matches';
const NOTIFICATION_GROUP = 'notification-workers';
const NOTIFICATION_CONSUMER = `worker-${Math.random().toString(36).substring(2, 9)}`;

// Flag to track if Redis is available
let redisAvailable = false;

/**
 * Start the notification worker
 */
export async function startNotificationWorker() {
  logger.info('🚀 [Notification Worker] Starting notification worker...');

  try {
    // Get Redis client
    const redis = await getRedisClient();

    if (!redis) {
      logger.warn(
        '⚠️ [Notification Worker] Redis client not available, notifications will be stored in database only'
      );
      redisAvailable = false;
      return;
    }

    // Test Redis connection
    try {
      await redis.ping();
      redisAvailable = true;
    } catch (error) {
      logger.warn(
        '⚠️ [Notification Worker] Redis connection failed, notifications will be stored in database only'
      );
      redisAvailable = false;

      // In development mode, just return without error
      if (dev) {
        return;
      }

      throw error;
    }

    // Create consumer group if it doesn't exist
    try {
      await redis.xgroup('CREATE', NOTIFICATION_QUEUE, NOTIFICATION_GROUP, '0', 'MKSTREAM');
      logger.info(`✅ [Notification Worker] Created consumer group ${NOTIFICATION_GROUP}`);
    } catch (error: any) {
      // Ignore BUSYGROUP error (group already exists)
      if (!error.message.includes('BUSYGROUP')) {
        throw error;
      }
    }

    // Start processing loop
    processNotifications();

    logger.info('✅ [Notification Worker] Notification worker started successfully');
  } catch (error) {
    logger.error('❌ [Notification Worker] Failed to start notification worker:', error);

    // In development mode, don't throw the error
    if (!dev) {
      throw error;
    }
  }
}

/**
 * Process notifications from the Redis queue (event-driven, non-polling)
 */
async function processNotifications() {
  try {
    // If Redis is not available, don't try to process notifications
    if (!redisAvailable) {
      if (dev) return;
      return;
    }

    // Get Redis client
    const redis = await getRedisClient();
    if (!redis) {
      logger.warn('⚠️ [Notification Worker] Redis client not available');
      redisAvailable = false;
      return;
    }

    // Test Redis connection
    try {
      await redis.ping();
    } catch {
      logger.warn('⚠️ [Notification Worker] Redis connection failed');
      redisAvailable = false;
      return;
    }

    // Set up a subscriber for notification events
    const subscriber = redis.duplicate();

    // Subscribe to notification events channel
    await subscriber.subscribe('notifications:events');
    logger.info('✅ [Notification Worker] Subscribed to notifications:events channel');

    // Process any existing messages in the stream first
    await processNotificationMessages();

    // Listen for notification events
    subscriber.on('message', async (channel, message) => {
      if (channel === 'notifications:events') {
        logger.info(`📬 [Notification Worker] Received notification event: ${message}`);
        await processNotificationMessages();
      }
    });

    logger.info('✅ [Notification Worker] Notification worker is now listening for events');
  } catch (error) {
    logger.error('❌ [Notification Worker] Error setting up notification worker:', error);
    redisAvailable = false;
  }
}

/**
 * Process notification messages from the Redis stream
 */
async function processNotificationMessages() {
  try {
    // Get Redis client
    const redis = await getRedisClient();
    if (!redis) return;

    // Read from the stream (one-time read, not continuous polling)
    const streams = await redis.xreadgroup(
      'GROUP',
      NOTIFICATION_GROUP,
      NOTIFICATION_CONSUMER,
      'COUNT',
      10, // Process more messages at once for efficiency
      'STREAMS',
      NOTIFICATION_QUEUE,
      '>'
    );

    // If no messages, just return
    if (!streams || streams.length === 0) {
      return;
    }

    // Process each message
    if (streams && streams[0] && Array.isArray(streams[0])) {
      const [, messages] = streams[0] as [string, Array<[string, string[]]>];

      logger.info(`📬 [Notification Worker] Processing ${messages.length} notifications`);

      if (Array.isArray(messages)) {
        for (const [id, fields] of messages) {
          try {
            // Get the notification data
            const notificationStr = fields[1];
            const notification = JSON.parse(notificationStr);

            // Process the notification based on type
            if (notification.type === 'job') {
              // Send job notification
              await sendJobNotification(notification.userId, {
                title: notification.title,
                message: notification.message,
                url: notification.url,
                type: NotificationType.JOB,
                priority:
                  notification.priority === 'high'
                    ? NotificationPriority.HIGH
                    : NotificationPriority.MEDIUM,
                metadata: notification.metadata,
              });
            } else if (notification.global) {
              // Send global notification
              await sendGlobalNotification({
                title: notification.title,
                message: notification.message,
                url: notification.url,
                type: notification.type || NotificationType.SYSTEM,
                priority: notification.priority || NotificationPriority.MEDIUM,
                metadata: notification.metadata,
              });
            } else {
              // Send user notification
              await sendNotificationToUser(notification.userId, {
                title: notification.title,
                message: notification.message,
                url: notification.url,
                type: notification.type || NotificationType.INFO,
                priority: notification.priority || NotificationPriority.MEDIUM,
                metadata: notification.metadata,
              });
            }

            // Acknowledge the message
            await redis.xack(NOTIFICATION_QUEUE, NOTIFICATION_GROUP, id);
            logger.info(`✅ [Notification Worker] Processed notification: ${id}`);
          } catch (error) {
            logger.error(`❌ [Notification Worker] Error processing notification: ${id}`, error);
            // Still acknowledge the message to avoid reprocessing
            await redis.xack(NOTIFICATION_QUEUE, NOTIFICATION_GROUP, id);
          }
        }
      }
    }
  } catch (error) {
    logger.error('❌ [Notification Worker] Error processing notification messages:', error);
  }
}
