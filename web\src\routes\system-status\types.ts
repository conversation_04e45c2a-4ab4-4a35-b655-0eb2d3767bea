// Status types
export type StatusType = 'operational' | 'degraded' | 'outage' | 'maintenance' | 'unknown';

// Service status interface
export interface ServiceStatus {
  name: string;
  status: StatusType;
  description?: string;
  lastUpdated?: Date;
}

// Status tag types
export type StatusTagType =
  | 'investigating'
  | 'identified'
  | 'monitoring'
  | 'resolved'
  | 'scheduled'
  | 'in-progress'
  | 'completed'
  | 'cancelled';

// Severity level types
export type SeverityLevelType = 'minor' | 'major' | 'critical' | 'maintenance' | 'info';

// Incident interface
export interface Incident {
  id: string;
  title: string;
  status: StatusTagType;
  severity?: SeverityLevelType;
  date: Date;
  description: string;
  startTime?: string | Date;
  endTime?: string | Date;
  affectedServices?: string[];
  updates?: { date: Date; message: string; status?: StatusTagType }[] | MaintenanceUpdate[];
}

// System metrics interface
export interface SystemMetrics {
  uptime: number;
  emailDeliveryRate: number;
  apiResponseTime: number;
  jobSuccessRate: number;
}

// Maintenance event update interface
export interface MaintenanceUpdate {
  timestamp: string | Date;
  message: string;
  status?: StatusTagType;
}

// Maintenance event interface
export interface MaintenanceEvent {
  id: string;
  title: string;
  description: string;
  status: StatusTagType;
  severity: SeverityLevelType;
  startTime: string | Date;
  endTime: string | Date;
  affectedServices?: string[];
  updates?: MaintenanceUpdate[];
  progress?: number; // For status bar visualization (0-100)
}

// Page data interface
export interface SystemStatusPageData {
  services: ServiceStatus[];
  serviceHistory: Record<string, any>;
  serviceHealth: Record<string, any>;
  maintenance: {
    upcoming: MaintenanceEvent[];
    inProgress: MaintenanceEvent[];
    past: MaintenanceEvent[];
  };
  uptime: number;
  email: {
    deliveryRate: number;
    queueSize: number;
    processingCount: number;
  };
  jobs: {
    successRate: number;
    totalProcessed: number;
    failureRate: number;
  };
  apiResponseTime: number;
  lastUpdated: string;
}
