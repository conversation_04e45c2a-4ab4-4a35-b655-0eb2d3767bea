/**
 * Worker Heartbeat Utility
 *
 * This module provides utilities for workers to report their health status
 * to the central health registry. Workers should call the reportHeartbeat
 * function periodically to update their status.
 */

import { redis } from "../redis.js";
import os from "node:os";

// Redis keys
const WORKER_HEALTH_KEY = "worker:health";
const WORKER_METRICS_KEY = "worker:metrics";

// Worker status types
export enum WorkerStatus {
  HEALTHY = "healthy",
  DEGRADED = "degraded",
  UNHEALTHY = "unhealthy",
  UNKNOWN = "unknown",
}

// Worker metrics interface
export interface WorkerMetrics {
  cpu: number; // CPU usage percentage
  memory: number; // Memory usage percentage
  queueSize: number; // Number of items in queue
  processingCount: number; // Number of items being processed
  responseTime: number; // Average response time in ms
  errorRate: number; // Error rate percentage
  successRate: number; // Success rate percentage
  capacity: number; // Available capacity percentage (0-100)
}

// Metrics collection state
let cpuUsage = process.cpuUsage();
let lastCpuUsageTime = process.hrtime.bigint();
let lastReportTime = Date.now();

// Performance metrics
const performanceMetrics = {
  requestCount: 0,
  errorCount: 0,
  totalResponseTime: 0,
  queueSize: 0,
  processingCount: 0,
};

/**
 * Report worker heartbeat to the health registry
 * @param workerType The type of worker (e.g., 'resume-parsing')
 * @param queueSize Current queue size (optional)
 * @param processingCount Number of items being processed (optional)
 */
export async function reportHeartbeat(
  workerType: string,
  queueSize?: number,
  processingCount?: number
): Promise<boolean> {
  try {
    // Update metrics if provided
    if (queueSize !== undefined) {
      performanceMetrics.queueSize = queueSize;
    }

    if (processingCount !== undefined) {
      performanceMetrics.processingCount = processingCount;
    }

    // Get system metrics
    const metrics = await collectMetrics();

    // Determine worker status based on metrics
    let status = WorkerStatus.HEALTHY;

    if (metrics.cpu > 90 || metrics.memory > 90 || metrics.errorRate > 20) {
      status = WorkerStatus.UNHEALTHY;
    } else if (
      metrics.cpu > 70 ||
      metrics.memory > 70 ||
      metrics.errorRate > 5
    ) {
      status = WorkerStatus.DEGRADED;
    }

    // Update health data
    const healthData = {
      status,
      healthy: status === WorkerStatus.HEALTHY,
      lastHeartbeat: new Date().toISOString(),
    };

    // Store health data in Redis
    await redis.hset(WORKER_HEALTH_KEY, workerType, JSON.stringify(healthData));

    // Store metrics data in Redis
    await redis.hset(WORKER_METRICS_KEY, workerType, JSON.stringify(metrics));

    // Reset request counters every 30 minutes
    const now = Date.now();
    if (now - lastReportTime > 1800000) {
      performanceMetrics.requestCount = 0;
      performanceMetrics.errorCount = 0;
      performanceMetrics.totalResponseTime = 0;
      lastReportTime = now;
    }

    return true;
  } catch (error) {
    console.error(`Error reporting heartbeat for worker ${workerType}:`, error);
    return false;
  }
}

/**
 * Record a request processed by the worker
 * @param responseTime Response time in milliseconds
 * @param isError Whether the request resulted in an error
 */
export function recordRequest(
  responseTime: number,
  isError: boolean = false
): void {
  performanceMetrics.requestCount++;
  performanceMetrics.totalResponseTime += responseTime;

  if (isError) {
    performanceMetrics.errorCount++;
  }
}

/**
 * Collect system and application metrics
 */
async function collectMetrics(): Promise<WorkerMetrics> {
  // Calculate CPU usage
  const currentCpuUsage = process.cpuUsage();
  const currentTime = process.hrtime.bigint();

  const userDiff = currentCpuUsage.user - cpuUsage.user;
  const sysDiff = currentCpuUsage.system - cpuUsage.system;
  const timeDiff = Number(currentTime - lastCpuUsageTime) / 1000000; // Convert to ms

  // Update for next calculation
  cpuUsage = currentCpuUsage;
  lastCpuUsageTime = currentTime;

  // Calculate CPU percentage (process-specific)
  const cpuPercent = Math.min(
    100,
    ((userDiff + sysDiff) / 1000 / timeDiff) * 100
  );

  // Get system memory usage
  const totalMem = os.totalmem();
  const freeMem = os.freemem();
  const memoryPercent = Math.min(100, ((totalMem - freeMem) / totalMem) * 100);

  // Calculate process memory usage
  const memoryUsage = process.memoryUsage();
  const processMemPercent = Math.min(100, (memoryUsage.rss / totalMem) * 100);

  // Calculate error rate
  const errorRate =
    performanceMetrics.requestCount > 0
      ? (performanceMetrics.errorCount / performanceMetrics.requestCount) * 100
      : 0;

  // Calculate success rate
  const successRate =
    performanceMetrics.requestCount > 0
      ? ((performanceMetrics.requestCount - performanceMetrics.errorCount) /
          performanceMetrics.requestCount) *
        100
      : 100;

  // Calculate average response time
  const avgResponseTime =
    performanceMetrics.requestCount > 0
      ? performanceMetrics.totalResponseTime / performanceMetrics.requestCount
      : 0;

  // Calculate available capacity based on CPU, memory, and queue size
  const cpuCapacity = Math.max(0, 100 - cpuPercent);
  const memCapacity = Math.max(0, 100 - processMemPercent);

  // Capacity is the minimum of CPU and memory capacity
  const capacity = Math.min(cpuCapacity, memCapacity);

  return {
    cpu: Math.round(cpuPercent * 10) / 10,
    memory: Math.round(processMemPercent * 10) / 10,
    queueSize: performanceMetrics.queueSize,
    processingCount: performanceMetrics.processingCount,
    responseTime: Math.round(avgResponseTime * 10) / 10,
    errorRate: Math.round(errorRate * 10) / 10,
    successRate: Math.round(successRate * 10) / 10,
    capacity: Math.round(capacity * 10) / 10,
  };
}

/**
 * Start periodic heartbeat reporting
 * @param workerType The type of worker
 * @param interval Heartbeat interval in milliseconds (default: 1800000)
 */
export function startHeartbeatReporting(
  workerType: string,
  interval: number = 1800000
): NodeJS.Timeout {
  console.log(
    `[${workerType}] Starting heartbeat reporting with interval ${interval}ms`
  );

  // Report initial heartbeat
  reportHeartbeat(workerType).catch((error) => {
    console.error(`[${workerType}] Error reporting initial heartbeat:`, error);
  });

  // Set up interval for periodic reporting
  return setInterval(() => {
    reportHeartbeat(workerType).catch((error) => {
      console.error(`[${workerType}] Error reporting heartbeat:`, error);
    });
  }, interval);
}
