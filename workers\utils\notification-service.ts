// File: workers/utils/notification-service.ts
import { prisma } from "./prisma.js";
import { redis } from "../redis.js";

// Notification types
export enum NotificationType {
  INFO = "info",
  SUCCESS = "success",
  WARNING = "warning",
  ERROR = "error",
  SYSTEM = "system",
  JOB = "job",
  APPLICATION = "application",
  RESUME = "resume",
}

// Notification priorities
export enum NotificationPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
}

// Notification data interface
export interface NotificationData {
  userId: string;
  title: string;
  message: string;
  url?: string;
  type?: NotificationType;
  priority?: NotificationPriority;
  data?: Record<string, any>;
  expiresAt?: Date;
}

/**
 * Create a notification for a user
 * @param data Notification data
 * @returns Promise<boolean> Success status
 */
export async function createNotification(data: NotificationData): Promise<boolean> {
  try {
    // Create notification data
    const notificationData = {
      userId: data.userId,
      title: data.title,
      message: data.message,
      url: data.url,
      type: data.type || NotificationType.INFO,
      priority: data.priority || NotificationPriority.MEDIUM,
      metadata: data.data ? JSON.stringify(data.data) : null,
      expiresAt: data.expiresAt,
      read: false,
    };

    // Store notification in database
    const notification = await prisma.notification.create({
      data: notificationData,
    });
    console.log(`[Notification] Created notification in database with ID ${notification.id}`);

    // Send notification via Redis
    try {
      // Generate a unique request ID for this notification
      const requestId = `notification:${notification.id}:${Date.now()}`;

      // Publish to user-specific channel with the request ID
      await redis.publish(
        `user:${data.userId}:notifications`,
        JSON.stringify({
          id: notification.id,
          title: notification.title,
          message: notification.message,
          url: notification.url,
          type: notification.type,
          timestamp: notification.createdAt.toISOString(),
          requestId: requestId,
        })
      );

      console.log(`[Notification] Published notification to Redis for user ${data.userId}`);
      return true;
    } catch (redisError) {
      console.error("[Notification] Error publishing to Redis:", redisError);
      // We still created the notification in the database, so return true
      return true;
    }
  } catch (error) {
    console.error("[Notification] Error creating notification:", error);
    return false;
  }
}

/**
 * Create a resume-related notification
 * @param userId User ID
 * @param data Notification data
 * @returns Promise<boolean> Success status
 */
export async function createResumeNotification(
  userId: string,
  data: Omit<NotificationData, "userId" | "type">
): Promise<boolean> {
  return createNotification({
    userId,
    ...data,
    type: NotificationType.RESUME,
  });
}

/**
 * Send a notification for a completed worker process
 * @param userId User ID
 * @param processId Worker process ID
 * @param processType Worker process type
 * @param title Notification title
 * @param message Notification message
 * @param url Optional URL to include in the notification
 * @returns Promise<boolean> Success status
 */
export async function sendWorkerProcessCompletedNotification(
  userId: string,
  processId: string,
  processType: string,
  title: string,
  message: string,
  url?: string
): Promise<boolean> {
  return createNotification({
    userId,
    title,
    message,
    url,
    type: NotificationType.SUCCESS,
    priority: NotificationPriority.MEDIUM,
    data: {
      processId,
      processType,
      status: "completed",
    },
  });
}

/**
 * Send a notification for a failed worker process
 * @param userId User ID
 * @param processId Worker process ID
 * @param processType Worker process type
 * @param title Notification title
 * @param message Notification message
 * @param error Error message
 * @returns Promise<boolean> Success status
 */
export async function sendWorkerProcessFailedNotification(
  userId: string,
  processId: string,
  processType: string,
  title: string,
  message: string,
  error: string
): Promise<boolean> {
  return createNotification({
    userId,
    title,
    message,
    type: NotificationType.ERROR,
    priority: NotificationPriority.HIGH,
    data: {
      processId,
      processType,
      status: "failed",
      error,
    },
  });
}
