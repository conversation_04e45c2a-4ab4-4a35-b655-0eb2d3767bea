import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth.js';

export async function POST({ request, params, cookies }) {
  try {
    const token = cookies.get('auth_token');
    const user = token && verifySessionToken(token);

    if (!user) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;
    const { reason } = await request.json();

    if (!reason || reason.trim() === '') {
      return json({ error: 'Reason is required' }, { status: 400 });
    }

    // Get the job details
    const job = await prisma.job_listing.findUnique({
      where: { id },
    });

    if (!job) {
      return json({ error: 'Job not found' }, { status: 404 });
    }

    // In a real implementation, you would store the report in a database
    // For now, we'll just log it and return success
    console.log(`Job reported: ${id} by user ${user.id} - Reason: ${reason}`);

    // You could create a JobReport model in your Prisma schema and use it like this:
    /*
    const report = await prisma.jobReport.create({
      data: {
        jobId: id,
        userId: user.id,
        reason,
        status: 'Pending',
      },
    });
    */

    return json({
      success: true,
      message: 'Job reported successfully',
    });
  } catch (error) {
    console.error('Error reporting job:', error);
    return json({ error: 'Failed to report job' }, { status: 500 });
  }
}
