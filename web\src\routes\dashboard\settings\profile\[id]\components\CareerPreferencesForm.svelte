<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { Edit } from 'lucide-svelte';
  import type { ProfileData } from '$lib/types/profile';
  import EditCareerPreferenceModal from './EditCareerPreferenceModal.svelte';

  // Props
  const { profileData, onSave } = $props<{
    profileData: ProfileData;
    onSave: (data: Partial<ProfileData>) => Promise<boolean>;
  }>();

  // Form data
  let interestedRoles = $state<string[]>(profileData.jobPreferences?.interestedRoles || []);
  let preferredLocations = $state<string[]>(profileData.jobPreferences?.preferredLocations || []);
  let remotePreference = $state(profileData.jobPreferences?.remotePreference || '');
  let desiredIndustries = $state<string[]>(profileData.jobPreferences?.desiredIndustries || []);

  // Modal state
  let modalOpen = $state(false);
  let currentSection = $state<'roles' | 'locations' | 'remote' | 'industries'>('roles');
  let currentTitle = $state('');

  // Options from API
  let occupationOptions = $state<{ value: string; label: string }[]>([]);
  let industryOptions = $state<{ value: string; label: string }[]>([]);

  // Remote preference options
  const remoteOptions = [
    { value: 'remote-only', label: 'Remote Only' },
    { value: 'hybrid', label: 'Hybrid' },
    { value: 'on-site', label: 'On-site' },
    { value: 'flexible', label: 'Flexible' },
  ];

  // Fetch occupations from API
  async function fetchOccupations() {
    try {
      const response = await fetch('/api/occupations');
      if (response.ok) {
        const data = await response.json();
        occupationOptions = data.map((occupation: any) => ({
          value: occupation.title,
          label: occupation.title,
        }));
      }
    } catch (error) {
      console.error('Error fetching occupations:', error);
    }
  }

  // Fetch industries (using skills API with type=industry filter)
  async function fetchIndustries() {
    try {
      const response = await fetch('/api/skills?type=industry');
      if (response.ok) {
        const data = await response.json();
        industryOptions = data.map((industry: any) => ({
          value: industry.name,
          label: industry.name,
        }));
      } else {
        // Fallback to static list if API fails
        industryOptions = [
          { value: 'technology', label: 'Technology' },
          { value: 'healthcare', label: 'Healthcare' },
          { value: 'finance', label: 'Finance' },
          { value: 'education', label: 'Education' },
          { value: 'retail', label: 'Retail' },
          { value: 'manufacturing', label: 'Manufacturing' },
          { value: 'government', label: 'Government' },
          { value: 'nonprofit', label: 'Non-Profit' },
        ];
      }
    } catch (error) {
      console.error('Error fetching industries:', error);
      // Fallback to static list
      industryOptions = [
        { value: 'technology', label: 'Technology' },
        { value: 'healthcare', label: 'Healthcare' },
        { value: 'finance', label: 'Finance' },
        { value: 'education', label: 'Education' },
        { value: 'retail', label: 'Retail' },
        { value: 'manufacturing', label: 'Manufacturing' },
        { value: 'government', label: 'Government' },
        { value: 'nonprofit', label: 'Non-Profit' },
      ];
    }
  }

  // Fetch data on component initialization
  fetchOccupations();
  fetchIndustries();

  // Open edit modal
  function openEditModal(section: 'roles' | 'locations' | 'remote' | 'industries', title: string) {
    currentSection = section;
    currentTitle = title;
    modalOpen = true;
  }

  // Handle modal save
  async function handleModalSave(data: string[] | string): Promise<boolean> {
    try {
      if (currentSection === 'roles') {
        interestedRoles = data as string[];
      } else if (currentSection === 'locations') {
        preferredLocations = data as string[];
      } else if (currentSection === 'remote') {
        remotePreference = data as string;
      } else if (currentSection === 'industries') {
        desiredIndustries = data as string[];
      }

      // Prepare data for saving
      const updatedData: Partial<ProfileData> = {
        jobPreferences: {
          ...profileData.jobPreferences,
          interestedRoles,
          preferredLocations,
          remotePreference,
          desiredIndustries,
        },
      };

      // Save data
      const success = await onSave(updatedData);
      return success;
    } catch (error) {
      console.error(`Error saving ${currentSection}:`, error);
      return false;
    }
  }
</script>

<div class="space-y-6">
  <!-- Interested Roles -->
  <div class="space-y-2">
    <div class="flex items-center justify-between">
      <Label>Desired Roles</Label>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onclick={() => openEditModal('roles', 'Desired Roles')}
        class="flex items-center gap-1 text-xs">
        <Edit class="h-3.5 w-3.5" />
        Edit
      </Button>
    </div>

    {#if interestedRoles.length > 0}
      <div class="mt-2 flex flex-wrap gap-2">
        {#each interestedRoles as role}
          <Badge variant="secondary" class="flex items-center gap-1">
            {role}
          </Badge>
        {/each}
      </div>
    {:else}
      <p class="text-muted-foreground text-sm">No roles added yet</p>
    {/if}
  </div>

  <!-- Preferred Locations -->
  <div class="space-y-2">
    <div class="flex items-center justify-between">
      <Label>Preferred Locations</Label>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onclick={() => openEditModal('locations', 'Preferred Locations')}
        class="flex items-center gap-1 text-xs">
        <Edit class="h-3.5 w-3.5" />
        Edit
      </Button>
    </div>

    {#if preferredLocations.length > 0}
      <div class="mt-2 flex flex-wrap gap-2">
        {#each preferredLocations as location}
          <Badge variant="secondary" class="flex items-center gap-1">
            {location}
          </Badge>
        {/each}
      </div>
    {:else}
      <p class="text-muted-foreground text-sm">No locations added yet</p>
    {/if}
  </div>

  <!-- Remote Preference -->
  <div class="space-y-2">
    <div class="flex items-center justify-between">
      <Label for="remotePreference">Remote Preference</Label>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onclick={() => openEditModal('remote', 'Remote Preference')}
        class="flex items-center gap-1 text-xs">
        <Edit class="h-3.5 w-3.5" />
        Edit
      </Button>
    </div>

    {#if remotePreference}
      <div class="mt-2">
        <Badge variant="secondary">
          {remoteOptions.find((opt) => opt.value === remotePreference)?.label || remotePreference}
        </Badge>
      </div>
    {:else}
      <p class="text-muted-foreground text-sm">No preference set</p>
    {/if}
  </div>

  <!-- Desired Industries -->
  <div class="space-y-2">
    <div class="flex items-center justify-between">
      <Label>Desired Industries</Label>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onclick={() => openEditModal('industries', 'Desired Industries')}
        class="flex items-center gap-1 text-xs">
        <Edit class="h-3.5 w-3.5" />
        Edit
      </Button>
    </div>

    {#if desiredIndustries.length > 0}
      <div class="mt-2 flex flex-wrap gap-2">
        {#each desiredIndustries as industry}
          <Badge variant="secondary" class="flex items-center gap-1">
            {industry}
          </Badge>
        {/each}
      </div>
    {:else}
      <p class="text-muted-foreground text-sm">No industries added yet</p>
    {/if}
  </div>
</div>

<!-- Edit Modal -->
<EditCareerPreferenceModal
  open={modalOpen}
  sectionType={currentSection}
  sectionTitle={currentTitle}
  sectionData={currentSection === 'roles'
    ? interestedRoles
    : currentSection === 'locations'
      ? preferredLocations
      : currentSection === 'remote'
        ? remotePreference
        : desiredIndustries}
  options={currentSection === 'remote'
    ? remoteOptions
    : currentSection === 'industries'
      ? industryOptions
      : currentSection === 'roles'
        ? occupationOptions
        : undefined}
  onClose={() => (modalOpen = false)}
  onSave={handleModalSave} />
