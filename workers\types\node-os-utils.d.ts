declare module 'node-os-utils' {
  interface MemInfo {
    totalMemMb: number;
    usedMemMb: number;
    freeMemMb: number;
    freeMemPercentage: number;
    usedMemPercentage: number;
  }

  interface CpuInfo {
    manufacturer: string;
    brand: string;
    vendor: string;
    family: string;
    model: string;
    stepping: string;
    revision: string;
    voltage: string;
    speed: string;
    speedMin: string;
    speedMax: string;
    governor: string;
    cores: number;
    physicalCores: number;
    processors: number;
    socket: string;
    cache: {
      l1d: string;
      l1i: string;
      l2: string;
      l3: string;
    };
  }

  interface OsInfo {
    platform: string;
    distro: string;
    release: string;
    codename: string;
    kernel: string;
    arch: string;
    hostname: string;
    logofile: string;
  }

  interface NetStatInfo {
    interface: string;
    inputBytes: number;
    outputBytes: number;
  }

  interface NetCheckInfo {
    ms: number;
  }

  interface UserInfo {
    uid: number;
    gid: number;
    username: string;
    homedir: string;
    shell: string;
  }

  interface Mem {
    info(): Promise<MemInfo>;
    free(): Promise<{
      totalMemMb: number;
      freeMemMb: number;
      freeMemPercentage: number;
    }>;
    used(): Promise<{
      totalMemMb: number;
      usedMemMb: number;
      usedMemPercentage: number;
    }>;
    totalMem(): number;
  }

  interface CPU {
    usage(interval?: number): Promise<number>;
    free(interval?: number): Promise<number>;
    count(): number;
    model(): string;
    loadavg(): number[];
    loadavgTime(time: number): number;
    info(): Promise<CpuInfo>;
  }

  interface Drive {
    info(drive?: string): Promise<{
      totalGb: number;
      usedGb: number;
      freeGb: number;
      usedPercentage: number;
      freePercentage: number;
    }>;
    free(drive?: string): Promise<{
      totalGb: number;
      freeGb: number;
      freePercentage: number;
    }>;
    used(drive?: string): Promise<{
      totalGb: number;
      usedGb: number;
      usedPercentage: number;
    }>;
  }

  interface OS {
    oos(): string;
    platform(): string;
    uptime(): string;
    ip(): string;
    hostname(): string;
    type(): string;
    arch(): string;
  }

  interface NetStat {
    stats(): Promise<NetStatInfo[]>;
    inOut(interface?: string): Promise<{
      total: {
        inputMb: number;
        outputMb: number;
      };
      ms: number;
    }>;
  }

  interface NetCheck {
    info(): Promise<NetCheckInfo>;
  }

  interface Users {
    openedCount(): Promise<number>;
    list(): Promise<UserInfo[]>;
  }

  interface Proc {
    totalProcesses(): Promise<number>;
    zombieProcesses(): Promise<number>;
  }

  const cpu: CPU;
  const drive: Drive;
  const mem: Mem;
  const netstat: NetStat;
  const os: OS;
  const proc: Proc;
  const users: Users;
  const netCheck: NetCheck;

  export { cpu, drive, mem, netstat, os, proc, users, netCheck };
}
