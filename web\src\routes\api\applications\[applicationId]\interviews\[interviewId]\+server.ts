/**
 * API endpoints for managing a specific interview stage
 */

import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';

/**
 * Get a specific interview stage
 * GET /api/applications/:applicationId/interviews/:interviewId
 */
export async function GET({ params, locals }) {
  console.log('GET interview stage:', params.interviewId);
  console.log('User in locals:', locals.user ? `ID: ${locals.user.id}` : 'Not authenticated');

  // In development mode, we'll allow access without authentication
  const isDev = process.env.NODE_ENV === 'development';

  // Ensure user is authenticated (except in dev mode)
  if (!locals.user && !isDev) {
    console.log('Unauthorized access attempt - no user in locals');
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { applicationId, interviewId } = params;

  try {
    // In dev mode, we'll skip the user verification
    if (!isDev && locals.user) {
      // Verify the application belongs to the user
      const application = await prisma.application.findUnique({
        where: {
          id: applicationId,
          userId: locals.user.id,
        },
      });

      if (!application) {
        console.log('Application not found or does not belong to user');
        return json({ error: 'Application not found' }, { status: 404 });
      }
    }

    // Get the interview stage
    const interviewStage = await prisma.interviewStage.findUnique({
      where: {
        id: interviewId,
        applicationId,
      },
      include: {
        questions: true,
      },
    });

    if (!interviewStage) {
      console.log('Interview stage not found');
      return json({ error: 'Interview stage not found' }, { status: 404 });
    }

    console.log('Found interview stage:', interviewStage.id);
    return json({ interviewStage });
  } catch (error) {
    console.error('Error fetching interview stage:', error);
    return json({ error: 'Failed to fetch interview stage' }, { status: 500 });
  }
}

/**
 * Update an interview stage
 * PATCH /api/applications/:applicationId/interviews/:interviewId
 */
export async function PATCH({ request, params, locals }) {
  console.log('PATCH interview stage:', params.interviewId);
  console.log('User in locals:', locals.user ? `ID: ${locals.user.id}` : 'Not authenticated');

  // In development mode, we'll allow access without authentication
  const isDev = process.env.NODE_ENV === 'development';

  // Ensure user is authenticated (except in dev mode)
  if (!locals.user && !isDev) {
    console.log('Unauthorized access attempt - no user in locals');
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { applicationId, interviewId } = params;

  try {
    // In dev mode, we'll skip the user verification
    if (!isDev && locals.user) {
      // Verify the application belongs to the user
      const application = await prisma.application.findUnique({
        where: {
          id: applicationId,
          userId: locals.user.id,
        },
      });

      if (!application) {
        console.log('Application not found or does not belong to user');
        return json({ error: 'Application not found' }, { status: 404 });
      }
    }

    // Verify the interview stage exists
    const existingStage = await prisma.interviewStage.findUnique({
      where: {
        id: interviewId,
        applicationId,
      },
    });

    if (!existingStage) {
      console.log('Interview stage not found');
      return json({ error: 'Interview stage not found' }, { status: 404 });
    }

    // Parse the request body
    const body = await request.json();
    console.log('Update data:', body);

    // Update the interview stage
    const updatedStage = await prisma.interviewStage.update({
      where: {
        id: interviewId,
      },
      data: {
        stageName: body.stageName !== undefined ? body.stageName : undefined,
        stageDate: body.stageDate ? new Date(body.stageDate) : undefined,
        outcome: body.outcome !== undefined ? body.outcome : undefined,
        feedback: body.feedback !== undefined ? body.feedback : undefined,
        interviewers: body.interviewers !== undefined ? body.interviewers : undefined,
        duration: body.duration !== undefined ? (body.duration ? parseInt(body.duration, 10) : null) : undefined,
        notes: body.notes !== undefined ? body.notes : undefined,
        nextAction: body.nextAction !== undefined ? body.nextAction : undefined,
      },
    });

    console.log('Interview stage updated successfully:', updatedStage.id);
    return json({ interviewStage: updatedStage }, { status: 200 });
  } catch (error) {
    console.error('Error updating interview stage:', error);
    // Return more detailed error information in development mode
    if (isDev) {
      return json(
        {
          error: 'Failed to update interview stage',
          details: error.message,
          code: error.code,
        },
        { status: 500 }
      );
    } else {
      return json({ error: 'Failed to update interview stage' }, { status: 500 });
    }
  }
}

/**
 * Delete an interview stage
 * DELETE /api/applications/:applicationId/interviews/:interviewId
 */
export async function DELETE({ params, locals }) {
  console.log('DELETE interview stage:', params.interviewId);
  console.log('User in locals:', locals.user ? `ID: ${locals.user.id}` : 'Not authenticated');

  // In development mode, we'll allow access without authentication
  const isDev = process.env.NODE_ENV === 'development';

  // Ensure user is authenticated (except in dev mode)
  if (!locals.user && !isDev) {
    console.log('Unauthorized access attempt - no user in locals');
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { applicationId, interviewId } = params;

  try {
    // In dev mode, we'll skip the user verification
    if (!isDev && locals.user) {
      // Verify the application belongs to the user
      const application = await prisma.application.findUnique({
        where: {
          id: applicationId,
          userId: locals.user.id,
        },
      });

      if (!application) {
        console.log('Application not found or does not belong to user');
        return json({ error: 'Application not found' }, { status: 404 });
      }
    }

    // Verify the interview stage exists
    const existingStage = await prisma.interviewStage.findUnique({
      where: {
        id: interviewId,
        applicationId,
      },
    });

    if (!existingStage) {
      console.log('Interview stage not found');
      return json({ error: 'Interview stage not found' }, { status: 404 });
    }

    // Delete the interview stage (this will cascade delete questions)
    await prisma.interviewStage.delete({
      where: {
        id: interviewId,
      },
    });

    console.log('Interview stage deleted successfully');
    return json({ success: true }, { status: 200 });
  } catch (error) {
    console.error('Error deleting interview stage:', error);
    return json({ error: 'Failed to delete interview stage' }, { status: 500 });
  }
}
