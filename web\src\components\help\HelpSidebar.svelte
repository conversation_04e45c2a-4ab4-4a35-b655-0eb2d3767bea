<!-- src/components/help/HelpSidebar.svelte -->
<script lang="ts">
  // We don't need the navigating import anymore
  import * as Accordion from '$lib/components/ui/accordion';
  import * as ScrollArea from '$lib/components/ui/scroll-area';
  import { cn } from '$lib/utils';
  import * as lucideIcons from 'lucide-svelte';
  import { onMount } from 'svelte';

  // Props
  let { categories = [], className = '' } = $props<{
    categories: Array<{
      id: string;
      name: string;
      slug: string;
      icon?: string;
      children?: Array<{
        id: string;
        name: string;
        slug: string;
      }>;
      articles?: Array<{
        id: string;
        title: string;
        slug: string;
      }>;
    }>;
    className?: string;
  }>();

  // State
  let expandedCategories = $state<string[]>([]);
  let currentPath = $state('');

  // Update current path when the component mounts
  $effect(() => {
    currentPath = window.location.pathname;
  });

  // Expand the category that contains the current article
  onMount(() => {
    currentPath = window.location.pathname;
    const currentCategorySlug = currentPath.match(/\/help\/category\/([^/]+)/)?.[1];
    const currentArticleSlug = currentPath.match(/\/help\/([^/]+)$/)?.[1];

    if (currentCategorySlug) {
      const category = categories.find((c: any) => c.slug === currentCategorySlug);
      if (category) {
        expandedCategories = [category.id];
      }
    } else if (currentArticleSlug) {
      // Find which category contains the current article
      for (const category of categories) {
        if (category.articles?.some((a: any) => a.slug === currentArticleSlug)) {
          expandedCategories = [category.id];
          break;
        }
      }
    }
  });

  // We don't need the getIconComponent function anymore since we're using direct component references

  // Check if a link is active
  function isActive(path: string) {
    return currentPath === path;
  }
</script>

<div class={className}>
  <ScrollArea.Root class="h-[calc(100vh-10rem)]">
    <div class="pr-4">
      <div class="py-2">
        <a
          href="/help"
          class={cn(
            'flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium',
            isActive('/help') ? 'bg-accent text-accent-foreground' : 'hover:bg-accent/50'
          )}>
          <lucideIcons.Home class="h-4 w-4" />
          <span>Help Home</span>
        </a>
      </div>

      <Accordion.Root
        type="multiple"
        value={expandedCategories}
        onValueChange={(value) => (expandedCategories = value)}
        class="space-y-1">
        {#each categories as category}
          <Accordion.Item value={category.id} class="border-none">
            <Accordion.Trigger
              class={cn(
                'flex w-full items-center justify-between rounded-md px-3 py-2 text-sm font-medium',
                isActive(`/help/category/${category.slug}`)
                  ? 'bg-accent text-accent-foreground'
                  : 'hover:bg-accent/50'
              )}>
              <div class="flex items-center gap-2">
                {#if category.icon}
                  {#if category.icon === 'BookOpen'}
                    <lucideIcons.BookOpen class="h-4 w-4" />
                  {:else if category.icon === 'FileText'}
                    <lucideIcons.FileText class="h-4 w-4" />
                  {:else if category.icon === 'CreditCard'}
                    <lucideIcons.CreditCard class="h-4 w-4" />
                  {:else if category.icon === 'Shield'}
                    <lucideIcons.Shield class="h-4 w-4" />
                  {:else}
                    <lucideIcons.HelpCircle class="h-4 w-4" />
                  {/if}
                {:else}
                  <lucideIcons.HelpCircle class="h-4 w-4" />
                {/if}
                <span>{category.name}</span>
              </div>
            </Accordion.Trigger>
            <Accordion.Content class="pb-0 pt-1">
              <div class="ml-6 space-y-1">
                {#if category.children && category.children.length > 0}
                  {#each category.children as subCategory}
                    <a
                      href="/help/category/{subCategory.slug}"
                      class={cn(
                        'flex items-center gap-2 rounded-md px-3 py-2 text-sm',
                        isActive(`/help/category/${subCategory.slug}`)
                          ? 'bg-accent text-accent-foreground'
                          : 'hover:bg-accent/50'
                      )}>
                      {subCategory.name}
                    </a>
                  {/each}
                {/if}

                {#if category.articles && category.articles.length > 0}
                  {#each category.articles as article}
                    <a
                      href="/help/{article.slug}"
                      class={cn(
                        'flex items-center gap-2 rounded-md px-3 py-2 text-sm',
                        isActive(`/help/${article.slug}`)
                          ? 'bg-accent text-accent-foreground'
                          : 'hover:bg-accent/50'
                      )}>
                      {article.title}
                    </a>
                  {/each}
                {/if}
              </div>
            </Accordion.Content>
          </Accordion.Item>
        {/each}
      </Accordion.Root>
    </div>
  </ScrollArea.Root>
</div>
