/**
 * Keyboard Shortcuts Type Definitions
 *
 * This file contains type definitions for keyboard shortcuts in the application.
 * All shortcuts should use Alt+ or Windows key+ combinations (no bare single keys or Shift+ commands).
 */

/**
 * Represents a single keyboard shortcut
 */
export interface KeyboardShortcut {
  /** Unique identifier for the shortcut */
  id: string;
  /** Human-readable name of the shortcut */
  action: string;
  /** Key combination (e.g., 'Alt+K', 'Win+S') */
  keys: string;
  /** Function to execute when shortcut is triggered */
  handler: (event: KeyboardEvent) => void;
  /** Optional description of what the shortcut does */
  description?: string;
  /** Whether the shortcut is currently enabled */
  enabled?: boolean;
}

/**
 * Represents a group of related shortcuts
 */
export interface ShortcutGroup {
  /** Unique identifier for the group */
  id: string;
  /** Display name for the group */
  name: string;
  /** Shortcuts in this group */
  shortcuts: KeyboardShortcut[];
  /** Pages where this group should be active */
  activePages?: string[];
}

/**
 * Modifier keys that can be used in shortcuts
 */
export enum ModifierKey {
  ALT = 'Alt',
  WINDOWS = 'Win',
  COMMAND = '⌘', // For Mac display
  CONTROL = 'Ctrl', // For Windows/Linux display
}

/**
 * Page identifiers for activating shortcut groups
 */
export enum ShortcutPage {
  GLOBAL = 'global',
  DASHBOARD = 'dashboard',
  JOBS = 'jobs',
  APPLICATIONS = 'applications',
  RESUMES = 'resumes',
  DOCUMENTS = 'documents',
  TRACKER = 'tracker',
  AUTOMATION = 'automation',
  MATCHES = 'matches',
  SETTINGS = 'settings',
  ADMIN = 'admin',
  SYSTEM_STATUS = 'system-status',
  NOTIFICATIONS = 'notifications',
}

/**
 * Shortcut category identifiers
 */
export enum ShortcutCategory {
  NAVIGATION = 'navigation',
  ACTIONS = 'actions',
  UI = 'ui',
  ADMIN = 'admin',
  EDITING = 'editing',
}
