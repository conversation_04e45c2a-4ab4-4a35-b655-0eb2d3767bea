<!-- DocumentViewer.svelte -->
<script lang="ts">
  import { FileText, AlertTriangle } from 'lucide-svelte';
  import * as Tabs from '$lib/components/ui/tabs';
  import * as Card from '$lib/components/ui/card';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { onMount } from 'svelte';

  // Import universal document viewer
  import UniversalDocumentViewer from './UniversalDocumentViewer.svelte';

  const {
    document,
    height = '600px',
    showTabs = true,
  } = $props<{
    document: {
      id: string;
      label: string;
      fileUrl: string;
      type: string;
      fileName?: string;
      source?: string;
    };
    height?: string;
    showTabs?: boolean;
  }>();

  let loading = $state(true);
  let error = $state(false);
  let activeTab = $state('preview');
  let contentType = $state('');

  // Document type detection functions
  function getDocumentType() {
    if (isPlaceholder()) return 'placeholder';
    if (isPdf()) return 'pdf';
    if (isDocx()) return 'docx';
    if (isXlsx()) return 'xlsx';
    if (isImage()) return 'image';
    return 'unsupported';
  }

  function isPdf() {
    if (!document.fileUrl) return false;

    const fileUrl = document.fileUrl.toLowerCase();
    const fileName = document.fileName?.toLowerCase() || '';
    const type = document.type?.toLowerCase() || '';

    return (
      fileUrl.endsWith('.pdf') ||
      fileName.endsWith('.pdf') ||
      type === 'application/pdf' ||
      type === 'pdf'
    );
  }

  function isDocx() {
    if (!document.fileUrl) return false;

    const fileUrl = document.fileUrl.toLowerCase();
    const fileName = document.fileName?.toLowerCase() || '';
    const type = document.type?.toLowerCase() || '';

    return (
      fileUrl.endsWith('.docx') ||
      fileUrl.endsWith('.doc') ||
      fileName.endsWith('.docx') ||
      fileName.endsWith('.doc') ||
      type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
      type === 'application/msword' ||
      type === 'docx' ||
      type === 'doc'
    );
  }

  function isXlsx() {
    if (!document.fileUrl) return false;

    const fileUrl = document.fileUrl.toLowerCase();
    const fileName = document.fileName?.toLowerCase() || '';
    const type = document.type?.toLowerCase() || '';

    return (
      fileUrl.endsWith('.xlsx') ||
      fileUrl.endsWith('.xls') ||
      fileName.endsWith('.xlsx') ||
      fileName.endsWith('.xls') ||
      type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      type === 'application/vnd.ms-excel' ||
      type === 'xlsx' ||
      type === 'xls'
    );
  }

  function isImage() {
    if (!document.fileUrl) return false;

    const fileUrl = document.fileUrl.toLowerCase();
    const fileName = document.fileName?.toLowerCase() || '';
    const type = document.type?.toLowerCase() || '';

    return (
      fileUrl.endsWith('.jpg') ||
      fileUrl.endsWith('.jpeg') ||
      fileUrl.endsWith('.png') ||
      fileUrl.endsWith('.gif') ||
      fileUrl.endsWith('.webp') ||
      fileName.endsWith('.jpg') ||
      fileName.endsWith('.jpeg') ||
      fileName.endsWith('.png') ||
      fileName.endsWith('.gif') ||
      fileName.endsWith('.webp') ||
      type.includes('image/') ||
      type === 'jpg' ||
      type === 'jpeg' ||
      type === 'png' ||
      type === 'gif' ||
      type === 'webp'
    );
  }

  function isPlaceholder() {
    return (
      !document.fileUrl ||
      document.fileUrl === '/placeholder.pdf' ||
      document.fileUrl.includes('placeholder')
    );
  }

  // Function to retry loading the document
  function retryLoading() {
    loading = true;
    error = false;

    // Re-evaluate the content type
    contentType = getDocumentType();
  }

  onMount(() => {
    // Reset states
    loading = true;
    error = false;

    // Determine document type
    contentType = getDocumentType();

    console.log('DocumentViewer mounted with document:', document);
    console.log('Document type detected:', contentType);

    // If it's a placeholder, don't try to load
    if (isPlaceholder()) {
      loading = false;
    } else {
      // For non-placeholder documents, loading will be handled by UniversalDocumentViewer
      // We'll set loading to false after a short delay to avoid flickering
      setTimeout(() => {
        loading = false;
      }, 100);
    }
  });
</script>

<div class="document-viewer w-full" style="height: {height};">
  {#if showTabs}
    <Tabs.Root value={activeTab} class="w-full">
      <Tabs.List class="grid w-full grid-cols-2">
        <Tabs.Trigger value="preview">Preview</Tabs.Trigger>
        <Tabs.Trigger value="properties">Properties</Tabs.Trigger>
      </Tabs.List>

      <Tabs.Content value="preview" class="h-full">
        <div class="relative mt-2 h-full w-full overflow-hidden rounded-md border">
          {#if isPlaceholder()}
            <div class="flex h-full w-full flex-col items-center justify-center p-4">
              <FileText class="text-muted-foreground mb-4 h-16 w-16" />
              <p class="text-muted-foreground mb-2 text-center">
                This document has not been generated yet
              </p>
            </div>
          {:else if loading}
            <div class="flex h-full w-full items-center justify-center">
              <Skeleton class="h-[90%] w-[90%] rounded-md" />
            </div>
          {:else if error}
            <div class="flex h-full w-full flex-col items-center justify-center p-4">
              <AlertTriangle class="text-destructive mb-4 h-16 w-16" />
              <p class="text-destructive mb-2 text-center">Failed to load document</p>
              <p class="text-muted-foreground mb-4 text-center text-sm">
                The document may be unavailable or in an unsupported format.
              </p>
              <button
                class="bg-muted hover:bg-muted/80 rounded px-3 py-1 text-sm"
                onclick={retryLoading}>
                Try Again
              </button>
            </div>
          {:else}
            <UniversalDocumentViewer {document} />
          {/if}
        </div>
      </Tabs.Content>

      <Tabs.Content value="properties" class="h-full">
        <Card.Root class="mt-2 h-full overflow-auto">
          <Card.Content class="p-4">
            <div class="space-y-4">
              <div>
                <h4 class="text-muted-foreground text-sm font-medium">Document Name</h4>
                <p>{document.label}</p>
              </div>

              <div>
                <h4 class="text-muted-foreground text-sm font-medium">Type</h4>
                <p>{document.type || contentType}</p>
              </div>

              {#if document.fileName}
                <div>
                  <h4 class="text-muted-foreground text-sm font-medium">File Name</h4>
                  <p class="break-all">{document.fileName}</p>
                </div>
              {/if}

              {#if document.source}
                <div>
                  <h4 class="text-muted-foreground text-sm font-medium">Source</h4>
                  <p>{document.source}</p>
                </div>
              {/if}

              {#if document.fileUrl && !isPlaceholder()}
                <div>
                  <h4 class="text-muted-foreground text-sm font-medium">File URL</h4>
                  <p class="break-all text-xs">{document.fileUrl}</p>
                </div>
              {/if}
            </div>
          </Card.Content>
        </Card.Root>
      </Tabs.Content>
    </Tabs.Root>
  {:else}
    <div class="relative h-full w-full overflow-hidden rounded-md border">
      {#if isPlaceholder()}
        <div class="flex h-full w-full flex-col items-center justify-center p-4">
          <FileText class="text-muted-foreground mb-4 h-16 w-16" />
          <p class="text-muted-foreground mb-2 text-center">
            This document has not been generated yet
          </p>
        </div>
      {:else if loading}
        <div class="flex h-full w-full items-center justify-center">
          <Skeleton class="h-[90%] w-[90%] rounded-md" />
        </div>
      {:else if error}
        <div class="flex h-full w-full flex-col items-center justify-center p-4">
          <AlertTriangle class="text-destructive mb-4 h-16 w-16" />
          <p class="text-destructive mb-2 text-center">Failed to load document</p>
          <p class="text-muted-foreground mb-4 text-center text-sm">
            The document may be unavailable or in an unsupported format.
          </p>
          <button
            class="bg-muted hover:bg-muted/80 rounded px-3 py-1 text-sm"
            onclick={retryLoading}>
            Try Again
          </button>
        </div>
      {:else}
        <UniversalDocumentViewer {document} />
      {/if}
    </div>
  {/if}
</div>
