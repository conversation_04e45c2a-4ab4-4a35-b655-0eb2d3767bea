# Email System Documentation

This document provides an overview of the email system, including the analytics tracking and webhook integration with Resend.

## Overview

The email system uses Resend as the email service provider and includes:

1. Email queue processing with Redis
2. Email analytics tracking via webhooks
3. Email template rendering
4. Broadcast email capabilities

## Email Analytics

Email analytics are tracked using Resend's webhook events. These events are stored in the `EmailEvent` table in the database and used to calculate metrics like open rates, click rates, and bounce rates.

### Webhook Events

Resend sends the following webhook events:

- `email.sent` - When an email is successfully sent
- `email.delivered` - When an email is successfully delivered
- `email.bounced` - When an email permanently bounces
- `email.opened` - When a recipient opens an email (requires tracking pixel)
- `email.clicked` - When a recipient clicks a link in an email (requires link tracking)
- `email.complained` - When a recipient marks an email as spam
- `email.delivery_delayed` - When delivery is temporarily delayed

### Setting Up Webhooks

1. Generate a webhook secret by visiting `/api/email/webhook/setup`
2. Add the webhook secret to your `.env` file:
   ```
   RESEND_WEBHOOK_SECRET=your_generated_secret
   ```
3. Configure the webhook in the Resend dashboard:
   - URL: `https://your-domain.com/api/email/webhook`
   - Secret: Your generated secret
   - Events: Select the events you want to track (delivered, opened, clicked, bounced, complained)

### Email Event Database Schema

Email events are stored in the `EmailEvent` table with the following schema:

```sql
CREATE TABLE "web"."EmailEvent" (
  "id" TEXT NOT NULL,
  "email" TEXT NOT NULL,
  "type" TEXT NOT NULL,
  "timestamp" TIMESTAMP(3) NOT NULL,
  "templateName" TEXT,
  "category" TEXT,
  "data" JSONB,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "EmailEvent_pkey" PRIMARY KEY ("id")
)
```

### Email Tags

For proper analytics tracking, all emails should include the following tags:

1. `template` - The template name (e.g., "welcome", "verification")
2. `category` - The email category (e.g., "transactional", "marketing")

These tags are automatically added to all emails sent through the email queue system.

## Testing Email Analytics

You can test the webhook processing by sending a test event:

```bash
curl -X POST http://localhost:5173/api/email/webhook/test \
  -H "Content-Type: application/json" \
  -d '{"type": "email.delivered", "email": "<EMAIL>"}'
```

## Email Analytics API

The following API endpoints are available for email analytics:

- `GET /api/email/analytics` - Get overall email analytics
- `GET /api/email/analytics/stats` - Get detailed email stats with filtering
- `GET /api/email/analytics/events` - Get individual email events
- `GET /api/email/analytics/export` - Export email events as CSV
- `GET /api/email/analytics/check` - Check if the EmailEvent table exists

## Calculating Email Metrics

Email metrics are calculated as follows:

- **Delivery Rate**: `(delivered / sent) * 100`
- **Open Rate**: `(opened / delivered) * 100`
- **Click Rate**: `(clicked / opened) * 100`
- **Bounce Rate**: `(bounced / sent) * 100`

## Troubleshooting

### Missing Events

If you're not seeing events in your analytics:

1. Check that the webhook is properly configured in Resend
2. Verify that the `RESEND_WEBHOOK_SECRET` is set correctly
3. Check the server logs for any webhook processing errors
4. Ensure emails have the proper tags for template and category

### Database Issues

If the `EmailEvent` table doesn't exist:

1. Visit `/api/email/analytics/check` to check and create the table
2. Check the database connection and permissions

## Best Practices

1. Always include template and category tags in all emails
2. Use consistent template names across the application
3. Monitor bounce rates to maintain good sender reputation
4. Regularly clean up old email events to manage database size

## Resources

- [Resend Webhook Documentation](https://resend.com/docs/dashboard/webhooks/introduction)
- [Resend Email API Documentation](https://resend.com/docs/api-reference/introduction)
