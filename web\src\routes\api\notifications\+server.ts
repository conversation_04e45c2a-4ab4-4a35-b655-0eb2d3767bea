// src/routes/api/notifications/+server.ts
import { json } from '@sveltejs/kit';
import { getUserFromToken } from '$lib/server/auth';
import { prisma } from '$lib/server/prisma';
import { getRedisClient } from '$lib/server/redis';
import type { Request<PERSON>and<PERSON> } from './$types';
import {
  getUserNotifications,
  markNotificationAsRead,
  deleteNotification,
} from '$lib/server/notification-triggers/notification-service';

/**
 * Get user notifications
 */
export const GET: RequestHandler = async ({ cookies, url }) => {
  // Authenticate the user
  const user = await getUserFromToken(cookies);

  if (!user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Get query parameters
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const includeRead = url.searchParams.get('includeRead') === 'true';
    const type = url.searchParams.get('type') || undefined;
    const requestId = url.searchParams.get('requestId');

    // Log request details
    console.log(`Notification API request from user ${user.id}:`, {
      limit,
      offset,
      includeRead,
      type,
      requestId,
    });

    // Get notifications
    const notifications = await getUserNotifications(user.id, {
      limit,
      offset,
      includeRead,
      type: type as any,
    });

    // Get unread count
    const unreadCount = await prisma.notification.count({
      where: {
        OR: [{ userId: user.id }, { global: true }],
        read: false,
      },
    });

    // If a request ID was provided, log it for debugging
    if (requestId) {
      console.log(
        `Responding to request with ID ${requestId} with ${notifications.length} notifications`
      );

      // Add the request ID to each notification in the response
      notifications.forEach((notification: any) => {
        notification.requestId = requestId;
      });
    }

    return json({
      success: true,
      notifications,
      unreadCount,
      requestId, // Include the request ID in the response
    });
  } catch (error) {
    console.error('Error getting notifications:', error);
    return json({ error: 'Failed to get notifications' }, { status: 500 });
  }
};

/**
 * Mark a notification as read or delete a notification
 */
export const POST: RequestHandler = async ({ cookies, request }) => {
  // Authenticate the user
  const user = await getUserFromToken(cookies);

  if (!user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const data = await request.json();

    // Validate required fields
    if (!data.action) {
      return json({ error: 'Action is required' }, { status: 400 });
    }

    // Special case for markAllAsRead action
    if (data.action === 'markAllAsRead') {
      // Mark all user's notifications as read
      await prisma.notification.updateMany({
        where: {
          OR: [{ userId: user.id }, { global: true }],
          read: false,
        },
        data: {
          read: true,
        },
      });

      // Send a WebSocket notification about the update
      try {
        const redis = await getRedisClient();
        if (redis) {
          // Generate a unique request ID for this update
          const requestId = `notification_read_all:${user.id}:${Date.now()}`;
          console.log(
            `Generated request ID: ${requestId} for marking all notifications as read for user ${user.id}`
          );

          // Publish to user-specific channel to notify of the update
          console.log(
            `Publishing mark all as read update to Redis channel user:${user.id}:notifications with request ID ${requestId}`
          );

          await redis.publish(
            `user:${user.id}:notifications`,
            JSON.stringify({
              type: 'notification_read',
              id: 'all',
              timestamp: new Date().toISOString(),
              requestId: requestId, // Include the request ID
            })
          );
        }
      } catch (error) {
        console.error('Error sending WebSocket notification for markAllAsRead:', error);
      }

      return json({
        success: true,
        message: 'All notifications marked as read',
      });
    }

    // For other actions, we need a notification ID
    if (!data.id) {
      return json({ error: 'Notification ID is required' }, { status: 400 });
    }

    // Check if the notification belongs to the user or is global
    const notification = await prisma.notification.findUnique({
      where: { id: data.id },
    });

    if (!notification) {
      return json({ error: 'Notification not found' }, { status: 404 });
    }

    // Only allow access to user's own notifications or global notifications
    if (notification.userId !== user.id && !notification.global) {
      return json({ error: 'Unauthorized to access this notification' }, { status: 403 });
    }

    // Handle different actions
    switch (data.action) {
      case 'markAsRead':
        await markNotificationAsRead(data.id);
        return json({
          success: true,
          message: 'Notification marked as read',
        });

      case 'delete':
        await deleteNotification(data.id);
        return json({
          success: true,
          message: 'Notification deleted',
        });

      default:
        return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error processing notification action:', error);
    return json({ error: 'Failed to process notification action' }, { status: 500 });
  }
};
