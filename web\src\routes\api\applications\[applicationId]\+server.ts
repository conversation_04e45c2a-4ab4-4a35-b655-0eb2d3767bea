/**
 * API endpoints for managing applications
 */

import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';

/**
 * Get a specific application
 * GET /api/applications/:applicationId
 */
export async function GET({ params, locals }) {
  console.log('GET application:', params.applicationId);
  console.log('User in locals:', locals.user ? `ID: ${locals.user.id}` : 'Not authenticated');

  // In development mode, we'll allow access without authentication
  const isDev = process.env.NODE_ENV === 'development';

  // Ensure user is authenticated (except in dev mode)
  if (!locals.user && !isDev) {
    console.log('Unauthorized access attempt - no user in locals');
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { applicationId } = params;

  try {
    // In dev mode, we'll skip the user verification
    if (!isDev && locals.user) {
      // Verify the application belongs to the user
      const application = await prisma.application.findUnique({
        where: {
          id: applicationId,
          userId: locals.user.id,
        },
      });

      if (!application) {
        console.log('Application not found or does not belong to user');
        return json({ error: 'Application not found' }, { status: 404 });
      }
    }

    // Get the application
    const application = await prisma.application.findUnique({
      where: {
        id: applicationId,
      },
    });

    if (!application) {
      console.log('Application not found');
      return json({ error: 'Application not found' }, { status: 404 });
    }

    console.log('Found application:', application.id);
    return json({ application });
  } catch (error) {
    console.error('Error fetching application:', error);
    return json({ error: 'Failed to fetch application' }, { status: 500 });
  }
}

/**
 * Update an application
 * PATCH /api/applications/:applicationId
 */
export async function PATCH({ request, params, locals }) {
  console.log('PATCH application:', params.applicationId);
  console.log('User in locals:', locals.user ? `ID: ${locals.user.id}` : 'Not authenticated');

  // In development mode, we'll allow access without authentication
  const isDev = process.env.NODE_ENV === 'development';

  // Ensure user is authenticated (except in dev mode)
  if (!locals.user && !isDev) {
    console.log('Unauthorized access attempt - no user in locals');
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { applicationId } = params;

  try {
    // In dev mode, we'll skip the user verification
    if (!isDev && locals.user) {
      // Verify the application belongs to the user
      const application = await prisma.application.findUnique({
        where: {
          id: applicationId,
          userId: locals.user.id,
        },
      });

      if (!application) {
        console.log('Application not found or does not belong to user');
        return json({ error: 'Application not found' }, { status: 404 });
      }
    }

    // Parse the request body
    const body = await request.json();
    console.log('Update data:', body);

    // Update the application
    const updatedApplication = await prisma.application.update({
      where: {
        id: applicationId,
      },
      data: {
        notes: body.notes !== undefined ? body.notes : undefined,
        nextAction: body.nextAction !== undefined ? body.nextAction : undefined,
      },
    });

    console.log('Application updated successfully:', updatedApplication.id);
    return json({ application: updatedApplication }, { status: 200 });
  } catch (error) {
    console.error('Error updating application:', error);
    // Return more detailed error information in development mode
    if (isDev) {
      return json(
        {
          error: 'Failed to update application',
          details: error.message,
          code: error.code,
        },
        { status: 500 }
      );
    } else {
      return json({ error: 'Failed to update application' }, { status: 500 });
    }
  }
}
