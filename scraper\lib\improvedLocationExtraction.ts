// cron/lib/improvedLocationExtraction.ts

import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";

const prisma = new PrismaClient();

// Cache for city and state data
let cityNamesCache: string[] = [];
let stateCodesCache: string[] = [];
let stateNamesCache: { name: string; code: string }[] = [];

/**
 * Initialize the location cache for improved location extraction
 * This should be called before using extractLocation in bulk operations
 */
export async function initImprovedLocationCache(): Promise<void> {
  try {
    // Get all city names from the database
    const cities = await prisma.city.findMany({
      select: { name: true },
    });
    cityNamesCache = cities.map((city) => city.name.toLowerCase());

    // Get all state codes and names from the database
    const states = await prisma.state.findMany({
      select: { code: true, name: true },
    });

    stateCodesCache = states
      .filter((state) => state.code) // Filter out null values
      .map((state) => state.code!.toUpperCase());

    stateNamesCache = states
      .filter((state) => state.code && state.name) // Filter out null values
      .map((state) => ({
        name: state.name!.toLowerCase(),
        code: state.code!.toUpperCase(),
      }));

    logger.info(
      `Loaded ${cityNamesCache.length} cities and ${stateCodesCache.length} states into improved location cache`
    );
  } catch (error) {
    logger.error("Failed to initialize improved location cache:", error);
    // Initialize with empty arrays if there's an error
    cityNamesCache = [];
    stateCodesCache = [];
    stateNamesCache = [];
  }
}

/**
 * Extract and clean location from job data
 * This is an improved version that handles various edge cases
 *
 * @param location Raw location string from job listing
 * @param jobTitle Job title for context (optional)
 * @param jobDescription Job description for context (optional)
 * @param searchCity City that was used in the search query (optional)
 * @param searchState State that was used in the search query (optional)
 * @returns Cleaned location string
 */
export function extractLocation(
  location: string,
  jobTitle?: string,
  jobDescription?: string,
  searchCity?: string,
  searchState?: string
): string {
  // If no location provided, try to extract from description or use search location
  if (!location) {
    if (jobDescription) {
      // Try to extract location from description
      const extractedLocation = extractLocationFromText(jobDescription);
      if (extractedLocation) {
        return extractedLocation;
      }
    }

    // If we have search city/state, use that as fallback
    if (searchCity && searchState) {
      return `${searchCity}, ${searchState}`;
    }

    return "";
  }

  // Check for extremely long locations (likely job descriptions)
  if (location.length > 50) {
    logger.warn(
      `Location too long (${location.length} chars): "${location.substring(0, 50)}..."`
    );

    // Try to extract city from the text
    const cityMatch = location.match(/([A-Z][a-zA-Z\s]+),\s*CA\b/);
    if (cityMatch) {
      return cityMatch[0];
    }

    // If we have search city/state, use that instead
    if (searchCity && searchState) {
      return `${searchCity}, ${searchState}`;
    }

    // Default to state code
    return searchState || "CA";
  }

  // Check for HTML or JavaScript in location
  if (
    location.includes("<") ||
    location.includes(">") ||
    location.includes("function") ||
    location.includes("window.") ||
    location.includes("document.") ||
    location.includes("var ") ||
    location.includes("const ") ||
    location.includes("let ") ||
    location.includes("script")
  ) {
    logger.warn(
      `HTML/JS detected in location: "${location.substring(0, 50)}..."`
    );

    // If we have search city/state, use that instead
    if (searchCity && searchState) {
      return `${searchCity}, ${searchState}`;
    }

    // Default to state code
    return searchState || "CA";
  }

  // Check for non-location text patterns
  if (isNonLocationText(location, jobTitle)) {
    // If we have search city/state, use that instead
    if (searchCity && searchState) {
      return `${searchCity}, ${searchState}`;
    }
    return searchState || "CA";
  }

  // Special case for "Anywhere, Ca" which should be treated as a location
  if (location.match(/^Anywhere,\s*[A-Za-z]{2}$/i)) {
    const match = location.match(/^Anywhere,\s*([A-Za-z]{2})$/i);
    if (match) {
      return `Anywhere, ${match[1].toUpperCase()}`;
    }
  }

  // If it's a remote indicator with a location (e.g., "Remote - San Diego"),
  // extract the actual location
  if (isRemoteLocation(location)) {
    // Try to extract a real location from the text
    const locationParts = location.split(/\s*[-–—]\s*/); // Split by various dash types

    // If we have something like "Remote - San Diego"
    if (locationParts.length > 1) {
      // Skip the first part (which is the remote indicator) and join the rest
      const possibleLocation = locationParts.slice(1).join(" ").trim();

      // Try to extract a city/state from this part
      const extractedLocation = extractCityState(possibleLocation);
      if (extractedLocation) {
        return extractedLocation;
      }
    }

    // If we have search city/state, use that as fallback
    if (searchCity && searchState) {
      return `${searchCity}, ${searchState}`;
    }

    // If we can't extract a location, return empty string
    // The remoteType field should handle the remote status
    return "";
  }

  // Basic cleaning
  let cleaned = cleanLocationText(location);

  // Try to extract City, State format
  const extractedLocation = extractCityState(cleaned);
  if (extractedLocation) {
    return extractedLocation;
  }

  // If we have a search city/state and the location is ambiguous, use the search location
  if (
    searchCity &&
    searchState &&
    (cleaned.length < 3 || !cleaned.match(/[A-Z][a-z]/))
  ) {
    return `${searchCity}, ${searchState}`;
  }

  // If all else fails, return the cleaned location
  return cleaned;
}

/**
 * Extract city and state from location text
 */
function extractCityState(location: string): string | null {
  if (!location) return null;

  // Try to extract City, State format (City, XX)
  const cityStateMatch = location.match(/([A-Z][a-zA-Z\s]+),\s*([A-Z]{2})/i);
  if (cityStateMatch) {
    const city = cityStateMatch[1].trim();
    const state = cityStateMatch[2].toUpperCase();
    return `${city}, ${state}`;
  }

  // Try to extract city and state without comma (City XX)
  const cityStateNoCommaMatch = location.match(
    /([A-Z][a-zA-Z\s]+)\s+([A-Z]{2})$/i
  );
  if (cityStateNoCommaMatch) {
    const city = cityStateNoCommaMatch[1].trim();
    const state = cityStateNoCommaMatch[2].toUpperCase();
    return `${city}, ${state}`;
  }

  // Try to extract city and state name (City, State Name)
  for (const stateInfo of stateNamesCache) {
    const cityStateNameRegex = new RegExp(
      `([A-Z][a-zA-Z\\s]+),\\s*(${stateInfo.name})`,
      "i"
    );
    const match = location.match(cityStateNameRegex);
    if (match) {
      const city = match[1].trim();
      return `${city}, ${stateInfo.code}`;
    }
  }

  // If it's just a state code, return as is
  if (location.length === 2 && /^[A-Za-z]{2}$/.test(location)) {
    return location.toUpperCase();
  }

  // If it's just a state name, convert to code
  const stateName = stateNamesCache.find(
    (s) => s.name.toLowerCase() === location.toLowerCase()
  );
  if (stateName) {
    return stateName.code;
  }

  // If it looks like just a city name
  if (/^[A-Z][a-zA-Z\s]+$/i.test(location) && location.length > 2) {
    // Check if it's in our city cache
    if (cityNamesCache.includes(location.toLowerCase())) {
      return location;
    }
  }

  return null;
}

/**
 * Extract location from text (like job description)
 */
function extractLocationFromText(text: string): string | null {
  // Try to find city names (proper nouns followed by optional state code)
  const cityPatterns = [
    /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*),\s*([A-Z]{2})\b/, // City, ST format
    /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b(?:\s+area)?\b/, // Just city name
  ];

  // Try each pattern
  for (const pattern of cityPatterns) {
    const match = text.match(pattern);
    if (match) {
      if (match[2]) {
        return `${match[1]}, ${match[2].toUpperCase()}`;
      } else {
        return match[1];
      }
    }
  }

  // Look for "Location: City, State" patterns
  const locationLabelPattern =
    /location[:\s]+([A-Z][a-zA-Z\s]+(?:,\s*[A-Z]{2})?)/i;
  const labelMatch = text.match(locationLabelPattern);
  if (labelMatch) {
    // Try to extract city/state from the matched text
    const extractedLocation = extractCityState(labelMatch[1]);
    if (extractedLocation) {
      return extractedLocation;
    }
    return labelMatch[1];
  }

  return null;
}

/**
 * Clean location text by removing common prefixes, suffixes, and normalizing
 */
function cleanLocationText(location: string): string {
  if (!location) return "";

  // Basic cleaning
  let cleaned = location
    .trim()
    // Remove common prefixes
    .replace(/^in\s+/i, "")
    .replace(/^at\s+/i, "")
    .replace(/^location[:\s]+/i, "")
    .replace(/^job\s+location[:\s]+/i, "")
    // Remove common suffixes
    .replace(/\s+area$/i, "")
    .replace(/\s+region$/i, "")
    .replace(/\s+and surrounding areas$/i, "")
    .replace(/\s+and vicinity$/i, "");

  // Fix common typos in city names
  cleaned = cleaned
    .replace(/Los Ngeles/i, "Los Angeles")
    .replace(/San Diegoo/i, "San Diego")
    .replace(/San Dieg\b/i, "San Diego")
    .replace(/El Caj N/i, "El Cajon");

  return cleaned;
}

/**
 * Check if text contains indicators that it's not a location
 */
function isNonLocationText(text: string, jobTitle?: string): boolean {
  if (!text) return false;

  const lowerText = text.toLowerCase();

  // Non-location indicators
  const nonLocationIndicators = [
    "be an early applicant",
    "additional verification required",
    "easy apply",
    "apply now",
    "click to apply",
    "verification required",
    "posted",
    "new job",
    "active job",
    "full time",
    "part time",
    "contract",
    "permanent",
    "temporary",
    "freelance",
    "internship",
    "service temporarily unavailable",
    "error",
    "job overview",
    "about the role",
    "about the job",
    "about the company",
    "about us",
    "qualifications",
    "requirements",
    "responsibilities",
    "experience",
    "education",
    "skills",
    "benefits",
    "salary",
    "compensation",
    "opportunity",
    "position",
    "career",
    "join our team",
    "we are seeking",
    "we are looking",
    "seeking a",
    "looking for a",
  ];

  // Check for non-location indicators
  if (
    nonLocationIndicators.some((indicator) => lowerText.includes(indicator))
  ) {
    return true;
  }

  // Check if the location contains the job title (likely not a location)
  if (jobTitle && lowerText.includes(jobTitle.toLowerCase())) {
    return true;
  }

  // Check if the text is too long to be a location
  if (text.length > 50) {
    return true;
  }

  // Check for other common non-location patterns
  if (/^\d+\s+days?\s+ago$/i.test(text)) {
    return true; // "X days ago" pattern
  }

  if (/^\$\d+/i.test(text)) {
    return true; // Salary information
  }

  // Check if location contains job title or other non-location text
  if (
    /advocate|advisor|nurse|therapist|counselor|intelligence|analyst|easy apply|engineer|developer|specialist|manager|director|assistant|coordinator|administrator|supervisor|technician|consultant|physician|doctor|md|psychiatrist|psychologist|dietitian|recruiter|representative|sales|marketing|customer|service|support|agent|associate|executive|officer|president|vice president|vp|ceo|cto|cfo|coo|hr|human resources|it|information technology|software|hardware|network|system|database|web|mobile|cloud|security|data|business|finance|accounting|legal|medical|clinical|healthcare|health care|social|worker|teacher|instructor|professor|principal|dean|student|intern|trainee|apprentice|fellow|resident|postdoc|researcher|scientist|analyst|strategist|planner|designer|architect|artist|writer|editor|producer|director|manager|supervisor|lead|senior|junior|entry|level|i|ii|iii|iv|v/i.test(
      text
    )
  ) {
    return true;
  }

  return false;
}

/**
 * Check if location indicates a remote job
 */
function isRemoteLocation(location: string): boolean {
  if (!location) return false;

  const lowerLocation = location.toLowerCase();

  const remoteIndicators = [
    "remote",
    "work from home",
    "wfh",
    "virtual",
    "anywhere",
    "nationwide",
    "telecommute",
    "work remotely",
    "remote eligible",
    "hybrid remote",
    "in person/remote",
  ];

  return remoteIndicators.some((indicator) =>
    lowerLocation.includes(indicator)
  );
}

/**
 * Validate if a location string is likely to be a real location
 */
export function validateLocation(location: string): boolean {
  if (!location) return false;

  // Check if location is too short
  if (location.length < 3) return false;

  // Check if location starts with a capital letter
  if (!/^[A-Z]/.test(location)) return false;

  // Check if location is too long (likely not a real location)
  if (location.length > 50) return false;

  // Check if it's a non-location text
  if (isNonLocationText(location)) {
    return false;
  }

  return true;
}

/**
 * Extract the remote type from job data
 *
 * @param location Raw location string from job listing
 * @param description Job description for context (optional)
 * @returns Remote type string: "Remote", "Hybrid", "In-person", or empty string if unknown
 */
export function extractRemoteType(
  location: string,
  description?: string
): string {
  if (!location && !description) return "";

  // Check for remote indicators
  if (isFullyRemoteJob(location, description)) {
    return "Remote";
  }

  // Check for hybrid indicators
  if (isHybridJob(location, description)) {
    return "Hybrid";
  }

  // Check for in-person indicators
  if (isInPersonJob(location, description)) {
    return "In-person";
  }

  return ""; // Unknown remote type
}

/**
 * Check if job is fully remote
 */
function isFullyRemoteJob(location: string, description?: string): boolean {
  if (!location && !description) return false;

  const fullyRemoteIndicators = [
    "fully remote",
    "100% remote",
    "completely remote",
    "remote only",
    "remote position",
    "work from anywhere",
    "work from home",
    "wfh",
    "telecommute",
    "virtual position",
  ];

  // Check location
  if (location) {
    const lowerLocation = location.toLowerCase();

    // Check for exact remote indicators
    if (
      lowerLocation === "remote" ||
      lowerLocation === "anywhere" ||
      lowerLocation === "nationwide"
    ) {
      return true;
    }

    // Check for fully remote indicators
    if (
      fullyRemoteIndicators.some((indicator) =>
        lowerLocation.includes(indicator)
      )
    ) {
      return true;
    }
  }

  // Check description
  if (description) {
    const lowerDesc = description.toLowerCase();

    // Check for fully remote indicators
    if (
      fullyRemoteIndicators.some((indicator) => lowerDesc.includes(indicator))
    ) {
      return true;
    }

    // Check for 100% remote
    if (/\b100%\s+remote\b/i.test(description)) {
      return true;
    }
  }

  return false;
}

/**
 * Check if job is hybrid
 */
function isHybridJob(location: string, description?: string): boolean {
  if (!location && !description) return false;

  const hybridIndicators = [
    "hybrid",
    "partially remote",
    "part remote",
    "remote/onsite",
    "onsite/remote",
    "in-office/remote",
    "remote/in-office",
    "flexible work",
    "flexible location",
  ];

  // Check location
  if (location) {
    const lowerLocation = location.toLowerCase();

    // Check for hybrid indicators
    if (
      hybridIndicators.some((indicator) => lowerLocation.includes(indicator))
    ) {
      return true;
    }
  }

  // Check description
  if (description) {
    const lowerDesc = description.toLowerCase();

    // Check for hybrid indicators
    if (hybridIndicators.some((indicator) => lowerDesc.includes(indicator))) {
      return true;
    }

    // Check for partial remote percentages
    if (
      /\b[1-9][0-9]%\s+remote\b/i.test(description) &&
      !/\b100%\s+remote\b/i.test(description)
    ) {
      return true;
    }

    // Check for days per week remote
    if (
      /\b[1-4]\s+days?\s+(per\s+week\s+)?(remote|from\s+home)\b/i.test(
        description
      )
    ) {
      return true;
    }
  }

  return false;
}

/**
 * Check if job is in-person
 */
function isInPersonJob(location: string, description?: string): boolean {
  if (!location && !description) return false;

  const inPersonIndicators = [
    "in person",
    "in-person",
    "on site",
    "on-site",
    "onsite",
    "in office",
    "in-office",
    "at the office",
    "no remote",
    "requires you to be in the office",
    "must be in office",
    "must work in office",
    "must be on site",
    "must work on site",
  ];

  // Check location
  if (location) {
    const lowerLocation = location.toLowerCase();

    // Check for in-person indicators
    if (
      inPersonIndicators.some((indicator) => lowerLocation.includes(indicator))
    ) {
      return true;
    }
  }

  // Check description
  if (description) {
    const lowerDesc = description.toLowerCase();

    // Check for in-person indicators
    if (inPersonIndicators.some((indicator) => lowerDesc.includes(indicator))) {
      return true;
    }

    // Check for 0% remote
    if (/\b0%\s+remote\b/i.test(description)) {
      return true;
    }
  }

  return false;
}

/**
 * Determine if a job is remote based on location and description
 * @deprecated Use extractRemoteType instead
 */
export function isRemoteJob(location: string, description?: string): boolean {
  return extractRemoteType(location, description) === "Remote";
}
