<script lang="ts">
  import { TrendingUp, Users, Calendar } from 'lucide-svelte';

  let { referralData } = $props<{
    referralData: any;
  }>();

  let analyticsData = $state<any>(null);
  let loadingAnalytics = $state(true);

  // Load analytics data
  const loadAnalytics = async () => {
    try {
      const response = await fetch('/api/referrals/analytics');
      if (response.ok) {
        analyticsData = await response.json();
      }
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      loadingAnalytics = false;
    }
  };

  // Load analytics on component mount
  loadAnalytics();

  // Reactive derived values
  const monthlyData = $derived(analyticsData?.monthlyAnalytics || []);
  const maxReferrals = $derived(Math.max(...monthlyData.map((d: any) => d.referrals), 1));
  const maxCumulative = $derived(Math.max(...monthlyData.map((d: any) => d.cumulative), 1));
</script>

<div class="space-y-6">
  <!-- Chart Header -->
  <div class="flex items-center justify-between">
    <div>
      <h3 class="text-lg font-semibold">Referral Analytics</h3>
      <p class="text-muted-foreground text-sm">Track your referral performance over time</p>
    </div>
    <div class="flex items-center gap-2">
      <TrendingUp class="text-primary h-5 w-5" />
      <span class="text-sm font-medium">Growth Trend</span>
    </div>
  </div>

  <!-- Chart Container -->
  <div class="bg-card rounded-lg border p-6">
    <div class="mb-4 flex items-center justify-between">
      <h4 class="font-medium">Monthly Referrals</h4>
      <div class="flex items-center gap-4 text-xs">
        <div class="flex items-center gap-1">
          <div class="bg-primary h-3 w-3 rounded-full"></div>
          <span>Monthly</span>
        </div>
        <div class="flex items-center gap-1">
          <div class="bg-success h-3 w-3 rounded-full"></div>
          <span>Cumulative</span>
        </div>
      </div>
    </div>

    <!-- Chart -->
    <div class="relative h-64">
      <div class="absolute inset-0 flex items-end justify-between gap-2">
        {#each monthlyData as data}
          <div class="flex flex-1 flex-col items-center gap-2">
            <!-- Bars -->
            <div class="relative flex w-full flex-col gap-1">
              <!-- Monthly bar -->
              <div class="bg-muted relative w-full rounded-t">
                <div
                  class="bg-primary w-full rounded-t transition-all duration-500"
                  style="height: {(data.referrals / maxReferrals) * 120}px; min-height: 4px;">
                </div>
              </div>
              <!-- Cumulative line point -->
              <div class="relative flex justify-center">
                <div
                  class="bg-success absolute h-2 w-2 rounded-full"
                  style="bottom: {(data.cumulative / maxCumulative) * 80}px;">
                </div>
              </div>
            </div>

            <!-- Labels -->
            <div class="text-center">
              <div class="text-xs font-medium">{data.month}</div>
              <div class="text-muted-foreground text-xs">{data.referrals}</div>
            </div>
          </div>
        {/each}
      </div>

      <!-- Y-axis labels -->
      <div
        class="text-muted-foreground absolute left-0 top-0 flex h-full flex-col justify-between text-xs">
        <span>{maxReferrals}</span>
        <span>{Math.floor(maxReferrals / 2)}</span>
        <span>0</span>
      </div>
    </div>
  </div>

  <!-- Stats Grid -->
  <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
    <div class="bg-card rounded-lg border p-4">
      <div class="flex items-center gap-2">
        <Users class="text-primary h-4 w-4" />
        <span class="text-sm font-medium">Total Referrals</span>
      </div>
      <div class="mt-2">
        <div class="text-2xl font-bold">{referralData?.referralCount || 0}</div>
        <div class="text-muted-foreground text-xs">All time</div>
      </div>
    </div>

    <div class="bg-card rounded-lg border p-4">
      <div class="flex items-center gap-2">
        <Calendar class="text-success h-4 w-4" />
        <span class="text-sm font-medium">This Month</span>
      </div>
      <div class="mt-2">
        <div class="text-2xl font-bold">
          {loadingAnalytics ? '...' : monthlyData[monthlyData.length - 1]?.referrals || 0}
        </div>
        <div class="text-muted-foreground text-xs">New referrals</div>
      </div>
    </div>

    <div class="bg-card rounded-lg border p-4">
      <div class="flex items-center gap-2">
        <TrendingUp class="text-warning h-4 w-4" />
        <span class="text-sm font-medium">Success Rate</span>
      </div>
      <div class="mt-2">
        <div class="text-2xl font-bold">
          {loadingAnalytics ? '...' : analyticsData?.conversionRate || 0}%
        </div>
        <div class="text-muted-foreground text-xs">Conversion rate</div>
      </div>
    </div>
  </div>
</div>
