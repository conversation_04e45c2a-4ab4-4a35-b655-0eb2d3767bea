// cron/lib/extractRemoteType.ts

/**
 * Extract the remote type from job data
 * 
 * @param location Raw location string from job listing
 * @param description Job description for context (optional)
 * @returns Remote type string: "Remote", "Hybrid", "In-person", or empty string if unknown
 */
export function extractRemoteType(
  location: string,
  description?: string
): string {
  if (!location && !description) return "";
  
  // Check for remote indicators
  if (isFullyRemoteJob(location, description)) {
    return "Remote";
  }
  
  // Check for hybrid indicators
  if (isHybridJob(location, description)) {
    return "Hybrid";
  }
  
  // Check for in-person indicators
  if (isInPersonJob(location, description)) {
    return "In-person";
  }
  
  return ""; // Unknown remote type
}

/**
 * Check if job is fully remote
 */
function isFullyRemoteJob(location: string, description?: string): boolean {
  if (!location && !description) return false;
  
  const fullyRemoteIndicators = [
    "fully remote",
    "100% remote",
    "completely remote",
    "remote only",
    "remote position",
    "work from anywhere",
    "work from home",
    "wfh",
    "telecommute",
    "virtual position"
  ];
  
  // Check location
  if (location) {
    const lowerLocation = location.toLowerCase();
    
    // Check for exact remote indicators
    if (lowerLocation === "remote" || 
        lowerLocation === "anywhere" || 
        lowerLocation === "nationwide") {
      return true;
    }
    
    // Check for fully remote indicators
    if (fullyRemoteIndicators.some(indicator => 
      lowerLocation.includes(indicator)
    )) {
      return true;
    }
  }
  
  // Check description
  if (description) {
    const lowerDesc = description.toLowerCase();
    
    // Check for fully remote indicators
    if (fullyRemoteIndicators.some(indicator => 
      lowerDesc.includes(indicator)
    )) {
      return true;
    }
    
    // Check for 100% remote
    if (/\b100%\s+remote\b/i.test(description)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Check if job is hybrid
 */
function isHybridJob(location: string, description?: string): boolean {
  if (!location && !description) return false;
  
  const hybridIndicators = [
    "hybrid",
    "partially remote",
    "part remote",
    "remote/onsite",
    "onsite/remote",
    "in-office/remote",
    "remote/in-office",
    "flexible work",
    "flexible location"
  ];
  
  // Check location
  if (location) {
    const lowerLocation = location.toLowerCase();
    
    // Check for hybrid indicators
    if (hybridIndicators.some(indicator => 
      lowerLocation.includes(indicator)
    )) {
      return true;
    }
  }
  
  // Check description
  if (description) {
    const lowerDesc = description.toLowerCase();
    
    // Check for hybrid indicators
    if (hybridIndicators.some(indicator => 
      lowerDesc.includes(indicator)
    )) {
      return true;
    }
    
    // Check for partial remote percentages
    if (/\b[1-9][0-9]%\s+remote\b/i.test(description) && 
        !/\b100%\s+remote\b/i.test(description)) {
      return true;
    }
    
    // Check for days per week remote
    if (/\b[1-4]\s+days?\s+(per\s+week\s+)?(remote|from\s+home)\b/i.test(description)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Check if job is in-person
 */
function isInPersonJob(location: string, description?: string): boolean {
  if (!location && !description) return false;
  
  const inPersonIndicators = [
    "in person",
    "in-person",
    "on site",
    "on-site",
    "onsite",
    "in office",
    "in-office",
    "at the office",
    "no remote",
    "requires you to be in the office",
    "must be in office",
    "must work in office",
    "must be on site",
    "must work on site"
  ];
  
  // Check location
  if (location) {
    const lowerLocation = location.toLowerCase();
    
    // Check for in-person indicators
    if (inPersonIndicators.some(indicator => 
      lowerLocation.includes(indicator)
    )) {
      return true;
    }
  }
  
  // Check description
  if (description) {
    const lowerDesc = description.toLowerCase();
    
    // Check for in-person indicators
    if (inPersonIndicators.some(indicator => 
      lowerDesc.includes(indicator)
    )) {
      return true;
    }
    
    // Check for 0% remote
    if (/\b0%\s+remote\b/i.test(description)) {
      return true;
    }
  }
  
  return false;
}
