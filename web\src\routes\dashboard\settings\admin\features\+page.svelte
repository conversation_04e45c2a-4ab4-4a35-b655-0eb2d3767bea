<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import * as SelectPrimitive from '$lib/components/ui/select/index.js';
  import SEO from '$components/shared/SEO.svelte';
  import { toast } from 'svelte-sonner';
  import { ChartBar, Loader2, Briefcase, Layers, Plus } from 'lucide-svelte';

  // Import tab components
  import AddFeatureTab from './components/AddFeatureTab.svelte';
  import ManageFeaturesTab from './components/ManageFeaturesTab.svelte';
  import { FEATURE_TYPES } from '$lib/data/feature-types';

  // Active tab state
  let activeTab = $state('manage');

  // State for seeding features
  let isSeeding = $state(false);
  let currentSeedingType = $state('');
  let selectedFeatureType = $state<string | undefined>(undefined);

  // Function to handle feature type selection and seed features
  async function handleFeatureTypeChange(value: string) {
    if (isSeeding) return;

    selectedFeatureType = value;

    const featureType = FEATURE_TYPES.find((ft) => ft.id === value);
    if (!featureType) {
      toast.error('Invalid feature type selected');
      return;
    }

    try {
      isSeeding = true;
      currentSeedingType = featureType.id;

      toast.loading(`Seeding ${featureType.name}...`, {
        id: 'seeding-toast',
      });

      const response = await fetch(featureType.endpoint, {
        method: 'POST',
        credentials: 'include',
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `Failed to seed ${featureType.name}: ${response.status}`);
      }

      const result = await response.json();

      toast.success(`${featureType.name} seeded`, {
        id: 'seeding-toast',
        description: result.message || `${featureType.name} have been added to the system`,
        duration: 3000,
      });

      // Dispatch event to reload features in the ManageFeaturesTab
      window.dispatchEvent(new CustomEvent('featureAdded'));
    } catch (err) {
      console.error(`Error seeding ${featureType.name}:`, err);
      toast.error(`Failed to seed ${featureType.name}`, {
        id: 'seeding-toast',
        description: err.message,
        duration: 5000,
      });
    } finally {
      isSeeding = false;
      currentSeedingType = '';
      selectedFeatureType = undefined;
    }
  }
</script>

<SEO title="Feature Management" />

<div class="flex items-center justify-between gap-1 border-b px-4 py-2">
  <h2 class="text-lg font-semibold">Feature Management</h2>
  <div class="space-y-4">
    <div class="flex gap-2">
      <Button
        variant="outline"
        onclick={() => (window.location.href = '/dashboard/settings/admin/plans')}>
        Back to Plans
      </Button>

      <div class="flex items-center gap-2">
        {#if isSeeding}
          <div class="flex items-center gap-2 rounded-md border px-3 py-2">
            <Loader2 class="h-4 w-4 animate-spin" />
            <span>Seeding {currentSeedingType}...</span>
          </div>
        {:else}
          <SelectPrimitive.Root type="single" onValueChange={handleFeatureTypeChange}>
            <SelectPrimitive.Trigger class="w-[200px]">
              <SelectPrimitive.Value placeholder="Seed Features..." />
            </SelectPrimitive.Trigger>
            <SelectPrimitive.Content class="w-[200px]">
              {#each FEATURE_TYPES as featureType}
                <SelectPrimitive.Item value={featureType.id}>
                  <div class="flex items-center gap-2">
                    {#if featureType.icon === 'briefcase'}
                      <Briefcase class="h-4 w-4" />
                    {:else if featureType.icon === 'chart-bar'}
                      <ChartBar class="h-4 w-4" />
                    {:else if featureType.icon === 'layers'}
                      <Layers class="h-4 w-4" />
                    {:else}
                      <Plus class="h-4 w-4" />
                    {/if}
                    <span>{featureType.name}</span>
                  </div>
                </SelectPrimitive.Item>
              {/each}
            </SelectPrimitive.Content>
          </SelectPrimitive.Root>
        {/if}
      </div>
    </div>
  </div>
</div>

<Tabs.Root value={activeTab} class="w-full" onValueChange={(value) => (activeTab = value)}>
  <Card.Content class="border-border border-b p-0">
    <Tabs.List class="flex flex-row gap-2 divide-x">
      <Tabs.Trigger value="manage" class="flex-1 rounded-none border-none"
        >Manage Features</Tabs.Trigger>
      <Tabs.Trigger value="add" class="flex-1">Add New Feature</Tabs.Trigger>
    </Tabs.List>
  </Card.Content>
  <!-- Manage Features Tab -->
  <Tabs.Content value="manage" class="p-4">
    <ManageFeaturesTab />
  </Tabs.Content>

  <!-- Add New Feature Tab -->
  <Tabs.Content value="add" class="p-4">
    <AddFeatureTab />
  </Tabs.Content>
</Tabs.Root>
