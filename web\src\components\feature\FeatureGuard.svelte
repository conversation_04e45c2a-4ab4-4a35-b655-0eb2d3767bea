<script lang="ts">
  import { isFeatureEnabled } from '$lib/stores/store';
  import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
  import { AlertTriangle } from 'lucide-svelte';

  // Props
  const {
    featureId,
    fallback = true,
    children,
    fallbackContent,
  } = $props<{
    featureId: string;
    fallback?: boolean; // If true, show fallback content when feature is disabled
    children: any;
    fallbackContent?: any;
  }>();

  // Check if the feature is enabled
  const isEnabled = isFeatureEnabled(featureId);
</script>

{#if isEnabled}
  {@render children()}
{:else if fallback}
  {#if fallbackContent}
    {@render fallbackContent()}
  {:else}
    <Alert variant="destructive" class="mb-4">
      <AlertTriangle class="h-4 w-4" />
      <AlertTitle>Feature Unavailable</AlertTitle>
      <AlertDescription>This feature is not available on your current plan.</AlertDescription>
    </Alert>
  {/if}
{/if}
