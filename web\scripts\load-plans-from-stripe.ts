/**
 * Load Plans from Stripe Script
 *
 * This script loads plans from <PERSON>e and saves them to the database.
 * It can be run with: npx ts-node scripts/load-plans-from-stripe.ts
 */

import { PrismaClient } from '@prisma/client';
import { stripe } from '../src/lib/server/stripe';
import { FeatureAccessLevel } from '../src/lib/models/features/features';

const prisma = new PrismaClient();

/**
 * Add default features to a plan
 */
async function addDefaultFeaturesToPlan(planId: string) {
  try {
    console.log(`Adding default features to plan ${planId}...`);

    // Default core features that all plans should have
    const defaultFeatures = [
      {
        featureId: 'dashboard',
        accessLevel: FeatureAccessLevel.Included,
      },
      {
        featureId: 'profile',
        accessLevel: FeatureAccessLevel.Included,
      },
    ];

    // Add each default feature to the plan
    for (const feature of defaultFeatures) {
      // Check if the feature already exists for this plan
      const existingFeature = await prisma.planFeature.findFirst({
        where: {
          planId: planId,
          featureId: feature.featureId,
        },
      });

      if (!existingFeature) {
        // Create the plan feature
        await prisma.planFeature.create({
          data: {
            id: `${feature.featureId}`,
            planId: planId,
            featureId: feature.featureId,
            accessLevel: feature.accessLevel,
            updatedAt: new Date(),
          },
        });

        console.log(`Added feature ${feature.featureId} to plan ${planId}`);
      } else {
        console.log(`Feature ${feature.featureId} already exists for plan ${planId}`);
      }
    }

    console.log(`Successfully added default features to plan ${planId}`);
  } catch (error) {
    console.error(`Error adding default features to plan ${planId}:`, error);
  }
}

/**
 * Load plans from Stripe and save them to the database
 */
async function loadPlansFromStripe() {
  try {
    console.log('Loading plans from Stripe...');

    // Get all active products from Stripe
    const products = await stripe.products.list({
      active: true,
      expand: ['data.default_price'],
    });

    console.log(`Found ${products.data.length} active products in Stripe`);

    // Filter products that have plan_id in metadata
    const planProducts = products.data.filter((product) => product.metadata.plan_id);

    console.log(`Found ${planProducts.length} products with plan_id metadata`);

    let count = 0;

    // Process each plan product
    for (const product of planProducts) {
      const planId = product.metadata.plan_id;
      const section = product.metadata.section || 'pro';

      // Get all prices for this product
      const prices = await stripe.prices.list({
        product: product.id,
        active: true,
      });

      // Find monthly and yearly prices
      const monthlyPrice = prices.data.find(
        (price) => price.recurring?.interval === 'month' && price.recurring?.interval_count === 1
      );

      const yearlyPrice = prices.data.find(
        (price) => price.recurring?.interval === 'year' && price.recurring?.interval_count === 1
      );

      if (!monthlyPrice && !yearlyPrice) {
        console.log(`Skipping product ${product.id} (${product.name}) - no valid prices found`);
        continue;
      }

      // Check if plan already exists in database
      const existingPlan = await prisma.plan.findUnique({
        where: { id: planId },
      });

      const planData = {
        name: product.name,
        description: product.description || '',
        section: section,
        monthlyPrice: monthlyPrice?.unit_amount || 0,
        annualPrice: yearlyPrice?.unit_amount || 0,
        stripePriceMonthlyId: monthlyPrice?.id || null,
        stripePriceYearlyId: yearlyPrice?.id || null,
        popular: product.metadata.popular === 'true',
        updatedAt: new Date(),
      };

      if (existingPlan) {
        // Update existing plan
        await prisma.plan.update({
          where: { id: planId },
          data: planData,
        });

        console.log(`Updated plan ${planId} (${product.name}) in database`);
      } else {
        // Create new plan
        await prisma.plan.create({
          data: {
            id: planId,
            ...planData,
          },
        });

        // Add default features for new plans
        await addDefaultFeaturesToPlan(planId);

        console.log(`Created plan ${planId} (${product.name}) in database`);
      }

      count++;
    }

    console.log(`Successfully loaded ${count} plans from Stripe`);
    return count;
  } catch (error) {
    console.error('Error loading plans from Stripe:', error);
    throw error;
  }
}

/**
 * Main function to run the load script
 */
async function main() {
  try {
    console.log('Starting Stripe load script...');

    // Load plans from Stripe
    const count = await loadPlansFromStripe();

    console.log(`Successfully loaded ${count} plans from Stripe`);
    console.log('Stripe load script completed!');
  } catch (error) {
    console.error('Error running Stripe load script:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the main function
main();
