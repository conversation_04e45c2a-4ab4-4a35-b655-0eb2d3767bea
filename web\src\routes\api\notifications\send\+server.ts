// src/routes/api/notifications/send/+server.ts
import { json } from '@sveltejs/kit';
import { getUserFromToken } from '$lib/server/auth';
import { prisma } from '$lib/server/prisma';
import type { RequestHand<PERSON> } from './$types';
import {
  sendNotificationToUser,
  sendGlobalNotification,
  sendJobNotification,
  NotificationType,
  NotificationPriority,
  type NotificationData,
} from '$lib/server/notification-triggers/notification-service';

/**
 * Send a notification via the notification service
 *
 * This endpoint allows sending a notification to a specific user or globally.
 * It requires authentication and the user must have the appropriate permissions.
 */
export const POST: RequestHandler = async ({ cookies, request }) => {
  // Authenticate the user
  const user = await getUserFromToken(cookies);

  if (!user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const data = await request.json();

    // Validate required fields
    if (!data.title || !data.message) {
      return json({ error: 'Title and message are required' }, { status: 400 });
    }

    // Prepare notification data
    const notificationData: NotificationData = {
      title: data.title,
      message: data.message,
      url: data.url,
      type: data.type || NotificationType.INFO,
      priority: data.priority || NotificationPriority.MEDIUM,
      metadata: data.metadata,
      expiresAt: data.expiresAt ? new Date(data.expiresAt) : undefined,
    };

    // Check if this is a global notification
    if (data.global) {
      // Only admins can send global notifications
      const userData = await prisma.user.findUnique({
        where: { id: user.id },
        select: { isAdmin: true, role: true },
      });

      if (!userData || (!userData.isAdmin && userData.role !== 'admin')) {
        return json({ error: 'Unauthorized to send global notifications' }, { status: 403 });
      }

      // Send global notification
      const result = await sendGlobalNotification(notificationData);

      if (result) {
        return json({
          success: true,
          message: 'Global notification sent successfully',
        });
      } else {
        return json({ error: 'Failed to send global notification' }, { status: 500 });
      }
    }

    // Check if this is a job notification
    if (data.type === 'job') {
      // If userId is provided, send to that user
      if (data.userId) {
        const result = await sendJobNotification(data.userId, notificationData);

        if (result) {
          return json({
            success: true,
            message: 'Job notification sent successfully',
          });
        } else {
          return json({ error: 'Failed to send job notification' }, { status: 500 });
        }
      }
    }

    // If userId is provided, send to that user
    if (data.userId) {
      const result = await sendNotificationToUser(data.userId, notificationData);

      if (result) {
        return json({
          success: true,
          message: 'Notification sent successfully',
        });
      } else {
        return json({ error: 'Failed to send notification' }, { status: 500 });
      }
    }

    // If we get here, send to the current user
    const result = await sendNotificationToUser(user.id, notificationData);

    if (result) {
      return json({
        success: true,
        message: 'Notification sent successfully',
      });
    } else {
      return json({ error: 'Failed to send notification' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error sending notification:', error);
    return json({ error: 'Failed to send notification' }, { status: 500 });
  }
};
