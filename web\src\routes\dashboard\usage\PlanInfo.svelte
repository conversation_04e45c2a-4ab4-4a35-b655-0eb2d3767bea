<script lang="ts">
  import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Badge } from '$lib/components/ui/badge';
  import { Button } from '$lib/components/ui/button';
  import { ArrowUpRight } from 'lucide-svelte';
  import type { PlanTier } from '$lib/models/features/types';

  export let plan: PlanTier | null = null;
  
  // Format price for display
  function formatPrice(price: number | undefined): string {
    if (price === undefined) return 'N/A';
    return `$${price.toFixed(2)}`;
  }
</script>

<Card>
  <CardHeader>
    <div class="flex items-center justify-between">
      <div>
        <CardTitle>Current Plan</CardTitle>
        <CardDescription>Your subscription plan details</CardDescription>
      </div>
      {#if plan?.popular}
        <Badge variant="outline" class="bg-primary text-primary-foreground">Popular</Badge>
      {/if}
    </div>
  </CardHeader>
  <CardContent>
    {#if plan}
      <div class="space-y-4">
        <div>
          <h3 class="text-lg font-medium">{plan.name}</h3>
          <p class="text-muted-foreground text-sm">{plan.description || ''}</p>
        </div>
        
        <div class="grid grid-cols-2 gap-4">
          <div>
            <p class="text-sm font-medium">Monthly Price</p>
            <p class="text-muted-foreground text-sm">{formatPrice(plan.monthlyPrice)}</p>
          </div>
          <div>
            <p class="text-sm font-medium">Annual Price</p>
            <p class="text-muted-foreground text-sm">{formatPrice(plan.annualPrice)}</p>
          </div>
        </div>
        
        <Button variant="outline" size="sm" class="w-full" href="/pricing">
          View Plans
          <ArrowUpRight class="ml-2 h-4 w-4" />
        </Button>
      </div>
    {:else}
      <div class="text-center py-4">
        <p class="text-muted-foreground">No plan information available</p>
        <Button variant="outline" size="sm" class="mt-4" href="/pricing">
          View Plans
          <ArrowUpRight class="ml-2 h-4 w-4" />
        </Button>
      </div>
    {/if}
  </CardContent>
</Card>
