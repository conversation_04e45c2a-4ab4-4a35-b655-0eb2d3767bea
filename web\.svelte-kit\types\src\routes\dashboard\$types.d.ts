import type * as Kit from '@sveltejs/kit';

type Expand<T> = T extends infer O ? { [K in keyof O]: O[K] } : never;
// @ts-ignore
type MatcherParam<M> = M extends (param : string) => param is infer U ? U extends string ? U : string : string;
type RouteParams = {  };
type RouteId = '/dashboard';
type MaybeWithVoid<T> = {} extends T ? T | void : T;
export type RequiredKeys<T> = { [K in keyof T]-?: {} extends { [P in K]: T[K] } ? never : K; }[keyof T];
type OutputDataShape<T> = MaybeWithVoid<Omit<App.PageData, RequiredKeys<T>> & Partial<Pick<App.PageData, keyof T & keyof App.PageData>> & Record<string, any>>
type EnsureDefined<T> = T extends null | undefined ? {} : T;
type OptionalUnion<U extends Record<string, any>, A extends keyof U = U extends U ? keyof U : never> = U extends unknown ? { [P in Exclude<A, keyof U>]?: never } & U : never;
export type Snapshot<T = any> = Kit.Snapshot<T>;
type PageServerParentData = Omit<EnsureDefined<import('../$types.js').LayoutServerData>, keyof LayoutServerData> & EnsureDefined<LayoutServerData>;
type PageParentData = Omit<EnsureDefined<import('../$types.js').LayoutData>, keyof LayoutData> & EnsureDefined<LayoutData>;
type LayoutRouteId = RouteId | "/dashboard" | "/dashboard/automation" | "/dashboard/automation/[id]" | "/dashboard/builder" | "/dashboard/builder/superform/[id]" | "/dashboard/builder/[id]" | "/dashboard/documents" | "/dashboard/documents/[id]" | "/dashboard/documents/[id]/ats" | "/dashboard/features" | "/dashboard/jobs" | "/dashboard/jobs/[id]" | "/dashboard/matches" | "/dashboard/notifications" | "/dashboard/resumes" | "/dashboard/resumes/[id]" | "/dashboard/resumes/[id]/optimize" | "/dashboard/settings" | "/dashboard/settings/account" | "/dashboard/settings/admin" | "/dashboard/settings/admin/email" | "/dashboard/settings/admin/email/analytics" | "/dashboard/settings/admin/email/audiences" | "/dashboard/settings/admin/email/broadcast" | "/dashboard/settings/admin/email/queue" | "/dashboard/settings/admin/feature-usage" | "/dashboard/settings/admin/features" | "/dashboard/settings/admin/maintenance" | "/dashboard/settings/admin/notifications" | "/dashboard/settings/admin/plans" | "/dashboard/settings/admin/seed-features" | "/dashboard/settings/admin/subscriptions" | "/dashboard/settings/analysis" | "/dashboard/settings/billing" | "/dashboard/settings/email" | "/dashboard/settings/general" | "/dashboard/settings/interview-coach" | "/dashboard/settings/make-admin" | "/dashboard/settings/notifications" | "/dashboard/settings/profile" | "/dashboard/settings/profile/[id]" | "/dashboard/settings/referrals" | "/dashboard/settings/security" | "/dashboard/settings/team" | "/dashboard/settings/usage" | "/dashboard/tracker" | "/dashboard/usage"
type LayoutParams = RouteParams & { id?: string }
type LayoutParentData = EnsureDefined<import('../$types.js').LayoutData>;

export type PageServerLoad<OutputData extends OutputDataShape<PageServerParentData> = OutputDataShape<PageServerParentData>> = Kit.ServerLoad<RouteParams, PageServerParentData, OutputData, RouteId>;
export type PageServerLoadEvent = Parameters<PageServerLoad>[0];
export type ActionData = unknown;
export type PageServerData = Expand<OptionalUnion<EnsureDefined<Kit.LoadProperties<Awaited<ReturnType<typeof import('../../../../../src/routes/dashboard/+page.server.js').load>>>>>>;
export type PageData = Expand<Omit<PageParentData, keyof PageServerData> & EnsureDefined<PageServerData>>;
export type Action<OutputData extends Record<string, any> | void = Record<string, any> | void> = Kit.Action<RouteParams, OutputData, RouteId>
export type Actions<OutputData extends Record<string, any> | void = Record<string, any> | void> = Kit.Actions<RouteParams, OutputData, RouteId>
export type PageProps = { data: PageData; form: ActionData }
export type LayoutServerData = null;
export type LayoutData = Expand<LayoutParentData>;
export type LayoutProps = { data: LayoutData; children: import("svelte").Snippet }
export type RequestEvent = Kit.RequestEvent<RouteParams, RouteId>;