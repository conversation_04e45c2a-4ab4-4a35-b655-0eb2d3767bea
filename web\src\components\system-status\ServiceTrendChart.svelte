<script lang="ts">
  // Import necessary libraries
  import { onMount } from 'svelte';

  // Props
  export let historyData: any[] = [];
  export let metric: string = 'successRate'; // Default metric to display
  export let title: string = 'Last 30 Days';
  export let height: number = 100;

  // Local state
  let chartElement: HTMLCanvasElement;

  // Generate mock data for 30 days if not provided
  function generateMockData() {
    if (!historyData || historyData.length === 0) {
      const mockData = [];
      const today = new Date();

      for (let i = 29; i >= 0; i--) {
        const date = new Date();
        date.setDate(today.getDate() - i);

        // Generate random status with higher probability of operational
        const rand = Math.random();
        let status = 'operational';
        if (rand > 0.9) status = 'outage';
        else if (rand > 0.8) status = 'degraded';
        else if (rand > 0.7) status = 'maintenance';

        mockData.push({
          date: date.toISOString(),
          status,
          [metric]:
            status === 'operational'
              ? 100
              : status === 'degraded'
                ? 80
                : status === 'maintenance'
                  ? 60
                  : 0,
        });
      }

      return mockData;
    }

    return historyData;
  }

  // Format date for display
  function formatDate(dateStr: string): string {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }

  // Get status color
  function getStatusColor(status: string): string {
    switch (status) {
      case 'operational':
        return '#4CAF50'; // Green
      case 'degraded':
        return '#FFC107'; // Yellow
      case 'outage':
        return '#F44336'; // Red
      case 'maintenance':
        return '#2196F3'; // Blue
      default:
        return '#9E9E9E'; // Gray
    }
  }

  // Draw the chart
  function drawChart() {
    if (!chartElement) return;

    const data = generateMockData();
    if (data.length === 0) return;

    const ctx = chartElement.getContext('2d');
    if (!ctx) return;

    // Clear previous chart
    ctx.clearRect(0, 0, chartElement.width, chartElement.height);

    // Set dimensions
    const width = chartElement.width;
    const chartHeight = chartElement.height;

    // Calculate bar width and spacing
    const barWidth = (width / 30) * 0.8; // For 30 days
    const spacing = (width / 30) * 0.2;

    // Draw bars for each day
    data.forEach((day, i) => {
      const x = i * (barWidth + spacing) + spacing / 2;

      // Draw bar with color based on status
      ctx.fillStyle = getStatusColor(day.status);

      // Full height bar with slight spacing at bottom
      const barHeight = chartHeight - 5;
      ctx.fillRect(x, 0, barWidth, barHeight);
    });

    // Add grid lines (optional)
    ctx.strokeStyle = '#f0f0f0';
    ctx.lineWidth = 1;

    // Draw horizontal grid line
    ctx.beginPath();
    ctx.moveTo(0, chartHeight / 2);
    ctx.lineTo(width, chartHeight / 2);
    ctx.stroke();
  }

  // Handle window resize
  function handleResize() {
    if (chartElement) {
      // Set canvas dimensions based on container
      chartElement.width = chartElement.parentElement?.clientWidth || 300;
      chartElement.height = height;
      drawChart();
    }
  }

  // Initialize chart on mount
  onMount(() => {
    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  });

  // Redraw chart when data changes
  $: if (historyData) {
    // Wait for next tick to ensure DOM is updated
    setTimeout(drawChart, 0);
  }
</script>

<style>
  .trend-chart-container {
    width: 100%;
  }

  .chart-wrapper {
    width: 100%;
    position: relative;
  }

  canvas {
    display: block;
    width: 100%;
    height: 100%;
  }
</style>

<div class="trend-chart-container">
  <h4 class="mb-2 text-sm font-medium">{title}</h4>
  <div class="chart-wrapper" style="height: {height}px;">
    <canvas bind:this={chartElement}></canvas>
  </div>
  {#if historyData && historyData.length > 0}
    <div class="text-muted-foreground mt-1 flex justify-between text-xs">
      <span>{formatDate(historyData[0].date)}</span>
      <span>{formatDate(historyData[historyData.length - 1].date)}</span>
    </div>
  {/if}
</div>
