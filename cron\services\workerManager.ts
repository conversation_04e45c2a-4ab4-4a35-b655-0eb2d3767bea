import { WorkerPool } from "../workers/workerPool";
import { Worker, JobBatch } from "../utils/types";
import { logger } from "../utils/logger";

export class WorkerManager {
  private workerPool: WorkerPool;

  constructor(workerPool: WorkerPool) {
    this.workerPool = workerPool;
  }

  /**
   * Gets a worker for a specific batch
   */
  async getWorkerForBatch(batch: JobBatch): Promise<Worker | null> {
    try {
      // Get the specific worker assigned to this batch
      const worker = await this.workerPool.getSpecificWorker(batch.workerIndex);

      if (!worker) {
        logger.error(`❌ Failed to get a worker for batch ${batch.batchId}`);
        return null;
      }

      logger.info(`✅ Got worker #${worker.id} for batch ${batch.batchId}`);

      // Update worker state
      worker.currentBatch = batch;
      if (batch.occupations.length > 0) {
        worker.currentJob = {
          title: batch.occupations[0].title,
          id: batch.occupations[0].id,
        };
      }

      return worker;
    } catch (error) {
      logger.error(`Error getting worker for batch ${batch.batchId}:`, error);
      return null;
    }
  }

  /**
   * Releases a worker back to the pool
   */
  async releaseWorker(worker: Worker): Promise<void> {
    try {
      // Clear worker state
      worker.currentBatch = undefined;
      worker.currentJob = undefined;

      // Any additional cleanup needed

      logger.info(`✅ Released worker #${worker.id} back to the pool`);
    } catch (error) {
      logger.error(`Error releasing worker #${worker.id}:`, error);
    }
  }

  /**
   * Rotates proxy for a worker
   */
  async rotateProxyForWorker(worker: Worker): Promise<any> {
    logger.info(`🔄 Rotating proxy for worker #${worker.id}...`);
    try {
      return await this.workerPool.rotateProxyForWorker(worker);
    } catch (error) {
      logger.error(`Error rotating proxy for worker #${worker.id}:`, error);
      return false;
    }
  }
}
