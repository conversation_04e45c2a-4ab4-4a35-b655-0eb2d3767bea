import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { prisma } from '$lib/server/prisma';
import { SERVICE_FEATURES } from '../service-features';
import { verifySessionToken } from '$lib/server/auth';

/**
 * Seed service features into the database
 * POST /api/admin/features/seed-service
 */
export const POST: RequestHandler = async ({ cookies }) => {
  // Check if user is authenticated and is an admin
  const token = cookies.get('auth_token');
  if (!token) return new Response('Unauthorized', { status: 401 });

  const userData = await verifySessionToken(token);
  if (!userData?.id) return new Response('Unauthorized', { status: 401 });

  // Check if user is an admin
  const user = await prisma.user.findUnique({
    where: { id: userData.id },
    select: { isAdmin: true, role: true },
  });

  // Allow access if either isAdmin is true OR role is 'admin'
  if (!user || (!user.isAdmin && user.role !== 'admin')) {
    return new Response('Unauthorized - Admin access required', { status: 403 });
  }

  try {
    const results = {
      features: 0,
      updated: 0,
      limits: 0,
      updatedLimits: 0,
      errors: 0,
    };

    console.log(`Starting to seed ${SERVICE_FEATURES.length} service features...`);

    // Process features without a transaction to avoid timeout issues
    // Seed service features
    for (const feature of SERVICE_FEATURES) {
      try {
        console.log(`Processing feature: ${feature.id} - ${feature.name}`);

        // Check if feature exists
        const existingFeature = await prisma.feature.findUnique({
          where: { id: feature.id },
        });

        // Prepare feature data
        const featureData = {
          name: feature.name,
          description: feature.description ?? '',
          category: feature.category ?? 'general',
          icon: feature.icon ?? null,
          beta: feature.beta ?? false,
          updatedAt: new Date(),
        };

        // Create or update feature
        if (!existingFeature) {
          await prisma.feature.create({
            data: {
              id: feature.id,
              ...featureData,
            },
          });
          console.log(`Created new feature: ${feature.name}`);
          results.features++;
        } else {
          await prisma.feature.update({
            where: { id: feature.id },
            data: featureData,
          });
          console.log(`Updated existing feature: ${feature.name}`);
          results.updated++;
        }

        // Process feature limits if any
        if (feature.limits && Array.isArray(feature.limits) && feature.limits.length > 0) {
          console.log(`Processing ${feature.limits.length} limits for feature ${feature.id}`);

          for (const limit of feature.limits) {
            if (!limit?.id) {
              console.warn(`Skipping invalid limit for feature ${feature.id}`);
              continue;
            }

            // Check if limit exists
            const existingLimit = await prisma.featureLimit.findUnique({
              where: { id: limit.id },
            });

            // Prepare limit data
            const limitData = {
              name: limit.name,
              description: limit.description ?? '',
              defaultValue: (limit.defaultValue ?? 10).toString(),
              type: limit.type,
              unit: limit.unit ?? null,
              resetDay: limit.resetDay ?? null,
            };

            // Create or update limit
            if (!existingLimit) {
              await prisma.featureLimit.create({
                data: {
                  id: limit.id,
                  featureId: feature.id,
                  ...limitData,
                },
              });
              console.log(`Created new limit: ${limit.name} for feature ${feature.name}`);
              results.limits++;
            } else {
              await prisma.featureLimit.update({
                where: { id: limit.id },
                data: limitData,
              });
              console.log(`Updated existing limit: ${limit.name} for feature ${feature.name}`);
              results.updatedLimits++;
            }
          }
        } else {
          console.log(`No limits defined for feature ${feature.id}`);
        }
      } catch (error) {
        console.error(`Error processing feature ${feature.name}:`, error);
        if (error instanceof Error) {
          console.error('Error details:', error.message);
          console.error('Stack trace:', error.stack);
        }
        results.errors++;
        // Don't throw here to allow other features to be processed
      }
    }

    console.log('Service features seeding completed successfully');

    return json({
      success: true,
      message: `Seeded ${results.features} new service features, updated ${results.updated} existing features, added ${results.limits} limits, updated ${results.updatedLimits} limits`,
      results,
    });
  } catch (error) {
    console.error('Error seeding service features:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Stack trace:', error.stack);
    }
    return json(
      {
        success: false,
        error: 'Failed to seed service features',
        message: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
};
