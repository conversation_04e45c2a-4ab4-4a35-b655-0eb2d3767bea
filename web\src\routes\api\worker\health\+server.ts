// File: web/src/routes/api/worker/health/+server.ts
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getRedisClient } from '$lib/server/redis';

// Define worker types
const WORKER_TYPES = [
  'resume-parsing',
  'resume-optimization',
  'search',
  'ats-analysis',
  'job-specific-analysis',
  'email',
  'automation',
];

// Define worker status types
const WORKER_STATUS_TYPES = ['healthy', 'degraded', 'unhealthy', 'unknown'];

/**
 * Validate worker health data
 */
function validateWorkerHealth(body: any): { valid: boolean; error?: string } {
  const { workerType, status, metrics } = body;

  // Validate required fields
  if (!workerType || !metrics) {
    return {
      valid: false,
      error: 'Missing required fields: workerType and metrics are required',
    };
  }

  // Validate worker type
  if (!WORKER_TYPES.includes(workerType)) {
    return {
      valid: false,
      error: `Invalid worker type: ${workerType}`,
    };
  }

  // Validate status
  const workerStatus = status ?? 'healthy';
  if (!WORKER_STATUS_TYPES.includes(workerStatus)) {
    return {
      valid: false,
      error: `Invalid status: ${status}`,
    };
  }

  // Validate metrics
  const requiredMetricFields = [
    'cpu',
    'memory',
    'queueSize',
    'processingCount',
    'responseTime',
    'errorRate',
    'successRate',
    'capacity',
  ];

  for (const field of requiredMetricFields) {
    if (metrics[field] === undefined) {
      return {
        valid: false,
        error: `Missing required metric: ${field}`,
      };
    }
  }

  return { valid: true };
}

/**
 * Update worker health in Redis
 */
async function updateWorkerHealthInRedis(
  redis: any,
  workerType: string,
  status: string,
  metrics: any
): Promise<boolean> {
  try {
    // Calculate overall health based on metrics
    const healthy =
      metrics.cpu < 90 && metrics.memory < 90 && metrics.errorRate < 10 && metrics.capacity > 20;

    // Update health data
    const healthData = {
      status,
      healthy,
      lastHeartbeat: new Date().toISOString(),
    };

    // Store health data in Redis
    await redis.hset('worker:health', workerType, JSON.stringify(healthData));

    // Store metrics data in Redis
    await redis.hset('worker:metrics', workerType, JSON.stringify(metrics));

    return true;
  } catch (error) {
    console.error(`Error updating health for worker ${workerType}:`, error);
    return false;
  }
}

/**
 * POST handler for workers to report their health status
 *
 * This endpoint allows workers to report their health metrics to the central registry.
 * It's designed to be called periodically by workers as a heartbeat mechanism.
 */
export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    // Get the user from locals (only require auth in production)
    const user = locals.user;
    const isProduction = process.env.NODE_ENV === 'production';

    // In development mode, allow access without authentication
    if (isProduction && !user) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Parse the request body
    const body = await request.json();

    // Validate worker health data
    const validation = validateWorkerHealth(body);
    if (!validation.valid) {
      return json(
        {
          success: false,
          error: validation.error,
        },
        { status: 400 }
      );
    }

    // Get Redis client
    const redis = await getRedisClient();
    if (!redis) {
      return json(
        {
          success: false,
          error: 'Redis client not available',
        },
        { status: 500 }
      );
    }

    // Update worker health
    const { workerType, status = 'healthy', metrics } = body;
    const result = await updateWorkerHealthInRedis(redis, workerType, status, metrics);

    if (!result) {
      return json(
        {
          success: false,
          error: 'Failed to update worker health',
        },
        { status: 500 }
      );
    }

    return json({
      success: true,
      message: `Health status updated for worker: ${workerType}`,
    });
  } catch (error) {
    console.error('Error updating worker health:', error);
    return json(
      {
        success: false,
        error: 'Failed to update worker health',
        details: String(error),
      },
      { status: 500 }
    );
  }
};
