// cron/scripts/saveLinkedInSession.ts

import { chromium } from "playwright";
import fs from "fs/promises";
import { logger } from "../utils/logger";

// Use a more specific path in a dedicated directory to avoid clutter
const storagePath = "./auth/linkedin-session.json";

async function saveLinkedInSession() {
  // This script specifically needs to be non-headless for manual login
  // But we'll still respect production environment
  const isProduction = process.env.NODE_ENV === "production";

  // Only force headless in production, but allow manual override for this specific script
  // since it requires user interaction
  const browser = await chromium.launch({
    headless: isProduction && process.env.FORCE_HEADLESS === "true",
    args: ["--no-sandbox"],
  });

  const context = await browser.newContext({
    userAgent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    viewport: { width: 1280, height: 800 },
    locale: "en-US",
    extraHTTPHeaders: {
      "accept-language": "en-US,en;q=0.9",
      "sec-ch-ua":
        '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
      "sec-ch-ua-platform": '"Windows"',
      "sec-ch-ua-mobile": "?0",
      "upgrade-insecure-requests": "1",
    },
  });

  const page = await context.newPage();

  logger.info("👤 Please log in to LinkedIn manually...");
  await page.goto("https://www.linkedin.com/login", {
    waitUntil: "domcontentloaded",
  });

  // Wait until redirected to the feed page after login
  await page.waitForNavigation({ url: /.*linkedin.com\/feed.*/, timeout: 0 });

  // Save session to disk - this is one file we actually need to keep on disk
  // since it's used for authentication and is small
  await fs.mkdir("./auth", { recursive: true });

  // Get the storage state
  const storageState = await context.storageState();

  // Save only essential cookies to reduce file size
  const essentialCookies = storageState.cookies.filter(
    (cookie) =>
      cookie.name.includes("li_at") ||
      cookie.name.includes("JSESSIONID") ||
      cookie.name.includes("li_rm")
  );

  // Create a minimal storage state object
  const minimalStorageState = {
    cookies: essentialCookies,
    origins: storageState.origins,
  };

  // Save to disk
  await fs.writeFile(storagePath, JSON.stringify(minimalStorageState, null, 2));
  logger.info(
    `✅ LinkedIn session saved to ${storagePath} (${essentialCookies.length} essential cookies)`
  );
  logger.info(
    `💾 File size reduced by keeping only essential authentication cookies`
  );

  await browser.close();
}

await saveLinkedInSession();
