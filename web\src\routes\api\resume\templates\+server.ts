// src/routes/api/resume/templates/+server.ts
import { json } from '@sveltejs/kit';
import { logger } from '$lib/server/logger';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async () => {
  try {
    // In a real implementation, you would fetch templates from a database
    // For now, we'll return mock data
    const templates = [
      { id: 'modern', name: 'Modern', thumbnail: '/templates/modern.png' },
      { id: 'professional', name: 'Professional', thumbnail: '/templates/professional.png' },
      { id: 'creative', name: 'Creative', thumbnail: '/templates/creative.png' },
      { id: 'simple', name: 'Simple', thumbnail: '/templates/simple.png' },
    ];
    
    return json({
      templates,
      count: templates.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error fetching resume templates:', error);
    return json(
      {
        error: 'Failed to fetch resume templates',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
};
