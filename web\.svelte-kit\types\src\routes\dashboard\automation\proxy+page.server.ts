// @ts-nocheck
// File: src/routes/dashboard/automation/+page.server.ts
import { redirect, fail } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { getUserFromToken } from '$lib/server/auth.js';
import { calculateProfileCompletion } from '$lib/utils/profileHelpers';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import type { PageServerLoad, Actions } from '../$types.js';
import { automationFormSchema } from '$lib/validators/automation.js';

// Using the shared Prisma client from $lib/server/prisma

export const load = async ({ locals }) => {
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  locals.user = user;

  // Initialize the automation form
  const form = await superValidate(zod(automationFormSchema));

  // Get profiles with associated resumes
  const profiles = await prisma.profile.findMany({
    where: {
      OR: [
        { userId: user.id },
        {
          team: {
            members: {
              some: { userId: user.id },
            },
          },
        },
      ],
    },
    include: {
      data: true,
      team: true,
      // Include documents of type resume associated with this profile
      documents: {
        where: {
          type: 'resume',
        },
      },
    },
  });

  // Get real automation runs from the database
  const automationRuns = await prisma.automationRun.findMany({
    where: {
      OR: [
        { userId: user.id },
        {
          profile: {
            team: {
              members: {
                some: { userId: user.id },
              },
            },
          },
        },
      ],
    },
    include: {
      profile: {
        include: {
          data: true,
          documents: {
            where: {
              type: 'resume',
            },
          },
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  // Get all resumes for the user
  const documents = await prisma.document.findMany({
    where: {
      OR: [
        { userId: user.id },
        {
          profile: {
            team: {
              members: {
                some: { userId: user.id },
              },
            },
          },
        },
      ],
      type: 'resume',
    },
    select: {
      id: true,
      label: true,
      fileName: true,
      createdAt: true,
    },
  });

  // Format resumes for the dropdown
  const resumes = documents.map((doc) => ({
    id: doc.id,
    label: doc.label || doc.fileName || `Resume (${new Date(doc.createdAt).toLocaleDateString()})`,
  }));

  // Calculate profile completion percentages using shared utility (same as dashboard)
  const profilesWithCompletion = profiles.map((profile) => {
    let completionPercentage = 0;

    // Use shared utility if profile has data
    if (profile.data?.data) {
      // Parse profile data properly - it might be a JSON string
      let profileData = profile.data.data;
      if (typeof profileData === 'string') {
        try {
          profileData = JSON.parse(profileData);
        } catch (e) {
          console.error('Error parsing profile data JSON:', e);
          profileData = {};
        }
      }
      completionPercentage = calculateProfileCompletion(profileData as any);
    }

    return {
      ...profile,
      completionPercentage,
    };
  });

  return {
    user,
    profiles: profilesWithCompletion,
    automationRuns,
    resumes,
    form,
  };
};

export const actions = {
  default: async ({ request, locals }: import('./$types').RequestEvent) => {
    const user = locals.user;

    if (!user) {
      throw redirect(302, '/auth/sign-in');
    }

    const form = await superValidate(request, zod(automationFormSchema));

    if (!form.valid) {
      return fail(400, { form });
    }

    try {
      // Create the automation run with the form data
      const automationRun = await prisma.automationRun.create({
        data: {
          userId: user.id,
          profileId: form.data.profileId,
          keywords: form.data.keywords.join(', '),
          location: form.data.locations.join(', '),
          maxJobsToApply: form.data.maxJobsToApply,
          minMatchScore: form.data.minMatchScore,
          autoApplyEnabled: form.data.autoApplyEnabled,
          salaryMin: form.data.salaryRange[0] * 1000,
          salaryMax: form.data.salaryRange[1] * 1000,
          experienceLevelMin: form.data.experienceRange[0],
          experienceLevelMax: form.data.experienceRange[1],
          jobTypes: form.data.jobTypes,
          remotePreference: form.data.remotePreference,
          companySizePreference: form.data.companySizePreference,
          excludeCompanies: form.data.excludeCompanies,
          preferredCompanies: form.data.preferredCompanies,
          specifications: {
            advancedFiltering: true,
            profileMatchingEnabled: true,
            intelligentScoring: true,
          },
          status: 'pending',
        },
      });

      throw redirect(302, `/dashboard/automation/${automationRun.id}`);
    } catch (error) {
      console.error('Error creating automation run:', error);
      return fail(500, {
        form,
        error: 'Failed to create automation run',
      });
    }
  },
};
;null as any as Actions;