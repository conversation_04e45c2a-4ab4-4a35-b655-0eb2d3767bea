import { logger } from "../../utils/logger.js";
import { config } from "../../config.js";
import type { SystemMetrics } from "./monitor.js";

export enum CircuitState {
  CLOSED = "CLOSED", // Normal operation
  OPEN = "OPEN", // Circuit is open, requests will be rejected
  HALF_OPEN = "HALF_OPEN", // Testing if system has recovered
}

let currentState: CircuitState = CircuitState.CLOSED;
let lastStateChange = Date.now();
let consecutiveSuccesses = 0;

export function getCircuitState(): CircuitState {
  return currentState;
}

export function updateCircuitBreakerState(metrics: SystemMetrics) {
  const { memoryThreshold, cpuThreshold, resetThreshold } =
    config.health.circuitBreaker;

  // Check if resources are overloaded
  const isOverloaded =
    metrics.cpuUsage > cpuThreshold || metrics.memoryUsage > memoryThreshold;

  // Check if resources have recovered
  const hasRecovered =
    metrics.cpuUsage < resetThreshold && metrics.memoryUsage < resetThreshold;

  switch (currentState) {
    case CircuitState.CLOSED:
      if (isOverloaded) {
        transitionTo(CircuitState.OPEN);
        logger.warn(
          `🔴 Circuit OPENED: CPU: ${metrics.cpuUsage.toFixed(
            1
          )}%, Memory: ${metrics.memoryUsage.toFixed(1)}%`
        );
      }
      break;

    case CircuitState.OPEN:
      // After some time, try half-open state
      if (Date.now() - lastStateChange > 30000 && hasRecovered) {
        // 30 seconds
        transitionTo(CircuitState.HALF_OPEN);
        logger.info(
          `🟡 Circuit HALF-OPEN: CPU: ${metrics.cpuUsage.toFixed(
            1
          )}%, Memory: ${metrics.memoryUsage.toFixed(1)}%`
        );
      }
      break;

    case CircuitState.HALF_OPEN:
      if (isOverloaded) {
        transitionTo(CircuitState.OPEN);
        logger.warn(
          `🔴 Circuit RE-OPENED: CPU: ${metrics.cpuUsage.toFixed(
            1
          )}%, Memory: ${metrics.memoryUsage.toFixed(1)}%`
        );
      } else if (hasRecovered) {
        consecutiveSuccesses++;
        if (consecutiveSuccesses >= 3) {
          // Require 3 consecutive successful checks
          transitionTo(CircuitState.CLOSED);
          logger.info(
            `🟢 Circuit CLOSED: CPU: ${metrics.cpuUsage.toFixed(
              1
            )}%, Memory: ${metrics.memoryUsage.toFixed(1)}%`
          );
        }
      }
      break;
  }
}

function transitionTo(newState: CircuitState) {
  currentState = newState;
  lastStateChange = Date.now();
  consecutiveSuccesses = 0;
}

export function isCircuitClosed(): boolean {
  return currentState === CircuitState.CLOSED;
}

export function allowRequest(): boolean {
  if (currentState === CircuitState.CLOSED) {
    return true;
  }

  if (currentState === CircuitState.HALF_OPEN) {
    // In half-open state, allow limited requests to test system recovery
    return Math.random() < 0.1; // Allow ~10% of requests
  }

  return false;
}
