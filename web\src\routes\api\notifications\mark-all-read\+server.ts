// src/routes/api/notifications/mark-all-read/+server.ts
import { json } from '@sveltejs/kit';
import { getUserFromToken } from '$lib/server/auth';
import { prisma } from '$lib/server/prisma';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';

/**
 * Mark all notifications as read for the current user
 */
export const POST: RequestHandler = async ({ cookies }) => {
  // Authenticate the user
  const user = await getUserFromToken(cookies);
  
  if (!user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    // Mark all user's notifications as read
    await prisma.notification.updateMany({
      where: {
        OR: [
          { userId: user.id },
          { global: true },
        ],
        read: false,
      },
      data: {
        read: true,
      },
    });
    
    return json({
      success: true,
      message: 'All notifications marked as read',
    });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return json({ error: 'Failed to mark all notifications as read' }, { status: 500 });
  }
};
