/**
 * <PERSON><PERSON>t to query ParsedResume records from the database
 * 
 * This script:
 * 1. Connects to the database
 * 2. Queries the ParsedResume records
 * 3. Displays the results
 * 
 * Usage:
 * npx tsx query-parsed-resumes.ts
 */

import { PrismaClient } from '@prisma/client';

// Initialize Prisma client
const prisma = new PrismaClient();

/**
 * Main function to query ParsedResume records
 */
async function queryParsedResumes() {
  console.log('Querying ParsedResume records...');

  try {
    // Query the ParsedResume records
    const parsedResumes = await prisma.$queryRaw`
      SELECT id, name, email, phone, location, education, experience, skills, "parsedAt"
      FROM "workers"."ParsedResume"
      ORDER BY "parsedAt" DESC
      LIMIT 5
    `;

    // Display the results
    console.log(`Found ${Array.isArray(parsedResumes) ? parsedResumes.length : 0} ParsedResume records`);
    
    if (Array.isArray(parsedResumes) && parsedResumes.length > 0) {
      parsedResumes.forEach((resume: any, index: number) => {
        console.log(`\n--- ParsedResume ${index + 1} ---`);
        console.log(`ID: ${resume.id}`);
        console.log(`Parsed At: ${resume.parsedAt}`);
        console.log(`Name: ${resume.name || 'Not found'}`);
        console.log(`Email: ${resume.email || 'Not found'}`);
        console.log(`Phone: ${resume.phone || 'Not found'}`);
        console.log(`Location: ${resume.location || 'Not found'}`);
        
        // Parse and display education
        try {
          const education = typeof resume.education === 'string' 
            ? JSON.parse(resume.education) 
            : resume.education;
          
          console.log(`Education: ${Array.isArray(education) && education.length > 0 
            ? `${education.length} entries` 
            : 'Not found'}`);
          
          if (Array.isArray(education) && education.length > 0) {
            education.forEach((edu: any, i: number) => {
              console.log(`  ${i+1}. ${edu.institution || 'Unknown institution'} - ${edu.degree || 'Unknown degree'}`);
            });
          }
        } catch (error) {
          console.log(`Education: Error parsing data`);
        }
        
        // Parse and display experience
        try {
          const experience = typeof resume.experience === 'string' 
            ? JSON.parse(resume.experience) 
            : resume.experience;
          
          console.log(`Experience: ${Array.isArray(experience) && experience.length > 0 
            ? `${experience.length} entries` 
            : 'Not found'}`);
          
          if (Array.isArray(experience) && experience.length > 0) {
            experience.forEach((exp: any, i: number) => {
              console.log(`  ${i+1}. ${exp.title || 'Unknown title'} at ${exp.company || 'Unknown company'} - ${exp.date || 'Unknown date'}`);
            });
          }
        } catch (error) {
          console.log(`Experience: Error parsing data`);
        }
        
        // Parse and display skills
        try {
          const skills = typeof resume.skills === 'string' 
            ? JSON.parse(resume.skills) 
            : resume.skills;
          
          if (Array.isArray(skills)) {
            console.log(`Skills: ${skills.length} entries`);
            console.log(`  ${skills.map((s: any) => typeof s === 'object' ? s.name : s).join(', ')}`);
          } else if (skills && typeof skills === 'object') {
            console.log(`Skills: Object with ${Object.keys(skills).length} categories`);
            Object.entries(skills).forEach(([category, skillList]: [string, any]) => {
              console.log(`  ${category}: ${Array.isArray(skillList) ? skillList.join(', ') : 'Invalid format'}`);
            });
          } else {
            console.log(`Skills: Not found or invalid format`);
          }
        } catch (error) {
          console.log(`Skills: Error parsing data`);
        }
      });
    } else {
      console.log('No ParsedResume records found');
    }
  } catch (error) {
    console.error('Error querying ParsedResume records:', error);
  } finally {
    // Clean up
    await prisma.$disconnect();
  }
}

// Run the script
queryParsedResumes().catch(console.error);
