import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth.js';
import type { RequestHandler } from './$types';

/**
 * GET /api/applications
 * Get all applications for the current user
 */
export const GET: RequestHandler = async ({ cookies }) => {
  try {
    // Get the user from the session
    const token = cookies.get('auth_token');
    const user = token && verifySessionToken(token);

    if (!user) {
      return json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get all applications for the user
    const applications = await prisma.application.findMany({
      where: {
        userId: user.id,
      },
      orderBy: {
        appliedDate: 'desc',
      },
    });

    return json({
      success: true,
      applications,
    });
  } catch (error) {
    console.error('Error getting applications:', error);
    return json({ error: 'Failed to get applications' }, { status: 500 });
  }
};
