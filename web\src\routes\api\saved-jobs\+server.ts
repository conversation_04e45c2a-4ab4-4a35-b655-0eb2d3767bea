import { json } from '@sveltejs/kit';
import { PrismaClient } from '@prisma/client';
import type { RequestHandler } from './$types';

const prisma = new PrismaClient();

export const GET: RequestHandler = async ({ locals }) => {
  // Get the user from the session
  const user = locals.user;

  if (!user) {
    return json({ error: 'Authentication required' }, { status: 401 });
  }

  try {
    // Get saved jobs
    const savedJobs = await prisma.savedJob.findMany({
      where: {
        userId: user.id,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Fetch the job listings for the saved jobs
    const jobIds = savedJobs.map(job => job.jobId);
    
    // Get job listings from cron schema
    const jobListings = jobIds.length > 0 
      ? await prisma.job_listing.findMany({
          where: {
            id: {
              in: jobIds,
            },
          },
        })
      : [];
      
    // Combine saved jobs with job listings
    const savedJobsWithListings = savedJobs.map(savedJob => {
      const jobListing = jobListings.find(job => job.id === savedJob.jobId);
      return {
        ...savedJob,
        job_listing: jobListing || null,
      };
    });

    return json({
      success: true,
      savedJobs: savedJobsWithListings,
    });
  } catch (error) {
    console.error('Error fetching saved jobs:', error);
    return json({ error: 'Failed to fetch saved jobs' }, { status: 500 });
  }
};
