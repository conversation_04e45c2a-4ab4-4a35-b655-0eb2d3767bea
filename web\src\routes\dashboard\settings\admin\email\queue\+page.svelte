<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Button from '$lib/components/ui/button/index.js';
  import * as Table from '$lib/components/ui/table/index.js';
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import { toast } from 'svelte-sonner';
  import { onMount } from 'svelte';
  import {
    RefreshCw,
    CheckCircle,
    XCircle,
    Clock,
    AlertTriangle,
    Eye,
    RotateCw,
    Mail,
  } from 'lucide-svelte';

  // State
  let isLoading = $state(true);
  let isRetrying = $state(false);
  let queueStats = $state({
    waiting: 0,
    processing: 0,
    completed: 0,
    failed: 0,
  });
  let recentJobs = $state([]);
  let refreshInterval = $state(null);
  let selectedEmail = $state(null);
  let emailContent = $state({ html: '', text: '', subject: '' });
  let viewMode = $state('html');
  let showEmailDialog = $state(false);

  // Load data on mount
  onMount(async () => {
    await loadQueueStatus();

    // Set up auto-refresh every 10 seconds
    refreshInterval = setInterval(loadQueueStatus, 10000);

    // Clean up on unmount
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  });

  // Load queue status
  async function loadQueueStatus() {
    isLoading = true;

    try {
      const response = await fetch('/api/email/queue-status');

      if (response.ok) {
        const data = await response.json();
        queueStats = data.queue;
        recentJobs = data.recentJobs;
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to load queue status');
      }
    } catch (error) {
      console.error('Error loading queue status:', error);
      toast.error('Failed to load queue status');
    } finally {
      isLoading = false;
    }
  }

  // Format date
  function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString();
  }

  // Get job status badge class
  function getJobStatusBadgeClass(status) {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'waiting':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Process all jobs
  async function processAllJobs() {
    try {
      const response = await fetch('/api/email/process-queue', {
        method: 'POST',
      });

      if (response.ok) {
        toast.success('Processing all jobs');
        await loadQueueStatus();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to process jobs');
      }
    } catch (error) {
      console.error('Error processing jobs:', error);
      toast.error('Failed to process jobs');
    }
  }

  // Clear failed jobs
  async function clearFailedJobs() {
    try {
      const response = await fetch('/api/email/clear-failed', {
        method: 'POST',
      });

      if (response.ok) {
        toast.success('Failed jobs cleared');
        await loadQueueStatus();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to clear failed jobs');
      }
    } catch (error) {
      console.error('Error clearing failed jobs:', error);
      toast.error('Failed to clear failed jobs');
    }
  }

  // View email content
  async function viewEmailContent(email) {
    selectedEmail = email;
    showEmailDialog = true;

    try {
      const response = await fetch(`/api/email/view?id=${email.id}`);

      if (response.ok) {
        emailContent = await response.json();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to load email content');
      }
    } catch (error) {
      console.error('Error loading email content:', error);
      toast.error('Failed to load email content');
    }
  }

  // Retry failed email
  async function retryFailedEmail(email) {
    if (!email || !email.id) return;

    isRetrying = true;

    try {
      const response = await fetch('/api/email/retry-failed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: email.id,
        }),
      });

      if (response.ok) {
        toast.success('Email has been requeued');
        await loadQueueStatus();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to retry email');
      }
    } catch (error) {
      console.error('Error retrying email:', error);
      toast.error('Failed to retry email');
    } finally {
      isRetrying = false;
    }
  }
</script>

<div class="space-y-6">
  <div class="flex items-center justify-between">
    <h2 class="text-3xl font-bold tracking-tight">Email Queue</h2>
    <Button.Root variant="outline" size="sm" onclick={loadQueueStatus} disabled={isLoading}>
      {#if isLoading}
        <div
          class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent">
        </div>
      {:else}
        <RefreshCw class="mr-2 h-4 w-4" />
      {/if}
      Refresh
    </Button.Root>
  </div>

  <!-- Stats Cards -->
  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
    <Card.Root>
      <Card.Content class="p-6">
        <div class="flex items-start justify-between">
          <div>
            <p class="text-muted-foreground text-sm font-medium">Waiting</p>
            <h3 class="mt-1 text-2xl font-bold">{queueStats.waiting}</h3>
          </div>
          <div class="rounded-full bg-yellow-100 p-2">
            <Clock class="h-5 w-5 text-yellow-600" />
          </div>
        </div>
      </Card.Content>
    </Card.Root>

    <Card.Root>
      <Card.Content class="p-6">
        <div class="flex items-start justify-between">
          <div>
            <p class="text-muted-foreground text-sm font-medium">Processing</p>
            <h3 class="mt-1 text-2xl font-bold">{queueStats.processing}</h3>
          </div>
          <div class="rounded-full bg-blue-100 p-2">
            <RefreshCw class="h-5 w-5 text-blue-600" />
          </div>
        </div>
      </Card.Content>
    </Card.Root>

    <Card.Root>
      <Card.Content class="p-6">
        <div class="flex items-start justify-between">
          <div>
            <p class="text-muted-foreground text-sm font-medium">Completed</p>
            <h3 class="mt-1 text-2xl font-bold">{queueStats.completed}</h3>
          </div>
          <div class="rounded-full bg-green-100 p-2">
            <CheckCircle class="h-5 w-5 text-green-600" />
          </div>
        </div>
      </Card.Content>
    </Card.Root>

    <Card.Root>
      <Card.Content class="p-6">
        <div class="flex items-start justify-between">
          <div>
            <p class="text-muted-foreground text-sm font-medium">Failed</p>
            <h3 class="mt-1 text-2xl font-bold">{queueStats.failed}</h3>
          </div>
          <div class="rounded-full bg-red-100 p-2">
            <XCircle class="h-5 w-5 text-red-600" />
          </div>
        </div>
      </Card.Content>
    </Card.Root>
  </div>

  <!-- Actions -->
  <div class="flex flex-wrap gap-4">
    <Button.Root variant="default" onclick={processAllJobs}>
      <RefreshCw class="mr-2 h-4 w-4" />
      Process All Jobs
    </Button.Root>

    <Button.Root variant="destructive" onclick={clearFailedJobs} disabled={queueStats.failed === 0}>
      <AlertTriangle class="mr-2 h-4 w-4" />
      Clear Failed Jobs
    </Button.Root>
  </div>

  <!-- Recent Jobs -->
  <Card.Root>
    <Card.Header>
      <Card.Title>Recent Jobs</Card.Title>
      <Card.Description>Most recent jobs in the email queue</Card.Description>
    </Card.Header>

    <Card.Content>
      {#if isLoading}
        <div class="flex h-40 items-center justify-center">
          <div
            class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent">
          </div>
        </div>
      {:else if recentJobs.length === 0}
        <div class="text-muted-foreground flex h-40 items-center justify-center">
          <p>No jobs in the queue</p>
        </div>
      {:else}
        <Table.Root>
          <Table.Header>
            <Table.Row>
              <Table.Head>ID</Table.Head>
              <Table.Head>Type</Table.Head>
              <Table.Head>Recipient</Table.Head>
              <Table.Head>Status</Table.Head>
              <Table.Head>Created At</Table.Head>
              <Table.Head>Actions</Table.Head>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {#each recentJobs as job}
              <Table.Row>
                <Table.Cell class="font-mono text-xs">{job.id || '-'}</Table.Cell>
                <Table.Cell>{job.type || '-'}</Table.Cell>
                <Table.Cell>{job.to || job.email || '-'}</Table.Cell>
                <Table.Cell>
                  <span
                    class="rounded-full px-2 py-1 text-xs {getJobStatusBadgeClass(
                      job.status || 'waiting'
                    )}">
                    {job.status || 'waiting'}
                  </span>
                </Table.Cell>
                <Table.Cell>{formatDate(job.createdAt)}</Table.Cell>
                <Table.Cell>
                  <div class="flex space-x-2">
                    <Button.Root variant="outline" size="sm" onclick={() => viewEmailContent(job)}>
                      <Eye class="h-4 w-4" />
                    </Button.Root>
                    {#if job.status === 'failed'}
                      <Button.Root
                        variant="outline"
                        size="sm"
                        onclick={() => retryFailedEmail(job)}
                        disabled={isRetrying}>
                        <RotateCw class="h-4 w-4" />
                      </Button.Root>
                    {/if}
                  </div>
                </Table.Cell>
              </Table.Row>
            {/each}
          </Table.Body>
        </Table.Root>
      {/if}
    </Card.Content>
  </Card.Root>
</div>

<!-- Email Content Dialog -->
<Dialog.Root bind:open={showEmailDialog}>
  <Dialog.Content class="max-w-3xl">
    <Dialog.Header>
      <Dialog.Title>Email Content</Dialog.Title>
      <Dialog.Description>
        {selectedEmail?.type || 'Email'} to {selectedEmail?.to ||
          selectedEmail?.email ||
          'recipient'}
      </Dialog.Description>
    </Dialog.Header>

    <div class="mb-4">
      <div class="flex border-b">
        <button
          class={`px-4 py-2 ${viewMode === 'html' ? 'border-primary border-b-2 font-medium' : ''}`}
          onclick={() => (viewMode = 'html')}>
          HTML
        </button>
        <button
          class={`px-4 py-2 ${viewMode === 'text' ? 'border-primary border-b-2 font-medium' : ''}`}
          onclick={() => (viewMode = 'text')}>
          Text
        </button>
        <button
          class={`px-4 py-2 ${viewMode === 'json' ? 'border-primary border-b-2 font-medium' : ''}`}
          onclick={() => (viewMode = 'json')}>
          JSON
        </button>
      </div>

      <div class="mt-4">
        {#if viewMode === 'html'}
          <div class="h-96 overflow-auto rounded-md border p-4">
            {#if emailContent.html}
              <div class="prose prose-sm max-w-none">
                {@html emailContent.html}
              </div>
            {:else}
              <p class="text-muted-foreground">No HTML content available</p>
            {/if}
          </div>
        {:else if viewMode === 'text'}
          <div class="h-96 overflow-auto rounded-md border p-4">
            {#if emailContent.text}
              <pre class="whitespace-pre-wrap text-sm">{emailContent.text}</pre>
            {:else}
              <p class="text-muted-foreground">No text content available</p>
            {/if}
          </div>
        {:else}
          <div class="h-96 overflow-auto rounded-md border p-4">
            <pre class="whitespace-pre-wrap text-sm">{JSON.stringify(emailContent, null, 2)}</pre>
          </div>
        {/if}
      </div>
    </div>

    <Dialog.Footer>
      <Button.Root variant="outline" onclick={() => (showEmailDialog = false)}>Close</Button.Root>
      {#if selectedEmail?.status === 'failed'}
        <Button.Root onclick={() => retryFailedEmail(selectedEmail)} disabled={isRetrying}>
          Retry Email
        </Button.Root>
      {/if}
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
