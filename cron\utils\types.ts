// src/types.ts
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "playwright";

// Defining the Worker interface
export interface Worker {
  id: number;
  browser: Browser;
  context: BrowserContext;
  page: Page;
  busy: boolean;
  lastUsed: Date;
  captchaCount: number;
  jobsProcessed: number; // Track number of jobs processed for recycling
  currentJob?: { id: string; title: string }; // Storing current job details
  currentBatch?: JobBatch; // Storing current batch details
  markedForShutdown?: boolean; // Flag to indicate if worker should be shut down when released
  markedForRecycling?: boolean; // Flag to indicate if worker should be recycled when released
  errorCount?: number; // Track number of errors encountered by this worker
  lastErrorTime?: Date; // Track when the last error occurred
}

// Defining the JobBatch interface
export interface JobBatch {
  batchId: string;
  cityId: string;
  cityName: string;
  cityIndex: number;
  stateId: string;
  stateCode: string;
  stateName: string;
  occupations: { id: string; title: string }[];
  workerIndex: number;
}

export interface WorkerPoolOptions {
  maxWorkers: number;
  headless: boolean;
  slowMo?: number;
  recycleThreshold?: number; // Number of jobs after which to recycle a browser to prevent memory leaks
}
