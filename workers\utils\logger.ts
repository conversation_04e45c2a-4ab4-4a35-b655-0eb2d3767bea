/**
 * Logger Utility
 * 
 * This module provides a simple logging utility for workers.
 */

// Log levels
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

// Logger interface
export interface Logger {
  debug(message: string, ...args: any[]): void;
  info(message: string, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  error(message: string, ...args: any[]): void;
}

// Get current log level from environment
const currentLogLevel = (process.env.LOG_LEVEL || 'info').toLowerCase() as LogLevel;

// Log level priorities
const logLevelPriority: Record<LogLevel, number> = {
  [LogLevel.DEBUG]: 0,
  [LogLevel.INFO]: 1,
  [LogLevel.WARN]: 2,
  [LogLevel.ERROR]: 3
};

/**
 * Create a logger instance
 * @param namespace Logger namespace
 * @returns Logger instance
 */
export function createLogger(namespace: string): Logger {
  return {
    debug(message: string, ...args: any[]) {
      if (shouldLog(LogLevel.DEBUG)) {
        console.debug(`[${namespace}] ${message}`, ...args);
      }
    },
    
    info(message: string, ...args: any[]) {
      if (shouldLog(LogLevel.INFO)) {
        console.info(`[${namespace}] ${message}`, ...args);
      }
    },
    
    warn(message: string, ...args: any[]) {
      if (shouldLog(LogLevel.WARN)) {
        console.warn(`[${namespace}] ${message}`, ...args);
      }
    },
    
    error(message: string, ...args: any[]) {
      if (shouldLog(LogLevel.ERROR)) {
        console.error(`[${namespace}] ${message}`, ...args);
      }
    }
  };
}

/**
 * Check if a log level should be logged
 * @param level Log level to check
 * @returns Whether the log level should be logged
 */
function shouldLog(level: LogLevel): boolean {
  return logLevelPriority[level] >= logLevelPriority[currentLogLevel];
}
