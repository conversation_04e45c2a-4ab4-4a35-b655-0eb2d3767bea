<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { toast } from 'svelte-sonner';
  import type { ProfileData } from '$lib/types/profile';

  // Props
  const { profileData, onSave, onCancel } = $props<{
    profileData: ProfileData;
    onSave: (data: Partial<ProfileData>) => Promise<boolean>;
    onCancel: () => void;
  }>();

  // Form data
  let phone = $state(profileData.phone || profileData.personalInfo?.phone || '');
  let location = $state(profileData.location || profileData.personalInfo?.location || '');
  let website = $state(profileData.website || profileData.personalInfo?.website || '');
  let linkedin = $state(profileData.linkedin || profileData.personalInfo?.linkedin || '');

  // Handle form submission
  async function handleSubmit() {
    try {
      // Prepare data
      const updatedData: Partial<ProfileData> = {
        phone,
        location,
        website,
        linkedin,
        personalInfo: {
          ...profileData.personalInfo,
          phone,
          location,
          website,
          linkedin,
        },
      };

      // Save data
      const success = await onSave(updatedData);
      if (success) {
        toast.success('Contact details updated successfully');
      }
    } catch (error) {
      console.error('Error saving contact details:', error);
      toast.error('Failed to save contact details');
    }
  }
</script>

<form on:submit|preventDefault={handleSubmit} class="space-y-4">
  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
    <div class="space-y-2">
      <Label for="phone">Phone Number</Label>
      <Input id="phone" bind:value={phone} placeholder="Enter your phone number" />
    </div>
    <div class="space-y-2">
      <Label for="location">Location</Label>
      <Input id="location" bind:value={location} placeholder="City, State, Country" />
    </div>
    <div class="space-y-2">
      <Label for="website">Website</Label>
      <Input id="website" bind:value={website} placeholder="https://yourwebsite.com" />
    </div>
    <div class="space-y-2">
      <Label for="linkedin">LinkedIn</Label>
      <Input id="linkedin" bind:value={linkedin} placeholder="https://linkedin.com/in/yourprofile" />
    </div>
  </div>

  <div class="flex justify-end space-x-2 pt-4">
    <Button variant="outline" type="button" on:click={onCancel}>Cancel</Button>
    <Button type="submit">Save Changes</Button>
  </div>
</form>
