// File: web/src/routes/api/resume/[id]/update-status/+server.ts
import { prisma } from '$lib/server/prisma';
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types.js';

/**
 * POST handler to update resume parsing status
 *
 * This endpoint updates the resume parsing status in the database.
 * It's used to ensure consistency between the worker and the database.
 */
export const POST: RequestHandler = async ({ params, request, locals }) => {
  const user = locals.user;
  if (!user) return new Response('Unauthorized', { status: 401 });

  const resumeId = params.id;
  if (!resumeId) {
    return json({ error: 'Resume ID is required' }, { status: 400 });
  }

  try {
    // Parse request body
    const body = await request.json();
    const { isParsed, parsedData, forceUpdate } = body;

    // Get the resume with its document
    const resume = await prisma.resume.findUnique({
      where: { id: resumeId },
      include: {
        document: true,
      },
    });

    if (!resume) {
      return json({ error: 'Resume not found' }, { status: 404 });
    }

    // Check if the document belongs to the user
    if (resume.document.userId !== user.id) {
      return json({ error: 'Unauthorized access to resume' }, { status: 403 });
    }

    // If forceUpdate is true, check if there's a parsed resume in the workers schema
    if (forceUpdate && isParsed) {
      try {
        // Check if there's a parsed resume in the workers schema
        const parsedResume = await prisma.$queryRaw`
          SELECT * FROM "workers"."ParsedResume" WHERE "resumeId" = ${resumeId} LIMIT 1
        `;

        // If we found a parsed resume, use its data
        if (parsedResume && Array.isArray(parsedResume) && parsedResume.length > 0) {
          console.log(
            `Found parsed resume data in workers schema for ${resumeId}, using it for force update`
          );

          // Update the resume with the parsed data
          const updatedResume = await prisma.resume.update({
            where: { id: resumeId },
            data: {
              isParsed: true,
              parsedAt: new Date(),
              parsedData: parsedResume[0],
              updatedAt: new Date(),
            },
          });

          console.log(`Force updated resume ${resumeId} with data from workers schema`);

          return json({
            success: true,
            message: 'Resume status force updated successfully with data from workers schema',
            data: {
              isParsed: updatedResume.isParsed,
              parsedAt: updatedResume.parsedAt,
            },
          });
        }
      } catch (queryError) {
        console.error(`Error querying workers schema for parsed resume:`, queryError);
        // Continue with normal update if query fails
      }
    }

    // Update the resume
    const updatedResume = await prisma.resume.update({
      where: { id: resumeId },
      data: {
        isParsed: isParsed ?? resume.isParsed,
        parsedAt: isParsed ? new Date() : resume.parsedAt,
        parsedData: parsedData ?? resume.parsedData,
        updatedAt: new Date(),
      },
    });

    console.log(`Updated resume ${resumeId} status: isParsed=${isParsed}`);

    return json({
      success: true,
      message: forceUpdate
        ? 'Resume status force updated successfully'
        : 'Resume status updated successfully',
      data: {
        isParsed: updatedResume.isParsed,
        parsedAt: updatedResume.parsedAt,
      },
    });
  } catch (error) {
    console.error('Error updating resume status:', error);
    return json({ error: 'Failed to update resume status' }, { status: 500 });
  }
};
