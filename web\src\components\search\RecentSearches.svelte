<script lang="ts">
  import { store } from '$lib/stores/store';
  import { Button } from '$lib/components/ui/button';
  import { Clock, Star, StarOff, X } from 'lucide-svelte';
  import { formatDistanceToNow } from '$lib/utils';
  import type { SearchItem } from '$lib/stores/store';

  // Props
  const { maxItems = 5, onSelect } = $props<{
    maxItems?: number;
    onSelect?: (query: string) => void;
  }>();

  // Get recent searches
  const recentSearches = $derived(
    $store.searchHistory
      .sort((a: SearchItem, b: SearchItem) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, maxItems)
  );

  // Handle selecting a search item
  function handleSelect(query: string) {
    if (onSelect) {
      onSelect(query);
    }
  }

  // Toggle favorite status
  function toggleFavorite(id: string) {
    store.update((state: any) => ({
      ...state,
      searchHistory: state.searchHistory.map((item: SearchItem) =>
        item.id === id ? { ...item, favorite: !item.favorite } : item
      ),
    }));
  }

  // Remove search item
  function removeSearchItem(id: string) {
    store.update((state: any) => ({
      ...state,
      searchHistory: state.searchHistory.filter((item: SearchItem) => item.id !== id),
    }));
  }
</script>

{#if recentSearches.length > 0}
  <div class="space-y-1">
    <div class="text-muted-foreground flex items-center text-xs font-medium">
      <Clock class="mr-1 h-3 w-3" />
      <span>Recent Searches</span>
    </div>
    <ul class="space-y-1">
      {#each recentSearches as search (search.id)}
        <li class="hover:bg-muted/50 flex items-center justify-between rounded-md px-2 py-1">
          <button
            type="button"
            class="flex-1 cursor-pointer text-left text-sm"
            onclick={() => handleSelect(search.query)}>
            {search.query}
            <span class="text-muted-foreground ml-2 text-xs">
              {formatDistanceToNow(search.timestamp)}
            </span>
          </button>
          <div class="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="icon"
              class="h-6 w-6"
              onclick={() => toggleFavorite(search.id)}
              aria-label={search.favorite ? 'Remove from favorites' : 'Add to favorites'}>
              {#if search.favorite}
                <Star class="h-3 w-3 text-yellow-500" />
              {:else}
                <StarOff class="h-3 w-3" />
              {/if}
            </Button>
            <Button
              variant="ghost"
              size="icon"
              class="h-6 w-6"
              onclick={() => removeSearchItem(search.id)}
              aria-label="Remove search">
              <X class="h-3 w-3" />
            </Button>
          </div>
        </li>
      {/each}
    </ul>
  </div>
{/if}
