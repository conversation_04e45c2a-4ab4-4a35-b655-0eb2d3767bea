// src/routes/api/help/+server.ts
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { prisma } from '$lib/server/prisma';

// Get all help articles (with optional filtering)
export const GET: RequestHandler = async ({ url }) => {
  try {
    const categorySlug = url.searchParams.get('category') || undefined;
    const tagSlug = url.searchParams.get('tag') || undefined;
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const page = parseInt(url.searchParams.get('page') || '1');
    const skip = (page - 1) * limit;
    const featured = url.searchParams.get('featured') === 'true';

    // Build the query filters
    const filters: any = {
      published: true,
    };

    if (categorySlug) {
      filters.category = {
        slug: categorySlug,
      };
    }

    if (tagSlug) {
      filters.tags = {
        some: {
          slug: tagSlug,
        },
      };
    }

    if (featured) {
      // Assuming you want to feature articles with the highest view count
      // You could also add a 'featured' boolean field to the model if needed
      const featuredArticles = await prisma.helpArticle.findMany({
        where: {
          published: true,
        },
        orderBy: {
          viewCount: 'desc',
        },
        take: limit,
        include: {
          category: true,
          tags: true,
        },
      });

      return json(featuredArticles);
    }

    // Get articles from the database using Prisma models
    const [articles, totalCount] = await Promise.all([
      prisma.helpArticle.findMany({
        where: filters,
        orderBy: {
          updatedAt: 'desc',
        },
        skip,
        take: limit,
        include: {
          category: true,
          tags: true,
        },
      }),
      prisma.helpArticle.count({
        where: filters,
      }),
    ]);

    return json({
      articles,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching help articles:', error);
    return json({ error: 'Failed to fetch help articles' }, { status: 500 });
  }
};

// Create a new help article (admin only)
export const POST: RequestHandler = async ({ request, locals }) => {
  const user = locals.user;
  if (!user || user.role !== 'ADMIN') {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { title, slug, content, excerpt, categoryId, tagIds, published } = await request.json();

    // Create the article
    const article = await prisma.helpArticle.create({
      data: {
        title,
        slug,
        content,
        excerpt,
        published: published || false,
        categoryId,
        tags: {
          connect: tagIds?.map((id: string) => ({ id })) || [],
        },
      },
      include: {
        category: true,
        tags: true,
      },
    });

    return json(article);
  } catch (error) {
    console.error('Error creating help article:', error);
    return json({ error: 'Failed to create help article' }, { status: 500 });
  }
};
