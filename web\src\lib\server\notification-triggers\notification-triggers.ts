// src/lib/server/notification-triggers.ts
import { prisma } from '$lib/server/prisma';
import {
  sendJobNotification,
  sendApplicationNotification,
  NotificationType,
  NotificationPriority,
} from '$lib/server/notification-triggers/notification-service';

/**
 * Send a notification when a new job match is found
 */
export async function notifyJobMatch(
  userId: string,
  jobId: string,
  jobTitle: string,
  companyName: string,
  matchScore: number
): Promise<boolean> {
  try {
    // Get job details
    const job = await prisma.job_listing.findUnique({
      where: { id: jobId },
      select: {
        title: true,
        company: true,
        url: true,
      },
    });

    if (!job) {
      console.error(`Job not found: ${jobId}`);
      return false;
    }

    // Create notification
    const result = await sendJobNotification(userId, {
      title: 'New Job Match',
      message: `${job.title} at ${job.company} matches your profile with a score of ${matchScore}%`,
      url: `/dashboard/jobs/${jobId}`,
      type: NotificationType.JOB,
      priority: matchScore > 80 ? NotificationPriority.HIGH : NotificationPriority.MEDIUM,
      metadata: {
        jobId,
        matchScore,
        jobTitle: job.title,
        companyName: job.company,
      },
    });

    return result;
  } catch (error) {
    console.error('Error sending job match notification:', error);
    return false;
  }
}

/**
 * Send a notification when a job application status changes
 */
export async function notifyApplicationStatusChange(
  userId: string,
  applicationId: string,
  newStatus: string,
  oldStatus: string
): Promise<boolean> {
  try {
    // Get application details
    const application = await prisma.application.findUnique({
      where: { id: applicationId },
      select: {
        company: true,
        position: true,
      },
    });

    if (!application) {
      console.error(`Application not found: ${applicationId}`);
      return false;
    }

    // Create notification
    const result = await sendApplicationNotification(userId, {
      title: 'Application Status Updated',
      message: `Your application for ${application.position} at ${application.company} has been updated from ${oldStatus} to ${newStatus}`,
      url: `/dashboard/tracker/${applicationId}`,
      type: NotificationType.APPLICATION,
      priority: NotificationPriority.HIGH,
      metadata: {
        applicationId,
        newStatus,
        oldStatus,
        company: application.company,
        position: application.position,
      },
    });

    return result;
  } catch (error) {
    console.error('Error sending application status notification:', error);
    return false;
  }
}

/**
 * Send a notification when a new interview is scheduled
 */
export async function notifyInterviewScheduled(
  userId: string,
  interviewId: string,
  applicationId: string,
  interviewDate: Date,
  interviewType: string
): Promise<boolean> {
  try {
    // Get application details
    const application = await prisma.application.findUnique({
      where: { id: applicationId },
      select: {
        company: true,
        position: true,
      },
    });

    if (!application) {
      console.error(`Application not found: ${applicationId}`);
      return false;
    }

    // Format date
    const formattedDate = interviewDate.toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    });

    // Create notification
    const result = await sendApplicationNotification(userId, {
      title: 'Interview Scheduled',
      message: `You have a ${interviewType} interview for ${application.position} at ${application.company} on ${formattedDate}`,
      url: `/dashboard/tracker/${applicationId}`,
      type: NotificationType.INTERVIEW,
      priority: NotificationPriority.URGENT,
      metadata: {
        interviewId,
        applicationId,
        interviewDate: interviewDate.toISOString(),
        interviewType,
        company: application.company,
        position: application.position,
      },
    });

    return result;
  } catch (error) {
    console.error('Error sending interview notification:', error);
    return false;
  }
}

/**
 * Send a notification when a job alert has new matches
 */
export async function notifyJobAlertMatches(
  userId: string,
  jobAlertId: string,
  jobAlertName: string,
  matchCount: number
): Promise<boolean> {
  try {
    // Create notification
    const result = await sendJobNotification(userId, {
      title: 'New Job Matches',
      message: `Your job alert "${jobAlertName}" has ${matchCount} new matching jobs`,
      url: `/dashboard/matches?alert=${jobAlertId}`,
      type: NotificationType.JOB,
      priority: NotificationPriority.MEDIUM,
      metadata: {
        jobAlertId,
        jobAlertName,
        matchCount,
      },
    });

    return result;
  } catch (error) {
    console.error('Error sending job alert notification:', error);
    return false;
  }
}
