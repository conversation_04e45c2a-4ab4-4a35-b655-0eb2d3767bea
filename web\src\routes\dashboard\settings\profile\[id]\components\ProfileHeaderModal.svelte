<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import { Save } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import type { ProfileHeaderSchema } from '$lib/validators/profile';

  // Props
  const { open, data, onClose, onSave } = $props<{
    open: boolean;
    data: ProfileHeaderSchema;
    onClose: () => void;
    onSave: (data: ProfileHeaderSchema) => Promise<boolean>;
  }>();

  // Create a local copy of the data for editing
  let formData = $state({
    profileName: data.profileName || '',
    fullName: data.fullName || '',
    jobTitle: data.jobTitle || '',
    jobSearchStatus: data.jobSearchStatus || 'actively_looking',
  });

  // Form submission state
  let submitting = $state(false);
  let errors = $state<Record<string, string>>({});

  // Reset form when modal opens
  $effect(() => {
    if (open) {
      formData = {
        profileName: data.profileName || '',
        fullName: data.fullName || '',
        jobTitle: data.jobTitle || '',
        jobSearchStatus: data.jobSearchStatus || 'actively_looking',
      };
      errors = {};
    }
  });

  // Handle form submission
  async function handleSubmit() {
    // Basic validation
    errors = {};
    if (!formData.profileName) {
      errors.profileName = 'Profile name is required';
    }
    if (!formData.fullName) {
      errors.fullName = 'Full name is required';
    }

    // If there are errors, don't submit
    if (Object.keys(errors).length > 0) {
      return;
    }

    // Submit the form
    submitting = true;
    try {
      const success = await onSave(formData);
      if (success) {
        toast.success('Profile header updated successfully');
        onClose();
      }
    } catch (error) {
      console.error('Error saving profile header:', error);
      toast.error('Failed to save profile header');
    } finally {
      submitting = false;
    }
  }

  // Job search status options
  const jobSearchStatusOptions = [
    { value: 'actively_looking', label: 'Actively Looking' },
    { value: 'open_to_opportunities', label: 'Open to Opportunities' },
    { value: 'not_looking', label: 'Not Looking' },
  ];
</script>

<Dialog.Root open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
  <Dialog.Content class="sm:max-w-[500px]">
    <Dialog.Header>
      <Dialog.Title>Edit Profile</Dialog.Title>
      <Dialog.Description>
        Update your profile information. Click save when you're done.
      </Dialog.Description>
    </Dialog.Header>

    <div class="grid gap-4 py-4">
      <div class="grid gap-2">
        <Label for="profileName">Profile Name</Label>
        <Input
          id="profileName"
          bind:value={formData.profileName}
          placeholder="My Professional Profile" />
        {#if errors.profileName}
          <p class="text-destructive text-sm">{errors.profileName}</p>
        {/if}
      </div>

      <div class="grid gap-2">
        <Label for="fullName">Full Name</Label>
        <Input id="fullName" bind:value={formData.fullName} placeholder="John Doe" />
        {#if errors.fullName}
          <p class="text-destructive text-sm">{errors.fullName}</p>
        {/if}
      </div>

      <div class="grid gap-2">
        <Label for="jobTitle">Job Title</Label>
        <Input
          id="jobTitle"
          bind:value={formData.jobTitle}
          placeholder="Software Engineer" />
      </div>

      <div class="grid gap-2">
        <Label for="jobSearchStatus">Job Search Status</Label>
        <Select.Root
          value={formData.jobSearchStatus}
          onValueChange={(value) => (formData.jobSearchStatus = value)}>
          <Select.Trigger id="jobSearchStatus" class="w-full">
            <Select.Value placeholder="Select job search status" />
          </Select.Trigger>
          <Select.Content>
            <Select.Group>
              {#each jobSearchStatusOptions as option}
                <Select.Item value={option.value}>{option.label}</Select.Item>
              {/each}
            </Select.Group>
          </Select.Content>
        </Select.Root>
      </div>
    </div>

    <Dialog.Footer>
      <Button variant="outline" onclick={onClose}>Cancel</Button>
      <Button onclick={handleSubmit} disabled={submitting}>
        {#if submitting}
          <span class="mr-2">Saving...</span>
        {:else}
          <Save class="mr-2 h-4 w-4" />
          Save Changes
        {/if}
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
