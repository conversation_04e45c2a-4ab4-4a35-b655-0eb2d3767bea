// File: web/src/lib/server/worker-process-subscriber.ts
import { getRedisClient } from '$lib/server/redis';
import { prisma } from '$lib/server/prisma';
import { createNotification, NotificationType } from '$lib/server/notifications';

// Worker process status types
export enum WorkerProcessStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// Worker process types
export enum WorkerProcessType {
  RESUME_PARSING = 'resume-parsing',
  RESUME_OPTIMIZATION = 'resume-optimization',
  JOB_SEARCH = 'job-search',
}

/**
 * Initialize the worker process subscriber
 */
export async function initWorkerProcessSubscriber() {
  console.log('[Worker Process Subscriber] Initializing worker process subscriber...');

  try {
    // Get Redis client - use the shared connection
    const redis = await getRedisClient();
    if (!redis) {
      console.error('[Worker Process Subscriber] Redis client not available');
      return;
    }

    // Create a duplicate client for pub/sub to avoid interfering with the main client
    const subscriber = redis.duplicate();

    // Subscribe to worker process status channels
    await subscriber.subscribe(`resume-parsing::stream::status`);
    console.log('[Worker Process Subscriber] Subscribed to resume-parsing::stream::status channel');

    // Subscribe to the actual channel used by the Redis worker
    await subscriber.subscribe(`resume-parsing::status`);
    console.log('[Worker Process Subscriber] Subscribed to resume-parsing::status channel');

    // Listen for messages on the subscriber client
    subscriber.on('message', async (channel, message) => {
      try {
        console.log(`[Worker Process Subscriber] Received message on channel ${channel}:`, message);

        // Handle worker process status updates from both channels
        if (channel === 'resume-parsing::stream::status' || channel === 'resume-parsing::status') {
          await handleResumeParsingStatus(message);
        }
      } catch (error) {
        console.error('[Worker Process Subscriber] Error handling message:', error);
      }
    });

    console.log('[Worker Process Subscriber] Worker process subscriber initialized');
  } catch (error) {
    console.error(
      '[Worker Process Subscriber] Error initializing worker process subscriber:',
      error
    );
  }
}

/**
 * Update the worker process record in the database
 * @param jobId The job ID
 * @param status The new status
 */
async function updateWorkerProcessStatus(jobId: string, status: string) {
  try {
    await prisma.workerProcess.update({
      where: { id: jobId },
      data: {
        status,
        completedAt: status === WorkerProcessStatus.COMPLETED ? new Date() : undefined,
        updatedAt: new Date(),
      },
    });
    console.log(`[Worker Process Subscriber] Updated worker process ${jobId} status to ${status}`);
    return true;
  } catch {
    console.log(
      `[Worker Process Subscriber] Worker process ${jobId} not found, trying to find by resumeId`
    );
    return await tryUpdateWorkerProcessByResumeId(jobId, status);
  }
}

/**
 * Try to update the worker process by extracting resumeId from jobId and updating by resumeId.
 * @param jobId The job ID
 * @param status The new status
 */
async function tryUpdateWorkerProcessByResumeId(jobId: string, status: string) {
  // Extract resumeId from the jobId if it's in the old format
  let resumeId = null;
  if (jobId.startsWith('profile-parsing-')) {
    const parts = jobId.split('-');
    if (parts.length >= 4) {
      resumeId = parts[parts.length - 1];
    }
  }

  if (resumeId) {
    // Try to find the worker process by resumeId
    const workerProcess = await prisma.workerProcess.findFirst({
      where: {
        type: 'resume-parsing',
        data: {
          path: ['resumeId'],
          equals: resumeId,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    if (workerProcess) {
      // Update the found worker process
      await prisma.workerProcess.update({
        where: { id: workerProcess.id },
        data: {
          status,
          completedAt: status === WorkerProcessStatus.COMPLETED ? new Date() : undefined,
          updatedAt: new Date(),
        },
      });
      console.log(
        `[Worker Process Subscriber] Updated worker process ${workerProcess.id} for resumeId ${resumeId}`
      );
      return true;
    }
  }

  // If we still can't find the worker process, log an error
  console.error(
    `[Worker Process Subscriber] Worker process ${jobId} not found and couldn't find by resumeId`
  );
  return false;
}

/**
 * Update the resume record to mark it as parsed
 * @param resumeId The resume ID
 */
async function updateResumeAsParsed(resumeId: string) {
  try {
    await prisma.resume.update({
      where: { id: resumeId },
      data: {
        isParsed: true,
        parsedAt: new Date(),
      },
    });
    console.log(`[Worker Process Subscriber] Updated resume ${resumeId} to mark as parsed`);
    return true;
  } catch (error) {
    console.error('[Worker Process Subscriber] Error updating resume:', error);
    return false;
  }
}

/**
 * Create a notification for a completed worker process
 * @param userId The user ID
 * @param resumeId The resume ID
 * @param parsedResumeId The parsed resume ID
 * @param jobId The job ID
 * @param profileId The profile ID
 */
async function createCompletionNotification(
  userId: string,
  resumeId: string,
  parsedResumeId: string,
  jobId: string,
  profileId: string | null
) {
  try {
    await createNotification({
      userId,
      title: 'Resume Parsed Successfully',
      message: 'Your resume has been successfully parsed and is ready to view.',
      url: profileId ? `/dashboard/settings/profile/${profileId}` : undefined,
      type: NotificationType.SUCCESS,
      data: {
        resumeId,
        parsedResumeId,
        jobId,
        timestamp: new Date().toISOString(),
      },
    });
    console.log(
      `[Worker Process Subscriber] Created notification for user ${userId} with resumeId ${resumeId}`
    );
    return true;
  } catch (notificationError) {
    console.error('[Worker Process Subscriber] Error creating notification:', notificationError);
    return false;
  }
}

/**
 * Handle a completed resume parsing job
 * @param jobId The job ID
 * @param resumeId The resume ID
 * @param parsedResumeId The parsed resume ID
 */
async function handleCompletedResumeParsingJob(
  jobId: string,
  resumeId: string,
  parsedResumeId: string
) {
  try {
    // Get the worker process to get the user ID
    let workerProcess = await prisma.workerProcess.findUnique({
      where: { id: jobId },
    });

    // If not found with the given ID, try to find by resumeId
    if (!workerProcess) {
      console.log(
        `[Worker Process Subscriber] Worker process ${jobId} not found, trying to find by resumeId`
      );

      workerProcess = await prisma.workerProcess.findFirst({
        where: {
          type: 'resume-parsing',
          data: {
            path: ['resumeId'],
            equals: resumeId,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    }

    if (!workerProcess?.data || typeof workerProcess.data !== 'object') {
      console.error(
        `[Worker Process Subscriber] Worker process for resumeId ${resumeId} has no data`
      );
      return;
    }

    const userId = (workerProcess.data as any).userId;
    if (!userId) {
      console.error(`[Worker Process Subscriber] Worker process ${jobId} has no userId`);
      return;
    }

    // Get the profileId from the worker process data
    const profileId = (workerProcess.data as any).profileId ?? null;

    // Update the resume record
    await updateResumeAsParsed(resumeId);

    // Trigger ATS analysis for the parsed resume
    try {
      // Call the ATS analysis endpoint
      const response = await fetch(
        `${process.env.WORKER_API_URL ?? 'http://localhost:3002'}/api/ai/resume-analysis`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            resumeId,
          }),
        }
      );

      if (response.ok) {
        console.log(`ATS analysis triggered for resume ${resumeId}`);
      } else {
        console.error(
          `Failed to trigger ATS analysis for resume ${resumeId}: ${response.statusText}`
        );
      }
    } catch (error) {
      console.error(`Error triggering ATS analysis for resume ${resumeId}:`, error);
      // Continue even if ATS analysis fails - don't block the notification
    }

    // Send a WebSocket message to the client with detailed status
    createNotification({
      userId,
      title: 'Resume Parsing Completed',
      message: 'Your resume has been successfully parsed and ATS analysis has been started.',
      type: NotificationType.SUCCESS,
      data: {
        jobId,
        status: WorkerProcessStatus.COMPLETED,
        resumeId,
        parsedResumeId,
        userId,
        profileId,
        message: 'Resume parsing completed successfully',
        timestamp: new Date().toISOString(),
      },
    });

    // Create a notification for the user
    await createCompletionNotification(userId, resumeId, parsedResumeId, jobId, profileId);

    // If we have a profileId, update the profile's isParsing flag
    if (profileId) {
      try {
        await prisma.profile.update({
          where: { id: profileId },
          data: {
            updatedAt: new Date(),
          },
        });
        console.log(
          `[Worker Process Subscriber] Updated profile ${profileId} after parsing completed`
        );
      } catch (profileError) {
        console.error('[Worker Process Subscriber] Error updating profile:', profileError);
      }
    }
  } catch (error) {
    console.error('[Worker Process Subscriber] Error handling completed status:', error);
  }
}

/**
 * Handle resume parsing status updates
 * @param message The status message
 */
async function handleResumeParsingStatus(message: string) {
  try {
    // Parse the message
    const data = JSON.parse(message);
    const { jobId, status, resumeId, parsedResumeId, error } = data;

    console.log(`[Worker Process Subscriber] Resume parsing status update for job ${jobId}:`, data);

    // Update the worker process record in the database
    await updateWorkerProcessStatus(jobId, status);

    // If the status is completed, update the resume record and send notifications
    if (status === WorkerProcessStatus.COMPLETED && resumeId) {
      await handleCompletedResumeParsingJob(jobId, resumeId, parsedResumeId);
    }
    // If the status is failed, handle the failure
    else if (status === WorkerProcessStatus.FAILED && resumeId) {
      // Get the worker process to get the user ID
      let workerProcess = await prisma.workerProcess.findUnique({
        where: { id: jobId },
      });

      // If not found with the given ID, try to find by resumeId
      if (!workerProcess) {
        workerProcess = await prisma.workerProcess.findFirst({
          where: {
            type: 'resume-parsing',
            data: {
              path: ['resumeId'],
              equals: resumeId,
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        });
      }

      if (workerProcess?.data && typeof workerProcess.data === 'object') {
        const userId = (workerProcess.data as any).userId;
        const profileId = (workerProcess.data as any).profileId ?? null;

        if (userId) {
          // Create a notification for the user
          await createNotification({
            userId,
            title: 'Resume Parsing Failed',
            message: error
              ? `We couldn't parse your resume. Error: ${error}`
              : `We couldn't parse your resume. Please try again later.`,
            type: NotificationType.ERROR,
            data: {
              resumeId,
              jobId,
              error,
            },
          });

          // Send a WebSocket message to the client with detailed status
          createNotification({
            userId,
            title: 'Resume Parsing Failed',
            message: error ? `Resume parsing failed: ${error}` : 'Resume parsing failed',
            type: NotificationType.ERROR,
            data: {
              jobId,
              status: WorkerProcessStatus.FAILED,
              resumeId,
              profileId,
              error,
              timestamp: new Date().toISOString(),
            },
          });
        }
      }
    }
  } catch (error) {
    console.error('[Worker Process Subscriber] Error parsing message:', error);
  }
}
