// src/routes/api/maintenance/+server.ts
import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { logger } from '$lib/server/logger';
import { notifySystemMaintenance } from '$lib/server/notification-triggers/system-triggers';
import type { RequestHandler } from './$types';
import { updateServiceStatus } from './updateServiceStatus';

// Get all maintenance events
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Check if user is admin
    const user = locals.user;
    const isAdmin = user?.isAdmin === true;

    // Get query parameters
    const status = url.searchParams.get('status');
    const upcoming = url.searchParams.get('upcoming') === 'true';
    const past = url.searchParams.get('past') === 'true';
    const limit = parseInt(url.searchParams.get('limit') || '10');

    // Build query
    const query: any = {};

    // Filter by status if provided
    if (status) {
      query.status = status;
    }

    // Filter for upcoming events
    if (upcoming) {
      query.startTime = {
        gte: new Date(),
      };
    }

    // Filter for past events
    if (past) {
      query.endTime = {
        lt: new Date(),
      };
    }

    // Get maintenance events
    let events = [];

    try {
      events = await prisma.maintenanceEvent.findMany({
        where: query,
        orderBy: {
          startTime: upcoming ? 'asc' : 'desc',
        },
        take: limit,
      });
    } catch (error) {
      // If the table doesn't exist yet, return mock data
      logger.warn('MaintenanceEvent table may not exist yet, using mock data:', error);

      // Generate mock data based on the query
      if (status === 'in-progress') {
        events = [
          {
            id: 'mock-in-progress-1',
            title: 'Database Maintenance',
            description: 'Scheduled database optimization and index rebuilding.',
            startTime: new Date(Date.now() - 3600000), // 1 hour ago
            endTime: new Date(Date.now() + 3600000), // 1 hour from now
            status: 'in-progress',
            severity: 'info',
            affectedServices: ['Database', 'API'],
            createdBy: 'system',
            createdAt: new Date(Date.now() - 86400000), // 1 day ago
            updatedAt: new Date(),
            notifiedAt: new Date(Date.now() - 86400000),
            completedAt: null,
          },
        ];
      } else if (upcoming) {
        events = [
          {
            id: 'mock-upcoming-1',
            title: 'System Upgrade',
            description: 'Upgrading core system components for improved performance.',
            startTime: new Date(Date.now() + 86400000), // 1 day from now
            endTime: new Date(Date.now() + 90000000), // 25 hours from now
            status: 'scheduled',
            severity: 'info',
            affectedServices: ['Website', 'API', 'Job Search'],
            createdBy: 'system',
            createdAt: new Date(Date.now() - 604800000), // 1 week ago
            updatedAt: new Date(),
            notifiedAt: new Date(Date.now() - 604800000),
            completedAt: null,
          },
          {
            id: 'mock-upcoming-2',
            title: 'Security Patch Deployment',
            description: 'Applying critical security updates to all services.',
            startTime: new Date(Date.now() + 172800000), // 2 days from now
            endTime: new Date(Date.now() + 180000000), // 2 days + 2 hours from now
            status: 'scheduled',
            severity: 'warning',
            affectedServices: ['All Services'],
            createdBy: 'system',
            createdAt: new Date(Date.now() - 259200000), // 3 days ago
            updatedAt: new Date(),
            notifiedAt: new Date(Date.now() - 259200000),
            completedAt: null,
          },
        ];
      } else if (past) {
        events = [
          {
            id: 'mock-past-1',
            title: 'Network Infrastructure Update',
            description: 'Updated network infrastructure for improved reliability.',
            startTime: new Date(Date.now() - 604800000), // 1 week ago
            endTime: new Date(Date.now() - 590400000), // 1 week - 4 hours ago
            status: 'completed',
            severity: 'info',
            affectedServices: ['All Services'],
            createdBy: 'system',
            createdAt: new Date(Date.now() - 1209600000), // 2 weeks ago
            updatedAt: new Date(Date.now() - 590400000),
            notifiedAt: new Date(Date.now() - 1209600000),
            completedAt: new Date(Date.now() - 590400000),
          },
          {
            id: 'mock-past-2',
            title: 'Emergency Hotfix',
            description: 'Applied emergency fix for critical issue affecting job applications.',
            startTime: new Date(Date.now() - 259200000), // 3 days ago
            endTime: new Date(Date.now() - 255600000), // 3 days - 1 hour ago
            status: 'completed',
            severity: 'critical',
            affectedServices: ['Application System'],
            createdBy: 'system',
            createdAt: new Date(Date.now() - 262800000), // 3 days + 1 hour ago
            updatedAt: new Date(Date.now() - 255600000),
            notifiedAt: new Date(Date.now() - 262800000),
            completedAt: new Date(Date.now() - 255600000),
          },
        ];
      } else {
        // Default events
        events = [
          {
            id: 'mock-default-1',
            title: 'Database Maintenance',
            description: 'Scheduled database optimization and index rebuilding.',
            startTime: new Date(Date.now() - 3600000), // 1 hour ago
            endTime: new Date(Date.now() + 3600000), // 1 hour from now
            status: 'in-progress',
            severity: 'info',
            affectedServices: ['Database', 'API'],
            createdBy: 'system',
            createdAt: new Date(Date.now() - 86400000), // 1 day ago
            updatedAt: new Date(),
            notifiedAt: new Date(Date.now() - 86400000),
            completedAt: null,
          },
          {
            id: 'mock-default-2',
            title: 'System Upgrade',
            description: 'Upgrading core system components for improved performance.',
            startTime: new Date(Date.now() + 86400000), // 1 day from now
            endTime: new Date(Date.now() + 90000000), // 25 hours from now
            status: 'scheduled',
            severity: 'info',
            affectedServices: ['Website', 'API', 'Job Search'],
            createdBy: 'system',
            createdAt: new Date(Date.now() - 604800000), // 1 week ago
            updatedAt: new Date(),
            notifiedAt: new Date(Date.now() - 604800000),
            completedAt: null,
          },
        ];
      }
    }

    return json(events);
  } catch (error) {
    logger.error('Error fetching maintenance events:', error);
    return json({ error: 'Failed to fetch maintenance events' }, { status: 500 });
  }
};

// Create a new maintenance event
export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    // Check if user is admin
    const user = locals.user;
    if (!user || !user.isAdmin) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const data = await request.json();

    // Validate required fields
    if (
      !data.title ||
      !data.description ||
      !data.startTime ||
      !data.endTime ||
      !data.affectedServices
    ) {
      return json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Create maintenance event
    const event = await prisma.maintenanceEvent.create({
      data: {
        title: data.title,
        description: data.description,
        startTime: new Date(data.startTime),
        endTime: new Date(data.endTime),
        status: data.status || 'scheduled',
        affectedServices: data.affectedServices,
        createdBy: user.id,
        severity: data.severity || 'info',
      },
    });

    // Send notification if requested
    if (data.sendNotification) {
      await notifySystemMaintenance(
        new Date(data.startTime),
        new Date(data.endTime),
        data.description
      );

      // Update the event to mark notification as sent
      await prisma.maintenanceEvent.update({
        where: { id: event.id },
        data: { notifiedAt: new Date() },
      });
    }

    return json(event, { status: 201 });
  } catch (error) {
    logger.error('Error creating maintenance event:', error);
    return json({ error: 'Failed to create maintenance event' }, { status: 500 });
  }
};

// Update a maintenance event
export const PUT: RequestHandler = async ({ request, locals }) => {
  try {
    // Check if user is admin
    const user = locals.user;
    if (!user || !user.isAdmin) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const data = await request.json();

    // Validate required fields
    if (!data.id) {
      return json({ error: 'Missing event ID' }, { status: 400 });
    }

    // Check if event exists
    const existingEvent = await prisma.maintenanceEvent.findUnique({
      where: { id: data.id },
    });

    if (!existingEvent) {
      return json({ error: 'Maintenance event not found' }, { status: 404 });
    }

    // Update fields
    const updateData: any = {};

    if (data.title) updateData.title = data.title;
    if (data.description) updateData.description = data.description;
    if (data.startTime) updateData.startTime = new Date(data.startTime);
    if (data.endTime) updateData.endTime = new Date(data.endTime);
    if (data.status) updateData.status = data.status;
    if (data.affectedServices) updateData.affectedServices = data.affectedServices;
    if (data.severity) updateData.severity = data.severity;

    // Set completed time if status is completed
    if (data.status === 'completed' && existingEvent.status !== 'completed') {
      updateData.completedAt = new Date();
    }

    // Prepare history entry
    const historyData: any = {
      eventId: data.id,
      userId: user.id,
      comment: data.comment || null,
      metadata: {},
    };

    // Determine change type and record relevant changes
    if (data.status && data.status !== existingEvent.status) {
      historyData.changeType = 'status_change';
      historyData.previousStatus = existingEvent.status;
      historyData.newStatus = data.status;
      historyData.metadata.statusChange = true;
    } else if (data.comment) {
      historyData.changeType = 'comment';
    } else {
      historyData.changeType = 'update';

      // Record what fields were changed in metadata
      const changedFields = [];
      if (data.title && data.title !== existingEvent.title) changedFields.push('title');
      if (data.description && data.description !== existingEvent.description)
        changedFields.push('description');
      if (
        data.startTime &&
        new Date(data.startTime).toISOString() !== existingEvent.startTime.toISOString()
      )
        changedFields.push('startTime');
      if (
        data.endTime &&
        new Date(data.endTime).toISOString() !== existingEvent.endTime.toISOString()
      )
        changedFields.push('endTime');
      if (data.severity && data.severity !== existingEvent.severity) changedFields.push('severity');
      if (
        data.affectedServices &&
        JSON.stringify(data.affectedServices) !== JSON.stringify(existingEvent.affectedServices)
      )
        changedFields.push('affectedServices');

      historyData.metadata.changedFields = changedFields;
    }

    // Update the event
    const updatedEvent = await prisma.maintenanceEvent.update({
      where: { id: data.id },
      data: updateData,
    });

    // Create history entry
    await prisma.maintenanceEventHistory.create({
      data: historyData,
    });

    // Update service status if maintenance status changed
    if (data.status && data.status !== existingEvent.status) {
      // Update service status based on maintenance event
      await updateServiceStatus(updatedEvent, data.status);

      // Log the status change
      logger.info(
        `Maintenance event ${updatedEvent.id} status changed to ${data.status}, updating affected services`
      );
    }

    // Send notification if requested
    if (data.sendNotification) {
      await notifySystemMaintenance(
        new Date(updateData.startTime || existingEvent.startTime),
        new Date(updateData.endTime || existingEvent.endTime),
        updateData.description || existingEvent.description
      );

      // Update the event to mark notification as sent
      await prisma.maintenanceEvent.update({
        where: { id: updatedEvent.id },
        data: { notifiedAt: new Date() },
      });
    }

    return json(updatedEvent);
  } catch (error) {
    logger.error('Error updating maintenance event:', error);
    return json({ error: 'Failed to update maintenance event' }, { status: 500 });
  }
};

// Delete a maintenance event
export const DELETE: RequestHandler = async ({ request, locals }) => {
  try {
    // Check if user is admin
    const user = locals.user;
    if (!user || !user.isAdmin) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const data = await request.json();

    // Validate required fields
    if (!data.id) {
      return json({ error: 'Missing event ID' }, { status: 400 });
    }

    // Delete the event
    await prisma.maintenanceEvent.delete({
      where: { id: data.id },
    });

    return json({ success: true });
  } catch (error) {
    logger.error('Error deleting maintenance event:', error);
    return json({ error: 'Failed to delete maintenance event' }, { status: 500 });
  }
};

// The updateServiceStatus function is now imported from './updateServiceStatus'
