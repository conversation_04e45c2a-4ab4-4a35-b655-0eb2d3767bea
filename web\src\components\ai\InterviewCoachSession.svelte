<script lang="ts">
  import { X, Send, MessageSquare, Sparkles, ChevronLeft, ChevronRight } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import * as Card from '$lib/components/ui/card';
  import * as Dialog from '$lib/components/ui/dialog';
  import { Button } from '$lib/components/ui/button';
  import {
    createInterviewCoachingSession,
    submitInterviewResponse,
    updateInterviewSessionStatus,
    type AICoachingSession,
  } from '$lib/services/ai-service';

  // Props
  const { applicationId, jobTitle, company, jobDescription, onClose } = $props<{
    applicationId?: string;
    jobTitle: string;
    company?: string;
    jobDescription?: string;
    onClose: () => void;
  }>();

  // State
  let open = $state(true); // Dialog open state
  let session = $state<AICoachingSession | null>(null);
  let currentQuestionIndex = $state(0);
  let userResponse = $state('');
  let isLoading = $state(true);
  let isSubmitting = $state(false);
  let feedback = $state<string | null>(null);
  let showFeedback = $state(false);

  // Handle dialog close
  function handleDialogClose(isOpen: boolean) {
    if (!isOpen) {
      cancelSession();
    }
  }

  // Initialize the session
  $effect(() => {
    initSession();
  });

  // Computed properties
  const currentQuestion = $derived(
    session?.questions?.[currentQuestionIndex]?.question || 'Loading question...'
  );

  const hasAnsweredCurrentQuestion = $derived(
    session?.responses?.some((r) => r.questionIndex === currentQuestionIndex) || false
  );

  const currentFeedback = $derived(
    session?.feedback?.find((f) => f.questionIndex === currentQuestionIndex)?.feedback || null
  );

  const isLastQuestion = $derived(
    session ? currentQuestionIndex === session.questions.length - 1 : false
  );

  const isFirstQuestion = $derived(currentQuestionIndex === 0);

  const progress = $derived(
    session ? Math.round(((currentQuestionIndex + 1) / session.questions.length) * 100) : 0
  );

  // Initialize the session
  async function initSession() {
    try {
      isLoading = true;

      // Create a new coaching session
      const newSession = await createInterviewCoachingSession(
        jobTitle,
        applicationId,
        company,
        jobDescription
      );

      if (!newSession) {
        toast.error('Failed to create interview coaching session');
        onClose();
        return;
      }

      session = newSession;
    } catch (error) {
      console.error('Error initializing session:', error);
      toast.error('Failed to initialize interview coaching session');
      onClose();
    } finally {
      isLoading = false;
    }
  }

  // Submit the user's response
  async function submitResponse() {
    if (!session || !userResponse.trim()) return;

    try {
      isSubmitting = true;

      const result = await submitInterviewResponse(session.id, currentQuestionIndex, userResponse);

      if (!result) {
        toast.error('Failed to submit response');
        return;
      }

      // Update the session with the new response and feedback
      session = result.session;
      feedback = result.feedback;
      showFeedback = true;

      toast.success('Response submitted');
    } catch (error) {
      console.error('Error submitting response:', error);
      toast.error('Failed to submit response');
    } finally {
      isSubmitting = false;
    }
  }

  // Go to the next question
  function nextQuestion() {
    if (isLastQuestion) {
      completeSession();
      return;
    }

    showFeedback = false;
    feedback = null;
    userResponse = '';
    currentQuestionIndex++;
  }

  // Go to the previous question
  function prevQuestion() {
    if (isFirstQuestion) return;

    showFeedback = false;
    feedback = null;
    userResponse = '';
    currentQuestionIndex--;
  }

  // Complete the session
  async function completeSession() {
    if (!session) return;

    try {
      await updateInterviewSessionStatus(session.id, 'completed');
      toast.success('Interview coaching session completed');
      open = false;
      onClose();
    } catch (error) {
      console.error('Error completing session:', error);
      toast.error('Failed to complete session');
    }
  }

  // Cancel the session
  async function cancelSession() {
    if (!session) {
      onClose();
      return;
    }

    try {
      await updateInterviewSessionStatus(session.id, 'cancelled');
      toast.success('Interview coaching session cancelled');
      open = false;
      onClose();
    } catch (error) {
      console.error('Error cancelling session:', error);
      toast.error('Failed to cancel session');
      open = false;
      onClose();
    }
  }
</script>

<Dialog.Root bind:open onOpenChange={handleDialogClose}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-4xl p-0">
      <!-- Header -->
      <div class="flex items-center justify-between border-b p-4">
        <div>
          <h2 class="text-xl font-bold">AI Interview Coach</h2>
          <p class="text-sm text-gray-500">
            {jobTitle}
            {company ? `at ${company}` : ''}
          </p>
        </div>
        <Dialog.Close class="rounded-full p-1 hover:bg-gray-100">
          <X class="h-5 w-5" />
          <span class="sr-only">Close</span>
        </Dialog.Close>
      </div>

      {#if isLoading}
        <div class="flex h-96 items-center justify-center">
          <div class="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-500">
          </div>
          <span class="ml-2">Preparing your interview questions...</span>
        </div>
      {:else}
        <div class="grid h-[600px] grid-cols-1 md:grid-cols-2">
          <!-- Question Panel -->
          <div class="flex flex-col border-r">
            <div class="flex-1 p-6">
              <div class="mb-4 flex items-center">
                <div
                  class="mr-2 flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-700">
                  <span>{currentQuestionIndex + 1}</span>
                </div>
                <div class="text-sm text-gray-500">
                  Question {currentQuestionIndex + 1} of {session?.questions?.length || 0}
                </div>
              </div>

              <Card.Root class="mb-4">
                <Card.Content class="p-4">
                  <div class="flex items-start">
                    <MessageSquare class="mr-2 mt-1 h-5 w-5 text-blue-500" />
                    <p>{currentQuestion}</p>
                  </div>
                </Card.Content>
              </Card.Root>

              <div class="mb-4">
                <textarea
                  class="w-full rounded-md border border-gray-300 p-2"
                  placeholder="Type your response here..."
                  rows={8}
                  bind:value={userResponse}
                  disabled={isSubmitting || hasAnsweredCurrentQuestion}></textarea>
              </div>

              <div class="flex justify-between">
                <Button
                  variant="outline"
                  disabled={isFirstQuestion || isSubmitting}
                  onclick={prevQuestion}>
                  <ChevronLeft class="mr-1 h-4 w-4" />
                  Previous
                </Button>

                {#if hasAnsweredCurrentQuestion}
                  <Button onclick={nextQuestion}>
                    {isLastQuestion ? 'Complete' : 'Next'}
                    <ChevronRight class="ml-1 h-4 w-4" />
                  </Button>
                {:else}
                  <Button disabled={!userResponse.trim() || isSubmitting} onclick={submitResponse}>
                    {isSubmitting ? 'Submitting...' : 'Submit'}
                    <Send class="ml-1 h-4 w-4" />
                  </Button>
                {/if}
              </div>
            </div>

            <div class="border-t p-4">
              <div class="h-2 w-full rounded-full bg-gray-200">
                <div class="h-2 rounded-full bg-blue-500" style="width: {progress}%"></div>
              </div>
            </div>
          </div>

          <!-- Feedback Panel -->
          <div class="flex flex-col bg-gray-50">
            <div class="flex-1 p-6">
              <div class="mb-4 flex items-center">
                <Sparkles class="mr-2 h-5 w-5 text-blue-500" />
                <h3 class="text-lg font-medium">AI Feedback</h3>
              </div>

              {#if showFeedback && currentFeedback}
                <Card.Root>
                  <Card.Content class="p-4">
                    <div class="prose prose-sm max-w-none">
                      <p>{currentFeedback}</p>
                    </div>
                  </Card.Content>
                </Card.Root>
              {:else if hasAnsweredCurrentQuestion}
                <div class="flex h-full items-center justify-center">
                  <Button
                    variant="outline"
                    class="border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100"
                    onclick={() => (showFeedback = true)}>
                    <Sparkles class="mr-2 h-4 w-4" />
                    Show Feedback
                  </Button>
                </div>
              {:else}
                <div class="flex h-full flex-col items-center justify-center text-center">
                  <Sparkles class="mb-2 h-8 w-8 text-blue-300" />
                  <p class="text-gray-500">
                    Submit your response to receive AI feedback on your interview answer.
                  </p>
                </div>
              {/if}
            </div>
          </div>
        </div>
      {/if}
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
