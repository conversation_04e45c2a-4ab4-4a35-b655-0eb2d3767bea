import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';
import { OpenAI } from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { profileId, jobId } = await request.json();

    if (!profileId || !jobId) {
      return json({ error: 'Profile ID and Job ID are required' }, { status: 400 });
    }

    // Get the profile
    const profile = await prisma.profile.findUnique({
      where: {
        id: profileId,
        userId: locals.user.id
      },
      include: {
        education: true,
        experience: true,
        skills: true,
        certifications: true
      }
    });

    if (!profile) {
      return json({ error: 'Profile not found' }, { status: 404 });
    }

    // Get the job
    const job = await prisma.jobListing.findUnique({
      where: {
        id: jobId
      },
      select: {
        title: true,
        company: true,
        description: true,
        requirements: true,
        skills: true,
        location: true,
        salary: true,
        remote: true
      }
    });

    if (!job) {
      return json({ error: 'Job not found' }, { status: 404 });
    }

    // Generate job match analysis
    const analysis = await generateJobMatchAnalysis(profile, job);

    // Save the analysis to the database
    const savedAnalysis = await prisma.jobMatchAnalysis.create({
      data: {
        userId: locals.user.id,
        profileId,
        jobId,
        overallMatchScore: analysis.overallMatchScore,
        skillsMatchScore: analysis.skillsMatchScore,
        experienceMatchScore: analysis.experienceMatchScore,
        educationMatchScore: analysis.educationMatchScore,
        keywordMatchScore: analysis.keywordMatchScore,
        matchedSkills: analysis.matchedSkills,
        missingSkills: analysis.missingSkills,
        strengthAreas: analysis.strengthAreas,
        improvementAreas: analysis.improvementAreas,
        recommendations: analysis.recommendations,
        jobTitle: job.title,
        company: job.company
      }
    });

    // Update feature usage
    await prisma.featureUsage.upsert({
      where: {
        userId_featureId_limitId: {
          userId: locals.user.id,
          featureId: 'ai_job_matching',
          limitId: 'job_match_analyses_monthly'
        }
      },
      update: {
        usage: {
          increment: 1
        }
      },
      create: {
        userId: locals.user.id,
        featureId: 'ai_job_matching',
        limitId: 'job_match_analyses_monthly',
        usage: 1
      }
    });

    return json({ analysis: savedAnalysis });
  } catch (error) {
    console.error('Error generating job match analysis:', error);
    return json({ error: 'Failed to generate job match analysis' }, { status: 500 });
  }
};

export const GET: RequestHandler = async ({ url, locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const profileId = url.searchParams.get('profileId');
    const jobId = url.searchParams.get('jobId');

    if (!profileId || !jobId) {
      return json({ error: 'Profile ID and Job ID are required' }, { status: 400 });
    }

    // Get the latest job match analysis
    const analysis = await prisma.jobMatchAnalysis.findFirst({
      where: {
        userId: locals.user.id,
        profileId,
        jobId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!analysis) {
      return json({ error: 'No job match analysis found' }, { status: 404 });
    }

    return json({ analysis });
  } catch (error) {
    console.error('Error fetching job match analysis:', error);
    return json({ error: 'Failed to fetch job match analysis' }, { status: 500 });
  }
};

// Helper function to generate job match analysis
async function generateJobMatchAnalysis(profile: any, job: any): Promise<any> {
  try {
    // Prepare the prompt
    const prompt = `Analyze how well this candidate's profile matches the job:

Profile:
${JSON.stringify(profile)}

Job:
${JSON.stringify(job)}

Provide a detailed analysis with the following scores (0-1, where 1 is perfect match):
1. Overall Match Score
2. Skills Match Score
3. Experience Match Score
4. Education Match Score
5. Keyword Match Score

Also provide:
- Matched skills
- Missing skills
- Strength areas
- Areas for improvement
- Recommendations for the candidate

Return the analysis as a JSON object with these fields.
`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: 'You are an expert job match analyzer.' },
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 2000,
      response_format: { type: 'json_object' }
    });

    // Parse the response
    const content = response.choices[0]?.message?.content || '';
    const analysisResult = JSON.parse(content);

    // Ensure all required fields are present
    return {
      overallMatchScore: analysisResult.overallMatchScore || 0,
      skillsMatchScore: analysisResult.skillsMatchScore || 0,
      experienceMatchScore: analysisResult.experienceMatchScore || 0,
      educationMatchScore: analysisResult.educationMatchScore || 0,
      keywordMatchScore: analysisResult.keywordMatchScore || 0,
      matchedSkills: analysisResult.matchedSkills || [],
      missingSkills: analysisResult.missingSkills || [],
      strengthAreas: analysisResult.strengthAreas || [],
      improvementAreas: analysisResult.improvementAreas || [],
      recommendations: analysisResult.recommendations || []
    };
  } catch (error) {
    console.error('Error in job match analysis generation:', error);
    
    // Return default values if analysis fails
    return {
      overallMatchScore: 0.7,
      skillsMatchScore: 0.65,
      experienceMatchScore: 0.75,
      educationMatchScore: 0.8,
      keywordMatchScore: 0.6,
      matchedSkills: ['communication', 'teamwork'],
      missingSkills: ['specific technical skills'],
      strengthAreas: ['education', 'general experience'],
      improvementAreas: ['technical skills', 'industry-specific experience'],
      recommendations: ['Consider highlighting relevant experience more prominently', 'Add more specific technical skills to your profile']
    };
  }
}
