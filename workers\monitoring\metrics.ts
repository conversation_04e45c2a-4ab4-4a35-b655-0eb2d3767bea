/**
 * Worker Metrics Collection
 *
 * This module provides functions for collecting and analyzing worker metrics.
 */

import os from "node:os";
import { createLogger } from "../utils/logger.js";
import { WorkerType, CircuitState } from "./index.js";
import { redis } from "../redis.js";
import { getContainerMetrics } from "../utils/containerMetrics.js";

// Create a logger for the metrics system
const logger = createLogger("worker-metrics");

// Redis keys
const WORKER_CIRCUIT_KEY = "worker:circuit";

// Track last report time for metrics reset
let lastReportTime = Date.now();

// Performance metrics
const performanceMetrics: Record<
  string,
  {
    requestCount: number;
    errorCount: number;
    totalResponseTime: number;
    queueSize: number;
    processingCount: number;
  }
> = {};

/**
 * Collect system and application metrics
 */
export async function collectMetrics(workerType: WorkerType): Promise<{
  cpu: number;
  memory: number;
  queueSize: number;
  processingCount: number;
  responseTime: number;
  errorRate: number;
  successRate: number;
  capacity: number;
  uptime: number;
}> {
  // Initialize performance metrics for this worker type if not exists
  if (!performanceMetrics[workerType]) {
    performanceMetrics[workerType] = {
      requestCount: 0,
      errorCount: 0,
      totalResponseTime: 0,
      queueSize: 0,
      processingCount: 0,
    };
  }

  // Get container metrics
  let cpuPercent = 0;
  let processMemPercent = 0;

  try {
    // Get container metrics
    const containerMetrics = await getContainerMetrics();

    if (containerMetrics) {
      // Use container metrics
      cpuPercent = containerMetrics.cpuUsagePercent;
      processMemPercent = containerMetrics.memoryUsagePercent;

      logger.debug(
        `📊 Using container metrics - CPU: ${cpuPercent.toFixed(1)}%, Memory: ${processMemPercent.toFixed(1)}%`
      );
    } else {
      // Not in a container, log a warning
      logger.warn(
        "⚠️ Not running in a container, using default values for metrics"
      );
    }
  } catch (error) {
    logger.error(`❌ Error getting container metrics: ${error}`);
  }

  // Ensure we don't have NaN values
  if (isNaN(cpuPercent)) {
    logger.warn(`⚠️ CPU usage is NaN, using fallback value of 0`);
    cpuPercent = 0;
  }

  if (isNaN(processMemPercent)) {
    logger.warn(`⚠️ Memory usage is NaN, using fallback value of 0`);
    processMemPercent = 0;
  }

  // Use fixed capacity values based on container metrics

  // Get metrics from performance metrics
  const {
    requestCount,
    errorCount,
    totalResponseTime,
    queueSize,
    processingCount,
  } = performanceMetrics[workerType];

  // Calculate error rate
  const errorRate = requestCount > 0 ? (errorCount / requestCount) * 100 : 0;

  // Calculate success rate
  const successRate =
    requestCount > 0 ? ((requestCount - errorCount) / requestCount) * 100 : 100;

  // Calculate average response time
  const avgResponseTime =
    requestCount > 0 ? totalResponseTime / requestCount : 0;

  // Calculate available capacity based on container metrics
  const cpuCapacity = Math.max(0, 100 - cpuPercent);
  const memCapacity = Math.max(0, 100 - processMemPercent);

  // Capacity is the minimum of CPU and memory capacity
  const capacity = Math.min(cpuCapacity, memCapacity);

  // Reset request counters every minute
  const now = Date.now();
  if (now - lastReportTime > 60000) {
    performanceMetrics[workerType].requestCount = 0;
    performanceMetrics[workerType].errorCount = 0;
    performanceMetrics[workerType].totalResponseTime = 0;
    lastReportTime = now;
  }

  return {
    cpu: Math.round(cpuPercent * 10) / 10,
    memory: Math.round(processMemPercent * 10) / 10,
    queueSize,
    processingCount,
    responseTime: Math.round(avgResponseTime * 10) / 10,
    errorRate: Math.round(errorRate * 10) / 10,
    successRate: Math.round(successRate * 10) / 10,
    capacity: Math.round(capacity * 10) / 10,
    uptime: Math.floor(process.uptime()),
  };
}

/**
 * Record a request processed by the worker
 * @param workerType The worker type
 * @param responseTime Response time in milliseconds
 * @param isError Whether the request resulted in an error
 */
export function recordRequest(
  workerType: WorkerType,
  responseTime: number,
  isError: boolean = false
): void {
  // Initialize performance metrics for this worker type if not exists
  if (!performanceMetrics[workerType]) {
    performanceMetrics[workerType] = {
      requestCount: 0,
      errorCount: 0,
      totalResponseTime: 0,
      queueSize: 0,
      processingCount: 0,
    };
  }

  performanceMetrics[workerType].requestCount++;
  performanceMetrics[workerType].totalResponseTime += responseTime;

  if (isError) {
    performanceMetrics[workerType].errorCount++;
  }
}

/**
 * Update queue metrics for a worker
 * @param workerType The worker type
 * @param queueSize Current queue size
 * @param processingCount Number of items being processed
 */
export function updateQueueMetrics(
  workerType: WorkerType,
  queueSize: number,
  processingCount: number
): void {
  // Initialize performance metrics for this worker type if not exists
  if (!performanceMetrics[workerType]) {
    performanceMetrics[workerType] = {
      requestCount: 0,
      errorCount: 0,
      totalResponseTime: 0,
      queueSize: 0,
      processingCount: 0,
    };
  }

  performanceMetrics[workerType].queueSize = queueSize;
  performanceMetrics[workerType].processingCount = processingCount;
}

/**
 * Update circuit breaker status based on metrics
 */
export async function updateCircuitBreaker(
  workerType: WorkerType,
  metrics: {
    cpu: number;
    memory: number;
    errorRate: number;
    capacity: number;
    successRate: number;
  }
): Promise<void> {
  try {
    // Get current circuit state
    const circuitJson = await redis.hget(WORKER_CIRCUIT_KEY, workerType);
    let circuit = {
      state: CircuitState.CLOSED,
      failureCount: 0,
      lastStateChange: new Date().toISOString(),
    };

    if (circuitJson) {
      try {
        circuit = JSON.parse(circuitJson);
      } catch (error) {
        logger.error(`Error parsing circuit data for ${workerType}:`, error);
      }
    }

    // Update circuit state based on metrics
    const newCircuit = { ...circuit };

    // Check if metrics indicate a problem
    if (metrics.errorRate > 50 || metrics.capacity < 10) {
      newCircuit.failureCount++;
    } else {
      newCircuit.failureCount = 0;
    }

    // Update circuit state based on failure count
    if (circuit.state === CircuitState.CLOSED && newCircuit.failureCount >= 5) {
      // Open the circuit after 5 consecutive failures
      newCircuit.state = CircuitState.OPEN;
      newCircuit.lastStateChange = new Date().toISOString();
      logger.warn(`Circuit breaker opened for worker type: ${workerType}`);
    } else if (circuit.state === CircuitState.OPEN) {
      // Check if it's time to try half-open
      const lastChange = new Date(circuit.lastStateChange).getTime();
      const now = Date.now();

      // Try half-open after 30 seconds
      if (now - lastChange > 30000) {
        newCircuit.state = CircuitState.HALF_OPEN;
        newCircuit.lastStateChange = new Date().toISOString();
        logger.info(
          `Circuit breaker half-opened for worker type: ${workerType}`
        );
      }
    } else if (circuit.state === CircuitState.HALF_OPEN) {
      if (newCircuit.failureCount > 0) {
        // If any failure in half-open state, go back to open
        newCircuit.state = CircuitState.OPEN;
        newCircuit.lastStateChange = new Date().toISOString();
        logger.warn(`Circuit breaker re-opened for worker type: ${workerType}`);
      } else if (metrics.successRate > 90 && metrics.capacity > 50) {
        // If success in half-open state, close the circuit
        newCircuit.state = CircuitState.CLOSED;
        newCircuit.failureCount = 0;
        newCircuit.lastStateChange = new Date().toISOString();
        logger.info(`Circuit breaker closed for worker type: ${workerType}`);
      }
    }

    // Store updated circuit state
    if (
      newCircuit.state !== circuit.state ||
      newCircuit.failureCount !== circuit.failureCount
    ) {
      await redis.hset(
        WORKER_CIRCUIT_KEY,
        workerType,
        JSON.stringify(newCircuit)
      );
    }
  } catch (error) {
    logger.error(`Error updating circuit breaker for ${workerType}:`, error);
  }
}
