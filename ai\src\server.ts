import express from "express";
import cors from "cors";
import helmet from "helmet";
import { rateLimit } from "express-rate-limit";
import { config } from "./config.js";
import routes from "./routes/index.js";
import { setupHealthMonitor } from "./services/health/monitor.js";
import { logger } from "./utils/logger.js";
import { AppError } from "./utils/errors.js";

export async function createServer() {
  const app = express();

  // Middleware
  app.use(helmet());
  app.use(cors());
  app.use(express.json({ limit: "10mb" }));

  // Simple request logger middleware
  app.use((req, res, next) => {
    const start = Date.now();

    // Log when the request completes
    res.on("finish", () => {
      const duration = Date.now() - start;
      logger.info(
        `🌐 ${req.method} ${req.originalUrl} ${res.statusCode} - ${duration}ms`
      );
    });

    next();
  });

  // Rate limiting
  app.use(
    rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      standardHeaders: true,
      legacyHeaders: false,
    })
  );

  // Routes
  app.use("/api", routes);

  // Error handling
  app.use(
    (
      err: any,
      req: express.Request,
      res: express.Response,
      next: express.NextFunction
    ) => {
      if (err instanceof AppError) {
        res.status(err.status).json({
          error: {
            message: err.message,
            status: err.status,
          },
        });
      } else {
        logger.error(err);
        res.status(500).json({
          error: {
            message: "Internal Server Error",
            ...(process.env.NODE_ENV !== "production" && { stack: err.stack }),
          },
        });
      }
    }
  );

  return app;
}

export async function startServer() {
  try {
    const app = await createServer();

    // Start health monitoring
    setupHealthMonitor();

    const server = app.listen(config.server.port, () => {
      logger.info(
        `🚀 Server running at http://${config.server.host}:${config.server.port}`
      );
    });

    // Graceful shutdown
    process.on("SIGTERM", () => {
      logger.info("⚠️ SIGTERM signal received: closing HTTP server");
      server.close(() => {
        logger.info("✅ HTTP server closed");
      });
    });

    return server;
  } catch (error) {
    logger.error("Failed to start server:", error);
    process.exit(1);
  }
}
