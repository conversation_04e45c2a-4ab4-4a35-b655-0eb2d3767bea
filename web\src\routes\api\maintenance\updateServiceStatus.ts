// src/routes/api/maintenance/updateServiceStatus.ts
import { prisma } from '$lib/server/prisma';
import { logger } from '$lib/server/logger';

/**
 * Update service status based on maintenance event
 * @param event Maintenance event
 * @param status Optional status override
 */
export async function updateServiceStatus(event: any, status?: string) {
  try {
    // Use event status if not explicitly provided
    const maintenanceStatus = status || event.status;

    // Parse affected services
    const affectedServices = Array.isArray(event.affectedServices)
      ? event.affectedServices
      : typeof event.affectedServices === 'string'
        ? event.affectedServices.split(',').map((s: string) => s.trim())
        : [];

    // Map affected services to our service categories
    const serviceMapping: Record<string, string[]> = {
      Matches: ['API', 'Job Search', 'Matching'],
      Jobs: ['API', 'Job Search', 'Jobs'],
      Tracker: ['API', 'Application System', 'Tracker'],
      Documents: ['API', 'Resume Builder', 'Documents'],
      Automation: ['Worker', 'Automation'],
      System: ['Database', 'System', 'Core'],
      Website: ['Web', 'Website', 'Frontend'],
    };

    // Determine which services are affected
    const affectedCategories = Object.entries(serviceMapping)
      .filter(([_, keywords]) =>
        keywords.some((keyword) =>
          affectedServices.some((service) => service.toLowerCase().includes(keyword.toLowerCase()))
        )
      )
      .map(([category]) => category);

    // If no specific services are matched, don't update any service status
    if (affectedCategories.length === 0) {
      return;
    }

    // Get current service statuses
    const services = await prisma.serviceStatus.findMany({
      where: {
        name: {
          in: affectedCategories,
        },
      },
    });

    // Update service status based on maintenance status
    for (const service of services) {
      let newStatus = service.status;

      if (maintenanceStatus === 'in-progress') {
        // If maintenance is in progress, set service status to 'maintenance'
        newStatus = 'maintenance';
      } else if (maintenanceStatus === 'completed') {
        // If maintenance is completed, set service status back to 'operational'
        newStatus = 'operational';
      }

      // Only update if status has changed
      if (service.status !== newStatus) {
        // Update service status
        await prisma.serviceStatus.update({
          where: { id: service.id },
          data: {
            status: newStatus,
            lastCheckedAt: new Date(),
          },
        });

        // Record status change in history
        await prisma.serviceStatusHistory.create({
          data: {
            serviceId: service.id,
            status: newStatus,
          },
        });

        logger.info(`Updated status for ${service.name} to ${newStatus} due to maintenance event`);
      }
    }
  } catch (error) {
    logger.error('Error updating service status from maintenance event:', error);
  }
}
