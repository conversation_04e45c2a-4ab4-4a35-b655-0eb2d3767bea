import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken, getUserFromToken } from '$lib/server/auth.js';
import type { RequestHandler } from './$types';

/**
 * GET /api/jobs/[id]
 * Get job details by ID
 */
export const GET: RequestHandler = async ({ params, cookies }) => {
  try {
    // Try both token methods for compatibility
    let user = null;
    const authToken = cookies.get('auth_token');
    const token = cookies.get('token');
    
    if (authToken) {
      user = await verifySessionToken(authToken);
    } else if (token) {
      user = await getUserFromToken(token);
    }

    if (!user) {
      return json({ error: 'Authentication required' }, { status: 401 });
    }

    const { id } = params;

    if (!id) {
      return json({ error: 'Job ID is required' }, { status: 400 });
    }

    // Get job details
    const job = await prisma.job_listing.findUnique({
      where: { id },
    });

    if (!job) {
      return json({ error: 'Job not found' }, { status: 404 });
    }

    // Get job match score if available
    const jobMatch = await prisma.job_match_result.findFirst({
      where: {
        jobId: id,
        userId: user.id,
      },
    });

    // Get similar jobs
    const similarJobs = await prisma.job_listing.findMany({
      where: {
        id: { not: id },
        OR: [
          { title: { contains: job.title.split(' ')[0] } },
          { company: { equals: job.company } },
        ],
        isActive: true,
      },
      take: 5,
    });

    return json({
      success: true,
      job,
      matchScore: jobMatch?.matchScore || null,
      similarJobs,
    });
  } catch (error) {
    console.error('Error getting job details:', error);
    return json({ error: 'Failed to get job details' }, { status: 500 });
  }
};

/**
 * POST /api/jobs/[id]/save
 * Save a job to user's saved jobs
 */
export const POST: RequestHandler = async ({ params, request, cookies }) => {
  try {
    // Try both token methods for compatibility
    let user = null;
    const authToken = cookies.get('auth_token');
    const token = cookies.get('token');
    
    if (authToken) {
      user = await verifySessionToken(authToken);
    } else if (token) {
      user = await getUserFromToken(token);
    }

    if (!user) {
      return json({ error: 'Authentication required' }, { status: 401 });
    }

    const { id } = params;
    const { action } = await request.json();

    if (!id) {
      return json({ error: 'Job ID is required' }, { status: 400 });
    }

    // Check if job exists
    const job = await prisma.job_listing.findUnique({
      where: { id },
    });

    if (!job) {
      return json({ error: 'Job not found' }, { status: 404 });
    }

    // Handle different actions
    if (action === 'save') {
      // Check if job is already saved
      const existingSavedJob = await prisma.savedJob.findFirst({
        where: {
          userId: user.id,
          jobId: id,
        },
      });

      if (existingSavedJob) {
        return json({
          success: true,
          message: 'Job already saved',
          savedJob: existingSavedJob,
        });
      }

      // Save the job
      const savedJob = await prisma.savedJob.create({
        data: {
          userId: user.id,
          jobId: id,
        },
      });

      return json({
        success: true,
        message: 'Job saved successfully',
        savedJob,
      });
    } else if (action === 'unsave') {
      // Remove from saved jobs
      await prisma.savedJob.deleteMany({
        where: {
          userId: user.id,
          jobId: id,
        },
      });

      return json({
        success: true,
        message: 'Job removed from saved jobs',
      });
    } else {
      return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error saving job:', error);
    return json({ error: 'Failed to save job' }, { status: 500 });
  }
};
