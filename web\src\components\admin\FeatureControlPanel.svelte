<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import * as Card from '$lib/components/ui/card';
  import * as Table from '$lib/components/ui/table';
  import { Badge } from '$lib/components/ui/badge';
  import { Switch } from '$lib/components/ui/switch';
  import { 
    FEATURE_FLAGS, 
    ENVIRONMENT_CONFIG,
    isFeatureEnabled, 
    shouldBypassLimits,
    toggleFeature,
    getEnabledFeatures,
    listAllFeatures
  } from '$lib/config/feature-flags';
  import { Settings, Eye, EyeOff, RefreshCw } from 'lucide-svelte';

  let searchQuery = $state('');
  let showOnlyEnabled = $state(false);

  // Filter features based on search and toggle
  const filteredFeatures = $derived(() => {
    const features = Object.entries(FEATURE_FLAGS);
    
    return features.filter(([featureId, config]) => {
      // Search filter
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        return (
          featureId.toLowerCase().includes(query) ||
          config.description?.toLowerCase().includes(query)
        );
      }

      // Show only enabled filter
      if (showOnlyEnabled) {
        return config.enabled;
      }

      return true;
    });
  });

  function handleToggleFeature(featureId: string, enabled: boolean) {
    toggleFeature(featureId, enabled);
    // Force reactivity update
    FEATURE_FLAGS[featureId] = { ...FEATURE_FLAGS[featureId] };
  }

  function enableAllFeatures() {
    Object.keys(FEATURE_FLAGS).forEach(featureId => {
      toggleFeature(featureId, true);
    });
  }

  function disableAllFeatures() {
    Object.keys(FEATURE_FLAGS).forEach(featureId => {
      toggleFeature(featureId, false);
    });
  }

  function logFeatureStatus() {
    listAllFeatures();
  }
</script>

<Card.Root>
  <Card.Header>
    <div class="flex items-center justify-between">
      <div>
        <Card.Title class="flex items-center gap-2">
          <Settings class="h-5 w-5" />
          Feature Control Panel
        </Card.Title>
        <Card.Description>
          Manage feature flags and access controls across the application
        </Card.Description>
      </div>
      <Badge variant="outline">
        {getEnabledFeatures().length} / {Object.keys(FEATURE_FLAGS).length} enabled
      </Badge>
    </div>
  </Card.Header>

  <Card.Content class="space-y-6">
    <!-- Environment Status -->
    <div class="rounded-lg border p-4">
      <h3 class="mb-3 font-semibold">Environment Configuration</h3>
      <div class="grid grid-cols-2 gap-4 text-sm">
        <div class="flex justify-between">
          <span>Disable All Limits:</span>
          <Badge variant={ENVIRONMENT_CONFIG.DISABLE_ALL_LIMITS ? 'default' : 'outline'}>
            {ENVIRONMENT_CONFIG.DISABLE_ALL_LIMITS ? 'Yes' : 'No'}
          </Badge>
        </div>
        <div class="flex justify-between">
          <span>Development Bypass:</span>
          <Badge variant={ENVIRONMENT_CONFIG.DEVELOPMENT_BYPASS ? 'default' : 'outline'}>
            {ENVIRONMENT_CONFIG.DEVELOPMENT_BYPASS ? 'Yes' : 'No'}
          </Badge>
        </div>
        <div class="flex justify-between">
          <span>Enable All Features:</span>
          <Badge variant={ENVIRONMENT_CONFIG.ENABLE_ALL_FEATURES ? 'default' : 'outline'}>
            {ENVIRONMENT_CONFIG.ENABLE_ALL_FEATURES ? 'Yes' : 'No'}
          </Badge>
        </div>
        <div class="flex justify-between">
          <span>Disabled Features:</span>
          <Badge variant="outline">
            {ENVIRONMENT_CONFIG.DISABLED_FEATURES.length || 'None'}
          </Badge>
        </div>
      </div>
    </div>

    <!-- Controls -->
    <div class="flex flex-wrap items-center gap-4">
      <Input
        placeholder="Search features..."
        bind:value={searchQuery}
        class="max-w-sm" />
      
      <div class="flex items-center gap-2">
        <Switch bind:checked={showOnlyEnabled} />
        <span class="text-sm">Show only enabled</span>
      </div>

      <div class="flex gap-2">
        <Button variant="outline" size="sm" onclick={enableAllFeatures}>
          Enable All
        </Button>
        <Button variant="outline" size="sm" onclick={disableAllFeatures}>
          Disable All
        </Button>
        <Button variant="outline" size="sm" onclick={logFeatureStatus}>
          <RefreshCw class="mr-2 h-4 w-4" />
          Log Status
        </Button>
      </div>
    </div>

    <!-- Features Table -->
    <div class="rounded-md border">
      <Table.Root>
        <Table.Header>
          <Table.Row>
            <Table.Head>Feature ID</Table.Head>
            <Table.Head>Description</Table.Head>
            <Table.Head>Status</Table.Head>
            <Table.Head>Bypass Limits</Table.Head>
            <Table.Head>Actions</Table.Head>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {#each filteredFeatures() as [featureId, config] (featureId)}
            <Table.Row>
              <Table.Cell class="font-mono text-sm">{featureId}</Table.Cell>
              <Table.Cell class="max-w-xs truncate">
                {config.description || 'No description'}
              </Table.Cell>
              <Table.Cell>
                <Badge variant={config.enabled ? 'default' : 'secondary'}>
                  {config.enabled ? 'Enabled' : 'Disabled'}
                </Badge>
              </Table.Cell>
              <Table.Cell>
                <Badge variant={shouldBypassLimits(featureId) ? 'default' : 'outline'}>
                  {shouldBypassLimits(featureId) ? 'Yes' : 'No'}
                </Badge>
              </Table.Cell>
              <Table.Cell>
                <div class="flex items-center gap-2">
                  <Switch
                    checked={config.enabled}
                    onCheckedChange={(checked) => handleToggleFeature(featureId, checked)} />
                  <Button
                    variant="ghost"
                    size="sm"
                    onclick={() => console.log('Feature details:', { featureId, config, isEnabled: isFeatureEnabled(featureId) })}>
                    {#if config.enabled}
                      <Eye class="h-4 w-4" />
                    {:else}
                      <EyeOff class="h-4 w-4" />
                    {/if}
                  </Button>
                </div>
              </Table.Cell>
            </Table.Row>
          {/each}
        </Table.Body>
      </Table.Root>
    </div>

    {#if filteredFeatures().length === 0}
      <div class="py-8 text-center text-gray-500">
        No features match your search criteria.
      </div>
    {/if}
  </Card.Content>
</Card.Root>
