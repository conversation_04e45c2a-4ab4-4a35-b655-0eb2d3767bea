<script lang="ts">
  import { CalendarIcon } from 'lucide-svelte';
  import type { DateRange } from 'bits-ui';
  import { DateFormatter, type DateValue, getLocalTimeZone } from '@internationalized/date';
  import { cn } from '$lib/utils.js';
  import { Button } from '$lib/components/ui/button';
  import * as Popover from '$lib/components/ui/popover';
  import { RangeCalendar } from '$lib/components/ui/range-calendar';
  import { createEventDispatcher } from 'svelte';

  export let value: DateRange | undefined = undefined;
  export let placeholder = 'Select date range';
  export let buttonClass = '';
  export let disabled = false;

  const df = new DateFormatter('en-US', {
    dateStyle: 'medium',
  });

  let popoverOpen = false;

  const dispatch = createEventDispatcher<{
    change: { startDate: Date | undefined; endDate: Date | undefined };
  }>();

  // Convert from CalendarDate to JavaScript Date
  function toJsDate(date: DateValue | undefined): Date | undefined {
    if (!date) return undefined;
    return date.toDate(getLocalTimeZone());
  }

  // Handle value changes
  $: if (value) {
    const startDate = toJsDate(value.start);
    const endDate = toJsDate(value.end);

    // Only dispatch if we have both dates
    if (startDate && endDate) {
      dispatch('change', { startDate, endDate });

      // Close popover after selection is complete
      if (popoverOpen) {
        setTimeout(() => {
          popoverOpen = false;
        }, 100);
      }
    }
  }

  // No need for a separate handler as the reactive statement handles value changes
</script>

<div>
  <Popover.Root bind:open={popoverOpen}>
    <Popover.Trigger>
      <Button
        variant="outline"
        {disabled}
        class={cn(
          'w-full justify-between text-left font-normal',
          !value && 'text-muted-foreground',
          buttonClass
        )}>
        <div class="flex items-center">
          <CalendarIcon class="mr-2 h-4 w-4" />
          {#if value && value.start}
            {#if value.end}
              {df.format(value.start.toDate(getLocalTimeZone()))} - {df.format(
                value.end.toDate(getLocalTimeZone())
              )}
            {:else}
              {df.format(value.start.toDate(getLocalTimeZone()))}
            {/if}
          {:else}
            {placeholder}
          {/if}
        </div>
      </Button>
    </Popover.Trigger>
    <Popover.Content class="w-auto p-0" align="start">
      <RangeCalendar bind:value placeholder={value?.start} numberOfMonths={2} />
    </Popover.Content>
  </Popover.Root>
</div>
