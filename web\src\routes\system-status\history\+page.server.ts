import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { prisma } from '$lib/server/prisma';
import { logger } from '$lib/server/logger';

export const load: PageServerLoad = async ({ url }) => {
  try {
    // Get month and year from query parameters or use current date
    const monthParam = url.searchParams.get('month');
    const yearParam = url.searchParams.get('year');

    const now = new Date();
    const month = monthParam ? parseInt(monthParam) : now.getMonth() + 1; // 1-12
    const year = yearParam ? parseInt(yearParam) : now.getFullYear();

    // Prepare date range for the query
    const startDate = new Date(year, month - 1, 1); // First day of month
    const endDate = new Date(year, month, 0); // Last day of month

    // Initialize empty array for maintenance events
    let maintenanceEvents: any[] = [];

    try {
      // Try to query using Prisma model if it exists
      if (prisma && 'maintenanceEvent' in prisma) {
        maintenanceEvents = await (prisma as any).maintenanceEvent.findMany({
          where: {
            OR: [
              {
                // Events that start in the specified month
                startTime: {
                  gte: startDate,
                  lte: endDate,
                },
              },
              {
                // Events that end in the specified month
                endTime: {
                  gte: startDate,
                  lte: endDate,
                },
              },
            ],
          },
          orderBy: {
            startTime: 'desc',
          },
          include: {
            history: true,
          },
        });
      } else {
        // If model doesn't exist, try raw SQL
        try {
          const events = await prisma.$queryRaw`
            SELECT * FROM "web"."MaintenanceEvent"
            WHERE
              ("startTime" >= ${startDate} AND "startTime" <= ${endDate})
              OR
              ("endTime" >= ${startDate} AND "endTime" <= ${endDate})
            ORDER BY "startTime" DESC
          `;

          // Get history for each event
          if (Array.isArray(events)) {
            maintenanceEvents = events;

            // For each event, get its history
            for (const event of maintenanceEvents) {
              try {
                const history = await prisma.$queryRaw`
                  SELECT * FROM "web"."MaintenanceEventHistory"
                  WHERE "eventId" = ${event.id}
                  ORDER BY "createdAt" ASC
                `;

                // Add history to the event
                event.history = Array.isArray(history) ? history : [];
              } catch (historyError) {
                logger.warn(`Error fetching history for event ${event.id}:`, historyError);
                event.history = [];
              }
            }
          }
        } catch (sqlError) {
          logger.warn('Error fetching maintenance events with raw SQL:', sqlError);
        }
      }
    } catch (dbError) {
      logger.warn('Error querying maintenance events:', dbError);
    }

    // Map history entries to updates format
    const eventsWithUpdates = maintenanceEvents.map((event) => {
      // Convert history entries to updates
      const updates = (event.history || []).map((historyEntry: any) => ({
        id: historyEntry.id,
        timestamp: historyEntry.createdAt,
        message:
          historyEntry.comment ||
          (historyEntry.changeType === 'status_change'
            ? `Status changed from ${historyEntry.previousStatus || 'unknown'} to ${historyEntry.newStatus || 'unknown'}`
            : 'Update received'),
      }));

      // Add initial update if no updates exist
      if (updates.length === 0) {
        updates.push({
          id: 'initial',
          timestamp: event.createdAt,
          message: `Maintenance event created with status: ${event.status}`,
        });
      }

      return {
        ...event,
        updates,
      };
    });

    // Return the data
    return {
      maintenance: eventsWithUpdates,
      currentMonth: month,
      currentYear: year,
      hasNextMonth: month < now.getMonth() + 1 || year < now.getFullYear(),
      hasPrevMonth: true, // We always allow going back in history
    };
  } catch (err) {
    logger.error('Error loading maintenance history:', err);

    // Return empty data instead of throwing an error
    const now = new Date();
    const month = monthParam ? parseInt(monthParam) : now.getMonth() + 1;
    const year = yearParam ? parseInt(yearParam) : now.getFullYear();

    return {
      maintenance: [],
      currentMonth: month,
      currentYear: year,
      hasNextMonth: month < now.getMonth() + 1 || year < now.getFullYear(),
      hasPrevMonth: true,
    };
  }
};
