<script lang="ts">
  import { But<PERSON> } from '$lib/components/ui/button/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import * as Tabs from '$lib/components/ui/tabs';
  import * as Accordion from '$lib/components/ui/accordion/index.js';
  import * as <PERSON><PERSON><PERSON><PERSON> from '$lib/components/ui/scroll-area';
  import { 
    User, 
    FileText, 
    Briefcase, 
    GraduationCap, 
    Wrench, 
    Languages, 
    Award, 
    Certificate, 
    ArrowLeft 
  } from 'lucide-svelte';
  import { goto } from '$app/navigation';
  import { toast } from 'svelte-sonner';
  import { parseProfileData } from '$lib/utils/profileHelpers';

  // Get data from page load
  export let data;
  
  // Parse profile data
  const profile = data.profile;
  const profileData = data.profileData || {};
  
  // Define tabs for the left sidebar
  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'personal', label: 'Personal Info', icon: FileText },
    { id: 'preferences', label: 'Job Preferences', icon: Briefcase },
    { id: 'experience', label: 'Experience', icon: Briefcase },
    { id: 'education', label: 'Education', icon: GraduationCap },
    { id: 'skills', label: 'Skills', icon: Wrench },
    { id: 'languages', label: 'Languages', icon: Languages },
    { id: 'achievements', label: 'Achievements', icon: Award },
    { id: 'certifications', label: 'Certifications', icon: Certificate },
  ];
  
  // Active tab state
  let activeTab = 'profile';
  
  // Handle tab change
  function handleTabChange(tabId) {
    activeTab = tabId;
  }
  
  // Go back function
  function goBack() {
    goto('/dashboard');
  }
</script>

<div class="container mx-auto p-4">
  <!-- Header with breadcrumb -->
  <div class="mb-6">
    <div class="mb-2 flex items-center gap-2">
      <Button variant="ghost" size="sm" class="h-auto p-0" onclick={goBack}>
        <ArrowLeft class="mr-1 h-4 w-4" />
        Back
      </Button>
      <span class="text-muted-foreground">/</span>
      <span class="text-muted-foreground max-w-[200px] truncate">{profile?.name || 'Profile'}</span>
    </div>
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold">{profile?.name || 'Profile'}</h1>
    </div>
  </div>

  <!-- Main content with left sidebar tabs and right content area -->
  <div class="grid grid-cols-1 gap-6 md:grid-cols-4">
    <!-- Left sidebar with tabs -->
    <div class="md:col-span-1">
      <ScrollArea.Root className="h-[calc(100vh-200px)]">
        <ScrollArea.Viewport className="h-full w-full">
          <div class="space-y-1 pr-4">
            {#each tabs as tab}
              <Button 
                variant={activeTab === tab.id ? "default" : "ghost"} 
                class="w-full justify-start" 
                onclick={() => handleTabChange(tab.id)}
              >
                <svelte:component this={tab.icon} class="mr-2 h-4 w-4" />
                {tab.label}
              </Button>
            {/each}
          </div>
        </ScrollArea.Viewport>
        <ScrollArea.Scrollbar orientation="vertical">
          <ScrollArea.Thumb />
        </ScrollArea.Scrollbar>
      </ScrollArea.Root>
    </div>

    <!-- Right content area with accordions -->
    <div class="md:col-span-3">
      <ScrollArea.Root className="h-[calc(100vh-200px)]">
        <ScrollArea.Viewport className="h-full w-full">
          <!-- Profile Overview -->
          {#if activeTab === 'profile'}
            <div class="space-y-4">
              <h2 class="text-xl font-semibold">Profile Overview</h2>
              <div class="rounded-lg border p-4">
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <h3 class="text-sm font-medium text-muted-foreground">Full Name</h3>
                    <p>{profileData.fullName || profileData.personalInfo?.fullName || 'Not specified'}</p>
                  </div>
                  <div>
                    <h3 class="text-sm font-medium text-muted-foreground">Email</h3>
                    <p>{profileData.email || profileData.personalInfo?.email || 'Not specified'}</p>
                  </div>
                  <div>
                    <h3 class="text-sm font-medium text-muted-foreground">Job Title</h3>
                    <p>{profileData.jobType || profileData.personalInfo?.jobTitle || 'Not specified'}</p>
                  </div>
                  <div>
                    <h3 class="text-sm font-medium text-muted-foreground">Industry</h3>
                    <p>{profileData.industry || 'Not specified'}</p>
                  </div>
                </div>
              </div>
              
              <h3 class="text-lg font-medium">Summary</h3>
              <div class="rounded-lg border p-4">
                {#if profileData.summary || profileData.personalInfo?.summary}
                  <p class="whitespace-pre-wrap">{profileData.summary || profileData.personalInfo?.summary}</p>
                {:else}
                  <p class="text-muted-foreground italic">No summary provided</p>
                {/if}
              </div>
            </div>
          {/if}

          <!-- Personal Information -->
          {#if activeTab === 'personal'}
            <div class="space-y-4">
              <h2 class="text-xl font-semibold">Personal Information</h2>
              <Accordion.Root type="single" collapsible>
                <Accordion.Item value="personal-info">
                  <Accordion.Trigger class="flex w-full items-center justify-between px-4 py-2">
                    <div class="flex items-center gap-2">
                      <User class="h-5 w-5" />
                      <span class="text-lg font-medium">Contact Details</span>
                    </div>
                  </Accordion.Trigger>
                  <Accordion.Content class="px-4 pb-4">
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div>
                        <h3 class="text-sm font-medium text-muted-foreground">Full Name</h3>
                        <p>{profileData.fullName || profileData.personalInfo?.fullName || 'Not specified'}</p>
                      </div>
                      <div>
                        <h3 class="text-sm font-medium text-muted-foreground">Email</h3>
                        <p>{profileData.email || profileData.personalInfo?.email || 'Not specified'}</p>
                      </div>
                      <div>
                        <h3 class="text-sm font-medium text-muted-foreground">Phone</h3>
                        <p>{profileData.phone || profileData.personalInfo?.phone || 'Not specified'}</p>
                      </div>
                      <div>
                        <h3 class="text-sm font-medium text-muted-foreground">Location</h3>
                        <p>{profileData.location || profileData.personalInfo?.location || 'Not specified'}</p>
                      </div>
                      <div>
                        <h3 class="text-sm font-medium text-muted-foreground">Website</h3>
                        <p>{profileData.website || profileData.personalInfo?.website || 'Not specified'}</p>
                      </div>
                    </div>
                  </Accordion.Content>
                </Accordion.Item>
              </Accordion.Root>
            </div>
          {/if}

          <!-- Job Preferences -->
          {#if activeTab === 'preferences'}
            <div class="space-y-4">
              <h2 class="text-xl font-semibold">Job Preferences</h2>
              <Accordion.Root type="single" collapsible>
                <Accordion.Item value="job-preferences">
                  <Accordion.Trigger class="flex w-full items-center justify-between px-4 py-2">
                    <div class="flex items-center gap-2">
                      <Briefcase class="h-5 w-5" />
                      <span class="text-lg font-medium">Career Preferences</span>
                    </div>
                  </Accordion.Trigger>
                  <Accordion.Content class="px-4 pb-4">
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                      {#if profileData.jobPreferences}
                        <div>
                          <h3 class="text-sm font-medium text-muted-foreground">Desired Roles</h3>
                          <p>{profileData.jobPreferences.interestedRoles?.join(', ') || 'Not specified'}</p>
                        </div>
                        <div>
                          <h3 class="text-sm font-medium text-muted-foreground">Preferred Locations</h3>
                          <p>{profileData.jobPreferences.preferredLocations?.join(', ') || 'Not specified'}</p>
                        </div>
                        <div>
                          <h3 class="text-sm font-medium text-muted-foreground">Remote Preference</h3>
                          <p>{profileData.jobPreferences.remotePreference || 'Not specified'}</p>
                        </div>
                        <div>
                          <h3 class="text-sm font-medium text-muted-foreground">Desired Industries</h3>
                          <p>{profileData.jobPreferences.desiredIndustries?.join(', ') || 'Not specified'}</p>
                        </div>
                      {:else}
                        <div class="col-span-2 text-muted-foreground italic">
                          No job preferences specified
                        </div>
                      {/if}
                    </div>
                  </Accordion.Content>
                </Accordion.Item>
              </Accordion.Root>
            </div>
          {/if}

          <!-- Experience -->
          {#if activeTab === 'experience'}
            <div class="space-y-4">
              <h2 class="text-xl font-semibold">Work Experience</h2>
              {#if profileData.workExperience && profileData.workExperience.length > 0}
                <Accordion.Root type="multiple">
                  {#each profileData.workExperience as exp, i}
                    <Accordion.Item value={`exp-${i}`}>
                      <Accordion.Trigger class="flex w-full items-center justify-between px-4 py-2">
                        <div class="flex items-center gap-2">
                          <Briefcase class="h-5 w-5" />
                          <span class="text-lg font-medium">{exp.title || exp.jobTitle} at {exp.company}</span>
                        </div>
                      </Accordion.Trigger>
                      <Accordion.Content class="px-4 pb-4">
                        <div class="space-y-2">
                          <div>
                            <h3 class="text-sm font-medium text-muted-foreground">Company</h3>
                            <p>{exp.company || 'Not specified'}</p>
                          </div>
                          <div>
                            <h3 class="text-sm font-medium text-muted-foreground">Title</h3>
                            <p>{exp.title || exp.jobTitle || 'Not specified'}</p>
                          </div>
                          <div>
                            <h3 class="text-sm font-medium text-muted-foreground">Duration</h3>
                            <p>
                              {exp.startDate || 'Unknown'} - {exp.current ? 'Present' : exp.endDate || 'Unknown'}
                            </p>
                          </div>
                          <div>
                            <h3 class="text-sm font-medium text-muted-foreground">Description</h3>
                            <p class="whitespace-pre-wrap">{exp.description || 'No description provided'}</p>
                          </div>
                        </div>
                      </Accordion.Content>
                    </Accordion.Item>
                  {/each}
                </Accordion.Root>
              {:else}
                <div class="rounded-lg border p-4 text-muted-foreground italic">
                  No work experience added
                </div>
              {/if}
            </div>
          {/if}

          <!-- Education -->
          {#if activeTab === 'education'}
            <div class="space-y-4">
              <h2 class="text-xl font-semibold">Education</h2>
              {#if profileData.education && profileData.education.length > 0}
                <Accordion.Root type="multiple">
                  {#each profileData.education as edu, i}
                    <Accordion.Item value={`edu-${i}`}>
                      <Accordion.Trigger class="flex w-full items-center justify-between px-4 py-2">
                        <div class="flex items-center gap-2">
                          <GraduationCap class="h-5 w-5" />
                          <span class="text-lg font-medium">{edu.degree || 'Degree'} at {edu.school || edu.institution}</span>
                        </div>
                      </Accordion.Trigger>
                      <Accordion.Content class="px-4 pb-4">
                        <div class="space-y-2">
                          <div>
                            <h3 class="text-sm font-medium text-muted-foreground">School</h3>
                            <p>{edu.school || edu.institution || 'Not specified'}</p>
                          </div>
                          <div>
                            <h3 class="text-sm font-medium text-muted-foreground">Degree</h3>
                            <p>{edu.degree || 'Not specified'}</p>
                          </div>
                          <div>
                            <h3 class="text-sm font-medium text-muted-foreground">Field of Study</h3>
                            <p>{edu.field || 'Not specified'}</p>
                          </div>
                          <div>
                            <h3 class="text-sm font-medium text-muted-foreground">Duration</h3>
                            <p>
                              {edu.startDate || 'Unknown'} - {edu.current ? 'Present' : edu.endDate || 'Unknown'}
                            </p>
                          </div>
                          <div>
                            <h3 class="text-sm font-medium text-muted-foreground">Description</h3>
                            <p class="whitespace-pre-wrap">{edu.description || 'No description provided'}</p>
                          </div>
                        </div>
                      </Accordion.Content>
                    </Accordion.Item>
                  {/each}
                </Accordion.Root>
              {:else}
                <div class="rounded-lg border p-4 text-muted-foreground italic">
                  No education added
                </div>
              {/if}
            </div>
          {/if}

          <!-- Skills -->
          {#if activeTab === 'skills'}
            <div class="space-y-4">
              <h2 class="text-xl font-semibold">Skills</h2>
              <Accordion.Root type="single" collapsible>
                <Accordion.Item value="skills">
                  <Accordion.Trigger class="flex w-full items-center justify-between px-4 py-2">
                    <div class="flex items-center gap-2">
                      <Wrench class="h-5 w-5" />
                      <span class="text-lg font-medium">Skills & Expertise</span>
                    </div>
                  </Accordion.Trigger>
                  <Accordion.Content class="px-4 pb-4">
                    {#if profileData.skills || profileData.skillsData}
                      <div class="space-y-4">
                        {#if profileData.skillsData?.technical && profileData.skillsData.technical.length > 0}
                          <div>
                            <h3 class="mb-2 text-sm font-medium text-muted-foreground">Technical Skills</h3>
                            <div class="flex flex-wrap gap-2">
                              {#each profileData.skillsData.technical as skill}
                                <Badge variant="secondary">{skill}</Badge>
                              {/each}
                            </div>
                          </div>
                        {/if}
                        
                        {#if profileData.skillsData?.soft && profileData.skillsData.soft.length > 0}
                          <div>
                            <h3 class="mb-2 text-sm font-medium text-muted-foreground">Soft Skills</h3>
                            <div class="flex flex-wrap gap-2">
                              {#each profileData.skillsData.soft as skill}
                                <Badge variant="secondary">{skill}</Badge>
                              {/each}
                            </div>
                          </div>
                        {/if}
                        
                        {#if (!profileData.skillsData?.technical || profileData.skillsData.technical.length === 0) && 
                             (!profileData.skillsData?.soft || profileData.skillsData.soft.length === 0) && 
                             profileData.skills}
                          <div>
                            <h3 class="mb-2 text-sm font-medium text-muted-foreground">Skills</h3>
                            <div class="flex flex-wrap gap-2">
                              {#each Array.isArray(profileData.skills) ? profileData.skills : [profileData.skills] as skill}
                                <Badge variant="secondary">{skill}</Badge>
                              {/each}
                            </div>
                          </div>
                        {/if}
                      </div>
                    {:else}
                      <div class="text-muted-foreground italic">
                        No skills specified
                      </div>
                    {/if}
                  </Accordion.Content>
                </Accordion.Item>
              </Accordion.Root>
            </div>
          {/if}

          <!-- Languages -->
          {#if activeTab === 'languages'}
            <div class="space-y-4">
              <h2 class="text-xl font-semibold">Languages</h2>
              {#if profileData.languages && profileData.languages.length > 0}
                <Accordion.Root type="single" collapsible>
                  <Accordion.Item value="languages">
                    <Accordion.Trigger class="flex w-full items-center justify-between px-4 py-2">
                      <div class="flex items-center gap-2">
                        <Languages class="h-5 w-5" />
                        <span class="text-lg font-medium">Language Proficiency</span>
                      </div>
                    </Accordion.Trigger>
                    <Accordion.Content class="px-4 pb-4">
                      <div class="space-y-2">
                        {#each profileData.languages as lang}
                          <div class="flex items-center justify-between">
                            <span>{lang.name}</span>
                            <Badge>{lang.proficiency}</Badge>
                          </div>
                        {/each}
                      </div>
                    </Accordion.Content>
                  </Accordion.Item>
                </Accordion.Root>
              {:else}
                <div class="rounded-lg border p-4 text-muted-foreground italic">
                  No languages added
                </div>
              {/if}
            </div>
          {/if}

          <!-- Achievements -->
          {#if activeTab === 'achievements'}
            <div class="space-y-4">
              <h2 class="text-xl font-semibold">Achievements</h2>
              {#if profileData.achievements && profileData.achievements.length > 0}
                <Accordion.Root type="single" collapsible>
                  <Accordion.Item value="achievements">
                    <Accordion.Trigger class="flex w-full items-center justify-between px-4 py-2">
                      <div class="flex items-center gap-2">
                        <Award class="h-5 w-5" />
                        <span class="text-lg font-medium">Awards & Achievements</span>
                      </div>
                    </Accordion.Trigger>
                    <Accordion.Content class="px-4 pb-4">
                      <ul class="list-disc pl-5 space-y-2">
                        {#each profileData.achievements as achievement}
                          <li>{achievement.title || achievement}</li>
                        {/each}
                      </ul>
                    </Accordion.Content>
                  </Accordion.Item>
                </Accordion.Root>
              {:else}
                <div class="rounded-lg border p-4 text-muted-foreground italic">
                  No achievements added
                </div>
              {/if}
            </div>
          {/if}

          <!-- Certifications -->
          {#if activeTab === 'certifications'}
            <div class="space-y-4">
              <h2 class="text-xl font-semibold">Certifications</h2>
              {#if profileData.certifications && profileData.certifications.length > 0}
                <Accordion.Root type="multiple">
                  {#each profileData.certifications as cert, i}
                    <Accordion.Item value={`cert-${i}`}>
                      <Accordion.Trigger class="flex w-full items-center justify-between px-4 py-2">
                        <div class="flex items-center gap-2">
                          <Certificate class="h-5 w-5" />
                          <span class="text-lg font-medium">{cert.name}</span>
                        </div>
                      </Accordion.Trigger>
                      <Accordion.Content class="px-4 pb-4">
                        <div class="space-y-2">
                          <div>
                            <h3 class="text-sm font-medium text-muted-foreground">Issuing Organization</h3>
                            <p>{cert.issuer || 'Not specified'}</p>
                          </div>
                          <div>
                            <h3 class="text-sm font-medium text-muted-foreground">Issue Date</h3>
                            <p>{cert.issueDate || 'Not specified'}</p>
                          </div>
                          <div>
                            <h3 class="text-sm font-medium text-muted-foreground">Expiration Date</h3>
                            <p>{cert.expirationDate || 'No expiration'}</p>
                          </div>
                          <div>
                            <h3 class="text-sm font-medium text-muted-foreground">Credential ID</h3>
                            <p>{cert.credentialId || 'Not specified'}</p>
                          </div>
                        </div>
                      </Accordion.Content>
                    </Accordion.Item>
                  {/each}
                </Accordion.Root>
              {:else}
                <div class="rounded-lg border p-4 text-muted-foreground italic">
                  No certifications added
                </div>
              {/if}
            </div>
          {/if}
        </ScrollArea.Viewport>
        <ScrollArea.Scrollbar orientation="vertical">
          <ScrollArea.Thumb />
        </ScrollArea.Scrollbar>
      </ScrollArea.Root>
    </div>
  </div>
</div>
