import natural from "natural";
import { JobListing } from "@prisma/client";

const tokenizer = new natural.WordTokenizer();
const TfIdf = natural.TfIdf;

export function scoreJobAgainstResume(
  resumeText: string,
  job: Pick<JobListing, "title" | "description" | "company">
): number {
  const tfidf = new TfIdf();

  const jobText = [job.title, job.company, job.description ?? ""].join(" ");
  const jobTokens = tokenizer.tokenize(jobText.toLowerCase());
  const resumeTokens = tokenizer.tokenize(resumeText.toLowerCase());

  tfidf.addDocument(resumeTokens.join(" "));
  tfidf.addDocument(jobTokens.join(" "));

  const vector1 = tfidf.listTerms(0).reduce((acc, term) => {
    acc[term.term] = term.tfidf;
    return acc;
  }, {} as Record<string, number>);

  const vector2 = tfidf.listTerms(1).reduce((acc, term) => {
    acc[term.term] = term.tfidf;
    return acc;
  }, {} as Record<string, number>);

  return cosineSimilarity(vector1, vector2);
}

function cosineSimilarity(
  a: Record<string, number>,
  b: Record<string, number>
): number {
  const allTerms = new Set([...Object.keys(a), ...Object.keys(b)]);
  let dot = 0,
    magA = 0,
    magB = 0;

  for (const term of allTerms) {
    const valA = a[term] ?? 0;
    const valB = b[term] ?? 0;
    dot += valA * valB;
    magA += valA * valA;
    magB += valB * valB;
  }

  return magA && magB ? dot / (Math.sqrt(magA) * Math.sqrt(magB)) : 0;
}
