/**
 * Feature Limits Script
 *
 * This script defines feature limits for all features in the system.
 * It can be used to seed the database with feature limits.
 */

import { PrismaClient } from '@prisma/client';

// Enum definitions for type safety
export enum FeatureCategory {
  Core = 'core',
  JobSearch = 'job_search',
  Resume = 'resume',
  Applications = 'applications',
  Analytics = 'analytics',
  Team = 'team',
  Integration = 'integration',
  Communication = 'communication',
  Automation = 'automation',
  Security = 'security',
  Customization = 'customization',
  Advanced = 'advanced',
}

export enum LimitType {
  Monthly = 'monthly',
  Total = 'total',
  Concurrent = 'concurrent',
  Unlimited = 'unlimited',
}

// Define interfaces for type safety
export interface FeatureLimit {
  id: string;
  name: string;
  description: string;
  defaultValue: number;
  type: LimitType;
  unit?: string;
  resetDay?: number;
}

export interface FeatureLimitMap {
  [key: string]: FeatureLimit;
}

export interface FeatureToLimitsMap {
  [key: string]: string[];
}

export const prisma = new PrismaClient();

// Feature limits definition
export const FEATURE_LIMITS: FeatureLimitMap = {
  // Resume limits
  resume_scans_per_month: {
    id: 'resume_scans_per_month',
    name: 'Resume Scans',
    description: 'Number of resumes you can scan per month',
    defaultValue: 10,
    type: LimitType.Monthly,
    unit: 'scans',
    resetDay: 1,
  },
  resume_versions: {
    id: 'resume_versions',
    name: 'Resume Versions',
    description: 'Number of different resume versions you can create',
    defaultValue: 3,
    type: LimitType.Total,
    unit: 'versions',
  },
  resume_templates: {
    id: 'resume_templates',
    name: 'Resume Templates',
    description: 'Number of resume templates you can access',
    defaultValue: 5,
    type: LimitType.Total,
    unit: 'templates',
  },

  // Application limits
  applications_per_month: {
    id: 'applications_per_month',
    name: 'Applications',
    description: 'Number of job applications you can submit per month',
    defaultValue: 25,
    type: LimitType.Monthly,
    unit: 'applications',
    resetDay: 1,
  },
  cover_letters_per_month: {
    id: 'cover_letters_per_month',
    name: 'Cover Letters',
    description: 'Number of cover letters you can generate per month',
    defaultValue: 10,
    type: LimitType.Monthly,
    unit: 'letters',
    resetDay: 1,
  },

  // Team limits
  team_members: {
    id: 'team_members',
    name: 'Team Members',
    description: 'Number of team members you can add',
    defaultValue: 1,
    type: LimitType.Concurrent,
    unit: 'members',
  },
  team_projects: {
    id: 'team_projects',
    name: 'Team Projects',
    description: 'Number of team projects you can create',
    defaultValue: 3,
    type: LimitType.Total,
    unit: 'projects',
  },

  // Integration limits
  api_calls_per_month: {
    id: 'api_calls_per_month',
    name: 'API Calls',
    description: 'Number of API calls you can make per month',
    defaultValue: 100,
    type: LimitType.Monthly,
    unit: 'calls',
    resetDay: 1,
  },
  custom_integrations: {
    id: 'custom_integrations',
    name: 'Custom Integrations',
    description: 'Number of custom integrations you can create',
    defaultValue: 0,
    type: LimitType.Total,
    unit: 'integrations',
  },

  // Storage limits
  storage_gb: {
    id: 'storage_gb',
    name: 'Storage',
    description: 'Amount of storage space for your documents',
    defaultValue: 1,
    type: LimitType.Total,
    unit: 'GB',
  },
  document_count: {
    id: 'document_count',
    name: 'Document Count',
    description: 'Maximum number of documents you can upload',
    defaultValue: 20,
    type: LimitType.Total,
    unit: 'documents',
  },

  // Auto-Apply Limits
  auto_apply_jobs_monthly: {
    id: 'auto_apply_jobs_monthly',
    name: 'Auto-Apply Jobs',
    description: 'Number of jobs you can auto-apply to per month',
    defaultValue: 50,
    type: LimitType.Monthly,
    unit: 'applications',
    resetDay: 1,
  },
  job_search_monthly: {
    id: 'job_search_monthly',
    name: 'Job Searches',
    description: 'Number of job searches you can perform per month',
    defaultValue: 100,
    type: LimitType.Monthly,
    unit: 'searches',
    resetDay: 1,
  },
  saved_jobs_total: {
    id: 'saved_jobs_total',
    name: 'Saved Jobs',
    description: 'Maximum number of jobs you can save',
    defaultValue: 200,
    type: LimitType.Total,
    unit: 'jobs',
  },

  // AI Interview Limits
  ai_interview_sessions_monthly: {
    id: 'ai_interview_sessions_monthly',
    name: 'AI Interview Sessions',
    description: 'Number of AI interview practice sessions per month',
    defaultValue: 5,
    type: LimitType.Monthly,
    unit: 'sessions',
    resetDay: 1,
  },
  career_advice_requests_monthly: {
    id: 'career_advice_requests_monthly',
    name: 'Career Advice Requests',
    description: 'Number of career advice requests per month',
    defaultValue: 3,
    type: LimitType.Monthly,
    unit: 'requests',
    resetDay: 1,
  },

  // Job Tracker Limits
  job_notes_per_application: {
    id: 'job_notes_per_application',
    name: 'Job Notes',
    description: 'Number of notes you can add per job application',
    defaultValue: 10,
    type: LimitType.Total,
    unit: 'notes',
  },

  // Resume Builder Limits
  resume_versions_total: {
    id: 'resume_versions_total',
    name: 'Resume Versions',
    description: 'Number of different resume versions you can create',
    defaultValue: 5,
    type: LimitType.Total,
    unit: 'versions',
  },
  resume_templates_total: {
    id: 'resume_templates_total',
    name: 'Resume Templates',
    description: 'Number of premium resume templates available',
    defaultValue: 10,
    type: LimitType.Total,
    unit: 'templates',
  },

  // Automation Limits
  automation_runs_monthly: {
    id: 'automation_runs_monthly',
    name: 'Automation Runs',
    description: 'Number of automation workflows you can run per month',
    defaultValue: 5,
    type: LimitType.Monthly,
    unit: 'runs',
    resetDay: 1,
  },
  automation_tasks_per_run: {
    id: 'automation_tasks_per_run',
    name: 'Tasks Per Automation',
    description: 'Maximum number of tasks per automation workflow',
    defaultValue: 10,
    type: LimitType.Total,
    unit: 'tasks',
  },

  // Job Match Limits
  job_match_analysis_monthly: {
    id: 'job_match_analysis_monthly',
    name: 'Job Match Analysis',
    description: 'Number of detailed job match analyses per month',
    defaultValue: 20,
    type: LimitType.Monthly,
    unit: 'analyses',
    resetDay: 1,
  },

  // Analysis Limits
  skill_analysis_monthly: {
    id: 'skill_analysis_monthly',
    name: 'Skill Analysis Reports',
    description: 'Number of skill analysis reports you can generate per month',
    defaultValue: 5,
    type: LimitType.Monthly,
    unit: 'reports',
    resetDay: 1,
  },
  career_path_monthly: {
    id: 'career_path_monthly',
    name: 'Career Path Reports',
    description: 'Number of career path analysis reports you can generate per month',
    defaultValue: 3,
    type: LimitType.Monthly,
    unit: 'reports',
    resetDay: 1,
  },
};

// Feature to limit mapping
export const FEATURE_TO_LIMITS_MAP: FeatureToLimitsMap = {
  // Core features
  dashboard: [],
  profile: [],

  // Resume features
  resume_scanner: ['resume_scans_per_month'],
  resume_builder: ['resume_versions', 'resume_templates'],
  resume_ai: [],
  ats_optimization: ['ats_scans_monthly'],

  // Job search features
  job_search: ['job_search_monthly'],
  job_alerts: [],
  saved_searches: [],
  job_recommendations: [],

  // Application features
  application_tracker: [],
  application_submit: ['applications_per_month'],
  cover_letter_generator: ['cover_letters_per_month'],

  // Analytics features
  basic_analytics: [],
  advanced_analytics: [],
  skill_gap_analysis: ['skill_analysis_monthly'],
  career_path_analysis: ['career_path_monthly'],

  // Team features
  team_collaboration: ['team_members', 'team_projects'],
  team_admin: [],

  // Integration features
  api_access: ['api_calls_per_month'],
  custom_integrations: ['custom_integrations'],

  // Document features
  document_storage: ['storage_gb', 'document_count'],

  // Auto-Apply features
  one_click_apply: ['auto_apply_jobs_monthly'],
  advanced_job_search: ['job_search_monthly'],
  saved_job_searches: ['saved_jobs_total'],

  // Co-Pilot features
  ai_interview_coach: ['ai_interview_sessions_monthly'],
  career_advisor: ['career_advice_requests_monthly'],

  // Job Tracker features
  job_application_notes: ['job_notes_per_application'],

  // Resume Builder features
  resume_creator: ['resume_versions_total'],
  ats_checker: [],
  premium_templates: ['resume_templates_total'],
  resume_export: [],

  // Automation features
  job_application_automation: ['automation_runs_monthly', 'automation_tasks_per_run'],

  // Matches features
  ai_job_matching: ['job_match_analysis_monthly'],
  match_scoring: [],
};
