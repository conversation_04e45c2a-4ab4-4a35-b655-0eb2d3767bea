import express from "express";
import { getLatestMetrics } from "../services/health/monitor.js";
import {
  getCircuitState,
  CircuitState,
} from "../services/health/circuit-breaker.js";
import { getOllamaStatus } from "../services/llm/ollama.js";

const router = express.Router();

// Basic health check
router.get("/", (req, res) => {
  const circuitState = getCircuitState();
  const ollamaStatus = getOllamaStatus();

  // Service is healthy if circuit is not open AND Ollama is ready
  const isHealthy = circuitState !== CircuitState.OPEN && ollamaStatus.ready;

  res.status(isHealthy ? 200 : 503).json({
    status: isHealthy ? "ok" : "degraded",
    circuitState,
    ollama: {
      running: ollamaStatus.ollamaRunning,
      modelLoaded: ollamaStatus.modelLoaded,
      modelName: ollamaStatus.modelName,
      ready: ollamaStatus.ready,
    },
    timestamp: new Date().toISOString(),
  });
});

// Detailed health check
router.get("/details", (req, res) => {
  const metrics = getLatestMetrics();
  const circuitState = getCircuitState();
  const ollamaStatus = getOllamaStatus();

  // Service is healthy if circuit is closed AND Ollama is ready
  const isHealthy = circuitState === CircuitState.CLOSED && ollamaStatus.ready;

  res.json({
    status: isHealthy ? "ok" : "degraded",
    circuitState,
    ollama: {
      running: ollamaStatus.ollamaRunning,
      modelLoaded: ollamaStatus.modelLoaded,
      modelName: ollamaStatus.modelName,
      ready: ollamaStatus.ready,
    },
    metrics: {
      cpuUsage: `${metrics.cpuUsage.toFixed(1)}%`,
      memoryUsage: `${metrics.memoryUsage.toFixed(1)}%`,
      uptime: metrics.uptime,
    },
    timestamp: new Date().toISOString(),
  });
});

// Ollama-specific health check
router.get("/ollama", (req, res) => {
  const ollamaStatus = getOllamaStatus();

  res.status(ollamaStatus.ready ? 200 : 503).json({
    status: ollamaStatus.ready ? "ok" : "initializing",
    ollama: {
      running: ollamaStatus.ollamaRunning,
      modelLoaded: ollamaStatus.modelLoaded,
      modelName: ollamaStatus.modelName,
      ready: ollamaStatus.ready,
    },
    timestamp: new Date().toISOString(),
  });
});

export default router;
