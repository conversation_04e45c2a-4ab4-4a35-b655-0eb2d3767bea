// @ts-nocheck
import { verifySessionToken } from '$lib/server/auth.js';
import { prisma } from '$lib/server/prisma';
import type { PageServerLoad } from './$types';

export const load = async ({ cookies, url }: Parameters<PageServerLoad>[0]) => {
  const token = cookies.get('auth_token');
  const user = token ? await verifySessionToken(token) : null;

  // Extract search parameters from URL
  const collection = url.searchParams.get('collection');
  const title = url.searchParams.get('title') || '';
  const locations = url.searchParams.get('locations')?.split(',').filter(Boolean) || [];
  const location = url.searchParams.get('location') || '';
  const locationType = url.searchParams.get('locationType')?.split(',').filter(Boolean) || [];
  const experience = url.searchParams.get('experience')?.split(',').filter(Boolean) || [];
  let salary = url.searchParams.get('salary') || '';
  const state = url.searchParams.get('state') || '';
  const country = url.searchParams.get('country') || 'US';
  const datePosted = url.searchParams.get('datePosted') || '';
  const easyApply = url.searchParams.get('easyApply') === 'true';
  const companies = url.searchParams.get('companies')?.split(',').filter(Boolean) || [];
  const jobId = url.searchParams.get('jobId') || '';

  // Validate salary parameter
  const validSalaryValues = [
    '0-50000',
    '50000-75000',
    '75000-100000',
    '100000-150000',
    '150000+',
    '',
  ];
  if (!validSalaryValues.includes(salary)) {
    // Reset to empty if invalid
    salary = '';
  }

  // Get collection details if specified
  let collectionDetails = null;
  if (collection) {
    try {
      collectionDetails = await prisma.job_collections.findUnique({
        where: { slug: collection },
      });
    } catch (error) {
      console.error('Error fetching collection details:', error);
    }
  }

  // Get specific job details if jobId is provided
  let selectedJob = null;
  if (jobId) {
    try {
      selectedJob = await prisma.job_listing.findUnique({
        where: { id: jobId },
      });
    } catch (error) {
      console.error('Error fetching job details:', error);
    }
  }

  // Get initial job count for the search parameters
  let totalJobCount = 0;
  try {
    const filter: any = { isActive: true };

    if (title) {
      filter.title = { contains: title, mode: 'insensitive' };
    }

    if (location) {
      filter.location = { contains: location, mode: 'insensitive' };
    }

    if (state) {
      filter.stateId = state;
    }

    totalJobCount = await prisma.job_listing.count({ where: filter });
  } catch (error) {
    console.error('Error counting jobs:', error);
  }

  return {
    user,
    collection: collectionDetails,
    searchParams: {
      title,
      locations,
      location,
      locationType,
      experience,
      salary,
      state,
      country,
      datePosted,
      easyApply,
      companies,
    },
    selectedJob,
    totalJobCount,
  };
};
