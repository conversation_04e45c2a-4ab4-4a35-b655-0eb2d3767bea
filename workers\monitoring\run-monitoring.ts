#!/usr/bin/env node

/**
 * Direct monitoring runner to debug cron job issues
 *
 * This script runs the monitoring system directly to see what's happening
 * and why cron jobs might not be running.
 */

const { logger } = require("../utils/logger.js");
const {
  initWorkerMonitoring,
  reportWorkerHealth,
  WorkerType,
} = require("./index.js");

async function runMonitoringDirect() {
  logger.info("🔍 Starting direct monitoring system test");

  try {
    // Initialize the monitoring system
    logger.info("📊 Initializing worker monitoring...");
    const initialized = await initWorkerMonitoring();

    if (!initialized) {
      logger.error("❌ Failed to initialize worker monitoring");
      return;
    }

    logger.info("✅ Worker monitoring initialized successfully");

    // Report health immediately to see current state
    logger.info("🏥 Reporting current health status...");
    const healthReported = await reportWorkerHealth();

    if (!healthReported) {
      logger.error("❌ Failed to report worker health");
      return;
    }

    logger.info("✅ Health report completed");

    // Keep the process running for a few minutes to see periodic reports
    logger.info(
      "⏳ Keeping monitoring active for 5 minutes to observe behavior..."
    );

    let counter = 0;
    const interval = setInterval(async () => {
      counter++;
      logger.info(`📊 Manual health check #${counter}`);

      try {
        await reportWorkerHealth();
        logger.info(`✅ Manual health check #${counter} completed`);
      } catch (error) {
        logger.error(`❌ Manual health check #${counter} failed:`, error);
      }

      // Stop after 5 checks (5 minutes)
      if (counter >= 5) {
        clearInterval(interval);
        logger.info("🏁 Monitoring test completed");
        process.exit(0);
      }
    }, 60000); // Every minute
  } catch (error) {
    logger.error("❌ Monitoring test failed:", error);
    process.exit(1);
  }
}

async function main() {
  try {
    await runMonitoringDirect();
  } catch (error) {
    logger.error("❌ Script failed:", error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}
