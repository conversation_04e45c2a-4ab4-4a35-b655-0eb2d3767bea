<script lang="ts">
  import { But<PERSON> } from '$lib/components/ui/button';
  import { <PERSON>rk<PERSON>, AlertTriangle } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import { goto } from '$app/navigation';
  import FeatureGuard from '$lib/components/features/FeatureGuard.svelte';
  import { trackFeatureUsage } from '$lib/services/feature-service';

  // Props
  const { resumeId, userData, isParsed, disabled } = $props<{
    resumeId: string;
    userData: any;
    isParsed: boolean;
    disabled?: boolean;
  }>();

  // State
  let isLoading = false;
  let error: string | null = null;

  // Start ATS analysis
  async function startATSAnalysis() {
    if (!resumeId) {
      toast.error('Resume ID is required');
      return;
    }

    if (!isParsed) {
      toast.error('Resume must be parsed before analysis');
      return;
    }

    isLoading = true;
    error = null;

    try {
      // Track feature usage
      const trackingResult = await trackFeatureUsage('ats_optimization', 'ats_scans_monthly');
      
      if (!trackingResult.canUse) {
        toast.error(trackingResult.message || 'You have reached your limit for ATS scans');
        isLoading = false;
        return;
      }

      // Call the API to start analysis
      const response = await fetch('/api/ai/ats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resumeId,
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to start ATS analysis');
      }

      const data = await response.json();
      
      toast.success('ATS analysis started');
      
      // Navigate to the ATS analysis page
      goto(`/dashboard/documents/${resumeId}/ats`);
    } catch (err) {
      error = err instanceof Error ? err.message : 'An error occurred';
      toast.error(`Error: ${error}`);
      console.error('Error starting ATS analysis:', err);
    } finally {
      isLoading = false;
    }
  }
</script>

<FeatureGuard 
  {userData} 
  featureId="ats_optimization" 
  limitId="ats_scans_monthly"
  showUpgradeButton={true}
  upgradeButtonText="Upgrade for ATS Analysis"
  limitReachedMessage="You've reached your monthly limit for ATS scans"
  notIncludedMessage="ATS Analysis is not included in your current plan"
>
  <Button
    variant="outline"
    size="sm"
    class="flex items-center gap-2"
    on:click={startATSAnalysis}
    disabled={isLoading || disabled || !isParsed}
  >
    {#if isLoading}
      <div class="h-4 w-4 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
      Analyzing...
    {:else}
      <Sparkles class="h-4 w-4 text-blue-500" />
      Analyze with ATS
    {/if}
  </Button>
</FeatureGuard>
