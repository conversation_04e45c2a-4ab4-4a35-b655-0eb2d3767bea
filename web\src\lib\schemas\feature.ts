import { z } from 'zod';
import { FeatureCategory, LimitType } from '$lib/models/features/features';

// Schema for feature limits
export const featureLimitSchema = z.object({
  id: z.string().min(1, { message: 'Limit ID is required' }),
  name: z.string().min(1, { message: 'Limit name is required' }),
  description: z.string().optional(),
  defaultValue: z.coerce.number().min(0, { message: 'Default value must be a positive number' }),
  type: z.nativeEnum(LimitType),
  unit: z.string().optional(),
  resetDay: z.coerce.number().min(1).max(31).optional(),
});

// Schema for features
export const featureSchema = z.object({
  id: z.string().min(1, { message: 'Feature ID is required' }),
  name: z.string().min(1, { message: 'Feature name is required' }),
  description: z.string().optional(),
  category: z.nativeEnum(FeatureCategory),
  icon: z.string().optional(),
  beta: z.boolean().default(false),
  limits: z.array(featureLimitSchema).default([]),
});

// Type definitions based on the schemas
export type FeatureLimit = z.infer<typeof featureLimitSchema>;
export type Feature = z.infer<typeof featureSchema>;

// Default values for new features and limits
export const defaultFeature: Feature = {
  id: '',
  name: '',
  description: '',
  category: FeatureCategory.Core,
  icon: '',
  beta: false,
  limits: [],
};

export const defaultFeatureLimit: FeatureLimit = {
  id: '',
  name: '',
  description: '',
  defaultValue: 10,
  type: LimitType.Monthly,
  unit: '',
  resetDay: 1,
};
