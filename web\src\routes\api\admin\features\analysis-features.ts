import { FeatureCategory, LimitType } from '$lib/models/features/features';
import type { Feature, FeatureLimit } from '$lib/models/features/types';

/**
 * Feature limits for analysis features
 */
export const ANALYSIS_FEATURE_LIMITS: Record<string, FeatureLimit> = {
  // Skill Analysis Limits
  skill_analysis_monthly: {
    id: 'skill_analysis_monthly',
    name: 'Skill Analysis Reports',
    description: 'Number of skill analysis reports you can generate per month',
    defaultValue: 5,
    type: LimitType.Monthly,
    unit: 'reports',
    resetDay: 1,
  },

  // Career Path Analysis Limits
  career_path_monthly: {
    id: 'career_path_monthly',
    name: 'Career Path Reports',
    description: 'Number of career path analysis reports you can generate per month',
    defaultValue: 3,
    type: LimitType.Monthly,
    unit: 'reports',
    resetDay: 1,
  },

  // Application Performance Limits
  application_reports_monthly: {
    id: 'application_reports_monthly',
    name: 'Application Performance Reports',
    description: 'Number of application performance reports you can generate per month',
    defaultValue: 10,
    type: LimitType.Monthly,
    unit: 'reports',
    resetDay: 1,
  },

  // Resume Effectiveness Limits
  resume_effectiveness_monthly: {
    id: 'resume_effectiveness_monthly',
    name: 'Resume Effectiveness Reports',
    description: 'Number of resume effectiveness reports you can generate per month',
    defaultValue: 5,
    type: LimitType.Monthly,
    unit: 'reports',
    resetDay: 1,
  },

  // Market Insights Limits
  market_insights_monthly: {
    id: 'market_insights_monthly',
    name: 'Market Insight Reports',
    description: 'Number of market insight reports you can access per month',
    defaultValue: 10,
    type: LimitType.Monthly,
    unit: 'reports',
    resetDay: 1,
  },
};

/**
 * Analysis features to be added to the system
 */
export const ANALYSIS_FEATURES: Feature[] = [
  // Skill Analysis
  {
    id: 'skill_gap_analysis',
    name: 'Skill Gap Analysis',
    description: 'Analyze your skills against job requirements to identify areas for growth',
    category: FeatureCategory.Analytics,
    icon: 'bar-chart',
    beta: true,
    limits: [ANALYSIS_FEATURE_LIMITS.skill_analysis_monthly],
  },

  // Career Path Analysis
  {
    id: 'career_trajectory',
    name: 'Career Path Analysis',
    description: 'Visualize your career progression options based on your experience',
    category: FeatureCategory.Analytics,
    icon: 'trending-up',
    beta: true,
    limits: [ANALYSIS_FEATURE_LIMITS.career_path_monthly],
  },

  // Application Performance
  {
    id: 'application_analytics',
    name: 'Application Performance',
    description: 'Track and analyze your job application success rates',
    category: FeatureCategory.Analytics,
    icon: 'briefcase',
    beta: true,
    limits: [ANALYSIS_FEATURE_LIMITS.application_reports_monthly],
  },

  // Resume Effectiveness
  {
    id: 'resume_effectiveness',
    name: 'Resume Effectiveness',
    description: 'Measure how well your resume performs against job requirements',
    category: FeatureCategory.Analytics,
    icon: 'file-text',
    beta: true,
    limits: [ANALYSIS_FEATURE_LIMITS.resume_effectiveness_monthly],
  },

  // Market Insights
  {
    id: 'market_intelligence',
    name: 'Market Insights',
    description: 'Get insights on job market trends relevant to your career',
    category: FeatureCategory.Analytics,
    icon: 'globe',
    beta: true,
    limits: [ANALYSIS_FEATURE_LIMITS.market_insights_monthly],
  },

  // Skill Trend Analysis
  {
    id: 'skill_trend_analysis',
    name: 'Skill Trend Analysis',
    description: 'Track emerging skills and technologies in your industry',
    category: FeatureCategory.Analytics,
    icon: 'line-chart',
    beta: true,
    limits: [ANALYSIS_FEATURE_LIMITS.skill_analysis_monthly],
  },

  // Salary Insights
  {
    id: 'salary_insights',
    name: 'Salary Insights',
    description: 'Compare your salary expectations with market rates for your role',
    category: FeatureCategory.Analytics,
    icon: 'dollar-sign',
    beta: true,
    limits: [ANALYSIS_FEATURE_LIMITS.market_insights_monthly],
  },

  // Interview Performance
  {
    id: 'interview_analytics',
    name: 'Interview Performance',
    description: 'Track and analyze your interview success rates and feedback',
    category: FeatureCategory.Analytics,
    icon: 'users',
    beta: true,
    limits: [ANALYSIS_FEATURE_LIMITS.application_reports_monthly],
  },
];
