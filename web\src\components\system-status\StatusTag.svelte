<script lang="ts">
  import { cn } from '$lib/utils.js';
  import {
    AlertCircle,
    CheckCircle,
    Clock,
    Search,
    AlertTriangle,
    XCircle,
    Play,
  } from 'lucide-svelte';
  import type { StatusTagType, SeverityLevelType } from '../../routes/system-status/types';

  // Props
  const {
    status,
    severity,
    className = '',
  } = $props<{
    status: StatusTagType;
    severity?: SeverityLevelType;
    className?: string;
  }>();

  // Get tag color based on status
  function getTagColor(status: StatusTagType, severity?: SeverityLevelType): string {
    // First check severity if provided
    if (severity) {
      switch (severity) {
        case 'critical':
          return 'bg-red-100 text-red-800 border-red-200';
        case 'major':
          return 'bg-orange-100 text-orange-800 border-orange-200';
        case 'minor':
          return 'bg-yellow-100 text-yellow-800 border-yellow-200';
        case 'maintenance':
          return 'bg-blue-100 text-blue-800 border-blue-200';
        case 'info':
          return 'bg-gray-100 text-gray-800 border-gray-200';
      }
    }

    // Fall back to status-based colors
    switch (status) {
      case 'resolved':
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'monitoring':
      case 'in-progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'investigating':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'identified':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'scheduled':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }

  // Get icon based on status
  function getStatusIcon(status: StatusTagType) {
    switch (status) {
      case 'resolved':
      case 'completed':
        return CheckCircle;
      case 'monitoring':
        return AlertCircle;
      case 'in-progress':
        return Play;
      case 'investigating':
        return Search;
      case 'identified':
        return AlertTriangle;
      case 'scheduled':
        return Clock;
      case 'cancelled':
        return XCircle;
      default:
        return AlertCircle;
    }
  }

  // Format status text
  function formatStatusText(status: StatusTagType): string {
    switch (status) {
      case 'in-progress':
        return 'In Progress';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  }

  const tagColor = $derived(getTagColor(status, severity));
  const StatusIcon = $derived(getStatusIcon(status));
  const statusText = $derived(formatStatusText(status));
</script>

<div
  class={cn(
    'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold',
    tagColor,
    className
  )}>
  <StatusIcon class="mr-1 h-3 w-3" />
  {statusText}
</div>
