<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { Edit, Briefcase } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import EmploymentInfoModal from './EmploymentInfoModal.svelte';

  // Define employment information type
  export interface EmploymentInfoData {
    ethnicity?: string;
    authorizedUS?: boolean;
    authorizedCanada?: boolean;
    authorizedUK?: boolean;
    requireSponsorship?: boolean;
    disability?: boolean;
    lgbtq?: boolean;
    gender?: string;
    veteran?: boolean;
  }

  // Props
  const { data, onSave } = $props<{
    data: EmploymentInfoData;
    onSave: (data: EmploymentInfoData) => Promise<boolean>;
  }>();

  // Modal state
  let modalOpen = $state(false);

  // Open edit modal
  function openEditModal() {
    modalOpen = true;
  }

  // Handle modal save
  async function handleSave(updatedData: EmploymentInfoData): Promise<boolean> {
    try {
      const success = await onSave(updatedData);
      return success;
    } catch (error) {
      console.error('Error saving employment information:', error);
      toast.error('Failed to save employment information');
      return false;
    }
  }

  // Format boolean for display
  function formatBoolean(value?: boolean): string {
    if (value === undefined || value === null) return 'Not specified';
    return value ? 'Yes' : 'No';
  }
</script>

<div class="rounded-lg border p-6">
  <div class="flex items-center justify-between">
    <h2 class="text-xl font-semibold">Employment Information</h2>
    <Button variant="ghost" size="sm" onclick={openEditModal}>
      <Edit class="mr-2 h-4 w-4" />
      Edit
    </Button>
  </div>
  <p class="text-muted-foreground mt-2 text-sm">
    Add your employment information to help with job applications.
  </p>

  <div class="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">Ethnicity</h3>
      <p>{data?.ethnicity || 'Not specified'}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">Authorized to work in US</h3>
      <p>{formatBoolean(data?.authorizedUS)}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">Authorized to work in Canada</h3>
      <p>{formatBoolean(data?.authorizedCanada)}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">Authorized to work in UK</h3>
      <p>{formatBoolean(data?.authorizedUK)}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">Require sponsorship</h3>
      <p>{formatBoolean(data?.requireSponsorship)}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">Disability</h3>
      <p>{formatBoolean(data?.disability)}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">LGBTQ+</h3>
      <p>{formatBoolean(data?.lgbtq)}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">Gender</h3>
      <p>{data?.gender || 'Not specified'}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">Veteran</h3>
      <p>{formatBoolean(data?.veteran)}</p>
    </div>
  </div>
</div>

<!-- Employment Information Modal -->
<EmploymentInfoModal
  open={modalOpen}
  {data}
  onClose={() => (modalOpen = false)}
  onSave={handleSave} />
