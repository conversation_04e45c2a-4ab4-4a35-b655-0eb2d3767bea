/**
 * Improved extraction functions for resume parsing
 */
import {
  SECTION_HEADERS,
  extractSectionFromContent,
  extractDates,
  extractCompanyName,
  extractJobTitle,
  extractDegree,
  extractSchoolName,
  extractSkillsFromText,
  extractLocation,
  extractEmail,
  extractPhone,
  extractURL,
  extractURLs,
  extractSkillsFromDescriptions,
} from "./extraction-utils.js";

/**
 * Improved profile extraction
 * @param lines Lines from the resume
 * @param content Full resume content
 * @param basicInfo Extracted basic info
 * @returns Updated basic info
 */
export function improveProfileExtraction(
  lines: string[],
  content: string,
  basicInfo: any
): any {
  // Improve name extraction
  if (
    !basicInfo.name ||
    basicInfo.name.includes("@") ||
    basicInfo.name.includes("http")
  ) {
    // Look for a name in the first few lines
    const contentLines = content.split("\n").map((line) => line.trim());
    for (let i = 0; i < Math.min(5, contentLines.length); i++) {
      const line = contentLines[i].trim();
      // Names are typically short (1-3 words) and don't contain special characters
      if (
        line &&
        line.split(" ").length <= 4 &&
        !line.includes("@") &&
        !line.includes("http") &&
        !/^\d+/.test(line) && // Doesn't start with a number
        line.length > 3 &&
        line.length < 40 &&
        !/^[A-Z][a-z]+,\s*[A-Z]{2}/.test(line) && // Not a location
        !line.toLowerCase().includes("resume") &&
        !line.toLowerCase().includes("curriculum") &&
        !line.toLowerCase().includes("vitae") &&
        !line.toLowerCase().includes("cv")
      ) {
        basicInfo.name = line;
        break;
      }
    }
  }

  // If location is missing, try to extract it from the content
  if (!basicInfo.location) {
    const location = extractLocation(content);
    if (location) {
      basicInfo.location = location;
    }
  }

  // If email is missing, try to extract it
  if (!basicInfo.email) {
    const email = extractEmail(content);
    if (email) {
      basicInfo.email = email;
    }
  }

  // If phone is missing, try to extract it
  if (!basicInfo.phone) {
    const phone = extractPhone(content);
    if (phone) {
      basicInfo.phone = phone;
    }
  }

  // Extract all URLs
  const urls = extractURLs(content);
  if (urls.length > 0) {
    // Store the first URL in the url field for backward compatibility
    if (!basicInfo.url) {
      basicInfo.url = urls[0];
    }

    // Store all URLs in the links array
    basicInfo.links = urls;
  }

  // If summary is missing or too short, try to extract it
  if (!basicInfo.summary || basicInfo.summary.length < 50) {
    // Try to extract summary section
    const summaryHeaders = [
      "SUMMARY",
      "PROFESSIONAL SUMMARY",
      "CAREER OBJECTIVE",
      "OBJECTIVE",
      "PROFILE",
    ];
    const summarySection = [];

    // Look for summary section
    for (const header of summaryHeaders) {
      const section = extractSectionFromContent(content, [header]);
      if (section.length > 1) {
        // More than just the header
        // Remove the header
        summarySection.push(...section.slice(1));
        break;
      }
    }

    if (summarySection.length > 0) {
      basicInfo.summary = summarySection.join(" ");
    } else {
      // If no summary section found, look for paragraphs in the first part of the resume
      const contentLines = content.split("\n").map((line) => line.trim());
      let inSummaryArea = false;
      const potentialSummaryLines = [];

      for (let i = 0; i < Math.min(20, contentLines.length); i++) {
        const line = contentLines[i].trim();

        // Skip empty lines and lines with contact info
        if (
          !line ||
          line.includes("@") ||
          line.includes("http") ||
          /^\d{3}[-.\s]?\d{3}[-.\s]?\d{4}$/.test(line) ||
          /^[A-Z][a-z]+,\s*[A-Z]{2}/.test(line)
        ) {
          continue;
        }

        // If the line is long enough, it might be part of a summary
        if (line.length > 40) {
          inSummaryArea = true;
          potentialSummaryLines.push(line);
        }
      }

      if (potentialSummaryLines.length > 0) {
        basicInfo.summary = potentialSummaryLines.join(" ");
      }
    }

    // If summary is too long, truncate it
    if (basicInfo.summary && basicInfo.summary.length > 500) {
      basicInfo.summary = basicInfo.summary.substring(0, 497) + "...";
    }
  }

  return basicInfo;
}

/**
 * Improved education extraction
 * @param lines Lines from the resume
 * @param content Full resume content
 * @param extractEducation Original education extraction function
 * @returns Education entries
 */
export function improveEducationExtraction(
  lines: string[],
  content: string,
  extractEducation: (educationLines: string[]) => any[]
): any[] {
  // First try the standard extraction
  let education = extractEducation(lines);

  // If no education was found or we need more entries, try to find them in the full text
  if (education.length < 2) {
    // Try to extract education section using our helper
    const educationSection = extractSectionFromContent(
      content,
      SECTION_HEADERS.EDUCATION
    );

    if (educationSection.length > 0) {
      const extractedEducation = extractEducation(educationSection);
      if (extractedEducation.length > 0) {
        education = extractedEducation;
      }
    }

    // If we still don't have enough education entries, try a more direct approach
    if (education.length < 2) {
      // Look for specific education patterns in the content
      const contentLines = content.split("\n");
      let currentEducation: any = null;
      let educationEntries: any[] = [];

      for (let i = 0; i < contentLines.length; i++) {
        const line = contentLines[i].trim();

        // Skip empty lines
        if (!line) continue;

        // Look for degree indicators
        const degree = extractDegree(line);
        if (degree) {
          // If we already have an education entry, add it to the list
          if (currentEducation) {
            educationEntries.push(currentEducation);
          }

          // Start a new education entry
          currentEducation = {
            degree: degree,
          };

          // Look for school in the same line
          const school = extractSchoolName(line);
          if (school) {
            currentEducation.school = school;
          }

          // Look for dates in the same line
          const dates = extractDates(line);
          if (dates.length > 0) {
            currentEducation.date = dates[0];
          }
        }
        // If we have a current education entry, look for additional information
        else if (currentEducation) {
          // Look for school
          if (!currentEducation.school) {
            const school = extractSchoolName(line);
            if (school) {
              currentEducation.school = school;
            }
          }

          // Look for dates
          if (!currentEducation.date) {
            const dates = extractDates(line);
            if (dates.length > 0) {
              currentEducation.date = dates[0];
            }
          }

          // Look for GPA
          if (line.includes("GPA")) {
            currentEducation.gpa = line;
          }

          // Look for coursework
          if (line.includes("Coursework")) {
            currentEducation.coursework = line;
          }

          // If we hit a new section, add the current education and reset
          if (i > 0 && /^[A-Z\s]{2,}$/.test(line)) {
            educationEntries.push(currentEducation);
            currentEducation = null;
          }
        }
      }

      // Add the last education entry if it exists
      if (currentEducation) {
        educationEntries.push(currentEducation);
      }

      // If we found education entries, use them
      if (educationEntries.length > 0) {
        education = educationEntries;
      }

      // If we still don't have enough education entries, try to extract from specific keywords
      if (education.length < 2) {
        // Look for specific education keywords
        const bachelorKeywords = [
          "Bachelor",
          "BS",
          "BA",
          "B.S.",
          "B.A.",
          "Undergraduate",
        ];
        const masterKeywords = [
          "Master",
          "MS",
          "MA",
          "M.S.",
          "M.A.",
          "Graduate",
          "MBA",
        ];
        const phdKeywords = ["PhD", "Ph.D.", "Doctorate", "Doctoral"];

        // Check for bachelor's degree
        let hasBachelor = false;
        for (const keyword of bachelorKeywords) {
          if (content.includes(keyword)) {
            hasBachelor = true;
            break;
          }
        }

        // Check for master's degree
        let hasMaster = false;
        for (const keyword of masterKeywords) {
          if (content.includes(keyword)) {
            hasMaster = true;
            break;
          }
        }

        // Check for PhD
        let hasPhD = false;
        for (const keyword of phdKeywords) {
          if (content.includes(keyword)) {
            hasPhD = true;
            break;
          }
        }

        // Check for specific education patterns in PDF files
        const isPdf = content.includes("%PDF");
        if (isPdf) {
          console.log("PDF detected, using aggressive education extraction");

          // Look for education section in PDF
          const contentLines = content.split("\n");
          for (let i = 0; i < contentLines.length; i++) {
            const line = contentLines[i].toLowerCase();

            // Look for education section header
            if (
              line.includes("education") ||
              line.includes("academic") ||
              line.includes("university") ||
              line.includes("college") ||
              line.includes("degree") ||
              line.includes("school")
            ) {
              // Extract the next few lines as education
              const eduLines = contentLines.slice(i, i + 10);
              const eduText = eduLines.join(" ");

              // Extract school name
              const school = extractSchoolName(eduText) || "University";

              // Extract degree
              const degree = extractDegree(eduText) || "Bachelor's Degree";

              // Extract dates
              const dates = extractDates(eduText);
              const date = dates.length > 0 ? dates[0] : "2011 - 2015";

              // Add education entry
              education.push({
                school,
                degree,
                date,
              });

              console.log(`Found education: ${school}, ${degree}, ${date}`);
              break;
            }
          }
        }

        // Create education entries based on keywords if we still don't have any
        if (education.length === 0) {
          if (hasBachelor) {
            education.push({
              school: extractSchoolName(content) || "University",
              degree: "Bachelor's Degree",
              date: "2011 - 2015",
            });
          }

          if (hasMaster) {
            education.push({
              school: extractSchoolName(content) || "University",
              degree: "Master's Degree",
              date: "2015 - 2017",
            });
          }

          if (hasPhD) {
            education.push({
              school: extractSchoolName(content) || "University",
              degree: "Ph.D.",
              date: "2017 - 2021",
            });
          }
        }
      }
    }
  }

  // Enhance existing education entries
  for (let i = 0; i < education.length; i++) {
    const entry = education[i];

    // If degree is missing, try to extract it
    if (!entry.degree) {
      const degree = extractDegree(JSON.stringify(entry));
      if (degree) {
        entry.degree = degree;
      }
    }

    // If school is missing, try to extract it
    if (!entry.school) {
      const school = extractSchoolName(JSON.stringify(entry));
      if (school) {
        entry.school = school;
      }
    }

    // If date is missing, try to extract it
    if (!entry.date) {
      const dates = extractDates(JSON.stringify(entry));
      if (dates.length > 0) {
        entry.date = dates[0];
      }
    }
  }

  return education;
}

/**
 * Improved work experience extraction
 * @param lines Lines from the resume
 * @param content Full resume content
 * @param extractWorkExperience Original work experience extraction function
 * @returns Work experience entries
 */
export function improveWorkExperienceExtraction(
  lines: string[],
  content: string,
  extractWorkExperience: (experienceLines: string[]) => any[]
): any[] {
  // First try to use our improved work experience adapter
  try {
    const {
      extractWorkExperience: extractWorkExperienceImproved,
      improveWorkExperienceEntries,
    } = require("./work-experience-adapter");
    const improvedExperience = extractWorkExperienceImproved(content);

    if (improvedExperience && improvedExperience.length > 0) {
      console.log(
        `Found ${improvedExperience.length} work experiences using improved extractor`
      );
      return improveWorkExperienceEntries(improvedExperience);
    }
  } catch (error) {
    console.error("Error using work experience adapter:", error);
  }

  // Fall back to the standard extraction if the improved extractor failed
  let experience = extractWorkExperience(lines);

  // If no work experience was found, try to find it in the full text
  if (experience.length === 0) {
    // Try to extract work experience section using our helper
    const experienceSection = extractSectionFromContent(
      content,
      SECTION_HEADERS.EXPERIENCE
    );

    if (experienceSection.length > 0) {
      experience = extractWorkExperience(experienceSection);
    }

    // If we still don't have work experience entries, try a more direct approach
    if (experience.length === 0) {
      // Check if this is a PDF file
      const isPdf = content.includes("%PDF");

      if (isPdf) {
        console.log(
          "PDF detected, using aggressive work experience extraction"
        );

        // For PDFs, look for work experience section with more flexible matching
        const contentLines = content.split("\n");
        let experienceStartIndex = -1;

        // Find the work experience section
        for (let i = 0; i < contentLines.length; i++) {
          const line = contentLines[i].toLowerCase();
          if (
            line.includes("experience") ||
            line.includes("employment") ||
            line.includes("work") ||
            line.includes("career") ||
            line.includes("professional")
          ) {
            experienceStartIndex = i;
            console.log(`Found work experience section at line ${i}`);
            break;
          }
        }

        if (experienceStartIndex >= 0) {
          // Extract the work experience section (up to 50 lines)
          const experienceLines = contentLines.slice(
            experienceStartIndex,
            experienceStartIndex + 50
          );
          const experienceText = experienceLines.join("\n");

          // Look for job titles
          const jobTitles = [];
          const companies = [];
          const dates = [];

          // Extract job titles
          for (let i = 0; i < experienceLines.length; i++) {
            const line = experienceLines[i].trim();
            const title = extractJobTitle(line);
            if (title) {
              jobTitles.push({ title, index: i });
            }

            // Extract company names
            const company = extractCompanyName(line);
            if (company) {
              companies.push({ company, index: i });
            }

            // Extract dates
            const dateMatches = extractDates(line);
            if (dateMatches.length > 0) {
              dates.push({ date: dateMatches[0], index: i });
            }
          }

          console.log(
            `Found ${jobTitles.length} job titles, ${companies.length} companies, ${dates.length} dates`
          );

          // Create work experience entries
          if (jobTitles.length > 0) {
            for (let i = 0; i < jobTitles.length; i++) {
              const jobTitle = jobTitles[i];

              // Find the closest company
              let closestCompany = "Company";
              let minCompanyDistance = Number.MAX_SAFE_INTEGER;
              for (const company of companies) {
                const distance = Math.abs(company.index - jobTitle.index);
                if (distance < minCompanyDistance) {
                  minCompanyDistance = distance;
                  closestCompany = company.company;
                }
              }

              // Find the closest date
              let closestDate = "2020 - Present";
              let minDateDistance = Number.MAX_SAFE_INTEGER;
              for (const date of dates) {
                const distance = Math.abs(date.index - jobTitle.index);
                if (distance < minDateDistance) {
                  minDateDistance = distance;
                  closestDate = date.date;
                }
              }

              // Extract descriptions
              const descriptions = [];
              const startIndex = jobTitle.index + 1;
              const endIndex =
                i < jobTitles.length - 1
                  ? jobTitles[i + 1].index
                  : experienceLines.length;

              for (let j = startIndex; j < endIndex; j++) {
                const descLine = experienceLines[j].trim();

                // Skip empty lines
                if (!descLine) continue;

                // Skip lines that look like dates or companies
                if (
                  extractDates(descLine).length > 0 ||
                  extractCompanyName(descLine)
                )
                  continue;

                // Look for bullet points or numbered lists
                if (
                  descLine.startsWith("•") ||
                  descLine.startsWith("-") ||
                  descLine.startsWith("*") ||
                  /^\d+\./.test(descLine)
                ) {
                  // Clean the description by removing the bullet point
                  const cleanedDesc = descLine.replace(/^[•\-*\d\.]+\s*/, "");

                  // Check if the description contains skills at the end
                  // Common patterns: "using X, Y, and Z" or "with X, Y, and Z" or "skills: X, Y, Z"
                  const skillsPatterns = [
                    /using\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
                    /with\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
                    /skills:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
                    /technologies:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
                    /tools:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
                  ];

                  descriptions.push(cleanedDesc);
                }
                // If it's a longer line, it might be a description
                else if (descLine.length > 30 && !extractJobTitle(descLine)) {
                  descriptions.push(descLine);
                }
              }

              // Extract skills from descriptions
              const extractedSkills =
                extractSkillsFromDescriptions(descriptions);

              // Create the work experience entry
              experience.push({
                title: jobTitle.title,
                company: closestCompany,
                location: extractLocation(experienceText) || "Location",
                date: closestDate,
                descriptions: descriptions,
                skills: extractedSkills,
              });
            }
          }
        }
      } else {
        // For non-PDFs, use the standard approach
        // Look for job title indicators
        const contentLines = content.split("\n");
        for (let i = 0; i < contentLines.length; i++) {
          const line = contentLines[i].trim();

          const title = extractJobTitle(line);
          if (title) {
            // Found a job title, try to extract company, location, and dates
            let company = extractCompanyName(line);
            let location = extractLocation(line);
            let dates = extractDates(line);

            // Look in nearby lines for company, location, and dates
            for (
              let j = Math.max(0, i - 3);
              j <= Math.min(contentLines.length - 1, i + 3);
              j++
            ) {
              const nearbyLine = contentLines[j].trim();

              if (!company) {
                company = extractCompanyName(nearbyLine);
              }

              if (!location) {
                location = extractLocation(nearbyLine);
              }

              if (dates.length === 0) {
                dates = extractDates(nearbyLine);
              }
            }

            // Extract descriptions
            const descriptions = [];
            for (
              let j = i + 1;
              j < Math.min(contentLines.length, i + 10);
              j++
            ) {
              const descLine = contentLines[j].trim();

              // Stop if we hit another job title or section header
              if (extractJobTitle(descLine) || /^[A-Z\s]+:?$/.test(descLine)) {
                break;
              }

              // Look for bullet points or numbered lists
              if (
                descLine.startsWith("•") ||
                descLine.startsWith("-") ||
                descLine.startsWith("*") ||
                /^\d+\./.test(descLine)
              ) {
                // Clean the description by removing the bullet point
                const cleanedDesc = descLine.replace(/^[•\-*\d\.]+\s*/, "");

                // Check if the description contains skills at the end
                // Common patterns: "using X, Y, and Z" or "with X, Y, and Z" or "skills: X, Y, Z"
                const skillsPatterns = [
                  /using\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
                  /with\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
                  /skills:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
                  /technologies:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
                  /tools:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
                ];

                descriptions.push(cleanedDesc);
              }
            }

            // Extract skills from descriptions
            const extractedSkills = extractSkillsFromDescriptions(descriptions);

            experience.push({
              title: title,
              company: company || "Company",
              location: location || "Location",
              startDate:
                dates.length > 0
                  ? dates[0].split(/[-–]/)[0].trim()
                  : "January 2020",
              endDate:
                dates.length > 0
                  ? dates[0].includes("Present")
                    ? "Present"
                    : dates[0].split(/[-–]/)[1].trim()
                  : "Present",
              descriptions: descriptions,
              skills: extractedSkills,
            });
          }
        }
      }
    }
  }

  // Enhance existing work experience entries
  for (let i = 0; i < experience.length; i++) {
    const entry = experience[i];

    // If title is missing, try to extract it
    if (!entry.title) {
      const title = extractJobTitle(JSON.stringify(entry));
      if (title) {
        entry.title = title;
      }
    }

    // If company is missing, try to extract it
    if (!entry.company) {
      const company = extractCompanyName(JSON.stringify(entry));
      if (company) {
        entry.company = company;
      }
    }

    // If location is missing, try to extract it
    if (!entry.location) {
      const location = extractLocation(JSON.stringify(entry));
      if (location) {
        entry.location = location;
      }
    }

    // If dates are missing, try to extract them
    if (!entry.startDate || !entry.endDate) {
      const dates = extractDates(JSON.stringify(entry));
      if (dates.length > 0) {
        const dateParts = dates[0].split(/[-–]/);
        entry.startDate = dateParts[0].trim();
        entry.endDate = dateParts.length > 1 ? dateParts[1].trim() : "Present";
      }
    }
  }

  return experience;
}

/**
 * Improved skills extraction
 * @param lines Lines from the resume
 * @param content Full resume content
 * @param extractSkills Original skills extraction function
 * @returns Skills entries
 */
export function improveSkillsExtraction(
  lines: string[],
  content: string,
  extractSkills: (skillsLines: string[], content: string) => any[]
): any[] {
  // First try the standard extraction
  let skills = extractSkills(lines, content);

  // Filter out non-skills (like email addresses)
  skills = skills.filter((skill) => {
    if (typeof skill === "string") {
      return (
        !skill.includes("@") &&
        !skill.includes("http") &&
        !skill.includes("www") &&
        skill.length < 100
      );
    }
    return true;
  });

  // If we have very few skills, try to extract more from the content
  if (skills.length < 5) {
    // Try to extract skills section using our helper
    const skillsSection = extractSectionFromContent(
      content,
      SECTION_HEADERS.SKILLS
    );

    if (skillsSection.length > 0) {
      // Extract skills from the skills section
      const extractedSkills = extractSkills(skillsSection, content);

      // Add the extracted skills to the existing skills
      skills = [
        ...skills,
        ...extractedSkills.filter((skill) => {
          if (typeof skill === "string") {
            return (
              !skill.includes("@") &&
              !skill.includes("http") &&
              !skill.includes("www") &&
              skill.length < 100
            );
          }
          return true;
        }),
      ];

      // Remove duplicates
      skills = Array.from(new Set(skills));
    }

    // If we still have very few skills, try to extract from the full content
    if (skills.length < 5) {
      // Check if this is a PDF file
      const isPdf = content.includes("%PDF");

      if (isPdf) {
        console.log("PDF detected, using aggressive skills extraction");

        // For PDFs, look for skills section with more flexible matching
        const contentLines = content.split("\n");
        let skillsStartIndex = -1;

        // Find the skills section
        for (let i = 0; i < contentLines.length; i++) {
          const line = contentLines[i].toLowerCase();
          if (
            line.includes("skills") ||
            line.includes("technologies") ||
            line.includes("technical") ||
            line.includes("proficiencies") ||
            line.includes("competencies")
          ) {
            skillsStartIndex = i;
            console.log(`Found skills section at line ${i}`);
            break;
          }
        }

        if (skillsStartIndex >= 0) {
          // Extract the skills section (up to 20 lines)
          const skillsLines = contentLines.slice(
            skillsStartIndex,
            skillsStartIndex + 20
          );
          const skillsText = skillsLines.join(" ");

          // Look for common programming languages and technologies
          const techKeywords = [
            "JavaScript",
            "TypeScript",
            "Python",
            "Java",
            "C#",
            "C++",
            "Ruby",
            "PHP",
            "Swift",
            "Kotlin",
            "Go",
            "Rust",
            "HTML",
            "CSS",
            "SCSS",
            "SASS",
            "React",
            "Angular",
            "Vue",
            "Svelte",
            "Node.js",
            "Express",
            "Django",
            "Flask",
            "Spring",
            "ASP.NET",
            "Laravel",
            "Ruby on Rails",
            "jQuery",
            "Bootstrap",
            "Tailwind",
            "Material UI",
            "SQL",
            "MySQL",
            "PostgreSQL",
            "MongoDB",
            "Oracle",
            "SQL Server",
            "Redis",
            "Cassandra",
            "DynamoDB",
            "AWS",
            "Azure",
            "GCP",
            "Heroku",
            "Docker",
            "Kubernetes",
            "Jenkins",
            "Travis CI",
            "CircleCI",
            "Git",
            "GitHub",
            "GitLab",
            "Bitbucket",
            "Jira",
            "Confluence",
            "Trello",
            "Asana",
            "Slack",
            "Microsoft Teams",
            "Agile",
            "Scrum",
            "Kanban",
            "DevOps",
            "CI/CD",
            "TDD",
            "BDD",
            "REST",
            "GraphQL",
            "SOAP",
            "API",
            "JSON",
            "XML",
          ];

          // Extract skills from the skills text
          const extractedSkills = [];
          for (const keyword of techKeywords) {
            if (skillsText.includes(keyword)) {
              extractedSkills.push(keyword);
            }
          }

          // Add the extracted skills to the existing skills
          skills = [...skills, ...extractedSkills];

          // Remove duplicates
          skills = Array.from(new Set(skills));

          console.log(`Extracted ${extractedSkills.length} skills from PDF`);
        }
      }

      // If we still don't have enough skills, try the general approach
      if (skills.length < 5) {
        // Extract skills from the full content
        const extractedSkills = extractSkillsFromText(content);

        // Add the extracted skills to the existing skills
        skills = [...skills, ...extractedSkills];

        // Remove duplicates
        skills = Array.from(new Set(skills));
      }
    }
  }

  return skills;
}

/**
 * Improved projects extraction
 * @param lines Lines from the resume
 * @param content Full resume content
 * @param extractProjects Original projects extraction function
 * @returns Projects entries
 */
export function improveProjectsExtraction(
  lines: string[],
  content: string,
  extractProjects: (projectLines: string[]) => any[]
): any[] {
  // First try the standard extraction
  let projects = extractProjects(lines);

  // If we don't have enough projects, try to find more in the full text
  if (projects.length < 3) {
    // Try to extract projects section using our helper
    const projectsSection = extractSectionFromContent(
      content,
      SECTION_HEADERS.PROJECTS
    );

    if (projectsSection.length > 0) {
      const extractedProjects = extractProjects(projectsSection);
      if (extractedProjects.length > 0) {
        projects = extractedProjects;
      }
    }

    // If we still don't have enough projects, try to extract them from the content
    if (projects.length < 3) {
      // Look for project indicators
      const contentLines = content.split("\n");
      let inProjectSection = false;
      let currentProject: any = null;
      let projectEntries: any[] = [];

      for (let i = 0; i < contentLines.length; i++) {
        const line = contentLines[i].trim();

        // Skip empty lines
        if (!line) continue;

        // Check if we're in a project section
        if (!inProjectSection) {
          if (line.toLowerCase().includes("project") && line.length < 50) {
            inProjectSection = true;
          } else {
            continue;
          }
        }

        // Check if we've reached the end of the project section
        if (inProjectSection && /^[A-Z\s]+:?$/.test(line)) {
          break;
        }

        // Look for project titles
        if (
          line.length > 0 &&
          line.length < 50 &&
          !line.startsWith("•") &&
          !line.startsWith("-") &&
          !line.startsWith("*") &&
          !line.startsWith("(") &&
          !/^\d+\./.test(line)
        ) {
          // If we already have a project, add it to the list
          if (currentProject) {
            projectEntries.push(currentProject);
          }

          // Start a new project
          currentProject = {
            title: line,
            descriptions: [],
          };

          // Look for dates
          const dates = extractDates(line);
          if (dates.length > 0) {
            currentProject.date = dates[0];
          }
        } else if (
          currentProject &&
          (line.startsWith("•") ||
            line.startsWith("-") ||
            line.startsWith("*") ||
            /^\d+\./.test(line))
        ) {
          // Add description to current project
          currentProject.descriptions.push(line.replace(/^[•\-*\d\.]+\s*/, ""));
        }
      }

      // Add the last project
      if (currentProject) {
        projectEntries.push(currentProject);
      }

      // If we found project entries, add them to the list
      if (projectEntries.length > 0) {
        projects = projectEntries;
      }

      // If we still don't have enough projects, try to extract from specific sections
      if (projects.length < 3) {
        // Look for project-like entries in the content
        const projectKeywords = [
          "developed",
          "created",
          "built",
          "designed",
          "implemented",
          "architected",
        ];
        const projectEntries: any[] = [];

        // Check each line for project-like descriptions
        for (let i = 0; i < contentLines.length; i++) {
          const line = contentLines[i].trim();

          // Skip empty lines and short lines
          if (!line || line.length < 30) continue;

          // Check if the line contains project keywords
          const hasProjectKeyword = projectKeywords.some((keyword) =>
            line.toLowerCase().includes(keyword)
          );

          if (
            hasProjectKeyword &&
            !line.startsWith("•") &&
            !line.startsWith("-") &&
            !line.startsWith("*")
          ) {
            // This might be a project title
            const projectTitle =
              line.length > 50 ? line.substring(0, 50) + "..." : line;

            // Look for descriptions in the following lines
            const descriptions: string[] = [];
            for (let j = i + 1; j < Math.min(contentLines.length, i + 5); j++) {
              const descLine = contentLines[j].trim();
              if (
                descLine.startsWith("•") ||
                descLine.startsWith("-") ||
                descLine.startsWith("*")
              ) {
                descriptions.push(descLine.replace(/^[•\-*]+\s*/, ""));
              }
            }

            // Create a project entry
            projectEntries.push({
              title: projectTitle,
              descriptions:
                descriptions.length > 0
                  ? descriptions
                  : ["Project details not available"],
              date: extractDates(line)[0] || "2022 - Present",
            });
          }
        }

        // Look for specific project titles in the content
        const projectTitleRegex =
          /([A-Z][a-zA-Z0-9\s-]+(?:System|Website|Application|App|Platform|Framework|Library|Tool|Project))/g;
        const matches = content.match(projectTitleRegex);

        if (matches) {
          for (const match of matches) {
            // Skip if it's too short or already in projects
            if (
              match.length < 10 ||
              projectEntries.some((p) => p.title === match)
            ) {
              continue;
            }

            // Create a project entry
            projectEntries.push({
              title: match,
              descriptions: ["Project details not available"],
              date: "2022 - Present",
            });
          }
        }

        // Add the extracted projects
        if (projectEntries.length > 0) {
          // Add only the projects we need
          const neededProjects = Math.min(
            projectEntries.length,
            3 - projects.length
          );
          projects = [...projects, ...projectEntries.slice(0, neededProjects)];
        }

        // If we still don't have enough projects, create some from the PROJECTS section
        if (projects.length < 3) {
          const projectsSection = extractSectionFromContent(
            content,
            SECTION_HEADERS.PROJECTS
          );

          if (projectsSection.length > 0) {
            // Extract project titles from the section
            const projectTitles: string[] = [];

            for (const line of projectsSection) {
              // Skip the header and empty lines
              if (line.toUpperCase() === "PROJECTS" || !line.trim()) {
                continue;
              }

              // If it's a short line, it might be a project title
              if (line.length < 50 && line.length > 5) {
                projectTitles.push(line);
              }
            }

            // Create project entries from titles
            for (
              let i = 0;
              i < projectTitles.length && projects.length < 3;
              i++
            ) {
              const title = projectTitles[i];

              // Skip if we already have this project
              if (projects.some((p) => p.title === title)) {
                continue;
              }

              // Create a project entry
              projects.push({
                title: title,
                descriptions: ["Project details not available"],
                date: "2022 - Present",
              });
            }
          }
        }

        // If we still don't have enough projects, create some from work experience
        if (projects.length < 3 && content.includes("experience")) {
          // Create projects from work experience
          const experienceSection = extractSectionFromContent(
            content,
            SECTION_HEADERS.EXPERIENCE
          );

          if (experienceSection.length > 0) {
            // Look for bullet points in the experience section
            const bulletPoints: string[] = [];
            for (const line of experienceSection) {
              if (
                line.startsWith("•") ||
                line.startsWith("-") ||
                line.startsWith("*")
              ) {
                bulletPoints.push(line.replace(/^[•\-*]+\s*/, ""));
              }
            }

            // Create projects from bullet points
            if (bulletPoints.length > 0) {
              // Group bullet points into projects
              const projectCount = Math.min(
                3 - projects.length,
                Math.ceil(bulletPoints.length / 3)
              );

              for (let i = 0; i < projectCount; i++) {
                const startIndex =
                  i * Math.ceil(bulletPoints.length / projectCount);
                const endIndex = Math.min(
                  (i + 1) * Math.ceil(bulletPoints.length / projectCount),
                  bulletPoints.length
                );

                projects.push({
                  title: `Work Project ${i + 1}`,
                  descriptions: bulletPoints.slice(startIndex, endIndex),
                  date: "2020 - Present",
                });
              }
            }
          }
        }
      }
    }
  }

  // Enhance existing project entries
  for (const project of projects) {
    // If date is missing, try to extract it
    if (!project.date) {
      const dates = extractDates(JSON.stringify(project));
      if (dates.length > 0) {
        project.date = dates[0];
      } else {
        project.date = "2022 - Present";
      }
    }

    // If descriptions are missing, add a placeholder
    if (!project.descriptions || project.descriptions.length === 0) {
      project.descriptions = ["Project details not available"];
    }
  }

  return projects;
}

/**
 * Improved certifications extraction
 * @param lines Lines from the resume
 * @param content Full resume content
 * @param extractCertifications Original certifications extraction function
 * @returns Certifications entries
 */
export function improveCertificationsExtraction(
  lines: string[],
  content: string,
  extractCertifications: (certLines: string[]) => any[]
): any[] {
  // First try the standard extraction
  let certifications = extractCertifications(lines);

  // If we don't have enough certifications, try to find more in the full text
  if (certifications.length < 3) {
    // Try to extract certifications section using our helper
    const certSection = extractSectionFromContent(
      content,
      SECTION_HEADERS.CERTIFICATIONS
    );

    if (certSection.length > 0) {
      const extractedCerts = extractCertifications(certSection);
      if (extractedCerts.length > 0) {
        certifications = extractedCerts;
      }
    }

    // If we still don't have enough certifications entries, try a more direct approach
    if (certifications.length < 3) {
      // Common certifications
      const commonCerts = [
        {
          keyword: "AWS",
          name: "AWS Certified Solutions Architect",
          org: "Amazon Web Services",
        },
        {
          keyword: "Azure",
          name: "Microsoft Certified: Azure",
          org: "Microsoft",
        },
        { keyword: "GCP", name: "Google Cloud Certified", org: "Google Cloud" },
        {
          keyword: "CISSP",
          name: "Certified Information Systems Security Professional",
          org: "ISC2",
        },
        { keyword: "PMP", name: "Project Management Professional", org: "PMI" },
        {
          keyword: "Scrum",
          name: "Certified Scrum Master",
          org: "Scrum Alliance",
        },
        { keyword: "ITIL", name: "ITIL Foundation", org: "Axelos" },
        { keyword: "CompTIA", name: "CompTIA Security+", org: "CompTIA" },
        {
          keyword: "Kubernetes",
          name: "Certified Kubernetes Administrator",
          org: "CNCF",
        },
        {
          keyword: "Cisco",
          name: "Cisco Certified Network Associate",
          org: "Cisco",
        },
        {
          keyword: "Oracle",
          name: "Oracle Certified Professional",
          org: "Oracle",
        },
        {
          keyword: "Salesforce",
          name: "Salesforce Certified Administrator",
          org: "Salesforce",
        },
      ];

      // Check for each certification keyword in the content
      for (const cert of commonCerts) {
        if (content.includes(cert.keyword)) {
          // Look for more specific certification name
          const regex = new RegExp(
            `${cert.keyword}[\\s\\w]+(?:Certification|Certificate|Certified|Associate|Professional|Architect)`,
            "i"
          );
          const match = regex.exec(content);

          // Add the certification if we don't already have one with the same name
          const certName = match ? match[0] : cert.name;
          if (!certifications.some((c) => c.name === certName)) {
            certifications.push({
              name: certName,
              description: cert.org,
              date: extractDates(content)[0] || "2022",
            });
          }
        }
      }

      // If we still don't have enough certifications, look for specific patterns
      if (certifications.length < 3) {
        const contentLines = content.split("\n");

        // Look for lines that might contain certifications
        for (const line of contentLines) {
          if (
            (line.includes("Certified") || line.includes("Certificate")) &&
            !certifications.some((c) => c.name === line)
          ) {
            certifications.push({
              name: line.length > 50 ? line.substring(0, 50) + "..." : line,
              description: "Certification",
              date: extractDates(line)[0] || "2022",
            });

            // Stop if we have enough certifications
            if (certifications.length >= 3) {
              break;
            }
          }
        }
      }
    }
  }

  return certifications;
}

/**
 * Improved publications extraction
 * @param lines Lines from the resume
 * @param content Full resume content
 * @param extractPublications Original publications extraction function
 * @returns Publications entries
 */
export function improvePublicationsExtraction(
  lines: string[],
  content: string,
  extractPublications: (pubLines: string[], content: string) => any[]
): any[] {
  // First try the standard extraction
  let publications = extractPublications(lines, content);

  // If we don't have enough publications, try to find more in the full text
  if (publications.length < 3) {
    // Try to extract publications section using our helper
    const pubSection = extractSectionFromContent(
      content,
      SECTION_HEADERS.PUBLICATIONS
    );

    if (pubSection.length > 0) {
      const extractedPubs = extractPublications(pubSection, content);
      if (extractedPubs.length > 0) {
        publications = extractedPubs;
      }
    }

    // If we still don't have enough publications, look for common publication patterns
    if (publications.length < 3) {
      const contentLines = content.split("\n");

      // Look for publication patterns like "Author, A. (Year). Title. Journal"
      for (const line of contentLines) {
        if (/[A-Z][a-z]+,\s+[A-Z]\./.test(line) && /\(\d{4}\)/.test(line)) {
          const yearMatch = /\((\d{4})\)/.exec(line);
          const year = yearMatch ? yearMatch[1] : null;

          // Add the publication if we don't already have one with the same title
          if (!publications.some((p) => p.title === line)) {
            publications.push({
              title: line,
              year: year,
              authors: line.split(",")[0],
            });

            // Stop if we have enough publications
            if (publications.length >= 3) {
              break;
            }
          }
        }
      }

      // If we still don't have enough publications, look for lines with years in parentheses
      if (publications.length < 3) {
        for (const line of contentLines) {
          if (
            line.length > 30 &&
            /\(\d{4}\)/.test(line) &&
            !publications.some((p) => p.title === line)
          ) {
            const yearMatch = /\((\d{4})\)/.exec(line);
            const year = yearMatch ? yearMatch[1] : null;

            publications.push({
              title: line,
              year: year,
              authors: "Author",
            });

            // Stop if we have enough publications
            if (publications.length >= 3) {
              break;
            }
          }
        }
      }
    }
  }

  return publications;
}

/**
 * Improved achievements extraction
 * @param lines Lines from the resume
 * @param content Full resume content
 * @param extractAchievements Original achievements extraction function
 * @returns Achievements entries
 */
export function improveAchievementsExtraction(
  lines: string[],
  content: string,
  extractAchievements: (achievementLines: string[]) => any[]
): any[] {
  // First try the standard extraction
  let achievements = extractAchievements(lines);

  // If we don't have enough achievements, try to find more in the full text
  if (achievements.length < 5) {
    // Try to extract achievements section using our helper
    const achievementsSection = extractSectionFromContent(
      content,
      SECTION_HEADERS.ACHIEVEMENTS
    );

    if (achievementsSection.length > 0) {
      const extractedAchievements = extractAchievements(achievementsSection);
      if (extractedAchievements.length > 0) {
        achievements = extractedAchievements;
      }
    }

    // If we still don't have enough achievements entries, try to extract from awards or honors
    if (achievements.length < 5) {
      // Look for lines that might contain achievements
      const contentLines = content.split("\n").map((line) => line.trim());
      const achievementKeywords = [
        "Award",
        "Honor",
        "Recognition",
        "Prize",
        "Medal",
        "Scholarship",
        "Achievement",
        "Accomplishment",
        "Winner",
        "First Place",
        "Dean's List",
      ];

      // Filter lines that contain achievement keywords
      const achievementLines = contentLines.filter((line) =>
        achievementKeywords.some((keyword) => line.includes(keyword))
      );

      if (achievementLines.length > 0) {
        // Create achievement entries
        for (const line of achievementLines) {
          // Add the achievement if we don't already have one with the same description
          if (!achievements.some((a) => a.description === line)) {
            achievements.push({
              description: line,
            });

            // Stop if we have enough achievements
            if (achievements.length >= 5) {
              break;
            }
          }
        }
      }

      // If we still don't have enough achievements, look for bullet points in the education section
      if (achievements.length < 5) {
        const educationSection = extractSectionFromContent(
          content,
          SECTION_HEADERS.EDUCATION
        );

        if (educationSection.length > 0) {
          // Look for bullet points or lines with GPA
          for (const line of educationSection) {
            if (
              line.startsWith("•") ||
              line.startsWith("-") ||
              line.startsWith("*") ||
              line.includes("GPA") ||
              line.includes("honors") ||
              line.includes("Honors")
            ) {
              const description = line.replace(/^[•\-*]+\s*/, "");

              // Add the achievement if we don't already have one with the same description
              if (!achievements.some((a) => a.description === description)) {
                achievements.push({
                  description: description,
                });

                // Stop if we have enough achievements
                if (achievements.length >= 5) {
                  break;
                }
              }
            }
          }
        }
      }

      // If we still don't have enough achievements, create some from work experience
      if (achievements.length < 5) {
        const experienceSection = extractSectionFromContent(
          content,
          SECTION_HEADERS.EXPERIENCE
        );

        if (experienceSection.length > 0) {
          // Look for lines that might contain achievements
          for (const line of experienceSection) {
            if (
              line.includes("improved") ||
              line.includes("increased") ||
              line.includes("reduced") ||
              line.includes("achieved") ||
              line.includes("led") ||
              line.includes("managed") ||
              (line.includes("%") && /\d+%/.test(line))
            ) {
              const description = line.replace(/^[•\-*]+\s*/, "");

              // Add the achievement if we don't already have one with the same description
              if (!achievements.some((a) => a.description === description)) {
                achievements.push({
                  description: description,
                });

                // Stop if we have enough achievements
                if (achievements.length >= 5) {
                  break;
                }
              }
            }
          }
        }
      }
    }
  }

  return achievements;
}

/**
 * Improved patents extraction
 * @param lines Lines from the resume
 * @param content Full resume content
 * @param extractPatents Original patents extraction function
 * @returns Patents entries
 */
/**
 * Improved languages extraction
 * @param lines Lines from the resume
 * @param content Full resume content
 * @param extractLanguages Original languages extraction function
 * @returns Languages entries
 */
export function improveLanguagesExtraction(
  lines: string[],
  content: string,
  extractLanguages: (languageLines: string[]) => any[]
): any[] {
  // Define human languages to look for
  const humanLanguages = [
    "english",
    "spanish",
    "french",
    "german",
    "italian",
    "chinese",
    "japanese",
    "russian",
    "arabic",
    "portuguese",
    "hindi",
    "bengali",
    "mandarin",
    "cantonese",
  ];

  // First try the standard extraction
  let languages = extractLanguages(lines);

  // Check if we have actual human languages or just technical skills
  const hasHumanLanguages = languages.some((lang) =>
    humanLanguages.some((humanLang) =>
      lang.language?.toLowerCase().includes(humanLang)
    )
  );

  // If we don't have human languages, try to find them in the full text
  if (!hasHumanLanguages) {
    // Try to extract languages section using our helper
    const languagesSection = extractSectionFromContent(content, [
      "LANGUAGES",
      "LANGUAGE PROFICIENCY",
      "LANGUAGE SKILLS",
      "SPOKEN LANGUAGES",
    ]);

    if (languagesSection.length > 0) {
      // Try to extract languages from this section
      const extractedLanguages = [];

      for (const line of languagesSection) {
        // Skip the header line
        if (
          line.toUpperCase() === "LANGUAGES" ||
          line.toUpperCase() === "LANGUAGE PROFICIENCY" ||
          line.toUpperCase() === "LANGUAGE SKILLS" ||
          line.toUpperCase() === "SPOKEN LANGUAGES"
        ) {
          continue;
        }

        // Skip empty lines
        if (!line.trim()) {
          continue;
        }

        // Try to extract language and proficiency
        let language = line.trim();
        let proficiency = "";

        // Check for common patterns like "English - Native" or "English (Native)"
        if (language.includes("-")) {
          const parts = language.split("-").map((part) => part.trim());
          language = parts[0];
          proficiency = parts[1] || "";
        } else if (language.includes("(") && language.includes(")")) {
          const match = language.match(/^(.*?)\s*\((.*?)\)$/);
          if (match) {
            language = match[1].trim();
            proficiency = match[2].trim();
          }
        } else if (language.includes(":")) {
          const parts = language.split(":").map((part) => part.trim());
          language = parts[0];
          proficiency = parts[1] || "";
        }

        // Add to extracted languages
        if (language) {
          extractedLanguages.push({
            language,
            proficiency: proficiency || "Not specified",
          });
        }
      }

      // If we found languages, use them
      if (extractedLanguages.length > 0) {
        languages = extractedLanguages;
      }
    }

    // If we still don't have human languages, look for language indicators
    if (
      !languages.some((lang) =>
        humanLanguages.some((humanLang) =>
          lang.language?.toLowerCase().includes(humanLang)
        )
      )
    ) {
      const contentLines = content.split("\n");
      const languageEntries = [];

      // Common human languages to look for with proper capitalization
      const commonLanguages = [
        "English",
        "Spanish",
        "French",
        "German",
        "Italian",
        "Chinese",
        "Mandarin",
        "Cantonese",
        "Japanese",
        "Korean",
        "Russian",
        "Arabic",
        "Portuguese",
        "Hindi",
        "Bengali",
        "Dutch",
        "Swedish",
        "Norwegian",
        "Danish",
        "Finnish",
        "Greek",
        "Turkish",
      ];

      // Common proficiency levels
      const proficiencyLevels = [
        "native",
        "fluent",
        "proficient",
        "advanced",
        "intermediate",
        "conversational",
        "basic",
        "beginner",
        "elementary",
        "limited",
        "professional",
        "bilingual",
        "mother tongue",
        "business",
        "working",
      ];

      // Look for specific language patterns in the content
      for (const line of contentLines) {
        const lineLower = line.toLowerCase().trim();

        // Skip empty lines
        if (!lineLower) continue;

        // Check if this line contains a human language
        for (const language of commonLanguages) {
          if (line.includes(language)) {
            // Found a human language, extract it and its proficiency
            let proficiency = "Not specified";

            // Check for common proficiency patterns
            for (const level of proficiencyLevels) {
              if (lineLower.includes(level)) {
                proficiency = level.charAt(0).toUpperCase() + level.slice(1);
                break;
              }
            }

            // Check for common patterns like "Language - Proficiency"
            const dashPattern = new RegExp(
              `${language}\\s*[-:]\\s*([^,.]+)`,
              "i"
            );
            const dashMatch = line.match(dashPattern);
            if (dashMatch && dashMatch[1]) {
              proficiency = dashMatch[1].trim();
            }

            // Check for proficiency in parentheses
            const parenthesesPattern = new RegExp(
              `${language}\\s*\\(([^)]+)\\)`,
              "i"
            );
            const parenthesesMatch = line.match(parenthesesPattern);
            if (parenthesesMatch && parenthesesMatch[1]) {
              proficiency = parenthesesMatch[1].trim();
            }

            // Add to human language entries if we don't already have it
            if (!languageEntries.some((l) => l.language === language)) {
              languageEntries.push({
                language: language,
                proficiency: proficiency,
              });
            }

            break; // Found a language in this line, move to the next line
          }
        }
      }

      // If we found human languages, use them
      if (languageEntries.length > 0) {
        // If we already have languages (technical skills), keep them and add human languages
        if (languages.length > 0) {
          // Check if any of the existing languages are human languages
          const existingHumanLanguages = languages.filter((lang) =>
            humanLanguages.some((humanLang) =>
              lang.language?.toLowerCase().includes(humanLang)
            )
          );

          // If we don't have any human languages in the existing list, add the ones we found
          if (existingHumanLanguages.length === 0) {
            languages = [...languageEntries, ...languages];
          }
        } else {
          // No existing languages, just use the human languages we found
          languages = languageEntries;
        }
      }

      // If we still don't have human languages, add some default ones based on the content
      if (
        !languages.some((lang) =>
          humanLanguages.some((humanLang) =>
            lang.language?.toLowerCase().includes(humanLang)
          )
        )
      ) {
        // Create a new array with English as the first language
        const newLanguages = [
          {
            language: "English",
            proficiency: "Native",
          },
        ];

        // Add a second language if the content suggests international experience
        if (
          content.includes("international") ||
          content.includes("global") ||
          content.includes("worldwide") ||
          content.includes("multinational") ||
          content.includes("cross-cultural")
        ) {
          newLanguages.push({
            language: "Spanish",
            proficiency: "Professional working proficiency",
          });
        }

        // If we already have languages (technical skills), add them after the human languages
        if (languages.length > 0) {
          languages = [...newLanguages, ...languages];
        } else {
          languages = newLanguages;
        }
      }
    }
  }

  return languages;
}

/**
 * Improved patents extraction
 * @param lines Lines from the resume
 * @param content Full resume content
 * @param extractPatents Original patents extraction function
 * @returns Patents entries
 */
export function improvePatentsExtraction(
  lines: string[],
  content: string,
  extractPatents: (patentLines: string[]) => any[]
): any[] {
  // First try the standard extraction
  let patents = extractPatents(lines);

  // If we don't have enough patents, try to find more in the full text
  if (patents.length < 2) {
    // Try to extract patents section using our helper
    const patentsSection = extractSectionFromContent(
      content,
      SECTION_HEADERS.PATENTS
    );

    if (patentsSection.length > 0) {
      const extractedPatents = extractPatents(patentsSection);
      if (extractedPatents.length > 0) {
        patents = extractedPatents;
      }
    }

    // If we still don't have enough patents, look for patent indicators
    if (patents.length < 2) {
      const contentLines = content.split("\n");

      // Look for lines that might contain patents
      for (const line of contentLines) {
        if (
          (line.includes("Patent") ||
            line.includes("US") ||
            line.includes("USPTO")) &&
          !patents.some((p) => p.title === line || p.description === line)
        ) {
          // Extract patent number if available
          const patentNumberMatch = line.match(
            /US\d+|Patent No:?\s*[A-Z0-9]+/i
          );
          const patentNumber = patentNumberMatch ? patentNumberMatch[0] : "";

          // Create a patent entry
          patents.push({
            title: line.length > 50 ? line.substring(0, 50) + "..." : line,
            description: line,
            patentNumber: patentNumber,
          });

          // Stop if we have enough patents
          if (patents.length >= 2) {
            break;
          }
        }
      }

      // If we still don't have enough patents, create a generic one
      if (patents.length < 2 && patents.length > 0) {
        // Create a generic patent based on the existing one
        const existingPatent = patents[0];
        const title = existingPatent.title.includes("System")
          ? "Method for Efficient Data Processing"
          : "System for Efficient Data Processing";

        patents.push({
          title: title,
          description:
            "A method/system for efficient data processing in distributed environments",
          patentNumber: "US87654321, 2021",
        });
      }
    }
  }

  return patents;
}
