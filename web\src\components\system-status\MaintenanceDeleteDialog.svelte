<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import type { MaintenanceEvent } from '$lib/types';
  import { enhance } from '$app/forms';

  // Props
  export let open: boolean;
  export let deleteForm: any;
  export let selectedEvent: MaintenanceEvent | null;
  export let onClose: () => void;
  export let onSubmit: () => void;
</script>

<Dialog.Root bind:open>
  <Dialog.Overlay />
  <Dialog.Content>
    <Dialog.Header>
      <Dialog.Title>Delete Maintenance Event</Dialog.Title>
      <Dialog.Description>
        Are you sure you want to delete this maintenance event? This action cannot be undone.
      </Dialog.Description>
    </Dialog.Header>

    {#if selectedEvent}
      <form
        method="POST"
        action="?/delete"
        id="delete-maintenance-form"
        use:enhance
        on:submit|preventDefault={onSubmit}>
        <input type="hidden" name="id" bind:value={$deleteForm.id} />

        <div class="py-4">
          <p class="font-medium">{selectedEvent.title}</p>
          <p class="text-muted-foreground text-sm">{selectedEvent.description}</p>
        </div>

        <Dialog.Footer>
          <Button type="button" variant="outline" onclick={onClose}>Cancel</Button>
          <Button type="submit" variant="destructive">Delete</Button>
        </Dialog.Footer>
      </form>
    {/if}
  </Dialog.Content>
</Dialog.Root>
