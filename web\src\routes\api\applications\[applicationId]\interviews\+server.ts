/**
 * API endpoints for managing interview stages for an application
 */

import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';

/**
 * Get all interview stages for an application
 * GET /api/applications/:applicationId/interviews
 */
export async function GET({ params, locals }) {
  console.log('GET interview stages for application:', params.applicationId);
  console.log('User in locals:', locals.user ? `ID: ${locals.user.id}` : 'Not authenticated');

  // In development mode, we'll allow access without authentication
  const isDev = process.env.NODE_ENV === 'development';

  // Ensure user is authenticated (except in dev mode)
  if (!locals.user && !isDev) {
    console.log('Unauthorized access attempt - no user in locals');
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { applicationId } = params;

  try {
    // In dev mode, we'll skip the user verification
    if (!isDev && locals.user) {
      // Verify the application belongs to the user
      const application = await prisma.application.findUnique({
        where: {
          id: applicationId,
          userId: locals.user.id,
        },
      });

      if (!application) {
        console.log('Application not found or does not belong to user');
        return json({ error: 'Application not found' }, { status: 404 });
      }
    }

    // Get all interview stages for the application
    const interviewStages = await prisma.interviewStage.findMany({
      where: {
        applicationId,
      },
      include: {
        questions: true,
      },
      orderBy: {
        stageDate: 'desc',
      },
    });

    console.log(`Found ${interviewStages.length} interview stages`);

    // If no interview stages found, return an empty array instead of 404
    return json({ interviewStages });
  } catch (error) {
    console.error('Error fetching interview stages:', error);
    return json({ error: 'Failed to fetch interview stages' }, { status: 500 });
  }
}

/**
 * Create a new interview stage for an application
 * POST /api/applications/:applicationId/interviews
 */
export async function POST({ request, params, locals }) {
  console.log('POST new interview stage for application:', params.applicationId);
  console.log('User in locals:', locals.user ? `ID: ${locals.user.id}` : 'Not authenticated');

  // In development mode, we'll allow access without authentication
  const isDev = process.env.NODE_ENV === 'development';

  // Ensure user is authenticated (except in dev mode)
  if (!locals.user && !isDev) {
    console.log('Unauthorized access attempt - no user in locals');
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { applicationId } = params;

  try {
    // In dev mode, we'll skip the user verification
    if (!isDev && locals.user) {
      // Verify the application belongs to the user
      const application = await prisma.application.findUnique({
        where: {
          id: applicationId,
          userId: locals.user.id,
        },
      });

      if (!application) {
        console.log('Application not found or does not belong to user');
        return json({ error: 'Application not found' }, { status: 404 });
      }
    }

    // Parse the request body
    const body = await request.json();
    const { stageName, stageDate, outcome, feedback, interviewers, duration, notes } = body;

    console.log('Creating interview stage with data:', {
      stageName,
      stageDate,
      outcome,
      feedback,
      interviewers,
      duration,
    });

    // Validate required fields
    if (!stageName || !stageDate) {
      return json({ error: 'Stage name and date are required' }, { status: 400 });
    }

    // Check if the application exists
    let application = await prisma.application.findUnique({
      where: {
        id: applicationId,
      },
    });

    if (!application && isDev) {
      // In development mode, create a mock application if it doesn't exist
      console.log('Creating mock application for development mode');
      try {
        const mockApplication = await prisma.application.create({
          data: {
            id: applicationId,
            company: 'Mock Company',
            position: 'Mock Position',
            status: 'Applied',
            appliedDate: new Date(),
            resumeUploaded: false,
            userId: locals.user?.id || 'dev-user-123',
          },
        });
        console.log('Created mock application:', mockApplication);
        application = mockApplication;
      } catch (error) {
        console.error('Error creating mock application:', error);
        // Return more detailed error information in development mode
        if (isDev) {
          return json(
            {
              error: 'Failed to create mock application',
              details: error.message,
              code: error.code,
            },
            { status: 500 }
          );
        } else {
          return json({ error: 'Failed to create mock application' }, { status: 500 });
        }
      }
    } else if (!application) {
      console.error('Application not found:', applicationId);
      return json({ error: 'Application not found' }, { status: 404 });
    }

    // Create the interview stage
    const interviewStage = await prisma.interviewStage.create({
      data: {
        applicationId,
        stageName,
        stageDate: new Date(stageDate),
        outcome,
        feedback,
        interviewers,
        duration,
        notes,
      },
    });

    console.log('Interview stage created successfully:', interviewStage.id);
    return json({ interviewStage }, { status: 201 });
  } catch (error) {
    console.error('Error creating interview stage:', error);
    // Return more detailed error information in development mode
    if (isDev) {
      return json(
        {
          error: 'Failed to create interview stage',
          details: error.message,
          code: error.code,
        },
        { status: 500 }
      );
    } else {
      return json({ error: 'Failed to create interview stage' }, { status: 500 });
    }
  }
}
