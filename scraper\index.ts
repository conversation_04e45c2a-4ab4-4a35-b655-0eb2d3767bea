// scraper/index.ts
// Main entry point for the scraper service

import { logger } from "./utils/logger.js";
import { executeCommandStreaming } from "./utils/executeCommand.js";

class ScraperService {
  private isRunning = false;
  private currentJob: string | null = null;

  constructor() {
    // Handle graceful shutdown
    process.on("SIGINT", () => this.shutdown());
    process.on("SIGTERM", () => this.shutdown());
  }

  async start(): Promise<void> {
    logger.info("🚀 Starting scraper service...");
    this.isRunning = true;

    // Run the scraping cycle continuously
    while (this.isRunning) {
      try {
        // Run parallel job scraper
        await this.runJob("parallel-scraper", ["npm", "run", "parallel"]);

        if (!this.isRunning) break;

        // Wait before next full cycle
        logger.info("⏳ Waiting 6 hours before next scraping cycle...");
        await this.delay(21600000); // 6 hours
      } catch (error) {
        logger.error("❌ Error in scraping cycle:", error);

        // Wait before retrying
        await this.delay(600000); // 10 minutes
      }
    }
  }

  private async runJob(jobName: string, command: string[]): Promise<void> {
    this.currentJob = jobName;
    logger.info(`🔄 Starting ${jobName}...`);

    try {
      await executeCommandStreaming(
        process.cwd(),
        command[0],
        command.slice(1),
        jobName
      );
      logger.info(`✅ ${jobName} completed successfully`);
    } catch (error) {
      logger.error(`❌ ${jobName} failed:`, error);
      throw error;
    } finally {
      this.currentJob = null;
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private async shutdown(): Promise<void> {
    logger.info("🛑 Shutting down scraper service...");
    this.isRunning = false;

    if (this.currentJob) {
      logger.info(
        `⏳ Waiting for current job (${this.currentJob}) to complete...`
      );
      // Give current job up to 5 minutes to complete
      let waitTime = 0;
      while (this.currentJob && waitTime < 300000) {
        await this.delay(5000);
        waitTime += 5000;
      }
    }

    logger.info("✅ Scraper service shut down gracefully");
    process.exit(0);
  }
}

// Start the service
const scraperService = new ScraperService();
scraperService.start().catch((error) => {
  logger.error("❌ Failed to start scraper service:", error);
  process.exit(1);
});
