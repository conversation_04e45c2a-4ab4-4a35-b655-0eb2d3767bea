// @ts-nocheck
// File: src/routes/dashboard/resume/+page.server.ts
import type { PageServerLoad } from './$types.js';
import { redirect } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth.js';

// Using the shared Prisma client from $lib/server/prisma

export const load = async ({ locals }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  // Query resumes directly associated with the user's ID or team ID
  const resumes = await prisma.resume.findMany({
    where: {
      OR: [
        {
          userId: user.id, // Directly fetch resumes associated with the user's ID
        },
        {
          profile: {
            team: {
              members: {
                some: {
                  userId: user.id, // Fetch resumes associated with a team's profiles
                },
              },
            },
          },
        },
      ],
    },
    include: {
      document: true, // Include the document to get label, fileUrl, etc.
      profile: {
        include: {
          jobSearches: true,
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  // Query profiles associated with the user or team
  const profiles = await prisma.profile.findMany({
    where: {
      OR: [
        { userId: user.id }, // Fetch profiles directly associated with the user
        {
          team: {
            members: {
              some: { userId: user.id }, // Fetch profiles associated with teams the user is a member of
            },
          },
        },
      ],
    },
  });

  return {
    resumes,
    profiles,
  };
};
