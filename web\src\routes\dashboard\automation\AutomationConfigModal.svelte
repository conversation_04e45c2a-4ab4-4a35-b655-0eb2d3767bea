<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label';
  import * as Dialog from '$lib/components/ui/dialog';
  import * as Select from '$lib/components/ui/select';
  import { Checkbox } from '$lib/components/ui/checkbox';
  import { Badge } from '$lib/components/ui/badge';
  import { Progress } from '$lib/components/ui/progress/index.js';
  import { getProfileData } from '$lib/utils/profile';
  import { checkAutomationEligibility } from '$lib/utils/profileHelpers';
  import { AlertTriangle, CheckCircle, Plus, X } from 'lucide-svelte';

  const { 
    open, 
    profiles, 
    onClose, 
    onSubmit 
  } = $props<{
    open: boolean;
    profiles: any[];
    onClose: () => void;
    onSubmit: (config: any) => void;
  }>();

  // Form state
  let selectedProfileId = $state('');
  let keywords = $state('');
  let location = $state('');
  let maxJobsToApply = $state(10);
  let minMatchScore = $state(70);
  let autoApplyEnabled = $state(false);
  let salaryMin = $state('');
  let salaryMax = $state('');
  let experienceLevelMin = $state('');
  let experienceLevelMax = $state('');
  let jobTypes = $state<string[]>([]);
  let remotePreference = $state('any');
  let companySizePreference = $state<string[]>([]);
  let excludeCompanies = $state<string[]>([]);
  let preferredCompanies = $state<string[]>([]);

  // UI state
  let isSubmitting = $state(false);
  let newExcludeCompany = $state('');
  let newPreferredCompany = $state('');

  // Job type options
  const jobTypeOptions = [
    { value: 'full-time', label: 'Full-time' },
    { value: 'part-time', label: 'Part-time' },
    { value: 'contract', label: 'Contract' },
    { value: 'temporary', label: 'Temporary' },
    { value: 'internship', label: 'Internship' }
  ];

  // Remote preference options
  const remoteOptions = [
    { value: 'any', label: 'Any' },
    { value: 'remote', label: 'Remote Only' },
    { value: 'hybrid', label: 'Hybrid' },
    { value: 'onsite', label: 'On-site Only' }
  ];

  // Company size options
  const companySizeOptions = [
    { value: 'startup', label: 'Startup (1-10)' },
    { value: 'small', label: 'Small (11-50)' },
    { value: 'medium', label: 'Medium (51-200)' },
    { value: 'large', label: 'Large (201-1000)' },
    { value: 'enterprise', label: 'Enterprise (1000+)' }
  ];

  // Get selected profile and eligibility
  const selectedProfile = $derived(() => {
    return profiles.find(p => p.id === selectedProfileId);
  });

  const profileEligibility = $derived(() => {
    if (!selectedProfile()) return null;
    return checkAutomationEligibility(selectedProfile());
  });

  // Reset form
  function resetForm() {
    selectedProfileId = '';
    keywords = '';
    location = '';
    maxJobsToApply = 10;
    minMatchScore = 70;
    autoApplyEnabled = false;
    salaryMin = '';
    salaryMax = '';
    experienceLevelMin = '';
    experienceLevelMax = '';
    jobTypes = [];
    remotePreference = 'any';
    companySizePreference = [];
    excludeCompanies = [];
    preferredCompanies = [];
    newExcludeCompany = '';
    newPreferredCompany = '';
  }

  // Handle form submission
  async function handleSubmit() {
    if (!selectedProfileId) return;
    
    const eligibility = profileEligibility();
    if (!eligibility?.isEligible) return;

    isSubmitting = true;

    try {
      const config = {
        profileId: selectedProfileId,
        keywords,
        location,
        maxJobsToApply,
        minMatchScore,
        autoApplyEnabled,
        salaryMin: salaryMin ? parseInt(salaryMin) : null,
        salaryMax: salaryMax ? parseInt(salaryMax) : null,
        experienceLevelMin: experienceLevelMin ? parseInt(experienceLevelMin) : null,
        experienceLevelMax: experienceLevelMax ? parseInt(experienceLevelMax) : null,
        jobTypes,
        remotePreference,
        companySizePreference,
        excludeCompanies,
        preferredCompanies
      };

      await onSubmit(config);
      resetForm();
      onClose();
    } catch (error) {
      console.error('Error submitting automation config:', error);
    } finally {
      isSubmitting = false;
    }
  }

  // Handle adding companies to lists
  function addExcludeCompany() {
    if (newExcludeCompany.trim()) {
      excludeCompanies = [...excludeCompanies, newExcludeCompany.trim()];
      newExcludeCompany = '';
    }
  }

  function addPreferredCompany() {
    if (newPreferredCompany.trim()) {
      preferredCompanies = [...preferredCompanies, newPreferredCompany.trim()];
      newPreferredCompany = '';
    }
  }

  function removeExcludeCompany(index: number) {
    excludeCompanies = excludeCompanies.filter((_, i) => i !== index);
  }

  function removePreferredCompany(index: number) {
    preferredCompanies = preferredCompanies.filter((_, i) => i !== index);
  }

  // Handle job type selection
  function toggleJobType(jobType: string) {
    if (jobTypes.includes(jobType)) {
      jobTypes = jobTypes.filter(t => t !== jobType);
    } else {
      jobTypes = [...jobTypes, jobType];
    }
  }

  // Handle company size selection
  function toggleCompanySize(size: string) {
    if (companySizePreference.includes(size)) {
      companySizePreference = companySizePreference.filter(s => s !== size);
    } else {
      companySizePreference = [...companySizePreference, size];
    }
  }
</script>

<Dialog.Root bind:open>
  <Dialog.Overlay />
  <Dialog.Content class="max-w-4xl max-h-[90vh] overflow-y-auto">
    <Dialog.Header>
      <Dialog.Title>Configure Automation Run</Dialog.Title>
      <Dialog.Description>
        Set up detailed automation specifications for intelligent job matching and application.
      </Dialog.Description>
    </Dialog.Header>

    <div class="grid gap-6 py-4">
      <!-- Profile Selection -->
      <div class="grid gap-2">
        <Label for="profile">Profile *</Label>
        <Select.Root
          type="single"
          value={selectedProfileId}
          onValueChange={(value) => {
            selectedProfileId = value || '';
          }}>
          <Select.Trigger>
            <Select.Value placeholder="Select a profile" />
          </Select.Trigger>
          <Select.Content>
            {#each profiles as profile (profile.id)}
              <Select.Item value={profile.id}>
                {getProfileData(profile).fullName || 'Unnamed Profile'}
              </Select.Item>
            {/each}
          </Select.Content>
        </Select.Root>
        
        {#if selectedProfile()}
          <Button
            variant="ghost"
            size="sm"
            onclick={() => (window.location.href = '/dashboard/settings/profile')}
            class="text-xs text-blue-500 hover:text-blue-400 self-start">
            <Plus class="mr-1 h-3 w-3" />
            Manage Profiles
          </Button>
        {/if}
      </div>

      <!-- Profile Eligibility Check -->
      {#if profileEligibility()}
        <div class="rounded-lg border p-4">
          <div class="flex items-center gap-2 mb-2">
            {#if profileEligibility()?.isEligible}
              <CheckCircle class="h-5 w-5 text-green-500" />
              <span class="font-medium text-green-700">Profile Eligible for Automation</span>
            {:else}
              <AlertTriangle class="h-5 w-5 text-orange-500" />
              <span class="font-medium text-orange-700">Profile Needs Completion</span>
            {/if}
          </div>
          
          <div class="mb-3">
            <div class="flex items-center justify-between text-sm mb-1">
              <span>Profile Completion</span>
              <span>{profileEligibility()?.completionPercentage}%</span>
            </div>
            <Progress value={profileEligibility()?.completionPercentage} max={100} />
          </div>

          {#if !profileEligibility()?.isEligible}
            <div class="space-y-1">
              <p class="text-sm font-medium text-gray-700">Missing Requirements:</p>
              {#each profileEligibility()?.missingRequirements || [] as requirement}
                <div class="flex items-center gap-2 text-sm text-gray-600">
                  <X class="h-3 w-3 text-red-500" />
                  {requirement}
                </div>
              {/each}
            </div>
          {/if}
        </div>
      {/if}

      <!-- Basic Search Criteria -->
      <div class="grid grid-cols-2 gap-4">
        <div class="grid gap-2">
          <Label for="keywords">Keywords</Label>
          <Input
            id="keywords"
            bind:value={keywords}
            placeholder="e.g. Software Engineer, Developer" />
        </div>
        <div class="grid gap-2">
          <Label for="location">Location</Label>
          <Input
            id="location"
            bind:value={location}
            placeholder="e.g. New York, Remote" />
        </div>
      </div>
    </div>

    <Dialog.Footer>
      <Button variant="outline" onclick={onClose}>Cancel</Button>
      <Button
        variant="default"
        onclick={handleSubmit}
        disabled={!selectedProfileId || !profileEligibility()?.isEligible || isSubmitting}>
        {#if isSubmitting}
          Creating...
        {:else}
          Continue to Advanced Settings
        {/if}
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
