<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Trash2 } from 'lucide-svelte';
  import { resetFeatureUsage } from '$lib/utils/feature-tracker';
  import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
  } from '$lib/components/ui/dialog';
  import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
  import { AlertCircle, CheckCircle } from 'lucide-svelte';

  export let onReset: () => void;
  export let featureId: string | undefined = undefined;
  export let limitId: string | undefined = undefined;
  export let featureName: string | undefined = undefined;
  export let limitName: string | undefined = undefined;
  
  let loading = false;
  let success = false;
  let error: string | null = null;
  let dialogOpen = false;
  
  // Get the reset message based on what's being reset
  function getResetMessage(): string {
    if (featureId && limitId) {
      return `Are you sure you want to reset usage for ${featureName || featureId} - ${limitName || limitId}?`;
    } else if (featureId) {
      return `Are you sure you want to reset all usage for ${featureName || featureId}?`;
    } else {
      return 'Are you sure you want to reset all feature usage?';
    }
  }
  
  // Handle reset button click
  async function handleReset() {
    loading = true;
    error = null;
    success = false;
    
    try {
      const result = await resetFeatureUsage(featureId, limitId);
      
      if (result.success) {
        success = true;
        // Call the callback to refresh the usage data
        onReset();
        
        // Close the dialog after a short delay
        setTimeout(() => {
          dialogOpen = false;
          success = false;
        }, 1500);
      } else {
        error = result.error || 'Failed to reset feature usage';
      }
    } catch (err) {
      console.error('Error resetting feature usage:', err);
      error = err.message || 'An error occurred while resetting feature usage';
    } finally {
      loading = false;
    }
  }
  
  // Handle dialog state change
  function handleDialogChange(event: CustomEvent<boolean>) {
    dialogOpen = event.detail;
    if (!dialogOpen) {
      // Reset state when dialog is closed
      error = null;
      success = false;
    }
  }
</script>

<Dialog bind:open={dialogOpen} on:change={handleDialogChange}>
  <DialogTrigger asChild let:builder>
    <Button 
      variant="outline" 
      size="sm" 
      class="text-destructive hover:bg-destructive hover:text-destructive-foreground"
      builders={[builder]}
    >
      <Trash2 class="mr-2 h-4 w-4" />
      Reset Usage
    </Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Reset Feature Usage</DialogTitle>
      <DialogDescription>
        {getResetMessage()}
        This action cannot be undone.
      </DialogDescription>
    </DialogHeader>
    
    {#if error}
      <Alert variant="destructive">
        <AlertCircle class="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    {/if}
    
    {#if success}
      <Alert>
        <CheckCircle class="h-4 w-4 text-green-500" />
        <AlertTitle>Success</AlertTitle>
        <AlertDescription>Feature usage reset successfully</AlertDescription>
      </Alert>
    {/if}
    
    <DialogFooter>
      <Button variant="outline" on:click={() => dialogOpen = false} disabled={loading}>
        Cancel
      </Button>
      <Button 
        variant="destructive" 
        on:click={handleReset} 
        disabled={loading}
      >
        {loading ? 'Resetting...' : 'Reset'}
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
