<script lang="ts">
  import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
  } from '$lib/components/ui/accordion';
  import { Badge } from '$lib/components/ui/badge';
  import FeatureCard from './FeatureCard.svelte';
  import type { FeatureWithDetailedUsage } from '$lib/services/feature-service';
  import { formatCategoryName } from './feature-utils';

  export let categories: string[] = [];
  export let featuresByCategory: Record<string, FeatureWithDetailedUsage[]> = {};
  export let onReset: () => void;
</script>

<Accordion type="single" class="w-full">
  {#each categories as category}
    <AccordionItem value={category}>
      <AccordionTrigger>
        <div class="flex items-center gap-2">
          <span>{formatCategoryName(category)}</span>
          <Badge variant="outline">{featuresByCategory[category].length}</Badge>
        </div>
      </AccordionTrigger>
      <AccordionContent>
        <div class="grid grid-cols-1 gap-4 pt-4 md:grid-cols-2">
          {#each featuresByCategory[category] as feature}
            <FeatureCard {feature} {onReset} />
          {/each}
        </div>
      </AccordionContent>
    </AccordionItem>
  {/each}
</Accordion>
