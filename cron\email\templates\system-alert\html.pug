extends ../layouts/base.pug

block content
  - var alertColor = (severity === 'critical') ? '#dc3545' : (severity === 'warning') ? '#ffc107' : (severity === 'info') ? '#0066cc' : '#6c757d'
  - var severityIcon = (severity === 'critical') ? '🔴' : (severity === 'warning') ? '🟠' : (severity === 'info') ? '🔵' : '⚪'
  - var baseUrl = appUrl || 'https://hirli.co'
  - var systemStatusUrl = baseUrl + '/system-status'

  h1(style=`color: ${alertColor};`) #{severityIcon} #{alertTitle || 'System Alert'}

  p.alert-message(style="font-size: 18px; margin-bottom: 20px; padding: 16px; background-color: #f8f9fa; border-left: 4px solid " + alertColor + "; border-radius: 4px;") #{alertMessage || 'An important system alert has been triggered.'}

  .card
    .card-header Alert Details
    table(style="width: 100%; table-layout: fixed; border-collapse: collapse; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);")
      colgroup
        col(style="width: 30%;")
        col(style="width: 70%;")
      thead
        tr(style="background-color: #f8f9fa;")
          th(style="padding: 12px 16px; text-align: left; border-bottom: 2px solid #dee2e6; font-weight: 600;") Property
          th(style="padding: 12px 16px; text-align: left; border-bottom: 2px solid #dee2e6; font-weight: 600;") Value
      tbody
        tr(style="border-bottom: 1px solid #eee;")
          td(style="padding: 12px 16px; font-weight: 500;") Timestamp
          td(style="padding: 12px 16px;") #{timestamp || new Date().toISOString()}
        tr(style="border-bottom: 1px solid #eee;")
          td(style="padding: 12px 16px; font-weight: 500;") Severity
          td(style="padding: 12px 16px; font-weight: bold; color: " + alertColor + ";") #{severityIcon} #{severity || 'warning'}
        tr(style="border-bottom: 1px solid #eee;")
          td(style="padding: 12px 16px; font-weight: 500;") Component
          td(style="padding: 12px 16px;") #{component || 'System'}
        if errorCode
          tr(style="border-bottom: 1px solid #eee;")
            td(style="padding: 12px 16px; font-weight: 500;") Error Code
            td(style="padding: 12px 16px;") #{errorCode}
        if affectedServices && affectedServices.length
          tr(style="border-bottom: 1px solid #eee;")
            td(style="padding: 12px 16px; font-weight: 500;") Affected Services
            td(style="padding: 12px 16px; word-break: break-all;") #{affectedServices.join(', ')}
        if jobType
          tr(style="border-bottom: 1px solid #eee;")
            td(style="padding: 12px 16px; font-weight: 500;") Job Type
            td(style="padding: 12px 16px;") #{jobType}
        if exitCode
          tr(style="border-bottom: 1px solid #eee;")
            td(style="padding: 12px 16px; font-weight: 500;") Exit Code
            td(style="padding: 12px 16px;") #{exitCode}

  if alertDetails || (details && Object.keys(details).length > 0)
    .card
      .card-header Technical Details
      if alertDetails
        pre(style="white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 14px; background-color: #f8f9fa; padding: 16px; border-radius: 8px; overflow: auto; border-left: 4px solid " + alertColor + ";") #{alertDetails}
      else if details
        table(style="width: 100%; table-layout: fixed; border-collapse: collapse; margin-top: 10px;")
          each value, key in details
            tr(style="border-bottom: 1px solid #eee;")
              td(style="padding: 8px 16px; font-weight: 500; width: 30%;") #{key}
              td(style="padding: 8px 16px; width: 70%; word-break: break-all;") #{value}

  if systemInfo
    .card
      .card-header System Information
      table(style="width: 100%; table-layout: fixed; border-collapse: collapse; margin-top: 10px;")
        if systemInfo.memoryUsage
          tr(style="border-bottom: 1px solid #eee;")
            td(style="padding: 8px 16px; font-weight: 500; width: 30%;") Memory Usage
            td(style="padding: 8px 16px; width: 70%;") #{systemInfo.memoryUsage}
        if systemInfo.cpuUsage
          tr(style="border-bottom: 1px solid #eee;")
            td(style="padding: 8px 16px; font-weight: 500; width: 30%;") CPU Usage
            td(style="padding: 8px 16px; width: 70%;") #{systemInfo.cpuUsage}
        if systemInfo.nodeVersion
          tr(style="border-bottom: 1px solid #eee;")
            td(style="padding: 8px 16px; font-weight: 500; width: 30%;") Node Version
            td(style="padding: 8px 16px; width: 70%;") #{systemInfo.nodeVersion}

  if recommendedActions && recommendedActions.length
    .card
      .card-header Recommended Actions
      ul(style="list-style-type: none; padding-left: 0;")
        each action in recommendedActions
          li(style="padding: 12px 16px; border-bottom: 1px solid #eee; margin-bottom: 8px; background-color: #f8f9fa; border-radius: 4px;")
            span(style="display: inline-block; margin-right: 8px; color: " + alertColor + ";") ▶
            span #{action}
  else if actionRequired
    .card
      .card-header Recommended Actions
      div(style="padding: 12px 16px; background-color: #f8f9fa; border-radius: 4px;")
        span(style="display: inline-block; margin-right: 8px; color: " + alertColor + ";") ▶
        span #{actionRequired}

  .card(style="margin-top: 24px;")
    .card-header Quick Links
    div(style="padding: 16px;")
      p(style="margin-bottom: 12px;")
        a.button(href=systemStatusUrl style=`background-color: ${alertColor}; display: inline-block; padding: 10px 20px; color: white; text-decoration: none; border-radius: 4px; font-weight: 500;`) View System Status Dashboard
      p(style="margin-bottom: 12px;")
        a(href=systemStatusUrl + '/history' style="color: #0066cc; text-decoration: underline;") View Incident History
      if severity === 'critical' || severity === 'warning'
        p(style="margin-bottom: 12px;")
          a(href=baseUrl + '/admin/maintenance' style="color: #0066cc; text-decoration: underline;") Manage Maintenance Events

  if error || errorMessage || errorStack
    .card(style="margin-top: 24px;")
      .card-header Error Details
      div(style="padding: 16px;")
        if error || errorMessage
          p(style="margin-bottom: 12px; font-weight: bold; color: #dc3545;") #{error || errorMessage}
        if errorStack
          pre(style="white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 14px; background-color: #f8f9fa; padding: 16px; border-radius: 8px; overflow: auto; border-left: 4px solid #dc3545; max-height: 300px;") #{errorStack}

  if severity === 'critical'
    p.highlight(style="color: #dc3545; font-weight: bold; margin-top: 24px; padding: 12px; background-color: #f8d7da; border-radius: 4px;") This is a critical alert that requires immediate attention.

  p(style="margin-top: 24px; color: #6c757d;") This is an automated alert. Please take appropriate action.

  p Regards,
  p.signature The #{appName || 'Hirli'} Team
