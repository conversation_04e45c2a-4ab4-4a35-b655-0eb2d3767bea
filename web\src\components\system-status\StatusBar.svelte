<script lang="ts">
  import { cn } from '$lib/utils.js';
  import type { StatusTagType } from '../../routes/system-status/types';

  // Define segment type
  type ProgressSegment = {
    color: string;
    width: string;
    pulse?: boolean;
  };

  // Props
  const {
    progress = 0,
    startTime,
    endTime,
    status,
    showTimes = true,
    className = '',
  } = $props<{
    progress?: number;
    startTime: string | Date;
    endTime: string | Date;
    status: StatusTagType;
    showTimes?: boolean;
    className?: string;
  }>();

  // Calculate progress if not provided
  let calculatedProgress = $derived(() => {
    if (progress > 0) return progress;
    
    // Handle different status types
    if (status === 'completed' || status === 'resolved') return 100;
    if (status === 'cancelled') return 100;
    if (status === 'scheduled' && new Date(startTime) > new Date()) return 0;
    
    const start = new Date(startTime).getTime();
    const end = new Date(endTime).getTime();
    const now = Date.now();
    
    if (now <= start) return 0;
    if (now >= end) {
      // If we're past the end time but status isn't completed, show 90%
      if (status !== 'completed' && status !== 'resolved') return 90;
      return 100;
    }
    
    return Math.round(((now - start) / (end - start)) * 100);
  });

  // Format date for display
  function formatTime(date: string | Date): string {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  }

  // Get status color
  function getStatusColor(status: StatusTagType): string {
    switch (status) {
      case 'resolved':
      case 'completed':
        return 'bg-green-500';
      case 'monitoring':
      case 'in-progress':
        return 'bg-blue-500';
      case 'investigating':
      case 'scheduled':
        return 'bg-yellow-500';
      case 'identified':
        return 'bg-orange-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  }

  // Get progress bar segments
  function getProgressSegments(): ProgressSegment[] {
    const progressValue = calculatedProgress as number;
    
    // For completed or cancelled, show full bar
    if (status === 'completed' || status === 'cancelled') {
      return [{ color: getStatusColor(status), width: '100%' }];
    }
    
    // For in-progress, show progress with different colors
    if (status === 'in-progress' || status === 'monitoring') {
      return [
        { color: getStatusColor(status), width: `${progressValue}%` },
        { color: 'bg-gray-200', width: `${100 - progressValue}%` }
      ];
    }
    
    // For scheduled, show a pulsing indicator
    if (status === 'scheduled') {
      return [
        { color: 'bg-gray-200', width: '100%' },
        { color: getStatusColor(status), width: '10%', pulse: true }
      ];
    }
    
    // Default case
    return [
      { color: getStatusColor(status), width: `${progressValue}%` },
      { color: 'bg-gray-200', width: `${100 - progressValue}%` }
    ];
  }
  
  const segments: ProgressSegment[] = $derived(() => getProgressSegments());
</script>

<div class={cn("w-full space-y-1", className)}>
  <div class="flex h-2 w-full overflow-hidden rounded-full bg-gray-200">
    {#each segments as segment}
      <div 
        class={cn(
          segment.color, 
          "h-full transition-all duration-500 ease-in-out",
          segment.pulse && "animate-pulse"
        )} 
        style="width: {segment.width}">
      </div>
    {/each}
  </div>
  
  {#if showTimes}
    <div class="flex justify-between text-xs text-gray-500">
      <span>{formatTime(startTime)}</span>
      <span>{formatTime(endTime)}</span>
    </div>
  {/if}
</div>
