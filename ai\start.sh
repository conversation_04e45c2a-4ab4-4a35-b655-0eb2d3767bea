#!/bin/sh

# Get the model name from environment variable or use default
MODEL_NAME=${LLM_MODEL:-mistral}

echo "Starting Ollama service..."
# Start Ollama in the background
ollama serve &
# Store the Ollama PID
OLLAMA_PID=$!

echo "Waiting for <PERSON>llama to initialize..."
# Wait for <PERSON>lla<PERSON> to start (with timeout)
MAX_RETRIES=30
RETRY_COUNT=0
until curl -s http://localhost:11434/api/tags > /dev/null 2>&1 || [ $RETRY_COUNT -eq $MAX_RETRIES ]; do
  echo "Waiting for Ollama API to become available... ($RETRY_COUNT/$MAX_RETRIES)"
  sleep 2
  RETRY_COUNT=$((RETRY_COUNT+1))
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
  echo "ERROR: Ollama failed to start within the timeout period"
  exit 1
fi

echo "Ollama started successfully. Downloading $MODEL_NAME model..."
# Pull the model with progress display
ollama pull $MODEL_NAME

# Verify model was downloaded successfully
if ! ollama list | grep -q $MODEL_NAME; then
  echo "ERROR: Failed to download $MODEL_NAME model"
  exit 1
fi

echo "$MODEL_NAME model downloaded successfully. Starting AI service..."
# Start the AI service
node dist/index.js
