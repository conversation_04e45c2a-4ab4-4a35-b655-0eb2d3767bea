// workers/utils/healthReporter.ts
// Utility for services to report their health status to the central database

import { Logger } from "./logger";

export interface HealthMetrics {
  cpuUsage?: number;
  memoryUsage?: number;
  activeWorkers?: number;
  queueSize?: number;
  errorRate?: number;
  uptime?: number;
  responseTime?: number;
  [key: string]: any;
}

export type HealthStatus =
  | "operational"
  | "degraded"
  | "outage"
  | "maintenance";

export interface HealthReport {
  service: string;
  status: HealthStatus;
  responseTime?: number;
  details?: HealthMetrics;
  timestamp?: string;
}

export class HealthReporter {
  private readonly serviceName: string;
  private readonly webApiUrl: string;
  private readonly reportInterval: number;
  private intervalId: NodeJS.Timeout | null = null;
  private lastReportTime = 0;
  private consecutiveFailures = 0;
  private readonly MAX_FAILURES = 3;

  constructor(
    serviceName: string,
    webApiUrl: string = process.env.WEB_API_URL ??
      "https://auto-apply-web.onrender.com",
    reportInterval: number = 60000 // 1 minute default
  ) {
    this.serviceName = serviceName;
    this.webApiUrl = webApiUrl.replace(/\/$/, ""); // Remove trailing slash
    this.reportInterval = reportInterval;
  }

  /**
   * Report current health status to the central database
   */
  async reportHealth(
    status: HealthStatus,
    metrics?: HealthMetrics
  ): Promise<boolean> {
    try {
      const healthReport: HealthReport = {
        service: this.serviceName,
        status,
        details: metrics,
        timestamp: new Date().toISOString(),
      };

      const response = await fetch(`${this.webApiUrl}/api/health/report`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(healthReport),
      });

      if (response.ok) {
        const result = await response.json();
        this.consecutiveFailures = 0;
        this.lastReportTime = Date.now();

        Logger.debug(
          `✅ Health reported for ${this.serviceName}: ${status} -> ${result.mappedService}`
        );
        return true;
      } else {
        const errorText = await response.text();
        Logger.error(
          `❌ Failed to report health for ${this.serviceName}: ${response.status} ${errorText}`
        );
        this.consecutiveFailures++;
        return false;
      }
    } catch (error) {
      this.consecutiveFailures++;
      Logger.error(`❌ Error reporting health for ${this.serviceName}:`, error);
      return false;
    }
  }

  /**
   * Start automatic health reporting
   */
  startReporting(
    getHealthStatus: () => Promise<{
      status: HealthStatus;
      metrics?: HealthMetrics;
    }>
  ): void {
    if (this.intervalId) {
      Logger.warn(`Health reporting already started for ${this.serviceName}`);
      return;
    }

    Logger.info(
      `🏥 Starting health reporting for ${this.serviceName} (interval: ${this.reportInterval}ms)`
    );

    this.intervalId = setInterval(async () => {
      try {
        const { status, metrics } = await getHealthStatus();
        await this.reportHealth(status, metrics);
      } catch (error) {
        Logger.error(
          `Error getting health status for ${this.serviceName}:`,
          error
        );
        await this.reportHealth("outage", { error: "Health check failed" });
      }
    }, this.reportInterval);

    // Report initial status immediately
    getHealthStatus()
      .then(({ status, metrics }) => this.reportHealth(status, metrics))
      .catch((error) => {
        Logger.error(
          `Error getting initial health status for ${this.serviceName}:`,
          error
        );
        this.reportHealth("outage", { error: "Initial health check failed" });
      });
  }

  /**
   * Stop automatic health reporting
   */
  stopReporting(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      Logger.info(`🛑 Stopped health reporting for ${this.serviceName}`);
    }
  }

  /**
   * Report service shutdown
   */
  async reportShutdown(): Promise<void> {
    Logger.info(`📴 Reporting shutdown for ${this.serviceName}`);
    await this.reportHealth("maintenance", {
      message: "Service shutting down",
      shutdownTime: new Date().toISOString(),
    });
  }

  /**
   * Get health status based on common metrics
   */
  static determineHealthStatus(metrics: HealthMetrics): HealthStatus {
    const {
      cpuUsage = 0,
      memoryUsage = 0,
      errorRate = 0,
      responseTime = 0,
    } = metrics;

    // Critical thresholds
    if (
      cpuUsage > 95 ||
      memoryUsage > 95 ||
      errorRate > 50 ||
      responseTime > 30000
    ) {
      return "outage";
    }

    // Degraded thresholds
    if (
      cpuUsage > 80 ||
      memoryUsage > 80 ||
      errorRate > 10 ||
      responseTime > 10000
    ) {
      return "degraded";
    }

    return "operational";
  }

  /**
   * Get service uptime in seconds
   */
  static getUptime(): number {
    return process.uptime();
  }

  /**
   * Get memory usage metrics
   */
  static getMemoryMetrics(): {
    memoryUsage: number;
    memoryUsed: number;
    memoryTotal: number;
  } {
    const memUsage = process.memoryUsage();
    const totalMemory = memUsage.heapTotal + memUsage.external;
    const usedMemory = memUsage.heapUsed;

    return {
      memoryUsage: totalMemory > 0 ? (usedMemory / totalMemory) * 100 : 0,
      memoryUsed: usedMemory,
      memoryTotal: totalMemory,
    };
  }

  /**
   * Check if health reporting is active
   */
  isReporting(): boolean {
    return this.intervalId !== null;
  }

  /**
   * Get last report time
   */
  getLastReportTime(): number {
    return this.lastReportTime;
  }

  /**
   * Get consecutive failure count
   */
  getConsecutiveFailures(): number {
    return this.consecutiveFailures;
  }

  /**
   * Check if health reporting is failing
   */
  isHealthy(): boolean {
    return this.consecutiveFailures < this.MAX_FAILURES;
  }
}

// Export a default instance for the worker service
export const workerHealthReporter = new HealthReporter("auto-apply-worker");

// Graceful shutdown handler
process.on("SIGTERM", async () => {
  Logger.info("Received SIGTERM, reporting shutdown...");
  await workerHealthReporter.reportShutdown();
  workerHealthReporter.stopReporting();
});

process.on("SIGINT", async () => {
  Logger.info("Received SIGINT, reporting shutdown...");
  await workerHealthReporter.reportShutdown();
  workerHealthReporter.stopReporting();
});
