// cron/jobs/fetchOccupations.ts

import { downloadOnetAlternateOccupations } from "lib/onet/downloadAlternateOccupations";
import { fetchAlternateTitles } from "lib/onet/fetchAlternateTitles";
import { logger } from "../utils/logger";
import fs from "fs/promises";
import path from "path";

const ALTERNATE_TITLES_PATH = path.resolve("data/Alternate Titles.xlsx");

async function run() {
  try {
    logger.info("🔁 Starting O*NET occupation sync job...");

    const altTitlesExists = await fs
      .stat(ALTERNATE_TITLES_PATH)
      .catch(() => null);
    if (!altTitlesExists) {
      logger.info(`❌ Missing file: ${ALTERNATE_TITLES_PATH}`);
      await downloadOnetAlternateOccupations();
    }
    await fetchAlternateTitles();

    logger.info("✅ O*NET occupation sync job complete.");
  } catch (err) {
    logger.error("❌ Failed to sync occupations:", err);
  }
}

await run();
