<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { Edit, Briefcase, MapPin, Building, DollarSign } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import JobPreferencesModal from './JobPreferencesModal.svelte';

  // Define job preferences type
  export interface JobPreferencesData {
    interestedRoles?: string[];
    preferredLocations?: string[];
    remotePreference?: 'remote' | 'hybrid' | 'onsite' | 'flexible';
    salary?: string;
    industries?: string[];
    jobTypes?: string[];
    experienceLevels?: string[];
    valueInRole?: string[];
    idealCompanySize?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
    avoidIndustries?: string[];
    avoidSkills?: string[];
    securityClearance?: boolean;
    jobSearchStatus?: 'actively_looking' | 'open_to_opportunities' | 'not_looking';
  }

  // Props
  const { data, onSave } = $props<{
    data: JobPreferencesData;
    onSave: (data: JobPreferencesData) => Promise<boolean>;
  }>();

  // Modal state
  let modalOpen = $state(false);

  // Open edit modal
  function openEditModal() {
    modalOpen = true;
  }

  // Handle modal save
  async function handleSave(updatedData: JobPreferencesData): Promise<boolean> {
    try {
      const success = await onSave(updatedData);
      return success;
    } catch (error) {
      console.error('Error saving job preferences:', error);
      toast.error('Failed to save job preferences');
      return false;
    }
  }

  // Format remote preference for display
  function formatRemotePreference(preference?: string): string {
    if (!preference) return 'Not specified';

    const options = {
      remote: 'Remote Only',
      hybrid: 'Hybrid',
      onsite: 'On-site Only',
      flexible: 'Flexible',
    };
    return options[preference as keyof typeof options] || 'Not specified';
  }

  // Format array for display
  function formatArray(arr?: string[]): string {
    if (!arr || arr.length === 0) return 'Not specified';
    return arr.join(', ');
  }

  // Format company size for display
  function formatCompanySize(size?: string): string {
    if (!size) return 'Not specified';

    const options = {
      startup: 'Startup (1-10 employees)',
      small: 'Small (11-50 employees)',
      medium: 'Medium (51-200 employees)',
      large: 'Large (201-1000 employees)',
      enterprise: 'Enterprise (1000+ employees)',
    };
    return options[size as keyof typeof options] || 'Not specified';
  }

  // Format job search status for display
  function formatJobSearchStatus(status?: string): string {
    if (!status) return 'Not specified';

    const options = {
      actively_looking: 'Actively Looking',
      open_to_opportunities: 'Open to Opportunities',
      not_looking: 'Not Looking',
    };
    return options[status as keyof typeof options] || 'Not specified';
  }
</script>

<div class="space-y-6">
  <!-- Basic Job Preferences -->
  <div class="rounded-lg border p-6">
    <div class="flex items-center justify-between">
      <h2 class="text-xl font-semibold">Job Preferences</h2>
      <Button variant="ghost" size="sm" onclick={openEditModal}>
        <Edit class="mr-2 h-4 w-4" />
        Edit
      </Button>
    </div>
    <p class="text-muted-foreground mt-2 text-sm">
      Set your job preferences to help us find the right opportunities for you.
    </p>

    <div class="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
      <div class="rounded-md border p-4">
        <div class="flex items-center">
          <div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full">
            <Briefcase class="text-primary h-5 w-5" />
          </div>
          <div>
            <h3 class="font-medium">Desired Roles</h3>
            <p class="text-muted-foreground text-sm">
              {formatArray(data?.interestedRoles)}
            </p>
          </div>
        </div>
      </div>

      <div class="rounded-md border p-4">
        <div class="flex items-center">
          <div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full">
            <MapPin class="text-primary h-5 w-5" />
          </div>
          <div>
            <h3 class="font-medium">Preferred Locations</h3>
            <p class="text-muted-foreground text-sm">
              {formatArray(data?.preferredLocations)}
            </p>
          </div>
        </div>
      </div>

      <div class="rounded-md border p-4">
        <div class="flex items-center">
          <div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full">
            <Building class="text-primary h-5 w-5" />
          </div>
          <div>
            <h3 class="font-medium">Remote Preference</h3>
            <p class="text-muted-foreground text-sm">
              {formatRemotePreference(data?.remotePreference)}
            </p>
          </div>
        </div>
      </div>

      <div class="rounded-md border p-4">
        <div class="flex items-center">
          <div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full">
            <DollarSign class="text-primary h-5 w-5" />
          </div>
          <div>
            <h3 class="font-medium">Desired Salary</h3>
            <p class="text-muted-foreground text-sm">
              {data?.salary || 'Not specified'}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Additional Job Preferences -->
  <div class="rounded-lg border p-6">
    <div class="flex items-center justify-between">
      <h2 class="text-xl font-semibold">Additional Job Preferences</h2>
      <Button variant="ghost" size="sm" onclick={openEditModal}>
        <Edit class="mr-2 h-4 w-4" />
        Edit
      </Button>
    </div>
    <p class="text-muted-foreground mt-2 text-sm">
      Provide more details about your job preferences to help us find the right opportunities for
      you.
    </p>

    <div class="mt-4 space-y-4">
      <div>
        <h3 class="font-medium">What do you value in a new role?</h3>
        <p class="text-muted-foreground text-sm">
          {formatArray(data?.valueInRole)}
        </p>
      </div>

      <div>
        <h3 class="font-medium">What is your ideal company size?</h3>
        <p class="text-muted-foreground text-sm">
          {data?.idealCompanySize ? formatCompanySize(data.idealCompanySize) : 'Not specified'}
        </p>
      </div>

      <div>
        <h3 class="font-medium">What industries are exciting to you?</h3>
        <p class="text-muted-foreground text-sm">
          {formatArray(data?.industries)}
        </p>
      </div>

      <div>
        <h3 class="font-medium">Are there any industries you don't want to work in?</h3>
        <p class="text-muted-foreground text-sm">
          {formatArray(data?.avoidIndustries)}
        </p>
      </div>

      <div>
        <h3 class="font-medium">Are there any skills you don't want to work with?</h3>
        <p class="text-muted-foreground text-sm">
          {formatArray(data?.avoidSkills)}
        </p>
      </div>

      <div>
        <h3 class="font-medium">
          Would you like to see roles that require top security clearance?
        </h3>
        <p class="text-muted-foreground text-sm">
          {data?.securityClearance === true
            ? 'Yes'
            : data?.securityClearance === false
              ? 'No'
              : 'Not specified'}
        </p>
      </div>

      <div>
        <h3 class="font-medium">What's the status of your job search?</h3>
        <p class="text-muted-foreground text-sm">
          {data?.jobSearchStatus ? formatJobSearchStatus(data.jobSearchStatus) : 'Not specified'}
        </p>
      </div>
    </div>
  </div>
</div>

<!-- Job Preferences Modal -->
<JobPreferencesModal
  open={modalOpen}
  {data}
  onClose={() => (modalOpen = false)}
  onSave={handleSave} />
