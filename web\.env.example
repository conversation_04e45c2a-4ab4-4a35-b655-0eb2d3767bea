# Feature Control Environment Variables

# Development mode (automatically detected)
NODE_ENV=development

# Browser-based feature control (use in browser console):
#
# Disable all feature limits:
# localStorage.setItem('disable-feature-limits', 'true')
#
# Disable specific features:
# localStorage.setItem('disabled-features', 'automation,ai_matching')
#
# Enable all features:
# localStorage.setItem('enable-all-features', 'true')
#
# Or use the helper functions:
# import { BrowserControls } from '$lib/config/feature-flags'
# BrowserControls.disableAllLimits()
# BrowserControls.disableFeatures(['automation'])
# BrowserControls.reset()
