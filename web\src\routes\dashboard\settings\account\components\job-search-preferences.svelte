<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Form from '$lib/components/ui/form/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import { Switch } from '$lib/components/ui/switch/index.js';

  const { form, formData } = $props<{ form: any; formData: any }>();

  const remoteOptions = [
    { value: 'remote', label: 'Remote Only' },
    { value: 'hybrid', label: 'Hybrid' },
    { value: 'onsite', label: 'On-site Only' },
    { value: 'flexible', label: 'Flexible' },
  ];

  // Handle setting change
  function handleSettingChange(setting: string, value: any) {
    formData.update((f: any) => ({ ...f, [setting]: value }));
    
    // Submit the form to save changes to the database
    setTimeout(() => {
      const submitButton = document.getElementById('submit-button') as HTMLButtonElement;
      submitButton?.click();
    }, 100);
  }
</script>

<Card.Root>
  <Card.Header class="p-6">
    <Card.Title>Job Search Preferences</Card.Title>
    <Card.Description>Configure your default job search and application preferences.</Card.Description>
  </Card.Header>
  <Card.Content class="space-y-6 p-6 pt-0">
    <Form.Field {form} name="defaultRemotePreference">
      <Form.Control>
        {#snippet children({ props })}
          <div class="space-y-0.5">
            <Form.Label>Default Remote Preference</Form.Label>
            <Form.Description>Your preferred work arrangement for job searches</Form.Description>
          </div>
          <Select.Root
            {...props}
            type="single"
            value={$formData.defaultRemotePreference || 'hybrid'}
            onValueChange={(value) => handleSettingChange('defaultRemotePreference', value)}>
            <Select.Trigger class="w-full">
              <Select.Value placeholder="Select remote preference" />
            </Select.Trigger>
            <Select.Content class="max-h-60">
              <Select.Group>
                {#each remoteOptions as option (option.value)}
                  <Select.Item value={option.value} label={option.label}
                    >{option.label}</Select.Item>
                {/each}
              </Select.Group>
            </Select.Content>
          </Select.Root>
        {/snippet}
      </Form.Control>
      <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="showSalaryInListings">
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <div class="font-medium">Show Salary in Listings</div>
          <Form.Description>Display salary information when available in job listings</Form.Description>
        </div>
        <Form.Control>
          <Switch
            checked={Boolean($formData.showSalaryInListings)}
            onCheckedChange={(checked) => handleSettingChange('showSalaryInListings', checked)} />
        </Form.Control>
      </div>
      <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="autoApplyEnabled">
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <div class="font-medium">Auto-Apply Enabled</div>
          <Form.Description>Enable automatic job application features (requires premium plan)</Form.Description>
        </div>
        <Form.Control>
          <Switch
            checked={Boolean($formData.autoApplyEnabled)}
            onCheckedChange={(checked) => handleSettingChange('autoApplyEnabled', checked)} />
        </Form.Control>
      </div>
      <Form.FieldErrors />
    </Form.Field>
  </Card.Content>
</Card.Root>
