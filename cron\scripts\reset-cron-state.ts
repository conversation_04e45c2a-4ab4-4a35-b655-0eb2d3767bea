#!/usr/bin/env node

/**
 * Reset cron job state and circuit breaker
 *
 * This script clears any stuck job states and resets the circuit breaker
 * to allow cron jobs to run again.
 */

const { logger } = require("../utils/logger.js");
const { getSharedCircuitBreaker } = require("../utils/sharedCircuitBreaker.js");
const { redis } = require("../../workers/redis.js");

async function resetCronState() {
  logger.info("🔄 Resetting cron job state and circuit breaker");

  try {
    // 1. Reset circuit breaker
    logger.info("🧠 Resetting circuit breaker...");
    try {
      const circuitBreaker = getSharedCircuitBreaker();
      circuitBreaker.closeCircuit();
      logger.info("✅ Circuit breaker reset to CLOSED state");
    } catch (error) {
      logger.error("❌ Failed to reset circuit breaker:", error);
    }

    // 2. Clear running jobs status
    logger.info("🏃 Clearing running jobs status...");
    try {
      // Clear the running jobs hash
      await redis.del("cron:running_jobs");
      logger.info("✅ Running jobs status cleared");
    } catch (error) {
      logger.error("❌ Failed to clear running jobs:", error);
    }

    // 3. Clear worker circuit states
    logger.info("🔧 Clearing worker circuit states...");
    try {
      await redis.del("worker:circuit");
      logger.info("✅ Worker circuit states cleared");
    } catch (error) {
      logger.error("❌ Failed to clear worker circuits:", error);
    }

    // 4. Reset worker health to healthy state
    logger.info("🏥 Resetting worker health states...");
    try {
      const workerTypes = [
        "resume-parsing",
        "resume-optimization",
        "search",
        "ats-analysis",
        "job-specific-analysis",
        "email",
        "automation",
        "parallelJobScraper",
        "enrichJobDetails",
        "scrapeJobDetails",
      ];

      for (const workerType of workerTypes) {
        const healthData = {
          status: "healthy",
          healthy: true,
          lastHeartbeat: new Date().toISOString(),
          hostname: require("os").hostname(),
          pid: process.pid,
        };

        await redis.hset(
          "worker:health",
          workerType,
          JSON.stringify(healthData)
        );
      }

      logger.info("✅ Worker health states reset to healthy");
    } catch (error) {
      logger.error("❌ Failed to reset worker health:", error);
    }

    // 5. Clear any job locks that might be stuck
    logger.info("🔒 Clearing job locks...");
    try {
      const lockKeys = await redis.keys("lock:*");
      if (lockKeys.length > 0) {
        await redis.del(...lockKeys);
        logger.info(`✅ Cleared ${lockKeys.length} job locks`);
      } else {
        logger.info("✅ No job locks found to clear");
      }
    } catch (error) {
      logger.error("❌ Failed to clear job locks:", error);
    }

    // 6. Verify the reset
    logger.info("🔍 Verifying reset...");
    try {
      const circuitBreaker = getSharedCircuitBreaker();
      const isClosed = await circuitBreaker.isClosed();
      const state = circuitBreaker.getState();

      logger.info(`🔧 Circuit breaker state: ${state}`);
      logger.info(`🔧 Circuit breaker closed: ${isClosed}`);

      const runningJobs = await redis.hgetall("cron:running_jobs");
      logger.info(`🏃 Running jobs: ${JSON.stringify(runningJobs)}`);

      const healthData = await redis.hgetall("worker:health");
      logger.info(`🏥 Health entries: ${Object.keys(healthData).length}`);
    } catch (error) {
      logger.error("❌ Failed to verify reset:", error);
    }

    logger.info("🎉 Cron state reset completed!");
    logger.info("📋 What was reset:");
    logger.info("  • Circuit breaker → CLOSED");
    logger.info("  • Running jobs status → Cleared");
    logger.info("  • Worker circuit states → Cleared");
    logger.info("  • Worker health → Reset to healthy");
    logger.info("  • Job locks → Cleared");

    logger.info("🔧 Next steps:");
    logger.info("  1. Try running a cron job manually to test");
    logger.info("  2. Monitor the logs for any new issues");
    logger.info("  3. Check if scheduled jobs start running");
  } catch (error) {
    logger.error("❌ Reset failed:", error);
  }
}

async function main() {
  try {
    await resetCronState();
    process.exit(0);
  } catch (error) {
    logger.error("❌ Reset script failed:", error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}
