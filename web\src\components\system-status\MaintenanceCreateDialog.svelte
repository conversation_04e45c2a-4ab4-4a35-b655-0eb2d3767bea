<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import * as Accordion from '$lib/components/ui/accordion/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import { Checkbox } from '$lib/components/ui/checkbox/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import StatusTag from '$components/system-status/StatusTag.svelte';
  import SeverityBadge from '$components/system-status/SeverityBadge.svelte';
  import StatusBar from '$components/system-status/StatusBar.svelte';
  import type { StatusTagType } from '$lib/types';
  import SuperDebug from 'sveltekit-superforms/client/SuperDebug.svelte';
  import { enhance } from '$app/forms';

  // Props
  export let open: boolean;
  export let createForm: any;
  export let createErrors: any;
  export let serviceOptions: Array<{ value: string; label: string }>;
  export let onClose: () => void;
  export let onSubmit: () => void;
  export let resetForm: () => void;
</script>

<Dialog.Root bind:open>
  <Dialog.Overlay />
  <Dialog.Content class="sm:max-w-[600px]">
    <Dialog.Header>
      <Dialog.Title>Schedule Maintenance</Dialog.Title>
      <Dialog.Description>
        Create a new scheduled maintenance event. This will be displayed on the system status page.
      </Dialog.Description>
    </Dialog.Header>

    <form
      method="POST"
      action="?/create"
      id="create-maintenance-form"
      use:enhance
      on:submit|preventDefault={onSubmit}>
      <!-- Ensure all required fields are included in the form submission -->
      <input type="hidden" name="affectedServices" value={$createForm.affectedServices} />
      <div class="grid gap-4 py-4">
        <div class="grid gap-2">
          <Label for="title">Title</Label>
          <Input id="title" placeholder="Database Maintenance" bind:value={$createForm.title} />
          {#if $createErrors.title}<p class="text-sm text-red-500">{$createErrors.title}</p>{/if}
        </div>

        <div class="grid gap-2">
          <Label for="description">Description</Label>
          <Textarea bind:value={$createForm.description} />
          {#if $createErrors.description}<p class="text-sm text-red-500">
              {$createErrors.description}
            </p>{/if}
        </div>

        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div class="grid gap-2">
            <Label for="startTime">Start Time</Label>
            <Input id="startTime" type="datetime-local" bind:value={$createForm.startTime} />
            {#if $createErrors.startTime}<p class="text-sm text-red-500">
                {$createErrors.startTime}
              </p>{/if}
          </div>

          <div class="grid gap-2">
            <Label for="endTime">End Time</Label>
            <Input id="endTime" type="datetime-local" bind:value={$createForm.endTime} />
            {#if $createErrors.endTime}<p class="text-sm text-red-500">
                {$createErrors.endTime}
              </p>{/if}
          </div>
        </div>

        <!-- Status Bar -->
        {#if $createForm.startTime && $createForm.endTime}
          <div class="mt-2">
            <Label>Progress</Label>
            <StatusBar
              startTime={$createForm.startTime}
              endTime={$createForm.endTime}
              status={$createForm.status} />
          </div>
        {/if}

        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div class="grid gap-2">
            <Label for="status">Status</Label>
            <div class="flex items-center gap-2">
              <Select.Root type="single" bind:value={$createForm.status}>
                <Select.SelectTrigger class="w-full">
                  <Select.SelectValue placeholder="Select status" />
                </Select.SelectTrigger>
                <Select.SelectContent class="w-full">
                  <Select.SelectItem value="scheduled">Scheduled</Select.SelectItem>
                  <Select.SelectItem value="in-progress">In Progress</Select.SelectItem>
                  <Select.SelectItem value="completed">Completed</Select.SelectItem>
                  <Select.SelectItem value="cancelled">Cancelled</Select.SelectItem>
                </Select.SelectContent>
              </Select.Root>
              <StatusTag status={$createForm.status as StatusTagType} />
            </div>
          </div>

          <div class="grid gap-2">
            <Label for="severity">Severity</Label>
            <div class="flex items-center gap-2">
              <Select.Root type="single" bind:value={$createForm.severity}>
                <Select.SelectTrigger class="w-full">
                  <Select.SelectValue placeholder="Select severity" />
                </Select.SelectTrigger>
                <Select.SelectContent class="w-full">
                  <Select.SelectItem value="info">Information</Select.SelectItem>
                  <Select.SelectItem value="maintenance">Maintenance</Select.SelectItem>
                  <Select.SelectItem value="minor">Minor Outage</Select.SelectItem>
                  <Select.SelectItem value="major">Major Outage</Select.SelectItem>
                  <Select.SelectItem value="critical">Critical Outage</Select.SelectItem>
                </Select.SelectContent>
              </Select.Root>
              <SeverityBadge severity={$createForm.severity} />
            </div>
          </div>
        </div>

        <div class="grid gap-2">
          <Label for="affectedServices">Affected Services</Label>
          <div>
            <Select.Root
              type="single"
              bind:value={$createForm.affectedServices[0]}
              onValueChange={(value) => {
                // Ensure we always have at least one service
                if (value) {
                  $createForm.affectedServices = [value];
                } else {
                  // Default to first service if none selected
                  $createForm.affectedServices = [serviceOptions[0].value];
                }
              }}>
              <Select.SelectTrigger class="w-full">
                <Select.SelectValue placeholder="Select a service" />
              </Select.SelectTrigger>
              <Select.SelectContent class="w-full">
                {#each serviceOptions as option}
                  <Select.SelectItem value={option.value}>{option.label}</Select.SelectItem>
                {/each}
              </Select.SelectContent>
            </Select.Root>
          </div>
          {#if $createErrors.affectedServices}<p class="text-sm text-red-500">
              {$createErrors.affectedServices}
            </p>{/if}
          <p class="text-muted-foreground text-xs">
            Select the service that will be affected by this maintenance
          </p>
        </div>

        <div class="flex items-center space-x-2">
          <Checkbox
            id="sendNotification"
            name="sendNotification"
            bind:checked={$createForm.sendNotification} />
          <Label for="sendNotification" class="text-sm font-normal">
            Send notification to all users
          </Label>
        </div>
      </div>

      {#if import.meta.env.DEV}
        <Accordion.Root type="single" class="mb-4">
          <Accordion.Item value="debug">
            <Accordion.Trigger
              class="flex w-full items-center justify-between rounded border p-3 text-left">
              <h3 class="text-sm font-medium">SuperForm Debug</h3>
            </Accordion.Trigger>
            <Accordion.Content class="rounded-b border border-t-0 p-4">
              <SuperDebug data={$createForm} />
            </Accordion.Content>
          </Accordion.Item>
        </Accordion.Root>
      {/if}

      <Dialog.Footer>
        <Button
          type="button"
          variant="outline"
          onclick={() => {
            resetForm();
            onClose();
          }}>
          Cancel
        </Button>
        <Button type="submit">Create</Button>
      </Dialog.Footer>
    </form>
  </Dialog.Content>
</Dialog.Root>
