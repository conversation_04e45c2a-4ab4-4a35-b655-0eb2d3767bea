// web/src/routes/api/health/services/+server.ts
// API endpoint to get service health data from the database

import { json } from '@sveltejs/kit';
import { logger } from '$lib/server/logger';
import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url }) => {
  try {
    const service = url.searchParams.get('service');
    const includeHistory = url.searchParams.get('history') === 'true';

    if (service) {
      // Get specific service status
      const serviceStatus = await prisma.serviceStatus.findUnique({
        where: { name: service },
        include: includeHistory
          ? {
              statusHistory: {
                orderBy: { recordedAt: 'desc' },
                take: 50, // Last 50 status changes
              },
            }
          : undefined,
      });

      if (!serviceStatus) {
        return json({ error: `Service '${service}' not found` }, { status: 404 });
      }

      const response: any = {
        service: serviceStatus.name,
        status: serviceStatus.status,
        description: serviceStatus.description,
        lastCheckedAt: serviceStatus.lastCheckedAt.toISOString(),
        timestamp: new Date().toISOString(),
      };

      if (includeHistory && serviceStatus.statusHistory) {
        response.history = serviceStatus.statusHistory.map((h) => ({
          status: h.status,
          recordedAt: h.recordedAt.toISOString(),
        }));
      }

      return json(response);
    } else {
      // Get all service statuses
      const services = await prisma.serviceStatus.findMany({
        orderBy: { name: 'asc' },
        include: includeHistory
          ? {
              statusHistory: {
                orderBy: { recordedAt: 'desc' },
                take: 10, // Last 10 status changes per service
              },
            }
          : undefined,
      });

      // Calculate overall system status
      let overallStatus = 'operational';
      if (services.some((s) => s.status === 'outage')) {
        overallStatus = 'outage';
      } else if (services.some((s) => s.status === 'degraded')) {
        overallStatus = 'degraded';
      } else if (services.some((s) => s.status === 'maintenance')) {
        overallStatus = 'maintenance';
      }

      // Count services by status
      const statusCounts = services.reduce(
        (counts, service) => {
          counts[service.status] = (counts[service.status] || 0) + 1;
          return counts;
        },
        {} as Record<string, number>
      );

      const response: any = {
        overallStatus,
        totalServices: services.length,
        statusCounts,
        services: services.map((s) => {
          const serviceData: any = {
            name: s.name,
            status: s.status,
            description: s.description,
            lastCheckedAt: s.lastCheckedAt.toISOString(),
          };

          if (includeHistory && s.statusHistory) {
            serviceData.history = s.statusHistory.map((h) => ({
              status: h.status,
              recordedAt: h.recordedAt.toISOString(),
            }));
          }

          return serviceData;
        }),
        timestamp: new Date().toISOString(),
      };

      return json(response);
    }
  } catch (error) {
    logger.error('Error retrieving service health from database:', error);

    return json(
      {
        error: 'Failed to retrieve service health',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
};

// POST endpoint to manually trigger service health updates
export const POST: RequestHandler = async ({ request }) => {
  try {
    const { action, service } = await request.json();

    if (action === 'refresh' && service) {
      // Mark service for refresh by updating lastCheckedAt to trigger a new check
      const updatedService = await prisma.serviceStatus.update({
        where: { name: service },
        data: {
          lastCheckedAt: new Date(0), // Force refresh by setting old timestamp
        },
      });

      logger.info(`Triggered refresh for service: ${service}`);

      return json({
        success: true,
        message: `Refresh triggered for ${service}`,
        service: {
          name: updatedService.name,
          status: updatedService.status,
          lastCheckedAt: updatedService.lastCheckedAt.toISOString(),
        },
        timestamp: new Date().toISOString(),
      });
    } else if (action === 'refresh-all') {
      // Refresh all services
      await prisma.serviceStatus.updateMany({
        data: {
          lastCheckedAt: new Date(0), // Force refresh by setting old timestamp
        },
      });

      logger.info('Triggered refresh for all services');

      return json({
        success: true,
        message: 'Refresh triggered for all services',
        timestamp: new Date().toISOString(),
      });
    } else {
      return json(
        {
          error: 'Invalid action or missing service parameter',
          validActions: ['refresh', 'refresh-all'],
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }
  } catch (error) {
    logger.error('Error processing service health action:', error);

    return json(
      {
        error: 'Failed to process service health action',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
};

// OPTIONS handler for CORS
export const OPTIONS: RequestHandler = async () => {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
};
