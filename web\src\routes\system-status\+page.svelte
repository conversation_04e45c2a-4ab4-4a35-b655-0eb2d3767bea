<script lang="ts">
  import SEO from '$components/shared/SEO.svelte';
  import StatusPage from './StatusPage.svelte';
  import { History } from 'lucide-svelte';

  // Import page data
  export let data;
</script>

<SEO
  title="System Status | Hirli"
  description="Check the current status of Hirli services and systems."
  keywords="system status, service status, uptime, Hirli status" />

<StatusPage pageData={data} />

<!-- View Notice History Link -->
<div class="container mb-8">
  <a
    href="/system-status/history"
    class="flex w-full items-center justify-center rounded-lg border p-3 text-center hover:bg-gray-50 dark:hover:bg-gray-900">
    <History class="mr-2 h-4 w-4" />
    <span>View notice history</span>
  </a>
</div>
