// cron/utils/enhancedAdaptiveResourceManager.ts
// Enhanced adaptive resource manager that adjusts worker count based on system resources

import { logger } from "./logger";
import os from "os";

/**
 * Options for the enhanced adaptive resource manager
 */
export interface EnhancedAdaptiveResourceManagerOptions {
  // Worker configuration
  initialWorkerCount: number;
  minWorkerCount: number;
  maxWorkerCount?: number;
  
  // Resource thresholds
  memoryThresholdPercent?: number; // Memory usage threshold to start reducing workers
  criticalMemoryThresholdPercent?: number; // Critical memory threshold for minimum workers
  cpuThresholdPercent?: number; // CPU usage threshold to start reducing workers
  
  // Scaling behavior
  scaleDownStep?: number; // How many workers to reduce at once
  scaleUpStep?: number; // How many workers to add at once
  stabilizationPeriodMs?: number; // Time to wait before scaling up after scaling down
  
  // Callbacks
  onWorkerCountChange?: (newCount: number, reason: string) => void;
}

/**
 * Enhanced Adaptive Resource Manager
 * 
 * This class monitors system resources and dynamically adjusts the number of
 * workers based on available resources, ensuring jobs continue to run even
 * under resource constraints by reducing concurrency rather than stopping.
 */
export class EnhancedAdaptiveResourceManager {
  // Worker configuration
  private currentWorkerCount: number;
  private minWorkerCount: number;
  private maxWorkerCount: number;
  
  // Resource thresholds
  private memoryThresholdPercent: number;
  private criticalMemoryThresholdPercent: number;
  private cpuThresholdPercent: number;
  
  // Scaling behavior
  private scaleDownStep: number;
  private scaleUpStep: number;
  private stabilizationPeriodMs: number;
  private lastScaleDownTime: number = 0;
  
  // Consecutive readings tracking
  private consecutiveHighMemoryReadings: number = 0;
  private consecutiveHighCpuReadings: number = 0;
  private consecutiveLowResourceReadings: number = 0;
  
  // Callbacks
  private onWorkerCountChange?: (newCount: number, reason: string) => void;
  
  constructor(options: EnhancedAdaptiveResourceManagerOptions) {
    // Set worker configuration
    this.currentWorkerCount = options.initialWorkerCount;
    this.minWorkerCount = options.minWorkerCount;
    this.maxWorkerCount = options.maxWorkerCount || options.initialWorkerCount;
    
    // Set resource thresholds with defaults
    this.memoryThresholdPercent = options.memoryThresholdPercent || 80;
    this.criticalMemoryThresholdPercent = options.criticalMemoryThresholdPercent || 90;
    this.cpuThresholdPercent = options.cpuThresholdPercent || 80;
    
    // Set scaling behavior with defaults
    this.scaleDownStep = options.scaleDownStep || 1;
    this.scaleUpStep = options.scaleUpStep || 1;
    this.stabilizationPeriodMs = options.stabilizationPeriodMs || 5 * 60 * 1000; // 5 minutes default
    
    // Set callbacks
    this.onWorkerCountChange = options.onWorkerCountChange;
    
    logger.info(`🔧 Enhanced Adaptive Resource Manager initialized with ${this.currentWorkerCount} workers`);
    logger.info(`   • Memory threshold: ${this.memoryThresholdPercent}%, Critical: ${this.criticalMemoryThresholdPercent}%`);
    logger.info(`   • CPU threshold: ${this.cpuThresholdPercent}%`);
    logger.info(`   • Worker range: ${this.minWorkerCount}-${this.maxWorkerCount}`);
  }
  
  /**
   * Get the current worker count
   */
  public getCurrentWorkerCount(): number {
    return this.currentWorkerCount;
  }
  
  /**
   * Set the worker count directly (manual override)
   */
  public setWorkerCount(count: number, reason: string = "manual"): number {
    // Ensure count is within bounds
    const boundedCount = Math.max(this.minWorkerCount, Math.min(this.maxWorkerCount, count));
    
    if (boundedCount !== this.currentWorkerCount) {
      const oldCount = this.currentWorkerCount;
      this.currentWorkerCount = boundedCount;
      
      logger.info(`🔄 Worker count changed from ${oldCount} to ${boundedCount} (${reason})`);
      
      if (this.onWorkerCountChange) {
        this.onWorkerCountChange(boundedCount, reason);
      }
    }
    
    return this.currentWorkerCount;
  }
  
  /**
   * Check system resources and adjust worker count accordingly
   * Returns the new worker count
   */
  public checkAndAdjustResources(otherJobsRunning: boolean = false): number {
    // Get current resource usage
    const { memoryUsagePercent, cpuUsagePercent } = this.getResourceUsage();
    
    // Track consecutive readings
    if (memoryUsagePercent >= this.memoryThresholdPercent) {
      this.consecutiveHighMemoryReadings++;
      this.consecutiveLowResourceReadings = 0;
    } else {
      this.consecutiveHighMemoryReadings = 0;
    }
    
    if (cpuUsagePercent >= this.cpuThresholdPercent) {
      this.consecutiveHighCpuReadings++;
      this.consecutiveLowResourceReadings = 0;
    } else {
      this.consecutiveHighCpuReadings = 0;
    }
    
    if (memoryUsagePercent < this.memoryThresholdPercent && cpuUsagePercent < this.cpuThresholdPercent) {
      this.consecutiveLowResourceReadings++;
    } else {
      this.consecutiveLowResourceReadings = 0;
    }
    
    // Log resource usage if it's high or we're not at max workers
    if (memoryUsagePercent >= this.memoryThresholdPercent - 5 || 
        cpuUsagePercent >= this.cpuThresholdPercent - 5 ||
        this.currentWorkerCount < this.maxWorkerCount) {
      logger.info(
        `📊 Resource usage - Memory: ${memoryUsagePercent.toFixed(2)}% (threshold: ${this.memoryThresholdPercent}%), ` +
        `CPU: ${cpuUsagePercent.toFixed(2)}% (threshold: ${this.cpuThresholdPercent}%), ` +
        `Workers: ${this.currentWorkerCount}/${this.maxWorkerCount}, ` +
        `Other jobs running: ${otherJobsRunning ? "yes" : "no"}`
      );
    }
    
    // Critical memory condition - immediately reduce to minimum
    if (memoryUsagePercent >= this.criticalMemoryThresholdPercent && this.currentWorkerCount > this.minWorkerCount) {
      logger.warn(`⚠️ Critical memory usage (${memoryUsagePercent.toFixed(2)}%) - reducing to minimum workers`);
      this.lastScaleDownTime = Date.now();
      return this.setWorkerCount(this.minWorkerCount, "critical memory");
    }
    
    // High memory or CPU with consecutive readings - scale down gradually
    if ((this.consecutiveHighMemoryReadings >= 2 || this.consecutiveHighCpuReadings >= 2) && 
        this.currentWorkerCount > this.minWorkerCount) {
      const newCount = Math.max(this.minWorkerCount, this.currentWorkerCount - this.scaleDownStep);
      logger.info(
        `⬇️ High resource usage - reducing workers from ${this.currentWorkerCount} to ${newCount} ` +
        `(Memory: ${memoryUsagePercent.toFixed(2)}%, CPU: ${cpuUsagePercent.toFixed(2)}%)`
      );
      this.lastScaleDownTime = Date.now();
      return this.setWorkerCount(newCount, "high resource usage");
    }
    
    // Low resource usage for a while and past stabilization period - scale up gradually
    const timeElapsedSinceScaleDown = Date.now() - this.lastScaleDownTime;
    if (this.consecutiveLowResourceReadings >= 3 && 
        timeElapsedSinceScaleDown > this.stabilizationPeriodMs &&
        this.currentWorkerCount < this.maxWorkerCount && 
        !otherJobsRunning) {
      const newCount = Math.min(this.maxWorkerCount, this.currentWorkerCount + this.scaleUpStep);
      logger.info(
        `⬆️ Low resource usage - increasing workers from ${this.currentWorkerCount} to ${newCount} ` +
        `(Memory: ${memoryUsagePercent.toFixed(2)}%, CPU: ${cpuUsagePercent.toFixed(2)}%)`
      );
      return this.setWorkerCount(newCount, "low resource usage");
    }
    
    // If other jobs are running, consider reducing workers to free up resources
    if (otherJobsRunning && this.currentWorkerCount > this.minWorkerCount && 
        (memoryUsagePercent >= this.memoryThresholdPercent - 10 || cpuUsagePercent >= this.cpuThresholdPercent - 10)) {
      const newCount = Math.max(this.minWorkerCount, this.currentWorkerCount - this.scaleDownStep);
      logger.info(
        `⬇️ Other jobs running - reducing workers from ${this.currentWorkerCount} to ${newCount} ` +
        `to free up resources`
      );
      this.lastScaleDownTime = Date.now();
      return this.setWorkerCount(newCount, "other jobs running");
    }
    
    // No change needed
    return this.currentWorkerCount;
  }
  
  /**
   * Get current system resource usage
   */
  private getResourceUsage(): { memoryUsagePercent: number, cpuUsagePercent: number } {
    // Check memory usage
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsagePercent = (usedMemory / totalMemory) * 100;
    
    // Check CPU usage (load average)
    const cpuCount = os.cpus().length;
    const loadAvg = os.loadavg()[0]; // 1 minute load average
    const loadPerCpu = loadAvg / cpuCount;
    const cpuUsagePercent = loadPerCpu * 100;
    
    return {
      memoryUsagePercent,
      cpuUsagePercent
    };
  }
}
