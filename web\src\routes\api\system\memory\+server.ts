// src/routes/api/system/memory/+server.ts
import { json } from '@sveltejs/kit';
import { logger } from '$lib/server/logger';
import type { RequestHandler } from './$types';
import os from 'os';

/**
 * Get system memory usage
 * @returns Memory usage information
 */
export const GET: RequestHandler = async ({ locals }) => {
  try {
    // Check if user is admin for detailed stats
    const user = locals.user;
    const isAdmin = user?.isAdmin === true;

    // Get memory usage
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsagePercent = Math.round((usedMemory / totalMemory) * 100);

    // Basic memory info for all users
    const memoryInfo = {
      total: formatBytes(totalMemory),
      used: formatBytes(usedMemory),
      free: formatBytes(freeMemory),
      usagePercent: memoryUsagePercent
    };

    // Add detailed stats for admins
    if (isAdmin) {
      const detailedInfo = {
        ...memoryInfo,
        totalBytes: totalMemory,
        usedBytes: usedMemory,
        freeBytes: freeMemory,
        // Add process memory info
        process: {
          rss: formatBytes(process.memoryUsage().rss),
          heapTotal: formatBytes(process.memoryUsage().heapTotal),
          heapUsed: formatBytes(process.memoryUsage().heapUsed),
          external: formatBytes(process.memoryUsage().external),
          arrayBuffers: formatBytes(process.memoryUsage().arrayBuffers || 0)
        },
        // Add system info
        system: {
          platform: os.platform(),
          arch: os.arch(),
          cpus: os.cpus().length,
          uptime: formatUptime(os.uptime())
        }
      };

      return json(detailedInfo);
    }

    return json(memoryInfo);
  } catch (error) {
    logger.error('Error fetching memory usage:', error);
    return json({ error: 'Failed to fetch memory usage' }, { status: 500 });
  }
};

/**
 * Format bytes to human readable format
 * @param bytes Number of bytes
 * @returns Formatted string
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format uptime to human readable format
 * @param seconds Uptime in seconds
 * @returns Formatted string
 */
function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / (3600 * 24));
  const hours = Math.floor((seconds % (3600 * 24)) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  const parts = [];
  if (days > 0) parts.push(`${days}d`);
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (secs > 0 || parts.length === 0) parts.push(`${secs}s`);
  
  return parts.join(' ');
}
