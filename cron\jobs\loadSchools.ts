// cron/scripts/loadSchools.ts

import { logger } from "../utils/logger";
import fs from "fs/promises";
import path from "path";
import { loadSchools } from "../lib/saveSchools";

const SCHOOLS = path.resolve("../cron/utils/json/schools.json");

async function run() {
  try {
    logger.info("🔁 Starting school taxonomy sync...");

    const schoolsExists = await fs.stat(SCHOOLS).catch(() => null);

    if (!schoolsExists) {
      logger.error(`❌ Missing file: ${SCHOOLS}`);
      return;
    }

    logger.info("🚀 Seeding schools...");
    await loadSchools();

    logger.info("✅ School taxonomy sync complete.");
  } catch (err) {
    logger.error("❌ Failed to sync school taxonomy:", err);
  }
}

await run();
