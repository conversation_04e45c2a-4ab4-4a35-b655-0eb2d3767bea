<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { toast } from 'svelte-sonner';
  import { Loader2 } from 'lucide-svelte';

  // Props
  const {
    resumeId,
    profileId,
    disabled = false,
  } = $props<{
    resumeId: string;
    profileId: string;
    disabled?: boolean;
  }>();

  // State
  let loading = $state(false);
  let result = $state<any>(null);

  // Test resume parsing
  async function testResumeParsing() {
    if (!resumeId || loading) return;

    loading = true;
    result = null;

    try {
      const response = await fetch('/api/resume/test-parsing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resumeId,
          profileId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to test resume parsing');
      }

      const data = await response.json();
      console.log('Resume parsing test response:', data);
      result = data;

      toast.success('Resume parsing test initiated');
    } catch (err) {
      console.error('Error testing resume parsing:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to test resume parsing');
    } finally {
      loading = false;
    }
  }
</script>

<div class="mt-4 border-t pt-4">
  <div class="flex items-center justify-between">
    <h4 class="text-sm font-medium">Debug Resume Parsing</h4>
    <Button
      variant="outline"
      size="sm"
      onclick={testResumeParsing}
      disabled={disabled || loading || !resumeId}>
      {#if loading}
        <Loader2 class="mr-2 h-4 w-4 animate-spin" />
        Testing...
      {:else}
        Test Resume Parsing
      {/if}
    </Button>
  </div>

  {#if result}
    <div class="mt-2 rounded-md bg-muted p-2 text-xs">
      <pre>{JSON.stringify(result, null, 2)}</pre>
    </div>
  {/if}
</div>
