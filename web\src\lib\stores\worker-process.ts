// File: web/src/lib/stores/worker-process.ts
import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';
import { messages } from './websocket-client';

// Worker process status types
export enum WorkerProcessStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// Worker process types
export enum WorkerProcessType {
  RESUME_PARSING = 'resume-parsing',
  RESUME_OPTIMIZATION = 'resume-optimization',
  JOB_SEARCH = 'job-search',
}

// Worker process interface
export interface WorkerProcess {
  id: string;
  type: string;
  status: string;
  data?: any;
  createdAt: Date;
  updatedAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
}

// Create a store for worker processes
export const workerProcesses = writable<Record<string, WorkerProcess>>({});

// Create a derived store for resume parsing processes
export const resumeParsingProcesses = derived(workerProcesses, ($workerProcesses) => {
  return Object.values($workerProcesses).filter(
    (process) => process.type === WorkerProcessType.RESUME_PARSING
  );
});

// Create a derived store for resume optimization processes
export const resumeOptimizationProcesses = derived(workerProcesses, ($workerProcesses) => {
  return Object.values($workerProcesses).filter(
    (process) => process.type === WorkerProcessType.RESUME_OPTIMIZATION
  );
});

// Create a derived store for job search processes
export const jobSearchProcesses = derived(workerProcesses, ($workerProcesses) => {
  return Object.values($workerProcesses).filter(
    (process) => process.type === WorkerProcessType.JOB_SEARCH
  );
});

// Create a derived store for active processes
export const activeProcesses = derived(workerProcesses, ($workerProcesses) => {
  return Object.values($workerProcesses).filter(
    (process) =>
      process.status === WorkerProcessStatus.PENDING ||
      process.status === WorkerProcessStatus.PROCESSING
  );
});

// Create a derived store for completed processes
export const completedProcesses = derived(workerProcesses, ($workerProcesses) => {
  return Object.values($workerProcesses).filter(
    (process) => process.status === WorkerProcessStatus.COMPLETED
  );
});

// Create a derived store for failed processes
export const failedProcesses = derived(workerProcesses, ($workerProcesses) => {
  return Object.values($workerProcesses).filter(
    (process) => process.status === WorkerProcessStatus.FAILED
  );
});

// Initialize the store by subscribing to WebSocket messages
if (browser) {
  messages.subscribe(($messages) => {
    // Filter for worker process status messages
    const statusMessages = $messages.filter((message) => message.type === 'worker-process-status');

    // Update the store with the latest status
    statusMessages.forEach((message) => {
      const { jobId, status, resumeId, parsedResumeId, userId } = message.data;

      // Update the worker process in the store
      workerProcesses.update(($workerProcesses) => {
        // If the process already exists, update it
        if ($workerProcesses[jobId]) {
          $workerProcesses[jobId] = {
            ...$workerProcesses[jobId],
            status,
            updatedAt: new Date(),
            completedAt: status === WorkerProcessStatus.COMPLETED ? new Date() : undefined,
            data: {
              ...$workerProcesses[jobId].data,
              resumeId,
              parsedResumeId,
              userId,
            },
          };
        } else {
          // Otherwise, create a new process
          $workerProcesses[jobId] = {
            id: jobId,
            type: 'resume-parsing', // Assume resume parsing for now
            status,
            createdAt: new Date(),
            updatedAt: new Date(),
            completedAt: status === WorkerProcessStatus.COMPLETED ? new Date() : undefined,
            data: {
              resumeId,
              parsedResumeId,
              userId,
            },
          };
        }

        return $workerProcesses;
      });
    });
  });
}

/**
 * Add a worker process to the store
 * @param process The worker process to add
 */
export function addWorkerProcess(process: WorkerProcess): void {
  workerProcesses.update(($workerProcesses) => {
    $workerProcesses[process.id] = process;
    return $workerProcesses;
  });
}

/**
 * Update a worker process in the store
 * @param id The ID of the worker process to update
 * @param updates The updates to apply to the worker process
 */
export function updateWorkerProcess(id: string, updates: Partial<WorkerProcess>): void {
  workerProcesses.update(($workerProcesses) => {
    if ($workerProcesses[id]) {
      $workerProcesses[id] = {
        ...$workerProcesses[id],
        ...updates,
        updatedAt: new Date(),
      };
    }
    return $workerProcesses;
  });
}

/**
 * Remove a worker process from the store
 * @param id The ID of the worker process to remove
 */
export function removeWorkerProcess(id: string): void {
  workerProcesses.update(($workerProcesses) => {
    delete $workerProcesses[id];
    return $workerProcesses;
  });
}

/**
 * Clear all worker processes from the store
 */
export function clearWorkerProcesses(): void {
  workerProcesses.set({});
}

/**
 * Get a worker process by ID
 * @param id The ID of the worker process to get
 * @returns The worker process, or undefined if not found
 */
export function getWorkerProcess(id: string): WorkerProcess | undefined {
  let result: WorkerProcess | undefined;
  workerProcesses.subscribe(($workerProcesses) => {
    result = $workerProcesses[id];
  })();
  return result;
}

/**
 * Get worker processes by resume ID
 * @param resumeId The resume ID to filter by
 * @returns An array of worker processes for the resume
 */
export function getWorkerProcessesByResumeId(resumeId: string): WorkerProcess[] {
  let result: WorkerProcess[] = [];
  workerProcesses.subscribe(($workerProcesses) => {
    result = Object.values($workerProcesses).filter(
      (process) => process.data?.resumeId === resumeId
    );
  })();
  return result;
}
