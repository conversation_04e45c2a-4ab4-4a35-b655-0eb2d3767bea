import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';

/**
 * Get job-specific ATS analysis
 * 
 * This endpoint analyzes a resume against a specific job description.
 */
export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    // Verify authentication
    const token = cookies.get('auth_token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tokenData = verifySessionToken(token);
    if (!tokenData || !tokenData.id) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // Get user ID from token
    const userId = tokenData.id;

    // Parse request body
    const body = await request.json();
    const { resumeId, jobId } = body;

    // Validate required fields
    if (!resumeId || !jobId) {
      return json(
        { error: 'Missing required fields', required: ['resumeId', 'jobId'] },
        { status: 400 }
      );
    }

    // Check if resume exists and belongs to the user
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        document: {
          userId
        }
      }
    });

    if (!resume) {
      return json({ error: 'Resume not found or access denied' }, { status: 404 });
    }

    // Check if job exists
    const job = await prisma.job.findUnique({
      where: { id: jobId }
    });

    if (!job) {
      return json({ error: 'Job not found' }, { status: 404 });
    }

    // Check if user has access to the resume_analysis feature
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscriptions: {
          where: { status: 'active' },
          include: {
            plan: {
              include: {
                features: {
                  where: { featureId: 'resume_analysis' }
                }
              }
            }
          }
        }
      }
    });

    const hasAccess = user?.subscriptions.some(sub => 
      sub.plan.features.some(feature => feature.featureId === 'resume_analysis')
    );

    if (!hasAccess && process.env.NODE_ENV === 'production') {
      return json({ error: 'Feature not available in your plan' }, { status: 403 });
    }

    // Check if job-specific analysis already exists
    let analysis = await prisma.jobSpecificATSAnalysis.findFirst({
      where: {
        atsAnalysisId: `ats-${resumeId}`,
        jobId
      }
    });

    // If analysis doesn't exist, generate a new one
    if (!analysis) {
      // In a real implementation, this would call the ATS analysis worker
      // For now, we'll generate mock analysis
      const mockAnalysis = generateMockJobSpecificATSAnalysis(job.title);
      
      // Get or create base ATS analysis
      let baseAnalysis = await prisma.atsAnalysis.findUnique({
        where: { resumeId }
      });
      
      if (!baseAnalysis) {
        const mockBaseAnalysis = generateMockBaseATSAnalysis();
        
        baseAnalysis = await prisma.atsAnalysis.create({
          data: {
            id: `ats-${resumeId}`,
            resumeId,
            overallScore: mockBaseAnalysis.overallScore,
            keywordScore: mockBaseAnalysis.keywordScore,
            formatScore: mockBaseAnalysis.formatScore,
            contentScore: mockBaseAnalysis.contentScore,
            readabilityScore: mockBaseAnalysis.readabilityScore,
            detectedIssues: mockBaseAnalysis.detectedIssues,
            suggestedKeywords: mockBaseAnalysis.suggestedKeywords,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });
      }
      
      // Create job-specific analysis
      analysis = await prisma.jobSpecificATSAnalysis.create({
        data: {
          id: `job-ats-${Date.now()}`,
          atsAnalysisId: baseAnalysis.id,
          jobId,
          jobTitle: job.title,
          jobDescription: job.description || '',
          overallScore: mockAnalysis.overallScore,
          keywordScore: mockAnalysis.keywordScore,
          formatScore: mockAnalysis.formatScore,
          contentScore: mockAnalysis.contentScore,
          readabilityScore: mockAnalysis.readabilityScore,
          matchPercentage: mockAnalysis.matchPercentage,
          detectedIssues: mockAnalysis.detectedIssues,
          suggestedKeywords: mockAnalysis.suggestedKeywords,
          recommendations: mockAnalysis.recommendations,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }

    return json({ success: true, analysis });
  } catch (error) {
    console.error('Error getting job-specific ATS analysis:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

/**
 * Generate mock base ATS analysis
 */
function generateMockBaseATSAnalysis() {
  return {
    overallScore: 75,
    keywordScore: 80,
    formatScore: 70,
    contentScore: 85,
    readabilityScore: 65,
    detectedIssues: [
      { section: 'format', message: 'Resume exceeds one page', severity: 'medium' },
      { section: 'content', message: 'Summary section is too generic', severity: 'medium' },
      { section: 'readability', message: 'Some sentences are too long', severity: 'low' }
    ],
    suggestedKeywords: [
      'JavaScript',
      'React',
      'Node.js',
      'TypeScript',
      'REST API',
      'GraphQL',
      'CI/CD',
      'Agile',
      'AWS',
      'Docker'
    ]
  };
}

/**
 * Generate mock job-specific ATS analysis
 * 
 * In a real implementation, this would call the ATS analysis worker
 */
function generateMockJobSpecificATSAnalysis(jobTitle: string) {
  // Adjust scores based on job title
  let matchPercentage = 70;
  let keywordScore = 75;
  
  if (jobTitle.toLowerCase().includes('senior')) {
    matchPercentage = 65;
    keywordScore = 70;
  } else if (jobTitle.toLowerCase().includes('junior')) {
    matchPercentage = 80;
    keywordScore = 85;
  }
  
  return {
    overallScore: 75,
    keywordScore,
    formatScore: 70,
    contentScore: 85,
    readabilityScore: 65,
    matchPercentage,
    detectedIssues: [
      { section: 'format', message: 'Resume exceeds one page', severity: 'medium' },
      { section: 'content', message: 'Summary section is too generic', severity: 'medium' },
      { section: 'readability', message: 'Some sentences are too long', severity: 'low' },
      { section: 'match', message: `Missing key skills required for ${jobTitle}`, severity: 'high' }
    ],
    suggestedKeywords: [
      'JavaScript',
      'React',
      'Node.js',
      'TypeScript',
      'REST API',
      'GraphQL',
      'CI/CD',
      'Agile',
      'AWS',
      'Docker'
    ],
    recommendations: [
      `Highlight your experience with technologies mentioned in the ${jobTitle} job description`,
      'Add more quantifiable achievements to demonstrate impact',
      'Include specific projects relevant to the role',
      'Tailor your summary to match the job requirements'
    ]
  };
}
