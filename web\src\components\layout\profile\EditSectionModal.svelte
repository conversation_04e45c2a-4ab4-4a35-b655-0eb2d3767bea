<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Save } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';

  // Props
  const { 
    open, 
    sectionTitle, 
    onClose, 
    onSave 
  } = $props<{
    open: boolean;
    sectionTitle: string;
    onClose: () => void;
    onSave: (data: any) => Promise<boolean>;
  }>();

  // Handle save
  async function handleSave(event: Event) {
    try {
      event.preventDefault();
      
      // This will be called by the parent component
      // which will pass the form data to the onSave callback
      const form = event.target as HTMLFormElement;
      const formData = new FormData(form);
      const data = Object.fromEntries(formData.entries());
      
      const success = await onSave(data);
      
      if (success) {
        toast.success(`${sectionTitle} updated successfully`);
        onClose();
      }
    } catch (error) {
      console.error(`Error saving ${sectionTitle}:`, error);
      toast.error(`Failed to save ${sectionTitle}`);
    }
  }
</script>

<Dialog.Root {open} onOpenChange={onClose}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="sm:max-w-[600px]">
      <Dialog.Header>
        <Dialog.Title>Edit {sectionTitle}</Dialog.Title>
        <Dialog.Description>
          Make changes to your {sectionTitle.toLowerCase()}.
        </Dialog.Description>
      </Dialog.Header>

      <div class="py-4">
        <slot name="form" {handleSave}></slot>
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={onClose}>Cancel</Button>
        <Button type="submit" form="edit-section-form" class="ml-2">
          <Save class="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
