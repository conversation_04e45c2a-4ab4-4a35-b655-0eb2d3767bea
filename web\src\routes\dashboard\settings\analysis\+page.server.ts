import { error } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';

export const load = async ({ locals }) => {
  // Ensure user is authenticated
  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  try {
    // Get user's applications
    const applications = await prisma.application
      .findMany({
        where: { userId: locals.user.id },
        include: {
          interviewStages: true, // Include interview stages instead of non-existent job relation
        },
        orderBy: { createdAt: 'desc' },
        take: 100, // Limit to recent applications
      })
      .catch((err: any) => {
        console.error('Error fetching applications:', err);
        return [];
      });

    // Get user's resumes through documents
    const resumes = await prisma.resume
      .findMany({
        where: {
          document: {
            userId: locals.user.id,
          },
        },
        include: {
          document: true,
        },
        orderBy: { updatedAt: 'desc' },
      })
      .catch((err: any) => {
        console.error('Error fetching resumes:', err);
        return [];
      });

    // In the future, we'll fetch real market data from an API or database

    return {
      user: locals.user,
      applications,
      resumes,
    };
  } catch (err) {
    console.error('Error loading analysis data:', err);
    throw error(500, 'Failed to load analysis data');
  }
};
