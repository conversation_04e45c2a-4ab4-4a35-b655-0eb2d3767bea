<!--
  Feature Usage Dashboard

  This component displays the user's feature usage in a user-friendly way.
  It shows progress bars for feature limits and provides a summary of usage.
-->
<script lang="ts">
  import { onMount } from 'svelte';
  import type {
    FeatureWithDetailedUsage,
    UserFeatureUsageWithPlan,
  } from '$lib/services/feature-service';
  import { FeatureCategory, FeatureAccessLevel } from '$lib/models/features/features';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
  import { AlertCircleIcon } from 'lucide-svelte';

  // Import our custom components
  import UsageSummary from './UsageSummary.svelte';
  import FeatureCategoryAccordion from './FeatureCategoryAccordion.svelte';
  import FeatureTracker from './FeatureTracker.svelte';
  import RefreshButton from './RefreshButton.svelte';
  import ResetButton from './ResetButton.svelte';

  // State
  let loading = true;
  let error: string | null = null;
  let usageData: UserFeatureUsageWithPlan | null = null;
  let usageSummary: any = null;
  let featuresByCategory: Record<string, FeatureWithDetailedUsage[]> = {};
  let categories: string[] = [];
  let allFeatures: FeatureWithDetailedUsage[] = [];
  let resumeUsage: { used: number; limit: number | null; remaining: number | null } | null = null;

  // Load the usage data
  async function loadUsageData() {
    loading = true;
    error = null;

    try {
      console.log('Loading feature usage data...');

      // Fetch data from our local API endpoint
      const response = await fetch('/dashboard/usage?type=all');

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('API call complete:', data);

      // Process feature usage data
      if (data.features) {
        usageData = data.features;
        console.log('Feature usage data:', usageData);

        if (usageData.error) {
          error = usageData.error;
        } else if (!usageData.features || usageData.features.length === 0) {
          error = 'No feature usage data available.';
        } else {
          // Store all features
          allFeatures = usageData.features;

          // Group features by category
          featuresByCategory = {};
          usageData.features.forEach((feature) => {
            const category = feature.category || 'other';
            if (!featuresByCategory[category]) {
              featuresByCategory[category] = [];
            }
            featuresByCategory[category].push(feature);
          });

          // Sort categories
          categories = Object.keys(featuresByCategory).sort((a, b) => {
            // Put core category first
            if (a === FeatureCategory.Core) return -1;
            if (b === FeatureCategory.Core) return 1;
            return a.localeCompare(b);
          });
        }

        // Generate usage summary
        usageSummary = {
          totalFeatures: allFeatures.length,
          featuresUsed: allFeatures.filter((f) => f.limits.some((l) => l.used > 0)).length,
          featuresWithLimits: allFeatures.filter((f) =>
            f.limits.some((l) => l.value !== 'unlimited')
          ).length,
          featuresAtLimit: allFeatures.filter((f) =>
            f.limits.some(
              (l) => l.value !== 'unlimited' && typeof l.value === 'number' && l.used >= l.value
            )
          ).length,
          topFeatures: [],
        };
        console.log('Usage summary data:', usageSummary);
      }

      // Process resume usage data
      if (data.resume) {
        resumeUsage = data.resume;
        console.log('Resume usage data:', resumeUsage);

        // Add resume usage to feature data if available
        if (resumeUsage && resumeUsage.used !== undefined && allFeatures) {
          // Check if we already have a resume scanner feature
          let resumeScannerFeature = allFeatures.find((f) => f.id === 'resume_scanner');

          if (resumeScannerFeature) {
            // Update the existing feature with the resume usage data
            const resumeScansLimit = resumeScannerFeature.limits.find(
              (l) => l.id === 'resume_scans_per_month'
            );
            if (resumeScansLimit) {
              resumeScansLimit.used = resumeUsage.used || 0;
              resumeScansLimit.value = resumeUsage.limit || 'unlimited';
              resumeScansLimit.remaining = resumeUsage.remaining || null;
              resumeScansLimit.percentUsed =
                resumeUsage.limit && resumeUsage.used !== undefined
                  ? Math.min(100, (resumeUsage.used / resumeUsage.limit) * 100)
                  : null;
            } else {
              // Add the limit if it doesn't exist
              resumeScannerFeature.limits.push({
                id: 'resume_scans_per_month',
                name: 'Resume Scans',
                description: 'Number of resumes you can scan per month',
                type: 'monthly',
                unit: 'scans',
                value: resumeUsage.limit || 'unlimited',
                used: resumeUsage.used || 0,
                remaining: resumeUsage.remaining || null,
                percentUsed:
                  resumeUsage.limit && resumeUsage.used !== undefined
                    ? Math.min(100, (resumeUsage.used / resumeUsage.limit) * 100)
                    : null,
                period: new Date().toISOString().substring(0, 7), // YYYY-MM
                lastUpdated: new Date(),
              });
            }
          } else if (allFeatures) {
            // Create a new resume scanner feature
            resumeScannerFeature = {
              id: 'resume_scanner',
              name: 'Resume Scanner',
              description: 'Scan and analyze resumes',
              category: 'resume',
              accessLevel: FeatureAccessLevel.Limited,
              limits: [
                {
                  id: 'resume_scans_per_month',
                  name: 'Resume Scans',
                  description: 'Number of resumes you can scan per month',
                  type: 'monthly',
                  unit: 'scans',
                  value: resumeUsage.limit || 'unlimited',
                  used: resumeUsage.used || 0,
                  remaining: resumeUsage.remaining || null,
                  percentUsed:
                    resumeUsage.limit && resumeUsage.used !== undefined
                      ? Math.min(100, (resumeUsage.used / resumeUsage.limit) * 100)
                      : null,
                  period: new Date().toISOString().substring(0, 7), // YYYY-MM
                  lastUpdated: new Date(),
                },
              ],
            };

            // Add the new feature to allFeatures
            allFeatures.push(resumeScannerFeature);

            // Add the feature to the appropriate category
            const category = 'resume';
            if (!featuresByCategory[category]) {
              featuresByCategory[category] = [];
              // Update categories array
              categories = [...categories, category].sort((a, b) => {
                // Put core category first
                if (a === FeatureCategory.Core) return -1;
                if (b === FeatureCategory.Core) return 1;
                return a.localeCompare(b);
              });
            }
            featuresByCategory[category].push(resumeScannerFeature);
          }
        }
      }
    } catch (err) {
      console.error('Error loading feature usage data:', err);
      error = err.message || 'Failed to load feature usage data.';
    } finally {
      console.log('Setting loading to false');
      loading = false;
    }
  }

  // Handle refresh button click
  function handleRefresh() {
    console.log('Refresh button clicked, loading usage data...');
    loadUsageData();
  }

  onMount(() => {
    console.log('Component mounted, loading usage data...');
    // Use setTimeout to ensure the component is fully mounted before loading data
    setTimeout(() => {
      loadUsageData();
    }, 100);
  });
</script>

<div class="container mx-auto p-6">
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-3xl font-bold tracking-tight">Feature Usage</h2>
        <p class="text-muted-foreground">
          Track your feature usage and limits based on your subscription plan.
        </p>
      </div>

      <div class="flex items-center gap-2">
        <ResetButton onReset={handleRefresh} />
        <RefreshButton {loading} onRefresh={handleRefresh} />
      </div>
    </div>

    <!-- Loading state -->
    {#if loading}
      <div class="space-y-4">
        <Skeleton class="h-[200px] w-full" />
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Skeleton class="h-[150px] w-full" />
          <Skeleton class="h-[150px] w-full" />
          <Skeleton class="h-[150px] w-full" />
        </div>
      </div>
    {:else if error}
      <!-- Error state -->
      <Alert variant="destructive">
        <AlertCircleIcon class="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    {:else}
      <!-- Usage summary -->
      {#if usageSummary}
        <UsageSummary {usageSummary} {resumeUsage} />
      {/if}

      <!-- Loading state debug -->
      <div class="text-muted-foreground mb-4 text-xs">
        <div class="flex items-center gap-2">
          <span>Loading state: {loading ? 'Loading...' : 'Complete'}</span>
          {#if loading}
            <div class="h-2 w-2 animate-pulse rounded-full bg-blue-500"></div>
          {/if}
        </div>
      </div>

      <!-- Feature tracker and categories -->
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <div class="lg:col-span-1">
          <FeatureTracker features={allFeatures} onTrackSuccess={handleRefresh} />

          <!-- Debug section (remove in production) -->
          <div class="mt-4 rounded-md border p-4">
            <h3 class="mb-2 text-sm font-semibold">Debug Information</h3>
            <div class="space-y-2">
              <div>
                <h4 class="text-xs font-medium">Resume Usage:</h4>
                <pre class="bg-muted overflow-auto rounded p-2 text-xs">
                  {resumeUsage
                    ? JSON.stringify(resumeUsage, null, 2)
                    : 'No resume usage data available'}
                </pre>
              </div>
              <div>
                <h4 class="text-xs font-medium">API Status:</h4>
                <ul class="text-xs">
                  <li>
                    Loading: <span
                      class={loading ? 'font-semibold text-yellow-500' : 'text-green-500'}
                      >{loading ? 'Yes' : 'No'}</span>
                  </li>
                  <li>
                    Error: <span class={error ? 'font-semibold text-red-500' : 'text-green-500'}
                      >{error ? error : 'None'}</span>
                  </li>
                  <li>
                    Features loaded: <span
                      class={allFeatures && allFeatures.length > 0
                        ? 'text-green-500'
                        : 'font-semibold text-red-500'}
                      >{allFeatures ? allFeatures.length : 0}</span>
                  </li>
                  <li>
                    Categories: <span
                      class={categories.length > 0
                        ? 'text-green-500'
                        : 'font-semibold text-red-500'}
                      >{categories.length > 0 ? categories.join(', ') : 'None'}</span>
                  </li>
                  <li>
                    Usage data: <span
                      class={usageData ? 'text-green-500' : 'font-semibold text-red-500'}
                      >{usageData ? 'Loaded' : 'Not loaded'}</span>
                  </li>
                  <li>
                    Usage summary: <span
                      class={usageSummary ? 'text-green-500' : 'font-semibold text-red-500'}
                      >{usageSummary ? 'Loaded' : 'Not loaded'}</span>
                  </li>
                  <li>
                    Resume usage: <span
                      class={resumeUsage ? 'text-green-500' : 'font-semibold text-red-500'}
                      >{resumeUsage ? 'Loaded' : 'Not loaded'}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="lg:col-span-2">
          <!-- Features by category -->
          <FeatureCategoryAccordion {categories} {featuresByCategory} onReset={handleRefresh} />
        </div>
      </div>
    {/if}
  </div>
</div>
