// File: web/src/routes/api/worker-process/+server.ts
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { prisma } from '$lib/server/prisma';
import { logger } from '$lib/server/logger';
import { createNotification, NotificationType } from '$lib/server/notifications';

/**
 * GET handler to retrieve worker process information
 *
 * This endpoint allows retrieving information about worker processes,
 * either a specific one by ID or all processes of a certain type.
 */
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Get the user from locals
    const user = locals.user;
    if (!user) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Get query parameters
    const id = url.searchParams.get('id');
    const type = url.searchParams.get('type');
    const status = url.searchParams.get('status');

    // Build the query
    const query: any = {};

    // If ID is provided, get a specific worker process
    if (id) {
      query.id = id;
    }

    // If type is provided, filter by type
    if (type) {
      query.type = type;
    }

    // If status is provided, filter by status
    if (status) {
      query.status = status;
    }

    // Execute the query
    let workerProcesses;
    if (id) {
      // Get a specific worker process
      workerProcesses = await prisma.workerProcess.findUnique({
        where: { id },
      });

      if (!workerProcesses) {
        return json({ error: 'Worker process not found' }, { status: 404 });
      }
    } else {
      // Get all worker processes matching the criteria
      workerProcesses = await prisma.workerProcess.findMany({
        where: query,
        orderBy: { createdAt: 'desc' },
      });
    }

    return json({ success: true, workerProcesses });
  } catch (error) {
    logger.error('Error retrieving worker process:', error);
    return json(
      {
        success: false,
        error: 'Failed to retrieve worker process',
        message: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
};

/**
 * POST handler to create a new worker process
 *
 * This endpoint allows creating a new worker process record in the database.
 */
export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    // Get the user from locals - in development mode, allow without authentication
    let user = locals.user;
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (!user && !isDevelopment) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    const { type, status, data } = body;

    if (!type) {
      return json({ error: 'Worker process type is required' }, { status: 400 });
    }

    if (!status) {
      return json({ error: 'Worker process status is required' }, { status: 400 });
    }

    // Generate a unique ID if not provided
    const id = body.id ?? `wp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    // Create the worker process
    const workerProcess = await prisma.workerProcess.create({
      data: {
        id,
        type,
        status,
        data: typeof data === 'string' ? data : JSON.stringify(data ?? {}),
        createdAt: new Date(),
        updatedAt: new Date(),
        startedAt: status === 'PENDING' ? null : new Date(),
      },
    });

    // Handle resume parsing process - create notification and broadcast message
    if (type === 'resume-parsing') {
      try {
        // Extract resumeId and profileId from data
        let resumeId = null;
        let profileId = null;

        if (typeof data === 'object') {
          resumeId = data.resumeId ?? null;
          profileId = data.profileId ?? null;
        } else if (typeof data === 'string') {
          try {
            const parsedData = JSON.parse(data);
            resumeId = parsedData.resumeId ?? null;
            profileId = parsedData.profileId ?? null;
          } catch (parseError) {
            logger.error('Error parsing data string:', parseError);
          }
        }

        // If we have a resumeId but no user ID, try to get it from the resume
        if (resumeId && user?.id === 'system') {
          try {
            // Get the resume to find the user ID
            const resume = await prisma.resume.findUnique({
              where: { id: resumeId },
              include: { document: true },
            });

            if (resume?.document?.userId) {
              // Update the user ID for this request
              user = { ...user, id: resume.document.userId };
              logger.info(`Found user ID ${resume.document.userId} for resume ${resumeId}`);
            }
          } catch (userIdError) {
            logger.error('Error getting user ID from resume:', userIdError);
            // Continue even if we can't get the user ID
          }
        }

        if (resumeId) {
          // Create a notification in the database if we have a user
          if (user?.id) {
            await createNotification({
              userId: user.id,
              title: 'Resume Parsing Started',
              message: 'Your resume is being parsed. This may take a few moments.',
              type: NotificationType.INFO,
              data: {
                resumeId,
                profileId,
                workerProcessId: id,
              },
            });
          }

          logger.info(
            `Created resume parsing notification for user ${user?.id ?? 'system'}, resume ${resumeId}`
          );
        }
      } catch (error) {
        logger.error('Error creating notification or broadcasting message:', error);
        // Continue even if notification creation fails
      }
    }

    return json({
      success: true,
      workerProcess,
      message: `Worker process created with ID ${id}`,
    });
  } catch (error) {
    logger.error('Error creating worker process:', error);
    return json(
      {
        success: false,
        error: 'Failed to create worker process',
        message: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
};

/**
 * PATCH handler to update a worker process
 *
 * This endpoint allows updating an existing worker process record.
 */
export const PATCH: RequestHandler = async ({ request, locals }) => {
  try {
    // Get the user from locals - in development mode, allow without authentication
    let user = locals.user;
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (!user && !isDevelopment) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    const { id, status, data } = body;

    if (!id) {
      return json({ error: 'Worker process ID is required' }, { status: 400 });
    }

    // Build the update data
    const updateData: any = {};

    if (status) {
      updateData.status = status;

      // Set timestamps based on status
      if (status === 'PROCESSING' && !body.startedAt) {
        updateData.startedAt = new Date();
      } else if (status === 'COMPLETED' || status === 'FAILED') {
        updateData.completedAt = new Date();
      }
    }

    if (data !== undefined) {
      updateData.data = typeof data === 'string' ? data : JSON.stringify(data ?? {});
    }

    if (body.error !== undefined) {
      updateData.error = body.error;
    }

    // Update the worker process
    const workerProcess = await prisma.workerProcess.update({
      where: { id },
      data: updateData,
    });

    // If this is a resume parsing process and status is COMPLETED or FAILED, create a notification
    if (
      workerProcess.type === 'resume-parsing' &&
      (status === 'COMPLETED' || status === 'FAILED')
    ) {
      try {
        // Get the data from the worker process
        let resumeId = null;
        let profileId = null;
        let parsedData = null;

        // Parse the data field
        if (typeof workerProcess.data === 'string') {
          try {
            const data = JSON.parse(workerProcess.data);
            resumeId = data.resumeId ?? null;
            profileId = data.profileId ?? null;
            parsedData = data.parsedData ?? null;
          } catch (parseError) {
            logger.error('Error parsing worker process data:', parseError);
          }
        } else if (
          typeof workerProcess.data === 'object' &&
          workerProcess.data !== null &&
          !Array.isArray(workerProcess.data)
        ) {
          const dataObj = workerProcess.data as Record<string, any>;
          resumeId = dataObj.resumeId ?? null;
          profileId = dataObj.profileId ?? null;
          parsedData = dataObj.parsedData ?? null;
        }

        // If we have a resumeId but no user ID, try to get it from the resume
        if (resumeId && user?.id === 'system') {
          try {
            // Get the resume to find the user ID
            const resume = await prisma.resume.findUnique({
              where: { id: resumeId },
              include: { document: true },
            });

            if (resume?.document?.userId) {
              // Update the user ID for this request
              user = { ...user, id: resume.document.userId };
              logger.info(`Found user ID ${resume.document.userId} for resume ${resumeId}`);
            }
          } catch (userIdError) {
            logger.error('Error getting user ID from resume:', userIdError);
            // Continue even if we can't get the user ID
          }
        }

        if (resumeId) {
          // Create a notification in the database
          if (user?.id) {
            await createNotification({
              userId: user?.id ?? 'system',
              title: status === 'COMPLETED' ? 'Resume Parsing Completed' : 'Resume Parsing Failed',
              message:
                status === 'COMPLETED'
                  ? 'Your resume has been successfully parsed.'
                  : 'There was an error parsing your resume. Please try again.',
              type: status === 'COMPLETED' ? NotificationType.SUCCESS : NotificationType.ERROR,
              data: {
                resumeId,
                profileId,
                workerProcessId: id,
                parsedData: status === 'COMPLETED' ? parsedData : null,
              },
            });
          }
          logger.info(
            `Created resume parsing ${status.toLowerCase()} notification for user ${user?.id ?? 'system'}, resume ${resumeId}`
          );
        }
      } catch (notificationError) {
        logger.error('Error creating notification or broadcasting message:', notificationError);
        // Continue even if notification creation fails
      }
    }

    return json({
      success: true,
      workerProcess,
      message: `Worker process ${id} updated`,
    });
  } catch (error) {
    logger.error('Error updating worker process:', error);
    return json(
      {
        success: false,
        error: 'Failed to update worker process',
        message: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
};
