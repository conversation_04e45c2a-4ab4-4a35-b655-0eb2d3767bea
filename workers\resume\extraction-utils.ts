/**
 * Utility functions for improved resume extraction
 */

/**
 * Common section headers used to identify different parts of a resume
 */
export const SECTION_HEADERS = {
  PROFILE: [
    "PROFILE",
    "PERSONAL INFORMATION",
    "CONTACT",
    "CONTACT INFORMATION",
  ],
  EDUCATION: [
    "ED<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON> BACKGROUND",
    "ACAD<PERSON>IC HISTORY",
    "EDUCATIONAL BACKGROUND",
    "DEGREES",
  ],
  EXPERIENCE: [
    "WORK EXPERIENCE",
    "EXPERIENCE",
    "EMPLOYMENT HISTORY",
    "PROFESSIONAL EXPERIENCE",
    "WORK HISTORY",
  ],
  SKILLS: [
    "SKILLS",
    "TECHNICAL SKILLS",
    "CORE SKILLS",
    "KEY SKILLS",
    "PROFESSIONAL SKILLS",
    "COMPETENCIES",
  ],
  PROJECTS: [
    "PROJECTS",
    "PERSONAL PROJECTS",
    "SIDE PROJECTS",
    "PRO<PERSON><PERSON><PERSON>ON<PERSON> PROJECTS",
    "KEY PROJECTS",
  ],
  CERTIFICATIONS: [
    "CERTIFICATIONS",
    "CERTIFICATES",
    "PROFESSIONAL CERTIFICATIONS",
    "LICENSES",
    "CREDENTIALS",
  ],
  LANGUAGES: [
    "LANGUAGES",
    "LANGUAGE PROFICIENCY",
    "LANGUAGE SKILLS",
    "SPOKEN LANGUAGES",
  ],
  PATENTS: ["PATENTS", "PATENT APPLICATIONS", "INTELLECTUAL PROPERTY", "IP"],
  PUBLICATIONS: [
    "PUBLICATIONS",
    "PUBLISHED WORKS",
    "RESEARCH PUBLICATIONS",
    "PAPERS",
    "ARTICLES",
  ],
  ACHIEVEMENTS: [
    "ACHIEVEMENTS",
    "AWARDS",
    "HONORS",
    "RECOGNITIONS",
    "ACCOMPLISHMENTS",
  ],
  VOLUNTEER: [
    "VOLUNTEER",
    "VOLUNTEER EXPERIENCE",
    "COMMUNITY SERVICE",
    "COMMUNITY INVOLVEMENT",
  ],
  INTERESTS: ["INTERESTS", "HOBBIES", "ACTIVITIES", "PERSONAL INTERESTS"],
  REFERENCES: ["REFERENCES", "PROFESSIONAL REFERENCES", "RECOMMENDATIONS"],
};

/**
 * Check if a line indicates the start of a new section
 * @param line Line to check
 * @returns True if the line is a section header
 */
export function isSectionHeader(line: string): boolean {
  const upperLine = line.toUpperCase().trim();
  return Object.values(SECTION_HEADERS).some((headers) =>
    headers.some(
      (header) =>
        upperLine === header ||
        upperLine === header + ":" ||
        upperLine === header + "S" ||
        upperLine === header + "S:"
    )
  );
}

/**
 * Extract a section from content using section headers
 * @param content Full resume content
 * @param sectionHeaders Headers that identify the section
 * @returns Lines in the section
 */
export function extractSectionFromContent(
  content: string,
  sectionHeaders: string[]
): string[] {
  const contentLines = content.split("\n").map((line) => line.trim());
  const sectionLines: string[] = [];
  let inSection = false;

  for (let i = 0; i < contentLines.length; i++) {
    const line = contentLines[i];
    const upperLine = line.toUpperCase().trim();

    // Check if this line is a header for our target section
    if (!inSection) {
      if (
        sectionHeaders.some(
          (header) =>
            upperLine === header ||
            upperLine === header + ":" ||
            upperLine === header + "S" ||
            upperLine === header + "S:"
        )
      ) {
        inSection = true;
        sectionLines.push(line);
        continue;
      }
    } else {
      // Check if we've reached the start of another section
      if (
        isSectionHeader(line) &&
        !sectionHeaders.some(
          (header) =>
            upperLine === header ||
            upperLine === header + ":" ||
            upperLine === header + "S" ||
            upperLine === header + "S:"
        )
      ) {
        break;
      }

      sectionLines.push(line);
    }
  }

  return sectionLines;
}

/**
 * Extract dates from a string
 * @param text Text to extract dates from
 * @returns Array of date strings
 */
export function extractDates(text: string): string[] {
  const datePatterns = [
    /\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{4}\s*(-|–|to)\s*(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{4}\b/gi,
    /\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{4}\s*(-|–|to)\s*Present\b/gi,
    /\b\d{4}\s*(-|–|to)\s*\d{4}\b/g,
    /\b\d{4}\s*(-|–|to)\s*Present\b/gi,
    /\b(January|February|March|April|May|June|July|August|September|October|November|December) \d{4}\b/gi,
  ];

  const dates: string[] = [];

  for (const pattern of datePatterns) {
    const matches = text.match(pattern);
    if (matches) {
      dates.push(...matches);
    }
  }

  return dates;
}

/**
 * Extract company name from a string
 * @param text Text to extract company name from
 * @returns Company name or null
 */
export function extractCompanyName(text: string): string | null {
  // Look for common company indicators
  const companyIndicators = [
    /([A-Z][a-zA-Z0-9\s&]+)(,|\s+Inc\.|\s+LLC|\s+Ltd\.|\s+Corp\.|\s+Corporation|\s+Company)/,
    /([A-Z][a-zA-Z0-9\s&]+),\s+([A-Z][a-zA-Z\s]+,\s+[A-Z]{2})/, // Company, City, State
  ];

  for (const pattern of companyIndicators) {
    const match = text.match(pattern);
    if (match && match[1]) {
      return match[1].trim();
    }
  }

  return null;
}

/**
 * Extract job title from a string
 * @param text Text to extract job title from
 * @returns Job title or null
 */
export function extractJobTitle(text: string): string | null {
  // Common job titles
  const jobTitles = [
    "Software Engineer",
    "Senior Software Engineer",
    "Lead Software Engineer",
    "Principal Software Engineer",
    "Software Developer",
    "Senior Software Developer",
    "Lead Software Developer",
    "Principal Software Developer",
    "Frontend Engineer",
    "Frontend Developer",
    "Backend Engineer",
    "Backend Developer",
    "Full Stack Engineer",
    "Full Stack Developer",
    "DevOps Engineer",
    "Site Reliability Engineer",
    "Data Scientist",
    "Data Engineer",
    "Machine Learning Engineer",
    "AI Engineer",
    "Product Manager",
    "Project Manager",
    "Program Manager",
    "Technical Program Manager",
    "UX Designer",
    "UI Designer",
    "UX/UI Designer",
    "Product Designer",
    "QA Engineer",
    "Quality Assurance Engineer",
    "Test Engineer",
    "Automation Engineer",
    "Systems Engineer",
    "Network Engineer",
    "Cloud Engineer",
    "Infrastructure Engineer",
    "Security Engineer",
    "Information Security Engineer",
    "Cybersecurity Engineer",
    "Technical Writer",
    "Documentation Specialist",
    "Content Developer",
    "Engineering Manager",
    "Technical Manager",
    "Development Manager",
    "IT Manager",
    "CTO",
    "CIO",
    "VP of Engineering",
    "Director of Engineering",
  ];

  // Check if any job title is in the text
  for (const title of jobTitles) {
    if (text.includes(title)) {
      return title;
    }
  }

  // Look for patterns like "Senior X Engineer" or "X Developer"
  const titlePatterns = [
    /(Senior|Lead|Principal|Staff|Junior)\s+([A-Z][a-z]+\s+)?(Engineer|Developer|Architect|Designer|Manager|Analyst|Consultant|Specialist)/,
    /([A-Z][a-z]+\s+)?(Engineer|Developer|Architect|Designer|Manager|Analyst|Consultant|Specialist)/,
  ];

  for (const pattern of titlePatterns) {
    const match = text.match(pattern);
    if (match) {
      return match[0].trim();
    }
  }

  return null;
}

/**
 * Extract degree from a string
 * @param text Text to extract degree from
 * @returns Degree or null
 */
export function extractDegree(text: string): string | null {
  // Common degrees
  const degrees = [
    "Bachelor of Science",
    "Bachelor of Arts",
    "Bachelor of Engineering",
    "Bachelor of Business Administration",
    "Master of Science",
    "Master of Arts",
    "Master of Engineering",
    "Master of Business Administration",
    "Doctor of Philosophy",
    "Ph.D.",
    "MBA",
    "B.S.",
    "B.A.",
    "M.S.",
    "M.A.",
    "B.E.",
    "M.E.",
  ];

  // Check if any degree is in the text
  for (const degree of degrees) {
    if (text.includes(degree)) {
      // Try to extract the full degree (e.g., "Bachelor of Science in Computer Science")
      const match = text.match(
        new RegExp(`${degree}\\s+in\\s+([A-Z][a-zA-Z\\s]+)`)
      );
      if (match && match[0]) {
        return match[0].trim();
      }
      return degree;
    }
  }

  // Look for patterns like "Bachelor's Degree in X" or "Master's in X"
  const degreePatterns = [
    /(Bachelor['']s|Master['']s|Doctoral)\s+(Degree\s+)?in\s+([A-Z][a-zA-Z\s]+)/,
    /(BS|BA|MS|MA|PhD|MD)\s+in\s+([A-Z][a-zA-Z\s]+)/,
  ];

  for (const pattern of degreePatterns) {
    const match = text.match(pattern);
    if (match) {
      return match[0].trim();
    }
  }

  return null;
}

/**
 * Extract school name from a string
 * @param text Text to extract school name from
 * @returns School name or null
 */
export function extractSchoolName(text: string): string | null {
  // Look for common school indicators
  const schoolPatterns = [
    /([A-Z][a-zA-Z\s]+University)/,
    /(University of [A-Z][a-zA-Z\s]+)/,
    /([A-Z][a-zA-Z\s]+ College)/,
    /([A-Z][a-zA-Z\s]+ Institute of Technology)/,
    /([A-Z][a-zA-Z\s]+ School of [A-Z][a-zA-Z\s]+)/,
  ];

  for (const pattern of schoolPatterns) {
    const match = text.match(pattern);
    if (match && match[0]) {
      return match[0].trim();
    }
  }

  return null;
}

/**
 * Extract skills from text
 * @param text Text to extract skills from
 * @returns Array of skills
 */
export function extractSkillsFromText(text: string): string[] {
  // Common programming languages and technologies
  const commonSkills = [
    "JavaScript",
    "TypeScript",
    "Python",
    "Java",
    "C#",
    "C++",
    "Ruby",
    "PHP",
    "Swift",
    "Kotlin",
    "Go",
    "Rust",
    "React",
    "Angular",
    "Vue",
    "Node.js",
    "Express",
    "Django",
    "Flask",
    "Spring",
    "ASP.NET",
    "Laravel",
    "HTML",
    "CSS",
    "SASS",
    "LESS",
    "Bootstrap",
    "Tailwind",
    "Material UI",
    "Chakra UI",
    "SQL",
    "MySQL",
    "PostgreSQL",
    "MongoDB",
    "Redis",
    "Elasticsearch",
    "DynamoDB",
    "Cassandra",
    "Oracle",
    "AWS",
    "Azure",
    "GCP",
    "Docker",
    "Kubernetes",
    "Terraform",
    "Jenkins",
    "GitHub Actions",
    "CircleCI",
    "Git",
    "SVN",
    "Mercurial",
    "Jira",
    "Confluence",
    "Trello",
    "Asana",
    "Notion",
    "Agile",
    "Scrum",
    "Kanban",
    "Waterfall",
    "TDD",
    "BDD",
    "CI/CD",
    "DevOps",
    "Machine Learning",
    "Deep Learning",
    "AI",
    "Data Science",
    "Data Analysis",
    "Data Visualization",
    "REST",
    "GraphQL",
    "gRPC",
    "WebSockets",
    "Microservices",
    "Serverless",
    "Blockchain",
  ];

  const skills: string[] = [];

  // Check for each common skill in the text
  for (const skill of commonSkills) {
    if (text.toLowerCase().includes(skill.toLowerCase())) {
      skills.push(skill);
    }
  }

  // Look for skill sections and extract individual skills
  const skillSections = text.match(/Skills:?\s*([^:]+)(?=\n|$)/gi);
  if (skillSections) {
    for (const section of skillSections) {
      // Remove category headers and labels
      const cleanedSection = section
        .replace(/Skills:?\s*/i, "")
        .replace(/Programming Languages:?\s*/i, "")
        .replace(/Frameworks & Libraries:?\s*/i, "")
        .replace(/Cloud & DevOps:?\s*/i, "")
        .replace(/Databases:?\s*/i, "")
        .replace(/Tools:?\s*/i, "")
        .replace(/Languages:?\s*/i, "")
        .replace(/Technologies:?\s*/i, "")
        .replace(/Software:?\s*/i, "");

      // Split by commas, bullets, or newlines
      const sectionItems = cleanedSection
        .split(/[,•\n]/)
        .map((s) => s.trim())
        .filter((s) => s.length > 0 && s.toLowerCase() !== "skills");

      // Process each skill to remove category labels
      for (let item of sectionItems) {
        // Skip if it's a category header
        if (
          /^(Programming Languages|Frameworks & Libraries|Cloud & DevOps|Databases|Tools|Languages|Technologies|Software):?$/i.test(
            item
          )
        ) {
          continue;
        }

        // Remove any remaining category prefixes
        item = item
          .replace(
            /^(Programming Languages|Frameworks & Libraries|Cloud & DevOps|Databases|Tools|Languages|Technologies|Software):\s*/i,
            ""
          )
          .trim();

        if (item.length > 0) {
          skills.push(item);
        }
      }
    }
  }

  // Remove duplicates
  return Array.from(new Set(skills));
}

/**
 * Extract location from text
 * @param text Text to extract location from
 * @returns Location or null
 */
export function extractLocation(text: string): string | null {
  // Look for common location patterns
  const locationPatterns = [
    /([A-Z][a-zA-Z\s]+,\s*[A-Z]{2})/g, // City, State
    /([A-Z][a-zA-Z\s]+,\s*[A-Z][a-zA-Z\s]+)/g, // City, Country
    /([A-Z][a-zA-Z\s]+,\s*[A-Z]{2}\s*\d{5})/g, // City, State ZIP
  ];

  for (const pattern of locationPatterns) {
    const matches = text.match(pattern);
    if (matches && matches.length > 0) {
      // Filter out matches that are likely not locations
      const validLocations = matches.filter(
        (loc) =>
          !loc.toLowerCase().includes("university") &&
          !loc.toLowerCase().includes("college") &&
          !loc.toLowerCase().includes("school") &&
          !loc.toLowerCase().includes("institute") &&
          !loc.toLowerCase().includes("academy")
      );

      if (validLocations.length > 0) {
        return validLocations[0];
      }
    }
  }

  return null;
}

/**
 * Extract email from text
 * @param text Text to extract email from
 * @returns Email or null
 */
export function extractEmail(text: string): string | null {
  const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
  const matches = text.match(emailPattern);

  if (matches && matches.length > 0) {
    return matches[0];
  }

  return null;
}

/**
 * Extract phone from text
 * @param text Text to extract phone from
 * @returns Phone or null
 */
export function extractPhone(text: string): string | null {
  const phonePatterns = [
    /\(\d{3}\)\s*\d{3}[-.\s]?\d{4}/g, // (*************
    /\d{3}[-.\s]?\d{3}[-.\s]?\d{4}/g, // ************
    /\+\d{1,3}\s?\(\d{3}\)\s*\d{3}[-.\s]?\d{4}/g, // +1 (*************
    /\+\d{1,3}\s?\d{3}[-.\s]?\d{3}[-.\s]?\d{4}/g, // ******-555-5555
  ];

  for (const pattern of phonePatterns) {
    const matches = text.match(pattern);
    if (matches && matches.length > 0) {
      return matches[0];
    }
  }

  return null;
}

/**
 * Extract URLs from text
 * @param text Text to extract URLs from
 * @returns Array of URLs
 */
export function extractURLs(text: string): string[] {
  const urlPatterns = [
    /https?:\/\/github\.com\/[a-zA-Z0-9_-]+/g,
    /https?:\/\/linkedin\.com\/in\/[a-zA-Z0-9_-]+/g,
    /https?:\/\/www\.linkedin\.com\/in\/[a-zA-Z0-9_-]+/g,
    /github\.com\/[a-zA-Z0-9_-]+/g,
    /linkedin\.com\/in\/[a-zA-Z0-9_-]+/g,
    /https?:\/\/[a-zA-Z0-9][-a-zA-Z0-9]*\.[a-zA-Z0-9][-a-zA-Z0-9]*\.[a-zA-Z]{2,}(\/\S*)?/g, // matches domains like example.com
    /https?:\/\/[a-zA-Z0-9][-a-zA-Z0-9]*\.[a-zA-Z]{2,}(\/\S*)?/g, // matches domains like example.com
    /([a-zA-Z0-9][-a-zA-Z0-9]*\.)+[a-zA-Z]{2,}(\/\S*)?/g, // matches bare domains without protocol
    /https?:\/\/\S+\.[a-zA-Z]{2,}\/\S+/g, // general URLs with paths
  ];

  const allUrls: string[] = [];

  for (const pattern of urlPatterns) {
    const matches = text.match(pattern);
    if (matches && matches.length > 0) {
      for (let url of matches) {
        // Add https:// if missing
        if (!url.startsWith("http")) {
          url = "https://" + url;
        }

        // Filter out email addresses which can be matched as URLs
        if (
          !url.includes("@") &&
          !url.includes("%PDF") &&
          !url.match(/^(stream|endstream|obj|endobj|xref|trailer|startxref)/) &&
          !url.match(/^https?:\/\/\d+\.\d+\.\d+\.\d+/) && // Filter IP addresses
          url.length < 100
        ) {
          // Avoid extremely long matches
          allUrls.push(url);
        }
      }
    }
  }

  // Remove duplicates
  return Array.from(new Set(allUrls));
}

/**
 * Extract URL from text (legacy function for backward compatibility)
 * @param text Text to extract URL from
 * @returns URL or null
 */
export function extractURL(text: string): string | null {
  const urls = extractURLs(text);
  return urls.length > 0 ? urls[0] : null;
}

/**
 * Extract skills from work experience descriptions
 * @param descriptions Array of work experience descriptions
 * @returns Array of skills
 */
export function extractSkillsFromDescriptions(
  descriptions: string[]
): string[] {
  const skills: string[] = [];

  // Common patterns for skills in descriptions
  const skillsPatterns = [
    /using\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /with\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /skills:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /technologies:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /tools:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /leveraging\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /utilizing\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /implemented\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /developed\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
  ];

  // Process each description
  for (const desc of descriptions) {
    // Check each pattern
    for (const pattern of skillsPatterns) {
      const match = desc.match(pattern);
      if (match && match[1]) {
        // Split the skills by commas and "and"
        const extractedSkills = match[1]
          .split(/,|\sand\s/)
          .map((s) => s.trim())
          .filter((s) => s.length > 0);

        // Add to the skills array
        skills.push(...extractedSkills);
      }
    }

    // Also check for common skills directly in the text
    const commonSkills = [
      "JavaScript",
      "TypeScript",
      "Python",
      "Java",
      "C#",
      "C++",
      "Ruby",
      "PHP",
      "React",
      "Angular",
      "Vue",
      "Node.js",
      "Express",
      "Django",
      "Flask",
      "Spring",
      "AWS",
      "Azure",
      "GCP",
      "Docker",
      "Kubernetes",
      "Git",
      "SQL",
      "MongoDB",
      "PostgreSQL",
      "MySQL",
      "Redis",
      "GraphQL",
      "REST",
      "API",
      "HTML",
      "CSS",
      "SASS",
      "LESS",
      "Bootstrap",
      "Tailwind",
      "Material UI",
      "Redux",
      "MobX",
      "jQuery",
      "WebSockets",
      "Microservices",
      "Serverless",
      "CI/CD",
      "Jenkins",
      "GitHub Actions",
      "Travis CI",
      "CircleCI",
      "Agile",
      "Scrum",
      "Kanban",
      "TDD",
      "BDD",
      "Jest",
      "Mocha",
      "Chai",
      "Cypress",
      "Selenium",
      "Webpack",
      "Babel",
      "ESLint",
      "Prettier",
      "npm",
      "yarn",
      "pnpm",
      "Vite",
      "Rollup",
      "esbuild",
      "Parcel",
      "Next.js",
      "Nuxt.js",
      "Gatsby",
      "SvelteKit",
      "Remix",
      "Electron",
      "React Native",
      "Flutter",
      "Swift",
      "Kotlin",
      "Objective-C",
      "Android",
      "iOS",
      "Xamarin",
      "Unity",
      "Unreal Engine",
      "TensorFlow",
      "PyTorch",
      "scikit-learn",
      "Pandas",
      "NumPy",
      "Matplotlib",
      "Jupyter",
      "R",
      "MATLAB",
      "Hadoop",
      "Spark",
      "Kafka",
      "Airflow",
      "Tableau",
      "Power BI",
      "D3.js",
      "Grafana",
      "Prometheus",
      "ELK Stack",
      "Logstash",
      "Kibana",
      "Elasticsearch",
      "Splunk",
      "New Relic",
      "Datadog",
      "Sentry",
      "Stripe",
      "PayPal",
      "Braintree",
      "Auth0",
      "Okta",
      "Firebase",
      "Supabase",
      "Amplify",
      "Vercel",
      "Netlify",
      "Heroku",
      "DigitalOcean",
      "Linode",
      "Vultr",
      "Cloudflare",
      "Akamai",
      "Fastly",
      "Terraform",
      "Ansible",
      "Chef",
      "Puppet",
      "Packer",
      "Vagrant",
      "Istio",
      "Envoy",
      "Linkerd",
      "Consul",
      "Vault",
      "Nginx",
      "Apache",
      "Caddy",
      "HAProxy",
      "RabbitMQ",
      "ActiveMQ",
      "ZeroMQ",
      "gRPC",
      "Protocol Buffers",
      "Thrift",
      "WebRTC",
      "Socket.io",
      "SignalR",
      "Pusher",
      "Ably",
      "PubNub",
      "Twilio",
      "SendGrid",
      "Mailchimp",
      "Mailgun",
      "Postmark",
      "Algolia",
      "Elasticsearch",
      "Solr",
      "Lucene",
      "Meilisearch",
      "Typesense",
      "OpenAI",
      "GPT",
      "BERT",
      "NLP",
      "Computer Vision",
      "Machine Learning",
      "Deep Learning",
      "AI",
    ];

    for (const skill of commonSkills) {
      if (desc.includes(skill)) {
        skills.push(skill);
      }
    }
  }

  // Remove duplicates and return
  return Array.from(new Set(skills));
}
