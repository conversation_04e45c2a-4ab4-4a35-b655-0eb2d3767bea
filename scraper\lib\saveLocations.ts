import { PrismaClient } from "@prisma/client";
import fs from "fs/promises";
import path from "path";
import { logger } from "../utils/logger";

const prisma = new PrismaClient();

/* ──────────────────────────────────────────────────────────────
   1️⃣  Country – we only care about the United States
   ────────────────────────────────────────────────────────────── */
export async function loadCountries() {
  const us = await prisma.country.upsert({
    where: { isoCode: "US" },
    update: {}, // no changes if it already exists
    create: { name: "United States", isoCode: "US" },
  });
  logger.debug("✅ Ensured country: United States (US)");
  return us;
}

/* ──────────────────────────────────────────────────────────────
   2️⃣  States – load from states.json and attach to the US row
   ────────────────────────────────────────────────────────────── */
export async function loadStates() {
  const statesPath = path.resolve("./utils/json/states.json");
  const rows: { name: string; abbreviation: string }[] = JSON.parse(
    await fs.readFile(statesPath, "utf-8")
  );

  const us = await loadCountries();

  let inserted = 0;

  for (const { name, abbreviation } of rows) {
    if (!name || !abbreviation) continue; // skip incomplete rows

    /* upsert by (countryId, code) so we never duplicate */
    const dbState = await prisma.state.upsert({
      where: {
        countryId_code: {
          countryId: us.id,
          code: abbreviation.toUpperCase(),
        },
      },
      update: {
        name, // keep name in sync if it changed
      },
      create: {
        name,
        code: abbreviation.toUpperCase(),
        countryId: us.id,
      },
    });

    if (dbState.createdAt.getTime() === Date.now()) {
      inserted++; // count only fresh inserts
    }

    logger.debug(`✅ State upserted: ${name} (${abbreviation})`);
  }

  logger.info(`✅ Loaded ${inserted} new states (processed ${rows.length})`);
}
