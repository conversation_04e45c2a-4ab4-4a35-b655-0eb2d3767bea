// cron/lib/notifications/jobMatchNotifier.ts
import { redis } from "../../utils/redis.js";
import { logger } from "../../utils/logger.js";

/**
 * Queue an in-app notification for a job match
 *
 * This function sends a notification to the notification service
 * via Redis to notify a user about a job match.
 */
export async function queueJobMatchNotification(params: {
  userId: string;
  jobId: string;
  jobTitle: string;
  companyName: string;
  matchScore: number;
  alertName?: string;
}) {
  try {
    const { userId, jobId, jobTitle, companyName, matchScore, alertName } =
      params;

    // Format the notification data
    const notificationData = {
      userId,
      title: alertName ? `New match for "${alertName}"` : "New Job Match",
      message: `${jobTitle} at ${companyName} matches your profile with a score of ${Math.round(matchScore * 100)}%`,
      url: `/dashboard/jobs/${jobId}`,
      type: "job",
      priority: matchScore > 0.8 ? "high" : "medium",
      metadata: {
        jobId,
        matchScore,
        jobTitle,
        companyName,
        alertName,
      },
    };

    // Add to Redis queue for notification service
    await redis.xadd(
      "notifications:job-matches",
      "*",
      "notification",
      JSON.stringify(notificationData)
    );

    logger.info(
      `✅ [Job Match Notifier] Queued in-app notification for user ${userId} about job ${jobId}`
    );
    return true;
  } catch (error) {
    logger.error(
      "❌ [Job Match Notifier] Failed to queue job match notification:",
      error
    );
    return false;
  }
}

/**
 * Queue in-app notifications for multiple job matches
 */
export async function queueJobMatchesNotification(params: {
  userId: string;
  alertName: string;
  matchCount: number;
  alertId: string;
}) {
  try {
    const { userId, alertName, matchCount, alertId } = params;

    // Format the notification data
    const notificationData = {
      userId,
      title: `New matches for "${alertName}"`,
      message: `Your job alert "${alertName}" has ${matchCount} new matching jobs`,
      url: `/dashboard/matches?alert=${alertId}`,
      type: "job",
      priority: "medium",
      metadata: {
        alertId,
        alertName,
        matchCount,
      },
    };

    // Add to Redis queue for notification service
    await redis.xadd(
      "notifications:job-matches",
      "*",
      "notification",
      JSON.stringify(notificationData)
    );

    logger.info(
      `✅ [Job Match Notifier] Queued in-app notification for user ${userId} about ${matchCount} job matches`
    );
    return true;
  } catch (error) {
    logger.error(
      "❌ [Job Match Notifier] Failed to queue job matches notification:",
      error
    );
    return false;
  }
}
