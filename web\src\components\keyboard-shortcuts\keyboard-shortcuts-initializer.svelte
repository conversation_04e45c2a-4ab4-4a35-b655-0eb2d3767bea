<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import {
    initKeyboardShortcuts,
    cleanupKeyboardShortcuts,
  } from '$lib/keyboard-shortcuts/shortcuts-store';
  import { getAllShortcuts } from '$lib/keyboard-shortcuts/shortcuts-registry';

  // Initialize keyboard shortcuts when component mounts
  onMount(() => {
    if (browser) {
      // Initialize keyboard shortcuts
      initKeyboardShortcuts();

      // Log available shortcuts for debugging
      console.log('Available keyboard shortcuts:', getAllShortcuts());

      // Set up global keyboard event handler for Alt+/ to open shortcuts dialog
      const handleAltSlash = (event: KeyboardEvent) => {
        if (event.altKey && event.key === '/') {
          event.preventDefault();
          document.dispatchEvent(new CustomEvent('toggle-keyboard-shortcuts'));
        }
      };

      // Add event listener for Alt+/ (this is a special case to ensure the shortcuts dialog can always be opened)
      document.addEventListener('keydown', handleAltSlash);

      // Cleanup function
      return () => {
        cleanupKeyboardShortcuts();
        document.removeEventListener('keydown', handleAltSlash);
      };
    }
  });
</script>

<!-- This is a utility component with no UI -->
