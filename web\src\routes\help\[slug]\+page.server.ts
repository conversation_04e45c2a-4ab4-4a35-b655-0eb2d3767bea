// src/routes/help/[slug]/+page.server.ts
import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import {
  getHelpArticleBySlug,
  getHelpArticlesByCategory,
  getHelpArticles,
} from '$lib/sanity/client';

export const load = (async ({ params }) => {
  const { slug } = params;

  try {
    // Fetch the article
    const article = await getHelpArticleBySlug(slug);

    if (!article) {
      throw error(404, 'Article not found');
    }

    // Note: We can't increment view count directly in Sanity from here
    // This would need to be done through a separate API endpoint that updates the Sanity document

    // Fetch related articles in the same category
    const categoryArticles = await getHelpArticlesByCategory(article.category);

    // Filter out the current article and limit to 5
    const relatedCategoryArticles = categoryArticles
      .filter((a) => a._id !== article._id)
      .slice(0, 5);

    // Fetch all articles to build the sidebar categories
    const allArticles = await getHelpArticles();

    // Group articles by category for the sidebar
    const articlesByCategory = allArticles.reduce((acc, article) => {
      if (!acc[article.category]) {
        acc[article.category] = {
          name: getCategoryName(article.category),
          slug: article.category,
          icon: getCategoryIcon(article.category),
          articles: [],
        };
      }

      acc[article.category].articles.push({
        id: article._id,
        title: article.title,
        slug: article.slug.current,
      });

      return acc;
    }, {});

    // Convert to array and sort categories
    const categories = Object.values(articlesByCategory).sort((a, b) =>
      a.name.localeCompare(b.name)
    );

    return {
      article,
      categoryArticles: relatedCategoryArticles,
      categories,
    };
  } catch (err) {
    console.error(`Error loading help article ${slug}:`, err);
    throw error(404, 'Article not found');
  }
}) satisfies PageServerLoad;

// Helper function to get category name from slug
function getCategoryName(slug: string): string {
  const categoryMap = {
    'getting-started': 'Getting Started',
    'auto-apply': 'Using Auto Apply',
    'account-billing': 'Account & Billing',
    troubleshooting: 'Troubleshooting',
    'privacy-security': 'Privacy & Security',
  };

  return categoryMap[slug as keyof typeof categoryMap] || slug;
}

// Helper function to get category icon from slug
function getCategoryIcon(slug: string): string {
  const iconMap = {
    'getting-started': 'BookOpen',
    'auto-apply': 'FileText',
    'account-billing': 'CreditCard',
    troubleshooting: 'HelpCircle',
    'privacy-security': 'Shield',
  };

  return iconMap[slug as keyof typeof iconMap] || 'HelpCircle';
}
