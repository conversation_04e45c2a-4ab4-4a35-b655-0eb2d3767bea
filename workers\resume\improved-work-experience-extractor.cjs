/**
 * Improved Work Experience Extractor
 * 
 * This module provides functions for extracting work experience from resume text.
 * It focuses on accurately identifying job titles, companies, dates, and descriptions.
 */

const fs = require('fs');
const path = require('path');
const mammoth = require('mammoth');

/**
 * Extract text from a DOCX file
 * @param {string} filePath - Path to the DOCX file
 * @returns {Promise<string>} - Extracted text
 */
async function extractTextFromDocx(filePath) {
  try {
    console.log(`Reading DOCX file: ${filePath}`);
    const result = await mammoth.extractRawText({ path: filePath });
    return result.value;
  } catch (error) {
    console.error(`Error extracting text: ${error.message}`);
    throw error;
  }
}

/**
 * Extract skills from work experience descriptions
 * @param {string[]} descriptions - Array of work experience descriptions
 * @returns {string[]} - Array of skills
 */
function extractSkillsFromDescriptions(descriptions) {
  const skills = [];
  
  // Common patterns for skills in descriptions
  const skillsPatterns = [
    /using\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /with\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /skills:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /technologies:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /tools:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
  ];
  
  // Process each description
  for (const desc of descriptions) {
    if (!desc) continue;
    
    // Check each pattern
    for (const pattern of skillsPatterns) {
      const match = desc.match(pattern);
      if (match && match[1]) {
        // Split the skills by commas and "and"
        const extractedSkills = match[1]
          .split(/,|\sand\s/)
          .map(s => s.trim())
          .filter(s => s.length > 0);
        
        // Add to the skills array
        skills.push(...extractedSkills);
      }
    }
    
    // Also check for common skills directly in the text
    const commonSkills = [
      "JavaScript", "TypeScript", "Python", "Java", "C#", "C++", "Ruby", "PHP",
      "React", "Angular", "Vue", "Node.js", "Express", "Django", "Flask", "Spring",
      "AWS", "Azure", "GCP", "Docker", "Kubernetes", "Git", "SQL", "MongoDB",
      "PostgreSQL", "MySQL", "Redis", "GraphQL", "REST", "API", "HTML", "CSS",
      "SASS", "LESS", "Bootstrap", "Tailwind", "Material UI", "Redux", "MobX",
      "jQuery", "WebSockets", "Microservices", "Serverless", "CI/CD", "Jenkins",
      "GitHub Actions", "Travis CI", "CircleCI", "Agile", "Scrum", "Kanban",
      "TDD", "BDD", "Jest", "Mocha", "Chai", "Cypress", "Selenium", "Webpack"
    ];
    
    for (const skill of commonSkills) {
      if (desc.includes(skill)) {
        skills.push(skill);
      }
    }
  }
  
  // Remove duplicates and return
  return Array.from(new Set(skills));
}

/**
 * Parse date string into a structured format
 * @param {string} dateStr - Date string (e.g., "01/2020 - Present" or "January 2020 - December 2021")
 * @returns {Object} - Structured date object with startDate and endDate
 */
function parseDate(dateStr) {
  if (!dateStr) {
    return { startDate: null, endDate: null };
  }
  
  // Handle various date formats
  const dateFormats = [
    // MM/YYYY - MM/YYYY or Present
    /(\d{1,2}\/\d{4})\s*[-–]\s*(\d{1,2}\/\d{4}|Present|Current)/i,
    // Month YYYY - Month YYYY or Present
    /(\w+ \d{4})\s*[-–]\s*(\w+ \d{4}|Present|Current)/i,
    // YYYY - YYYY or Present
    /(\d{4})\s*[-–]\s*(\d{4}|Present|Current)/i,
  ];
  
  for (const regex of dateFormats) {
    const match = dateStr.match(regex);
    if (match) {
      return {
        startDate: match[1].trim(),
        endDate: match[2].trim(),
      };
    }
  }
  
  // If no match, return the original string as startDate
  return {
    startDate: dateStr.trim(),
    endDate: null,
  };
}

/**
 * Extract work experience from resume text
 * @param {string} text - Resume text
 * @returns {Array<Object>} - Array of work experience objects
 */
function extractWorkExperience(text) {
  if (!text) return [];
  
  const lines = text.split('\n').filter(line => line && line.trim().length > 0);
  
  // Regular expressions for job titles, companies, and dates
  const jobTitleRegex = /(?:senior|lead|principal|junior|staff)?\s*(?:software|frontend|backend|fullstack|full stack|web|mobile|cloud|devops|data|machine learning|ml|ai|qa|test|security|network|systems|database|ui|ux|product|project|program|technical|solutions|application|systems|infrastructure|network|cloud|database|security|support|help desk|customer|client|sales|marketing|business|finance|hr|human resources|legal|administrative|executive|c-level|ceo|cto|cio|cfo|coo|vp|director|manager|supervisor|team lead|tech lead|architect|engineer|developer|programmer|analyst|specialist|consultant|administrator|technician|designer|researcher|scientist|strategist)\s*(?:engineer|developer|programmer|analyst|specialist|consultant|administrator|technician|designer|researcher|scientist|strategist|manager|director|lead|architect|officer)?/i;
  
  const dateRegex = /(?:\d{1,2}\/\d{4}|\w+ \d{4})\s*[-–]\s*(?:\d{1,2}\/\d{4}|\w+ \d{4}|Present|Current)/i;
  const shortDateRegex = /\d{2}\/\d{4}\s*[-–]\s*(?:\d{2}\/\d{4}|Present|Current)/i;
  const yearRangeRegex = /\d{4}\s*[-–]\s*(?:\d{4}|Present|Current)/i;
  const companyLocationRegex = /([^|]+)\s*\|\s*([^|]+)/;
  const technologiesRegex = /Technologies:\s*(.*)/i;
  
  // Find the "Work Experience" or "Professional Experience" section
  let experienceStartIndex = -1;
  let educationStartIndex = -1;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim().toLowerCase();
    if (line === "work experience" || line === "professional experience" || line === "experience") {
      experienceStartIndex = i + 1;
    } else if (line === "education" || line === "academic background") {
      educationStartIndex = i;
      break;
    }
  }
  
  // If we couldn't find the sections, use some heuristics
  if (experienceStartIndex === -1) {
    // Look for the first job title after the summary
    for (let i = 10; i < lines.length; i++) {
      if (companyLocationRegex.test(lines[i]) && jobTitleRegex.test(lines[i-1])) {
        experienceStartIndex = i - 1;
        break;
      }
    }
  }
  
  if (educationStartIndex === -1) {
    // Look for education keywords
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim().toLowerCase();
      if (line.includes("university") || line.includes("college") || line.includes("bachelor") || 
          line.includes("master") || line.includes("degree") || line.includes("education")) {
        educationStartIndex = i;
        break;
      }
    }
  }
  
  // If we still couldn't find the sections, use default values
  if (experienceStartIndex === -1) experienceStartIndex = 10; // Skip header
  if (educationStartIndex === -1) educationStartIndex = lines.length;
  
  // Process only the work experience section
  const experienceLines = lines.slice(experienceStartIndex, educationStartIndex);
  
  // Debug: Print the experience section
  console.log("\nExperience Section Lines:");
  for (let i = 0; i < Math.min(experienceLines.length, 20); i++) {
    console.log(`${i}: ${experienceLines[i]}`);
  }
  
  // For this specific resume format, we know the structure is:
  // Job Title
  // Company | Location
  // Date Range
  // Description bullet points
  
  // Extract work experience entries
  const workExperience = [];
  let currentJob = null;
  let descriptions = [];
  
  for (let i = 0; i < experienceLines.length; i++) {
    const line = experienceLines[i].trim();
    
    // Check if this line contains a company|location pattern
    const companyLocationMatch = line.match(companyLocationRegex);
    
    // Check if this line contains a date range
    const hasDateRange = dateRegex.test(line) || shortDateRegex.test(line) || yearRangeRegex.test(line);
    
    // If we find a job title pattern, it's likely the start of a new job
    if (jobTitleRegex.test(line) && line.length < 100) {
      // If we already have a job, save it before starting a new one
      if (currentJob) {
        currentJob.descriptions = descriptions;
        currentJob.skills = extractSkillsFromDescriptions(descriptions);
        workExperience.push(currentJob);
        descriptions = [];
      }
      
      // Start a new job
      currentJob = {
        title: line,
        company: "",
        location: "",
        startDate: null,
        endDate: null,
      };
      
      // Look ahead for company|location and date
      if (i + 1 < experienceLines.length && companyLocationRegex.test(experienceLines[i+1])) {
        const match = experienceLines[i+1].match(companyLocationRegex);
        currentJob.company = match[1].trim();
        currentJob.location = match[2].trim();
        i++; // Skip the company line
        
        // Look ahead for date
        if (i + 1 < experienceLines.length && 
            (dateRegex.test(experienceLines[i+1]) || 
             shortDateRegex.test(experienceLines[i+1]) || 
             yearRangeRegex.test(experienceLines[i+1]))) {
          const dateInfo = parseDate(experienceLines[i+1].trim());
          currentJob.startDate = dateInfo.startDate;
          currentJob.endDate = dateInfo.endDate;
          i++; // Skip the date line
        }
      }
    }
    // If we find a company|location pattern, it might be part of a job
    else if (companyLocationMatch && !hasDateRange) {
      if (currentJob && !currentJob.company) {
        currentJob.company = companyLocationMatch[1].trim();
        currentJob.location = companyLocationMatch[2].trim();
        
        // Look ahead for date
        if (i + 1 < experienceLines.length && 
            (dateRegex.test(experienceLines[i+1]) || 
             shortDateRegex.test(experienceLines[i+1]) || 
             yearRangeRegex.test(experienceLines[i+1]))) {
          const dateInfo = parseDate(experienceLines[i+1].trim());
          currentJob.startDate = dateInfo.startDate;
          currentJob.endDate = dateInfo.endDate;
          i++; // Skip the date line
        }
      }
    }
    // If we find a date range, it might be part of a job
    else if (hasDateRange) {
      if (currentJob && !currentJob.startDate) {
        const dateInfo = parseDate(line);
        currentJob.startDate = dateInfo.startDate;
        currentJob.endDate = dateInfo.endDate;
      }
    }
    // If we're in a job and this line looks like a bullet point or description
    else if (currentJob) {
      // Look for bullet points or numbered lists
      if (line.startsWith('•') || line.startsWith('-') || line.startsWith('*') || /^\d+\./.test(line)) {
        descriptions.push(line.replace(/^[•\-*\d\.]+\s*/, ''));
      }
      // If it's a longer line and doesn't look like a job title, it might be a description
      else if (line.length > 30 && !jobTitleRegex.test(line) && !hasDateRange) {
        descriptions.push(line);
      }
    }
  }
  
  // Add the last job if there is one
  if (currentJob) {
    currentJob.descriptions = descriptions;
    currentJob.skills = extractSkillsFromDescriptions(descriptions);
    workExperience.push(currentJob);
  }
  
  // Filter out entries that don't look like real jobs
  const filteredExperience = workExperience.filter(job => {
    // Must have either a title or company
    if (!job.title && !job.company) return false;
    
    // Skip entries that are just technologies
    if (job.title.toLowerCase().startsWith('technologies:')) return false;
    
    // Skip entries that are just references
    if (job.title.toLowerCase().includes('reference')) return false;
    
    return true;
  });
  
  return filteredExperience;
}

// Main function to test the extractor
async function main() {
  try {
    // Get the file path from command line arguments
    const filePath = process.argv[2] || '../web/static/uploads/resumes/Resume.docx';
    
    // Make sure the file exists
    try {
      await fs.promises.access(filePath, fs.constants.F_OK);
      console.log(`File exists: ${filePath}`);
    } catch (error) {
      console.error(`File does not exist: ${filePath}`);
      process.exit(1);
    }
    
    // Extract text from the DOCX file
    const text = await extractTextFromDocx(filePath);
    
    // Extract work experience
    const workExperience = extractWorkExperience(text);
    
    // Print the results
    console.log('\nExtracted Work Experience:');
    console.log(JSON.stringify(workExperience, null, 2));
    
    console.log(`\nFound ${workExperience.length} work experiences`);
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the main function if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  extractWorkExperience,
  parseDate,
  extractSkillsFromDescriptions
};
