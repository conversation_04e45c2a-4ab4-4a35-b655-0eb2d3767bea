# Base image with <PERSON><PERSON> installed (updated to match package.json version)
FROM mcr.microsoft.com/playwright:v1.52.0-jammy

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application
COPY . .

# Generate Prisma client and install tsx globally
RUN npx prisma generate && npm install -g tsx

# Set environment variables
ENV NODE_ENV=production
ENV TZ=America/New_York

# Start the scraper service
CMD ["npm", "start"]
