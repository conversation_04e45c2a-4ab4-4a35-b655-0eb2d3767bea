<script lang="ts">
  import * as Accordion from '$lib/components/ui/accordion/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { AlertCircle, MessageSquare } from 'lucide-svelte';
  import type { Incident, StatusTagType } from './types';
  import StatusTag from '$components/system-status/StatusTag.svelte';
  import SeverityBadge from '$components/system-status/SeverityBadge.svelte';
  import StatusBar from '$components/system-status/StatusBar.svelte';

  // Props
  const { incident, index } = $props<{
    incident: Incident;
    index: number;
  }>();

  // Format date
  function formatDate(date: Date | string): string {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  }

  // Format date for display (Apr 29, 2023)
  function formatDateWithYear(date: Date | string): string {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(new Date(date));
  }

  // Calculate progress for the status bar
  function calculateProgress(): number {
    if (!incident.startTime || !incident.endTime) return 0;

    const start = new Date(incident.startTime).getTime();
    const end = new Date(incident.endTime).getTime();
    const now = Date.now();

    if (now <= start) return 0;
    if (now >= end) return 100;

    return Math.round(((now - start) / (end - start)) * 100);
  }

  // Get badge variant for legacy components
  function getBadgeVariant(
    status: string
  ): 'success' | 'warning' | 'secondary' | 'default' | 'destructive' | 'outline' {
    switch (status) {
      case 'completed':
      case 'resolved':
        return 'success';
      case 'in-progress':
      case 'monitoring':
        return 'warning';
      case 'scheduled':
      case 'identified':
        return 'secondary';
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  }
</script>

<div class="overflow-hidden rounded-lg border">
  <Accordion.Item value={`incident-${index}`} class="border-0">
    <div class="bg-gray-50 dark:bg-gray-900">
      <Accordion.Trigger class="flex w-full items-center justify-between p-4 text-left">
        <div class="flex w-full items-center gap-4 pr-4">
          <div
            class="flex h-8 w-8 items-center justify-center rounded-full bg-red-100 dark:bg-red-900">
            <AlertCircle class="h-4 w-4 text-red-500 dark:text-red-400" />
          </div>
          <div class="flex w-full flex-col gap-1">
            <h3 class="text-lg font-medium">{incident.title}</h3>
            <div class="mt-1 flex items-center justify-between gap-2">
              <div class="flex flex-wrap gap-2">
                <StatusTag status={incident.status as StatusTagType} />
                {#if incident.severity}
                  <SeverityBadge severity={incident.severity} />
                {/if}
              </div>
              <span class="text-muted-foreground text-xs">
                Started {formatDate(incident.date || incident.startTime)}
              </span>
            </div>
          </div>
        </div>
      </Accordion.Trigger>
    </div>

    <Accordion.Content class="p-4 pt-0">
      <div class="border-t pt-4">
        <!-- Status Bar -->
        {#if incident.startTime && incident.endTime}
          <div class="mb-4">
            <div class="mb-2 flex items-center justify-between">
              <div class="text-xs text-gray-500">
                {formatDateWithYear(incident.startTime)} → {formatDateWithYear(incident.endTime)}
              </div>
              <StatusTag status={incident.status as StatusTagType} />
            </div>
            <StatusBar
              startTime={incident.startTime}
              endTime={incident.endTime}
              status={incident.status}
              progress={incident.progress || calculateProgress()}
              showTimes={false} />
          </div>
        {/if}

        <p class="whitespace-pre-line text-sm">{incident.description}</p>

        {#if incident.affectedServices && incident.affectedServices.length > 0}
          <div class="mt-4">
            <h4 class="mb-2 text-sm font-medium">Affected Services</h4>
            <div class="flex flex-wrap gap-2">
              {#each incident.affectedServices as service}
                <Badge variant="outline">{service}</Badge>
              {/each}
            </div>
          </div>
        {/if}

        {#if incident.updates && incident.updates.length > 0}
          <div class="mt-6">
            <h4 class="mb-3 text-sm font-medium">Updates</h4>
            <div class="space-y-4">
              {#each incident.updates as update}
                <div
                  class="relative pl-6 before:absolute before:left-0 before:top-0 before:h-full before:w-0.5 before:bg-gray-200 dark:before:bg-gray-800">
                  <div
                    class="absolute left-[-4px] top-1 h-2 w-2 rounded-full bg-gray-300 dark:bg-gray-700">
                  </div>
                  <div class="mb-1 flex items-center justify-between">
                    <div class="text-muted-foreground text-xs">
                      {formatDate(update.date || update.timestamp)}
                    </div>
                    {#if update.status}
                      <StatusTag status={update.status as StatusTagType} className="ml-2" />
                    {/if}
                  </div>
                  <p class="text-sm">{update.message}</p>

                  {#if update.comments && update.comments.length > 0}
                    <div class="mt-3 border-t pt-2">
                      <h5 class="mb-2 flex items-center gap-1 text-xs font-medium">
                        <MessageSquare class="h-3 w-3" />
                        Comments
                      </h5>
                      <div class="space-y-2">
                        {#each update.comments as comment}
                          <div class="bg-muted rounded-sm p-2 text-xs">
                            <div class="mb-1 flex items-center justify-between">
                              <span class="font-medium">{comment.author || 'System'}</span>
                              <span class="text-muted-foreground text-[10px]">
                                {new Date(comment.timestamp).toLocaleString()}
                              </span>
                            </div>
                            <p>{comment.text}</p>
                          </div>
                        {/each}
                      </div>
                    </div>
                  {/if}
                </div>
              {/each}
            </div>
          </div>
        {/if}
      </div>
    </Accordion.Content>
  </Accordion.Item>
</div>
