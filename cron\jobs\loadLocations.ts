// cron/scripts/loadLocations.ts

import { logger } from "../utils/logger";
import fs from "fs/promises";
import path from "path";
import { loadCountries, loadStates } from "../lib/saveLocations";

const COUNTRIES = path.resolve("../cron/utils/json/countries.json");
const STATES = path.resolve("../cron/utils/json/states.json");

async function run() {
  try {
    logger.info("🔁 Starting location taxonomy sync...");

    const countriesExists = await fs.stat(COUNTRIES).catch(() => null);
    const statesExists = await fs.stat(STATES).catch(() => null);

    if (!countriesExists || !statesExists) {
      if (!countriesExists) logger.error(`❌ Missing file: ${COUNTRIES}`);
      if (!statesExists) logger.error(`❌ Missing file: ${STATES}`);
      return;
    }

    logger.info("🚀 Seeding countries and states...");
    await loadCountries();
    await loadStates();

    logger.info("✅ Location taxonomy sync complete.");
  } catch (err) {
    logger.error("❌ Failed to sync location taxonomy:", err);
  }
}

await run();
