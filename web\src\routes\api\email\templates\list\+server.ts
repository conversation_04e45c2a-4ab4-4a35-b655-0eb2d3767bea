/**
 * Email Templates List API Endpoint
 * 
 * This endpoint provides a list of available email templates.
 */

import { json } from '@sveltejs/kit';
import { logger } from '$lib/server/logger';
import { EmailTemplate } from '$lib/server/email/types';

export async function GET() {
  try {
    // Return a list of all available templates
    const allTemplates = [
      {
        name: EmailTemplate.WELCOME,
        label: 'Welcome Email',
        category: 'Transactional',
        description: 'Sent to new users after registration',
      },
      {
        name: EmailTemplate.VERIFICATION,
        label: 'Email Verification',
        category: 'Transactional',
        description: 'Sent to verify a user\'s email address',
      },
      {
        name: EmailTemplate.PASSWORD_RESET,
        label: 'Password Reset',
        category: 'Transactional',
        description: 'Sent when a user requests a password reset',
      },
      {
        name: EmailTemplate.PASSWORD_CHANGED,
        label: 'Password Changed',
        category: 'Transactional',
        description: 'Sent when a user changes their password',
      },
      {
        name: EmailTemplate.JOB_APPLICATION_SUBMITTED,
        label: 'Job Application Submitted',
        category: 'Notification',
        description: 'Sent when a user submits a job application',
      },
      {
        name: EmailTemplate.JOB_APPLICATION_STATUS_UPDATE,
        label: 'Job Application Status Update',
        category: 'Notification',
        description: 'Sent when a job application status changes',
      },
      {
        name: EmailTemplate.RESUME_OPTIMIZATION_COMPLETE,
        label: 'Resume Optimization Complete',
        category: 'Notification',
        description: 'Sent when resume optimization is complete',
      },
      {
        name: EmailTemplate.WEEKLY_SUMMARY,
        label: 'Weekly Summary',
        category: 'Marketing',
        description: 'Weekly summary of activity',
      },
      {
        name: EmailTemplate.TEST_TEMPLATE,
        label: 'Test Template',
        category: 'System',
        description: 'Used for testing email delivery',
      },
    ];

    // Get unique categories
    const categories = [...new Set(allTemplates.map(template => template.category))];

    return json({
      allTemplates,
      categories,
    });
  } catch (error) {
    logger.error('Error listing templates:', error);
    return json({ error: 'Failed to list templates' }, { status: 500 });
  }
}
