version: '3'
services:
  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    volumes:
      - ollama_data:/root/.ollama
    ports:
      - "11434:11434"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

  ai-service:
    build: .
    container_name: ai-service
    environment:
      - NODE_ENV=production
      - PORT=3100
      - OLLAMA_URL=http://ollama:11434
      - LLM_MODEL=mistral
      - HEALTH_CHECK_INTERVAL=300000
      - METRICS_INTERVAL=60000
      - MEMORY_THRESHOLD=85
      - CPU_THRESHOLD=85
      - RESET_THRESHOLD=70
    ports:
      - "3100:3100"
    depends_on:
      ollama:
        condition: service_healthy
    restart: unless-stopped

volumes:
  ollama_data:
