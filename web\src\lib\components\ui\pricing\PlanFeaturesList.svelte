<script lang="ts">
  import { Check } from 'lucide-svelte';
  import type { PlanTier } from '$lib/types/billing';
  import { onMount } from 'svelte';

  const {
    plan = null,
    compact = false,
    showCount = true,
  } = $props<{
    plan?: PlanTier | null;
    compact?: boolean;
    showCount?: boolean;
  }>();

  // Store for enabled features
  let enabledFeatures = $state<any[]>([]);
  let loading = $state(true);

  // Initialize on mount
  onMount(() => {
    // Set loading to false and update features directly from the plan
    loading = false;
    updateEnabledFeatures();
  });

  // Always update when plan changes, regardless of allFeatures
  $effect(() => {
    if (plan) {
      console.log('[snapshot] Updating enabled features from plan:', $state.snapshot(plan));
      // Don't log the proxy directly to avoid warnings
      updateEnabledFeatures();
    }
  });

  // Function to update enabled features directly from the plan prop
  function updateEnabledFeatures() {
    if (!plan) {
      console.warn('Plan is null or undefined');
      enabledFeatures = [];
      return;
    }

    // Log the complete plan object for debugging
    console.log('Full plan object in PlanFeaturesList:', JSON.stringify(plan));

    if (!plan.features || !Array.isArray(plan.features)) {
      console.warn('Plan features are missing or not an array:', plan);
      // Create default features based on the plan ID if available
      if (plan.id) {
        console.log('Creating default features for plan ID:', plan.id);
        // Create default features based on the plan ID
        const defaultFeatures = [
          { featureId: 'dashboard', accessLevel: 'included' },
          { featureId: 'profile', accessLevel: 'included' },
          { featureId: 'resume_scanner', accessLevel: 'included' },
          { featureId: 'resume_builder', accessLevel: 'included' },
          { featureId: 'resume_ai', accessLevel: 'included' },
          { featureId: 'job_search_profiles', accessLevel: 'included' },
          { featureId: 'application_tracker', accessLevel: 'included' },
        ];

        // Map default features to display format
        enabledFeatures = defaultFeatures.map((planFeature: any) => {
          // Format the feature name from the ID
          const featureName = formatFeatureName(planFeature.featureId);

          return {
            ...planFeature,
            name: featureName,
            description: '',
            id: planFeature.featureId,
            icon: null,
          };
        });
      } else {
        enabledFeatures = [];
      }
      return;
    }

    console.log('[snapshot] Updating enabled features from plan object:', $state.snapshot(plan));
    console.log('Updating enabled features from plan object:', plan);

    // Get the enabled plan features (those with included, limited, or unlimited access)
    const enabledPlanFeatures = plan.features.filter((f: any) => {
      // Check if the feature has an accessLevel property
      if (!f.accessLevel) {
        console.warn('Feature missing accessLevel:', f);
        return false;
      }

      // Include features with these access levels
      return (
        f.accessLevel === 'included' || f.accessLevel === 'limited' || f.accessLevel === 'unlimited'
      );
    });

    console.log('[snapshot] Filtered enabled features:', $state.snapshot(enabledPlanFeatures));
    console.log('Filtered enabled features:', enabledPlanFeatures);

    // If no enabled features, create some default ones based on the plan ID
    if (enabledPlanFeatures.length === 0 && plan.id) {
      console.log('No enabled features found, creating defaults for plan:', plan.id);

      // Create default features based on the plan ID
      const defaultFeatures = [
        { featureId: 'dashboard', accessLevel: 'included' },
        { featureId: 'profile', accessLevel: 'included' },
        { featureId: 'resume_scanner', accessLevel: 'included' },
        { featureId: 'resume_builder', accessLevel: 'included' },
        { featureId: 'resume_ai', accessLevel: 'included' },
        { featureId: 'job_search_profiles', accessLevel: 'included' },
        { featureId: 'application_tracker', accessLevel: 'included' },
      ];

      // Map default features to display format
      enabledFeatures = defaultFeatures.map((planFeature: any) => {
        // Format the feature name from the ID
        const featureName = formatFeatureName(planFeature.featureId);

        return {
          ...planFeature,
          name: featureName,
          description: '',
          id: planFeature.featureId,
          icon: null,
        };
      });

      return;
    }

    // Map plan features directly to display format without database lookup
    enabledFeatures = enabledPlanFeatures.map((planFeature: any) => {
      // Format the feature name from the ID
      const featureName = formatFeatureName(planFeature.featureId);

      // Return a display-ready object
      return {
        ...planFeature,
        name: featureName,
        description: '',
        id: planFeature.featureId,
        icon: null,
      };
    });
  }

  // Helper function to format feature names from IDs
  function formatFeatureName(featureId: string): string {
    if (!featureId) return 'Unknown Feature';

    // Convert snake_case to Title Case
    return featureId
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // Log for debugging
  $effect(() => {
    if (plan) {
      console.log('[snapshot] Plan features:', $state.snapshot(plan.features));
      console.log('Plan features:', plan.features);
      console.log('[snapshot] Enabled features:', $state.snapshot(enabledFeatures));
      console.log('Enabled features:', enabledFeatures);
    }
  });

  // Get usage limits for display with type safety
  const usageLimits = $derived(
    plan?.limits || {
      resumesPerMonth: undefined,
      seats: undefined,
      profiles: undefined,
    }
  );

  // Helper function to get limit value as string
  function getLimitValue(limit: any): string {
    if (!limit) return '';
    if (typeof limit === 'string') return limit;
    if (typeof limit === 'number') return limit.toString();
    if (typeof limit === 'object' && limit.value !== undefined) {
      return limit.value.toString();
    }
    return '';
  }
</script>

<div class="space-y-2">
  {#if loading}
    <p class="text-muted-foreground text-sm">Loading features...</p>
  {:else if enabledFeatures.length > 0}
    <ul class="space-y-2">
      {#each enabledFeatures as feature}
        <li class="flex items-center justify-between text-sm">
          <div class="flex items-center">
            <Check class="mr-2 h-4 w-4 text-green-500" />
            <span>{feature.name}</span>

            {#if !compact && feature.description}
              <span class="text-muted-foreground ml-2 text-xs">
                {feature.description}
              </span>
            {/if}
          </div>

          {#if showCount && feature.id === 'resume_scanner' && usageLimits.resumesPerMonth}
            <span class="text-muted-foreground text-xs font-medium">
              {getLimitValue(usageLimits.resumesPerMonth)}
            </span>
          {/if}

          {#if showCount && feature.id === 'team_collaboration' && usageLimits.seats}
            <span class="text-muted-foreground text-xs font-medium">
              {getLimitValue(usageLimits.seats)}
              {getLimitValue(usageLimits.seats) === '1' ? 'seat' : 'seats'}
            </span>
          {/if}

          {#if showCount && feature.id === 'application_tracker' && usageLimits.profiles}
            <span class="text-muted-foreground text-xs font-medium">
              {getLimitValue(usageLimits.profiles)}
              {getLimitValue(usageLimits.profiles) === '1' ? 'profile' : 'profiles'}
            </span>
          {/if}
        </li>
      {/each}
    </ul>
  {:else}
    <p class="text-muted-foreground text-sm">No features available</p>
  {/if}
</div>
