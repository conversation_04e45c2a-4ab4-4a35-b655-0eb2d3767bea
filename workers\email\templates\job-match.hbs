<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>New Job Matches for You</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #ffffff;
    }
    .header {
      text-align: center;
      padding: 20px 0;
      border-bottom: 1px solid #eaeaea;
    }
    .logo {
      max-width: 150px;
      margin-bottom: 10px;
    }
    .content {
      padding: 20px 0;
    }
    .job-card {
      border: 1px solid #eaeaea;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
      background-color: #f9f9f9;
    }
    .job-title {
      font-size: 18px;
      font-weight: bold;
      color: #2563eb;
      margin-top: 0;
      margin-bottom: 5px;
    }
    .company {
      font-size: 16px;
      font-weight: 500;
      margin-top: 0;
      margin-bottom: 10px;
    }
    .job-details {
      margin-bottom: 15px;
    }
    .detail-row {
      display: flex;
      margin-bottom: 5px;
    }
    .detail-label {
      width: 100px;
      font-weight: 500;
      color: #666;
    }
    .match-score {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-weight: bold;
      background-color: #dcfce7;
      color: #166534;
    }
    .cta-button {
      display: inline-block;
      padding: 10px 20px;
      background-color: #2563eb;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      font-weight: 500;
      margin-top: 10px;
    }
    .other-jobs {
      margin-top: 30px;
      border-top: 1px solid #eaeaea;
      padding-top: 20px;
    }
    .other-job-item {
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #f0f0f0;
    }
    .other-job-title {
      font-weight: 500;
      color: #2563eb;
      text-decoration: none;
    }
    .footer {
      text-align: center;
      padding: 20px 0;
      border-top: 1px solid #eaeaea;
      font-size: 12px;
      color: #666;
    }
    .unsubscribe {
      color: #666;
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="{{appUrl}}/logo.png" alt="{{appName}} Logo" class="logo">
      <h1>New Job Matches</h1>
    </div>
    
    <div class="content">
      <p>Hello {{firstName}},</p>
      
      {{#if alertName}}
      <p>We found new job matches for your alert: <strong>{{alertName}}</strong></p>
      {{else}}
      <p>We found new job matches that might be perfect for you!</p>
      {{/if}}
      
      <div class="job-card">
        <h2 class="job-title">{{jobTitle}}</h2>
        <p class="company">{{companyName}}</p>
        
        <div class="job-details">
          <div class="detail-row">
            <span class="detail-label">Location:</span>
            <span>{{location}}</span>
          </div>
          
          {{#if salary}}
          <div class="detail-row">
            <span class="detail-label">Salary:</span>
            <span>{{salary}}</span>
          </div>
          {{/if}}
          
          <div class="detail-row">
            <span class="detail-label">Match Score:</span>
            <span class="match-score">{{matchScore}}%</span>
          </div>
        </div>
        
        {{#if jobDescription}}
        <p>{{jobDescription}}</p>
        {{/if}}
        
        <a href="{{jobUrl}}" class="cta-button">View Job Details</a>
      </div>
      
      {{#if otherJobs}}
      <div class="other-jobs">
        <h3>Other Matches You Might Like</h3>
        
        {{#each otherJobs}}
        <div class="other-job-item">
          <a href="{{this.url}}" class="other-job-title">{{this.title}}</a>
          <p>{{this.company}} • {{this.location}}</p>
        </div>
        {{/each}}
      </div>
      {{/if}}
      
      <p>
        Log in to your account to see all your job matches and set up more job alerts.
      </p>
      
      <a href="{{appUrl}}/dashboard/matches" class="cta-button">View All Matches</a>
    </div>
    
    <div class="footer">
      <p>
        &copy; {{year}} {{appName}}. All rights reserved.
      </p>
      <p>
        You're receiving this email because you signed up for job alerts.
        <a href="{{unsubscribeUrl}}" class="unsubscribe">Unsubscribe</a>
      </p>
    </div>
  </div>
</body>
</html>
