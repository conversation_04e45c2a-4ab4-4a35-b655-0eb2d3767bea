<script>
  import { Button } from '$lib/components/ui/button';
  import { goto } from '$app/navigation';
  import { Lock, AlertTriangle } from 'lucide-svelte';
  import { createFeatureAccess } from '$lib/models/features';
  import { onMount } from 'svelte';

  // Props
  export let userData;
  export let featureId;
  export let limitId = undefined;
  export let showUpgradeButton = true;
  export let upgradeButtonText = 'Upgrade Plan';
  export let upgradeButtonLink = '/dashboard/settings/billing';
  export let limitReachedMessage = 'You have reached the limit for this feature.';
  export let notIncludedMessage = 'This feature is not included in your current plan.';

  // Feature access logic
  let featureAccess;
  let hasAccess = true; // Always true in development mode
  let hasReachedLimit = false; // Always false in development mode
  let message = '';
  let isDevelopment = true; // Set to true to bypass feature checks

  // Initialize feature access only if not in development mode
  onMount(() => {
    if (!isDevelopment) {
      updateFeatureAccess();
    }
  });

  // Update when props change (only if not in development mode)
  $: if (!isDevelopment && userData && featureId) updateFeatureAccess();
  $: if (!isDevelopment && limitId) updateFeatureAccess();

  function updateFeatureAccess() {
    try {
      if (userData) {
        featureAccess = createFeatureAccess(userData);
        hasAccess = featureAccess.hasAccess(featureId);

        if (hasAccess && limitId) {
          hasReachedLimit = featureAccess.hasReachedLimit(featureId, limitId);

          if (hasReachedLimit) {
            const limitValue = featureAccess.getLimitValue(featureId, limitId);
            message = `${limitReachedMessage} (Limit: ${limitValue})`;
          } else {
            message = '';
          }
        } else if (!hasAccess) {
          message = notIncludedMessage;
        } else {
          message = '';
        }
      }
    } catch (error) {
      console.error('Error in FeatureGuard:', error);
      // In development mode, we'll still show the content despite errors
      if (!isDevelopment) {
        hasAccess = false;
        hasReachedLimit = false;
        message = 'Error checking feature access.';
      }
    }
  }
</script>

{#if isDevelopment || (hasAccess && !hasReachedLimit)}
  <slot />
{:else}
  <div
    class="flex flex-col items-center justify-center rounded-md border border-dashed p-8 text-center">
    <div class="bg-muted mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full">
      {#if hasReachedLimit}
        <AlertTriangle class="text-warning h-6 w-6" />
      {:else}
        <Lock class="text-muted-foreground h-6 w-6" />
      {/if}
    </div>
    <h3 class="mb-2 text-lg font-medium">
      {#if hasReachedLimit}
        Limit Reached
      {:else}
        Feature Not Available
      {/if}
    </h3>
    <p class="text-muted-foreground mb-4 max-w-md">
      {message}
    </p>
    {#if showUpgradeButton}
      <Button variant="outline" onclick={() => goto(upgradeButtonLink)}>
        {upgradeButtonText}
      </Button>
    {/if}
  </div>
{/if}
