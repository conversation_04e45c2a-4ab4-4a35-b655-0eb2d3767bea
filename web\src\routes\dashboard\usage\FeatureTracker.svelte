<script lang="ts">
  import { trackFeature } from '$lib/utils/feature-tracker';
  import { Button } from '$lib/components/ui/button';
  import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
  } from '$lib/components/ui/card';
  import { Label } from '$lib/components/ui/label';
  import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
  } from '$lib/components/ui/select';
  import { Input } from '$lib/components/ui/input';
  import { AlertCircle, CheckCircle } from 'lucide-svelte';
  import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
  import type { FeatureWithDetailedUsage } from '$lib/services/feature-service';

  export let features: FeatureWithDetailedUsage[] = [];
  export let onTrackSuccess: () => void = () => {}; // Callback for when tracking is successful

  let selectedFeatureId: string = '';
  let selectedLimitId: string = '';
  let amount: number = 1;
  let loading: boolean = false;
  let success: boolean = false;
  let error: string | null = null;
  let selectedFeature: FeatureWithDetailedUsage | null = null;
  let availableLimits: any[] = [];
  let selectedLimit: any = null;

  // Handle feature selection change
  function handleFeatureChange(event: CustomEvent) {
    selectedFeatureId = event.detail;
    selectedFeature = features.find((f) => f.id === selectedFeatureId) || null;
    availableLimits = selectedFeature?.limits || [];
    selectedLimitId = availableLimits.length > 0 ? availableLimits[0].id : '';
    selectedLimit = availableLimits.length > 0 ? availableLimits[0] : null;
  }

  // Handle limit selection change
  function handleLimitChange(event: CustomEvent) {
    selectedLimitId = event.detail;
    selectedLimit = availableLimits.find((l) => l.id === selectedLimitId) || null;
  }

  // Track feature usage
  async function handleTrackFeature() {
    if (!selectedFeatureId || !selectedLimitId) {
      error = 'Please select a feature and limit';
      return;
    }

    loading = true;
    error = null;
    success = false;

    try {
      const result = await trackFeature(selectedFeatureId, selectedLimitId, amount);

      if (result.success) {
        success = true;
        // Call the callback to refresh the usage data
        onTrackSuccess();
        setTimeout(() => {
          success = false;
        }, 3000);
      } else {
        error = result.error || 'Failed to track feature usage';
        if (result.limitReached) {
          error = 'You have reached your limit for this feature';
        }
      }
    } catch (err) {
      console.error('Error tracking feature:', err);
      error = err.message || 'An error occurred while tracking feature usage';
    } finally {
      loading = false;
    }
  }
</script>

<Card class="w-full">
  <CardHeader>
    <CardTitle>Track Feature Usage</CardTitle>
    <CardDescription>Test tracking feature usage to see how it affects your limits</CardDescription>
  </CardHeader>
  <CardContent>
    <div class="grid gap-4">
      <div class="grid gap-2">
        <Label for="feature">Feature</Label>
        <Select on:change={handleFeatureChange}>
          <SelectTrigger id="feature">
            <SelectValue placeholder="Select a feature" />
          </SelectTrigger>
          <SelectContent>
            {#each features as feature}
              <SelectItem value={feature.id}>{feature.name}</SelectItem>
            {/each}
          </SelectContent>
        </Select>
      </div>

      {#if selectedFeature}
        <div class="grid gap-2">
          <Label for="limit">Limit</Label>
          <Select on:change={handleLimitChange}>
            <SelectTrigger id="limit" disabled={availableLimits.length === 0}>
              <SelectValue placeholder="Select a limit" />
            </SelectTrigger>
            <SelectContent>
              {#each availableLimits as limit}
                <SelectItem value={limit.id}>{limit.name}</SelectItem>
              {/each}
            </SelectContent>
          </Select>
        </div>

        <div class="grid gap-2">
          <Label for="amount">Amount</Label>
          <Input id="amount" type="number" min="1" bind:value={amount} placeholder="1" />
        </div>
      {/if}

      {#if error}
        <Alert variant="destructive">
          <AlertCircle class="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      {/if}

      {#if success}
        <Alert>
          <CheckCircle class="h-4 w-4 text-green-500" />
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>Feature usage tracked successfully</AlertDescription>
        </Alert>
      {/if}
    </div>
  </CardContent>
  <CardFooter>
    <Button
      on:click={handleTrackFeature}
      disabled={loading || !selectedFeatureId || !selectedLimitId}
      class="w-full">
      {loading ? 'Tracking...' : 'Track Usage'}
    </Button>
  </CardFooter>
</Card>
