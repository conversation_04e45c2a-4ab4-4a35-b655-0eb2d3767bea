// cron/utils/proxyRotator.ts
import { logger } from "./logger";
import { Worker } from "./types";
import FingerprintJS from "@fingerprintjs/fingerprintjs";
import { getDefaultUserAgent, getRandomHeaders } from "./headers";

/**
 * Get a random viewport size (limited to max 960x960)
 */
function getRandomViewport() {
  // Define viewport sizes that are common but limited to max 960x960
  const viewports = [
    { width: 800, height: 600 }, // Classic size
    { width: 960, height: 720 }, // 4:3 aspect ratio within limits
    { width: 960, height: 540 }, // 16:9 aspect ratio within limits
    { width: 854, height: 480 }, // 16:9 aspect ratio smaller
    { width: 640, height: 360 }, // 16:9 aspect ratio even smaller
    { width: 960, height: 600 }, // 16:10 aspect ratio
    { width: 800, height: 500 }, // 16:10 aspect ratio smaller
  ];

  return viewports[Math.floor(Math.random() * viewports.length)];
}
import {
  generateRandomFingerprint,
  applyFingerprint,
} from "./browserFingerprint";
// axios is not used in this file
// import axios from "axios";

// Function is defined below

/**
 * Improved proxy rotation utility with better error handling and timeouts
 * This is a standalone function that can be used to rotate proxies for workers
 * without modifying the WorkerPool class
 */
export async function rotateProxyWithTimeout(
  worker: Worker,
  proxyUsername: string,
  proxyPassword: string,
  proxyHost: string,
  proxyPort: number,
  assignedProxies: Map<number, number>,
  usedPorts: Set<number>,
  assignProxyToWorker: (workerId: number) => number
): Promise<boolean> {
  if (!worker) return false;

  // Track if worker was busy before rotation
  const wasBusy = worker.busy;
  // Mark worker as busy during rotation to prevent concurrent use
  worker.busy = true;

  logger.info(`🔁 Rotating proxy for worker #${worker.id}...`);

  // Set a timeout for the entire rotation process
  const rotationTimeout = setTimeout(() => {
    logger.error(`⏱️ Proxy rotation timeout for worker #${worker.id}`);
  }, 30000); // 30 second timeout

  try {
    // 🧼 Cleanup old resources
    const currentPort = assignedProxies.get(worker.id);
    if (currentPort) usedPorts.delete(currentPort);
    assignedProxies.delete(worker.id);

    // Close old resources with timeouts
    try {
      // Use Promise.race to add timeout to close operations
      await Promise.race([
        worker.page.close(),
        new Promise((resolve) => setTimeout(resolve, 5000)),
      ]);

      await Promise.race([
        worker.context.close(),
        new Promise((resolve) => setTimeout(resolve, 5000)),
      ]);
    } catch (err) {
      logger.warn(
        `⚠️ Failed to close old context/page for worker #${worker.id}: ${err}`
      );
    }

    // Assign a new port using the provided function
    const newPort = assignProxyToWorker(worker.id);
    const proxyUrl = `http://${proxyUsername}:${proxyPassword}@${proxyHost}:${newPort}`;

    logger.info(`🌍 Rotated Smartproxy for worker #${worker.id} → ${proxyUrl}`);

    // Get random viewport dimensions for better fingerprinting
    const viewport = getRandomViewport();

    // ♻️ Reuse existing browser and create a new context/page
    const context = (await Promise.race([
      worker.browser.newContext({
        httpCredentials: {
          username: proxyUsername,
          password: proxyPassword,
        },
        viewport,
        userAgent: getDefaultUserAgent(),
        // Add additional fingerprinting evasion
        deviceScaleFactor: Math.random() > 0.5 ? 1 : 2,
        locale: ["en-US", "en-GB", "en-CA"][Math.floor(Math.random() * 3)],
        timezoneId: [
          "America/New_York",
          "America/Los_Angeles",
          "Europe/London",
        ][Math.floor(Math.random() * 3)],
      }),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error("Context creation timeout")), 10000)
      ),
    ])) as any;

    const page = await Promise.race([
      context.newPage(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error("Page creation timeout")), 10000)
      ),
    ]);

    // Set additional headers for better fingerprinting
    await page.setExtraHTTPHeaders(getRandomHeaders());

    // Initialize FingerprintJS for consistent fingerprinting
    await page.addInitScript(() => {
      // Load FingerprintJS
      const loadFingerprintJS = async () => {
        try {
          const fpPromise = FingerprintJS.load();
          const fp = await fpPromise;
          const result = await fp.get();

          // Store the visitor ID in localStorage for consistent identification
          window.localStorage.setItem("fpjs_visitor_id", result.visitorId);
          console.log("FingerprintJS visitor ID:", result.visitorId);

          return result.components;
        } catch (error) {
          console.error("Error loading FingerprintJS:", error);
          return null;
        }
      };

      // Execute FingerprintJS loading
      loadFingerprintJS();
    });

    // ✅ Replace worker's context + page in-place
    worker.context = context;
    worker.page = page;
    worker.lastUsed = new Date();
    worker.captchaCount = 0; // Reset captcha count after rotation

    // Restore previous busy state if worker wasn't busy before rotation
    if (!wasBusy) {
      worker.busy = false;
    }

    clearTimeout(rotationTimeout);
    logger.info(`✅ Worker #${worker.id} proxy rotation completed`);
    return true;
  } catch (error) {
    logger.error(`❌ Error rotating proxy for worker #${worker.id}: ${error}`);

    // Clear the timeout since we're handling the error
    clearTimeout(rotationTimeout);

    // If worker was not busy before, release it
    if (!wasBusy) {
      worker.busy = false;
    }
    return false;
  }
}

/**
 * Add random delays between requests to avoid detection
 * @param min Minimum delay in milliseconds
 * @param max Maximum delay in milliseconds
 * @returns Promise that resolves after a random delay
 */
export function randomDelay(
  min: number = 3000,
  max: number = 10000
): Promise<void> {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min;
  logger.info(`⏳ Adding random delay of ${delay}ms between requests`);
  return new Promise((resolve) => setTimeout(resolve, delay));
}

/**
 * Implements an exponential backoff strategy for retrying after CAPTCHA detection
 * @param attempt Current attempt number (starting from 1)
 * @param baseDelay Base delay in milliseconds
 * @param maxDelay Maximum delay in milliseconds
 * @returns Delay time in milliseconds
 */
export function calculateBackoffDelay(
  attempt: number,
  baseDelay: number = 60000,
  maxDelay: number = 15 * 60 * 1000
): number {
  // Calculate exponential backoff: baseDelay * 2^(attempt-1) with jitter
  const exponentialDelay = baseDelay * Math.pow(2, attempt - 1);

  // Add some randomness (jitter) to avoid thundering herd problem
  const jitter = Math.random() * 0.3 * exponentialDelay; // 0-30% jitter

  // Cap at maxDelay
  const delay = Math.min(exponentialDelay + jitter, maxDelay);

  logger.info(
    `🔄 Calculated backoff delay for attempt ${attempt}: ${Math.round(delay / 1000)} seconds`
  );

  return delay;
}

/**
 * Get the current IP address for a worker
 */
async function getCurrentIP(worker: Worker): Promise<string | null> {
  try {
    // Navigate to an IP checking service
    await Promise.race([
      worker.page.goto("https://api.ipify.org", { timeout: 10000 }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Navigation timeout")), 10000)
      ),
    ]);

    // Get the IP address from the page content
    const ip = await worker.page.content().then((content) => {
      // The content should just be the IP address
      const match = content.match(/\d+\.\d+\.\d+\.\d+/);
      return match ? match[0] : null;
    });

    return ip;
  } catch (error) {
    logger.warn(`⚠️ Error getting current IP: ${error}`);
    return null;
  }
}

/**
 * Enhanced proxy rotation with IP verification
 * This ensures we get a fresh IP address with each rotation
 */
export async function rotateProxyWithIPCheck(
  worker: Worker,
  proxyUsername: string,
  proxyPassword: string,
  proxyHost: string,
  assignedProxies: Map<number, number>,
  usedPorts: Set<number>,
  assignProxyToWorker: (workerId: number) => number,
  maxRetries: number = 3
): Promise<boolean> {
  if (!worker) return false;

  // Track if worker was busy before rotation
  const wasBusy = worker.busy;
  // Mark worker as busy during rotation to prevent concurrent use
  worker.busy = true;

  logger.info(
    `🔁 Rotating proxy for worker #${worker.id} with IP verification...`
  );

  // Set a timeout for the entire rotation process
  const rotationTimeout = setTimeout(() => {
    logger.error(`⏱️ Proxy rotation timeout for worker #${worker.id}`);
  }, 30000); // 30 second timeout

  // Store the current IP to ensure we get a different one
  let currentIP = await getCurrentIP(worker);
  logger.info(
    `🌍 Current IP for worker #${worker.id}: ${currentIP || "unknown"}`
  );

  let retryCount = 0;
  let success = false;

  while (retryCount < maxRetries && !success) {
    try {
      // Cleanup old resources
      const currentPort = assignedProxies.get(worker.id);
      if (currentPort) usedPorts.delete(currentPort);
      assignedProxies.delete(worker.id);

      // Close old resources with timeouts
      try {
        // Use Promise.race to add timeout to close operations
        await Promise.race([
          worker.page.close(),
          new Promise((resolve) => setTimeout(resolve, 5000)),
        ]);

        await Promise.race([
          worker.context.close(),
          new Promise((resolve) => setTimeout(resolve, 5000)),
        ]);
      } catch (err) {
        logger.warn(
          `⚠️ Failed to close old context/page for worker #${worker.id}: ${err}`
        );
      }

      // Assign a new port using the provided function
      const newPort = assignProxyToWorker(worker.id);
      const proxyUrl = `http://${proxyUsername}:${proxyPassword}@${proxyHost}:${newPort}`;

      logger.info(
        `🌍 Trying new Smartproxy for worker #${worker.id} → ${proxyUrl}`
      );

      // Generate a completely random fingerprint
      const fingerprint = generateRandomFingerprint();

      // Reuse existing browser and create a new context/page
      const context = (await Promise.race([
        worker.browser.newContext({
          httpCredentials: {
            username: proxyUsername,
            password: proxyPassword,
          },
          viewport: fingerprint.viewport,
          userAgent: getDefaultUserAgent(),
          deviceScaleFactor: fingerprint.deviceScaleFactor,
          locale: fingerprint.locale,
          timezoneId: fingerprint.timezoneId,
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Context creation timeout")), 10000)
        ),
      ])) as any; // Type assertion to avoid TypeScript errors

      const page = await Promise.race([
        context.newPage(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Page creation timeout")), 10000)
        ),
      ]);

      // Apply advanced fingerprinting
      await applyFingerprint(context, page, fingerprint);

      // Set additional headers for better fingerprinting
      await page.setExtraHTTPHeaders(getRandomHeaders());

      // Initialize FingerprintJS for consistent fingerprinting
      await page.addInitScript(() => {
        // Load FingerprintJS
        const loadFingerprintJS = async () => {
          try {
            const fpPromise = FingerprintJS.load();
            const fp = await fpPromise;
            const result = await fp.get();

            // Store the visitor ID in localStorage for consistent identification
            window.localStorage.setItem("fpjs_visitor_id", result.visitorId);
            console.log("FingerprintJS visitor ID:", result.visitorId);

            return result.components;
          } catch (error) {
            console.error("Error loading FingerprintJS:", error);
            return null;
          }
        };

        // Execute FingerprintJS loading
        loadFingerprintJS();
      });

      // Replace worker's context + page in-place
      worker.context = context;
      worker.page = page;
      worker.lastUsed = new Date();
      worker.captchaCount = 0; // Reset captcha count after rotation

      // Verify we got a new IP address
      const newIP = await getCurrentIP(worker);
      logger.info(`🌍 New IP for worker #${worker.id}: ${newIP || "unknown"}`);

      if (newIP && currentIP && newIP !== currentIP) {
        logger.info(`✅ Successfully rotated to a new IP: ${newIP}`);
        success = true;
      } else if (!currentIP || !newIP) {
        logger.info(
          `⚠️ Couldn't verify IP change, but proxy rotation completed`
        );
        success = true; // Assume success if we can't verify
      } else {
        logger.warn(`⚠️ Failed to get a new IP address, still on ${newIP}`);
        retryCount++;

        // Try again with a different port
        if (retryCount < maxRetries) {
          logger.info(
            `🔄 Retrying proxy rotation (attempt ${retryCount + 1}/${maxRetries})...`
          );

          // Close the resources we just created
          try {
            await page.close();
            await context.close();
          } catch (err) {
            logger.warn(`⚠️ Error closing resources during retry: ${err}`);
          }

          // Wait a bit before retrying
          await new Promise((resolve) => setTimeout(resolve, 2000));
          continue;
        }
      }

      // Restore previous busy state if worker wasn't busy before rotation
      if (!wasBusy) {
        worker.busy = false;
      }

      clearTimeout(rotationTimeout);

      if (success) {
        logger.info(
          `✅ Worker #${worker.id} proxy rotation completed with new IP`
        );
        return true;
      } else {
        logger.warn(
          `⚠️ Worker #${worker.id} proxy rotation completed but couldn't verify new IP after ${maxRetries} attempts`
        );
        return true; // Still return true as we did rotate the proxy
      }
    } catch (error) {
      logger.error(
        `❌ Error rotating proxy for worker #${worker.id}: ${error}`
      );
      retryCount++;

      // Wait a bit before retrying
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }
  }

  // Clear the timeout since we're done
  clearTimeout(rotationTimeout);

  // If we get here, all retries failed
  logger.error(
    `❌ All proxy rotation attempts failed for worker #${worker.id}`
  );

  // If worker was not busy before, release it
  if (!wasBusy) {
    worker.busy = false;
  }

  return false;
}
