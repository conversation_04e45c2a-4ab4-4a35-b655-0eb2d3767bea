-- CreateTable
CREATE TABLE "cron"."JobStats" (
    "id" TEXT NOT NULL,
    "jobType" TEXT NOT NULL,
    "itemsProcessed" INTEGER NOT NULL DEFAULT 0,
    "success" BOOLEAN NOT NULL DEFAULT false,
    "durationMs" INTEGER NOT NULL DEFAULT 0,
    "details" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "startTime" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endTime" TIMESTAMP(3),
    "error" TEXT,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "JobStats_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "JobStats_jobType_idx" ON "cron"."JobStats"("jobType");

-- CreateIndex
CREATE INDEX "JobStats_createdAt_idx" ON "cron"."JobStats"("createdAt");

-- CreateIndex
CREATE INDEX "JobStats_endTime_idx" ON "cron"."JobStats"("endTime");
