/**
 * Resume Parsing Subscriber
 *
 * This module subscribes to Redis channels for resume parsing status updates
 * and sends notifications to users when their resumes are parsed.
 */

import { getRedisClient } from '$lib/server/redis';
import { prisma } from '$lib/server/prisma';
import { createNotification } from '$lib/server/notifications';

// Redis channel for resume parsing status updates
const JOB_TYPE = 'resume-parsing';
const RESUME_PARSING_STATUS_CHANNEL = `${JOB_TYPE}::status`;
const RESUME_PARSING_STREAM_STATUS_CHANNEL = `${JOB_TYPE}::stream::status`;

// Job status constants (must match the worker's JobStatus enum)
enum JobStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// Track subscription status
let isSubscribed = false;

/**
 * Subscribe to resume parsing status updates
 */
export async function subscribeToResumeParsingUpdates(): Promise<void> {
  // Skip if already subscribed
  if (isSubscribed) {
    console.log('[Resume Parsing] Already subscribed to status channel');
    return;
  }

  try {
    console.log('[Resume Parsing] Subscribing to status channels...');

    // Get the Redis client
    const redis = await getRedisClient();
    if (!redis) {
      console.error('[Resume Parsing] Redis client not available');
      return;
    }

    // Create a duplicate Redis client for pub/sub
    const subscriber = redis.duplicate();

    // Subscribe to both status channels
    await subscriber.subscribe(RESUME_PARSING_STATUS_CHANNEL);
    await subscriber.subscribe(RESUME_PARSING_STREAM_STATUS_CHANNEL);
    console.log(`[Resume Parsing] Subscribed to both status channels`);

    // Listen for messages
    subscriber.on('message', async (channel, message) => {
      try {
        console.log(
          `[Resume Parsing] Received status update on ${channel}: ${message.substring(0, 200)}`
        );
        const statusUpdate = JSON.parse(message);

        // Handle status update
        await handleStatusUpdate(statusUpdate);
      } catch (error) {
        console.error('[Resume Parsing] Error handling status update:', error);
      }
    });

    // Mark as subscribed
    isSubscribed = true;
    console.log('[Resume Parsing] Successfully subscribed to status channel');
  } catch (error) {
    console.error('[Resume Parsing] Error subscribing to status channel:', error);
  }
}

/**
 * Handle resume parsing status update
 */
async function handleStatusUpdate(statusUpdate: any): Promise<void> {
  const { resumeId, status, error } = statusUpdate;

  if (!resumeId || !status) {
    console.warn('[Resume Parsing] Invalid status update:', statusUpdate);
    return;
  }

  console.log(`[Resume Parsing] Handling status update for resume ${resumeId}: ${status}`);

  try {
    // Get the resume to find the user ID
    const resume = await prisma.resume.findUnique({
      where: { id: resumeId },
      include: { document: true },
    });

    if (!resume) {
      console.warn(`[Resume Parsing] Resume ${resumeId} not found`);
      return;
    }

    const userId = resume.document.userId;

    // Handle completed status
    if (status === JobStatus.COMPLETED) {
      await handleCompletedStatus(resumeId, userId);

      // Get the profile ID if available
      let profileId = null;
      try {
        // Check if there's a worker process with this resumeId that has a profileId
        const workerProcess = await prisma.workerProcess.findFirst({
          where: {
            data: {
              path: ['resumeId'],
              equals: resumeId,
            },
          },
        });

        if (workerProcess?.data && typeof workerProcess.data === 'object') {
          profileId = (workerProcess.data as any).profileId ?? null;
          console.log(`[Resume Parsing] Found profileId ${profileId} for resume ${resumeId}`);
        }
      } catch (error) {
        console.error(`[Resume Parsing] Error getting profileId for resume ${resumeId}:`, error);
      }

      // Send WebSocket message to clients with the correct type
      await sendWebSocketMessage('resume_parsing_completed', {
        resumeId,
        status: 'completed',
        userId,
        profileId, // Include profileId in the message
        message: 'Resume parsing completed successfully',
      });
    }

    // Handle failed status
    if (status === JobStatus.FAILED) {
      await handleFailedStatus(resumeId, userId, error);

      // Send WebSocket message to clients
      await sendWebSocketMessage('resume_parsing_status', {
        resumeId,
        status: 'failed',
        error,
        userId,
      });
    }
  } catch (error) {
    console.error(`[Resume Parsing] Error handling status update:`, error);
  }
}

/**
 * Handle completed status
 */
async function handleCompletedStatus(resumeId: string, userId: string): Promise<void> {
  try {
    // Get resume details
    const resume = await prisma.resume.findUnique({
      where: { id: resumeId },
      include: {
        document: true,
      },
    });

    if (!resume) {
      console.warn(`[Resume Parsing] Resume ${resumeId} not found`);
      return;
    }

    // Create notification for the user
    await createNotification({
      userId,
      title: 'Resume Parsed',
      message: `Your resume "${resume.document?.label || 'Resume'}" has been successfully parsed.`,
      data: {
        resumeId,
        documentId: resume.documentId,
      },
    });

    console.log(`[Resume Parsing] Created notification for user ${userId} for resume ${resumeId}`);
  } catch (error) {
    console.error(
      `[Resume Parsing] Error handling completed status for resume ${resumeId}:`,
      error
    );
  }
}

/**
 * Send a WebSocket message to clients via Redis
 */
async function sendWebSocketMessage(type: string, data: any): Promise<void> {
  try {
    // Import the broadcastMessage function dynamically to avoid circular dependencies
    const { broadcastMessage } = await import('$lib/server/websocket');

    // Create the message payload
    const message = {
      type,
      data,
      timestamp: new Date().toISOString(),
    };

    // Use the broadcastMessage function to send the message
    await broadcastMessage(message);

    console.log(
      `[Resume Parsing] Sent WebSocket message of type ${type} for resume ${data.resumeId}`
    );
  } catch (error) {
    console.error(`[Resume Parsing] Error sending WebSocket message:`, error);
  }
}

// Removed unused getResumeData function

/**
 * Handle failed status
 */
async function handleFailedStatus(
  resumeId: string,
  userId: string,
  errorMessage?: string
): Promise<void> {
  try {
    // Get resume details
    const resume = await prisma.resume.findUnique({
      where: { id: resumeId },
      include: {
        document: true,
      },
    });

    if (!resume) {
      console.warn(`[Resume Parsing] Resume ${resumeId} not found`);
      return;
    }

    // Create notification for the user
    await createNotification({
      userId,
      title: 'Resume Parsing Failed',
      message: `We couldn't parse your resume "${resume.document?.label || 'Resume'}". Please try uploading it again.`,
      data: {
        resumeId,
        documentId: resume.documentId,
        error: errorMessage,
      },
    });

    // Update the resume to mark it as not parsed
    await prisma.resume.update({
      where: { id: resumeId },
      data: {
        isParsed: false,
        parsedAt: null,
      },
    });

    console.log(
      `[Resume Parsing] Created failure notification for user ${userId} for resume ${resumeId}`
    );
  } catch (error) {
    console.error(`[Resume Parsing] Error handling failed status for resume ${resumeId}:`, error);
  }
}
