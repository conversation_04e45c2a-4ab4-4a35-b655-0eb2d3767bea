// src/routes/dashboard/usage/+server.ts
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { verifySessionToken } from '$lib/server/auth';
import { getUserFeatureUsageWithPlanLimits } from '$lib/server/feature-usage';
import { prisma } from '$lib/server/prisma';
import { getPlanById } from '$lib/server/plan-sync';

/**
 * Get feature usage data for the current user
 * This provides a comprehensive view of all features, their usage, and limits based on the user's plan
 */
export const GET: RequestHandler = async ({ cookies, url }) => {
  const token = cookies.get('auth_token');

  if (!token) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userData = await verifySessionToken(token);

  if (!userData?.id) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Get the type of data requested
    const dataType = url.searchParams.get('type') || 'all';

    if (dataType === 'resume') {
      // Get resume usage data
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      // Get document submissions instead of resumeSubmission
      const used = await prisma.documentSubmission.count({
        where: {
          userId: userData.id,
          createdAt: {
            gte: startOfMonth,
          },
        },
      });

      // Get the user's plan from the database
      const userPlan = await getPlanById(userData.role || 'free');

      // Find the resume scanner feature and its monthly limit
      const resumeScannerFeature = userPlan?.features.find((f) => f.featureId === 'resume_scanner');
      const resumeScansLimit = resumeScannerFeature?.limits?.find(
        (l) => l.limitId === 'resume_scans_per_month'
      );

      // Get the limit value
      const limit = resumeScansLimit
        ? resumeScansLimit.value === 'unlimited'
          ? null
          : Number(resumeScansLimit.value)
        : null;

      const remaining = limit !== null ? Math.max(0, limit - used) : null;

      return json({ used, limit, remaining });
    } else if (dataType === 'features') {
      // Get user feature usage with plan limits
      const usageData = await getUserFeatureUsageWithPlanLimits(userData.id);
      return json(usageData);
    } else {
      // Get all data
      // Get resume usage data
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      // Get document submissions instead of resumeSubmission
      const used = await prisma.documentSubmission.count({
        where: {
          userId: userData.id,
          createdAt: {
            gte: startOfMonth,
          },
        },
      });

      // Get the user's plan from the database
      const userPlan = await getPlanById(userData.role || 'free');

      // Find the resume scanner feature and its monthly limit
      const resumeScannerFeature = userPlan?.features.find((f) => f.featureId === 'resume_scanner');
      const resumeScansLimit = resumeScannerFeature?.limits?.find(
        (l) => l.limitId === 'resume_scans_per_month'
      );

      // Get the limit value
      const limit = resumeScansLimit
        ? resumeScansLimit.value === 'unlimited'
          ? null
          : Number(resumeScansLimit.value)
        : null;

      const remaining = limit !== null ? Math.max(0, limit - used) : null;

      // Get user feature usage with plan limits
      const usageData = await getUserFeatureUsageWithPlanLimits(userData.id);

      return json({
        resume: { used, limit, remaining },
        features: usageData,
      });
    }
  } catch (error) {
    console.error('Error in usage API:', error);
    return json(
      {
        error: error.message || 'An error occurred while fetching usage data',
      },
      { status: 500 }
    );
  }
};
