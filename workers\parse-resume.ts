/**
 * <PERSON><PERSON><PERSON> to parse a resume and save it to the database
 *
 * This script:
 * 1. Creates a WorkerProcess entry
 * 2. Uses the enhanced-parser to parse a resume
 * 3. Saves the parsed data to the database using db-saver
 * 4. Updates the WorkerProcess status
 *
 * Usage:
 * npx tsx parse-resume.ts
 */

import { PrismaClient } from "@prisma/client";
import path from "path";
import fs from "fs/promises";
import { fileURLToPath } from "url";
import { dirname } from "path";

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Import the enhanced parser and db saver
import { parseResumeEnhanced } from "./resume/enhanced-parser.js";
import { saveToDatabase } from "./resume/db-saver.js";

// Initialize Prisma client
const prisma = new PrismaClient();

// Sample resume file path
const SAMPLE_RESUME_PATH = path.resolve(
  __dirname,
  "./resume/samples/software-engineer-1.pdf"
);

// Job types and statuses
const JobType = {
  RESUME_PARSING: "resume-parsing",
};

const JobStatus = {
  PENDING: "pending",
  PROCESSING: "processing",
  COMPLETED: "completed",
  FAILED: "failed",
};

/**
 * Main function to parse a resume
 */
async function parseResumeAndSave() {
  console.log("Starting resume parsing...");

  try {
    // Check if the sample resume file exists
    try {
      await fs.access(SAMPLE_RESUME_PATH);
      console.log(`Using resume file: ${SAMPLE_RESUME_PATH}`);
    } catch (error) {
      console.error(`Sample resume file not found: ${SAMPLE_RESUME_PATH}`);
      console.log("Please provide a valid sample resume file path.");
      return;
    }

    // Create a test resume ID
    const resumeId = `test-resume-${Date.now()}`;
    const userId = "test-user";
    console.log(`Using test resume ID: ${resumeId}`);

    // Create a WorkerProcess entry
    console.log("Creating WorkerProcess entry...");
    const workerProcess = await createWorkerProcess(resumeId, userId);
    console.log(`Created WorkerProcess: ${workerProcess.id}`);

    // Update WorkerProcess status to processing
    console.log("Updating WorkerProcess status to processing...");
    await updateWorkerProcessStatus(workerProcess.id, JobStatus.PROCESSING);
    console.log(`Updated WorkerProcess status to ${JobStatus.PROCESSING}`);

    // Parse the resume
    console.log("Parsing resume...");
    const startTime = Date.now();

    // Parse the resume using the enhanced parser
    console.log(`Parsing resume with enhanced parser...`);
    const parsedData = await parseResumeEnhanced(SAMPLE_RESUME_PATH);
    const parseTime = Date.now() - startTime;
    console.log(`Parsed resume in ${parseTime}ms`);
    console.log(
      "Parsed data:",
      JSON.stringify(parsedData).substring(0, 200) + "..."
    );

    // Clean the parsed data to remove null bytes
    console.log("Cleaning parsed data...");
    const cleanData = JSON.parse(
      JSON.stringify(parsedData).replace(/\\u0000/g, "")
    );

    // Save the cleaned data to the database
    console.log("Saving parsed data to database...");
    const jobData = {
      id: workerProcess.id,
      resumeId,
      userId,
      filePath: SAMPLE_RESUME_PATH,
    };
    console.log("Job data:", jobData);
    const parsedResumeId = await saveToDatabase(jobData, cleanData);
    console.log(`Saved parsed data to database with ID: ${parsedResumeId}`);

    // Update WorkerProcess status to completed
    console.log("Updating WorkerProcess status to completed...");
    await updateWorkerProcessStatus(
      workerProcess.id,
      JobStatus.COMPLETED,
      parsedResumeId
    );
    console.log(`Updated WorkerProcess status to ${JobStatus.COMPLETED}`);

    // Verify the ParsedResume record
    console.log("Verifying ParsedResume record...");
    const parsedResume = await verifyParsedResume(parsedResumeId);
    if (parsedResume) {
      console.log("ParsedResume record verified successfully!");
      console.log("- Name:", parsedResume.name || "Not found");
      console.log("- Email:", parsedResume.email || "Not found");
      console.log("- Phone:", parsedResume.phone || "Not found");
      console.log("- Location:", parsedResume.location || "Not found");

      if (parsedResume.skills) {
        const skills =
          typeof parsedResume.skills === "string"
            ? JSON.parse(parsedResume.skills)
            : parsedResume.skills;

        console.log(
          "- Skills:",
          Array.isArray(skills)
            ? skills.map((s) => (typeof s === "object" ? s.name : s)).join(", ")
            : "Not in expected format"
        );
      }
    } else {
      console.log("ParsedResume record verification failed!");
    }

    console.log("Resume parsing completed successfully.");
  } catch (error) {
    console.error("Error parsing resume:", error);
  } finally {
    // Clean up
    console.log("Cleaning up...");
    await prisma.$disconnect();
    console.log("Cleanup complete.");
  }
}

/**
 * Create a WorkerProcess entry for resume parsing
 */
async function createWorkerProcess(resumeId: string, userId: string) {
  return prisma.workerProcess.create({
    data: {
      type: JobType.RESUME_PARSING,
      status: JobStatus.PENDING,
      data: {
        resumeId,
        filePath: SAMPLE_RESUME_PATH,
        userId,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  });
}

/**
 * Update WorkerProcess status
 */
async function updateWorkerProcessStatus(
  jobId: string,
  status: string,
  parsedResumeId: string | null = null
) {
  const data: any = {
    status,
    updatedAt: new Date(),
  };

  if (status === JobStatus.PROCESSING) {
    data.startedAt = new Date();
  } else if (status === JobStatus.COMPLETED) {
    data.completedAt = new Date();
    if (parsedResumeId) {
      data.data = {
        parsedResumeId,
      };
    }
  }

  return prisma.workerProcess.update({
    where: { id: jobId },
    data,
  });
}

/**
 * Verify the ParsedResume record
 */
async function verifyParsedResume(parsedResumeId: string) {
  try {
    // Use raw query to check for ParsedResume record
    const result = await prisma.$queryRaw`
      SELECT * FROM "workers"."ParsedResume"
      WHERE "id" = ${parsedResumeId}
    `;

    if (Array.isArray(result) && result.length > 0) {
      console.log(`ParsedResume record found with ID: ${parsedResumeId}`);
      return result[0];
    } else {
      console.log(`No ParsedResume record found with ID: ${parsedResumeId}`);
      return null;
    }
  } catch (error) {
    console.error("Error verifying ParsedResume record:", error);
    return null;
  }
}

// Run the script
parseResumeAndSave().catch(console.error);
