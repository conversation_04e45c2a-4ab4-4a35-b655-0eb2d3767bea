import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';

/**
 * Get detailed job match analysis
 * 
 * This endpoint provides detailed analysis of how a profile matches a specific job.
 */
export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    // Verify authentication
    const token = cookies.get('auth_token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tokenData = verifySessionToken(token);
    if (!tokenData || !tokenData.id) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // Get user ID from token
    const userId = tokenData.id;

    // Parse request body
    const body = await request.json();
    const { profileId, jobId } = body;

    // Validate required fields
    if (!profileId || !jobId) {
      return json(
        { error: 'Missing required fields', required: ['profileId', 'jobId'] },
        { status: 400 }
      );
    }

    // Check if profile exists and belongs to the user
    const profile = await prisma.profile.findFirst({
      where: {
        id: profileId,
        userId
      }
    });

    if (!profile) {
      return json({ error: 'Profile not found or access denied' }, { status: 404 });
    }

    // Check if job exists
    const job = await prisma.job.findUnique({
      where: { id: jobId }
    });

    if (!job) {
      return json({ error: 'Job not found' }, { status: 404 });
    }

    // Check if user has access to the ai_job_matching feature
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscriptions: {
          where: { status: 'active' },
          include: {
            plan: {
              include: {
                features: {
                  where: { featureId: 'ai_job_matching' }
                }
              }
            }
          }
        },
        featureUsage: {
          where: { 
            featureId: 'ai_job_matching',
            limitId: 'job_match_analysis_monthly'
          }
        }
      }
    });

    const hasAccess = user?.subscriptions.some(sub => 
      sub.plan.features.some(feature => feature.featureId === 'ai_job_matching')
    );

    if (!hasAccess && process.env.NODE_ENV === 'production') {
      return json({ error: 'Feature not available in your plan' }, { status: 403 });
    }

    // Check usage limits
    const usageLimit = user?.subscriptions
      .flatMap(sub => sub.plan.features)
      .find(feature => feature.featureId === 'ai_job_matching')
      ?.limits?.find(limit => limit.limitId === 'job_match_analysis_monthly')
      ?.value;
    
    const currentUsage = user?.featureUsage
      .find(usage => 
        usage.featureId === 'ai_job_matching' && 
        usage.limitId === 'job_match_analysis_monthly'
      )?.usage || 0;
    
    if (usageLimit && currentUsage >= parseInt(usageLimit) && process.env.NODE_ENV === 'production') {
      return json({ error: 'Monthly usage limit reached' }, { status: 403 });
    }

    // Check if match details already exist
    let matchDetails = await prisma.enhancedJobMatch.findUnique({
      where: {
        profileId_jobId: {
          profileId,
          jobId
        }
      }
    });

    // If match details don't exist, generate new ones
    if (!matchDetails) {
      // In a real implementation, this would call the job matching worker
      // For now, we'll generate mock match details
      const mockMatchDetails = generateMockJobMatchDetails(job);
      
      matchDetails = await prisma.enhancedJobMatch.create({
        data: {
          id: `match-${Date.now()}`,
          userId,
          profileId,
          jobId,
          overallMatchScore: mockMatchDetails.overallMatchScore,
          skillsMatchScore: mockMatchDetails.skillsMatchScore,
          experienceMatchScore: mockMatchDetails.experienceMatchScore,
          educationMatchScore: mockMatchDetails.educationMatchScore,
          keywordMatchScore: mockMatchDetails.keywordMatchScore,
          matchDetails: {
            matchedSkills: mockMatchDetails.matchedSkills,
            missingSkills: mockMatchDetails.missingSkills
          },
          recommendations: mockMatchDetails.recommendations
        }
      });
    }

    // Update feature usage
    await prisma.featureUsage.upsert({
      where: {
        userId_featureId_limitId: {
          userId,
          featureId: 'ai_job_matching',
          limitId: 'job_match_analysis_monthly'
        }
      },
      update: {
        usage: { increment: 1 }
      },
      create: {
        userId,
        featureId: 'ai_job_matching',
        limitId: 'job_match_analysis_monthly',
        usage: 1
      }
    });

    // Format response
    const response = {
      overallMatchScore: matchDetails.overallMatchScore,
      skillsMatchScore: matchDetails.skillsMatchScore,
      experienceMatchScore: matchDetails.experienceMatchScore,
      educationMatchScore: matchDetails.educationMatchScore,
      keywordMatchScore: matchDetails.keywordMatchScore,
      matchedSkills: matchDetails.matchDetails?.matchedSkills || [],
      missingSkills: matchDetails.matchDetails?.missingSkills || [],
      recommendations: matchDetails.recommendations || []
    };

    return json({ success: true, matchDetails: response });
  } catch (error) {
    console.error('Error getting job match details:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

/**
 * Generate mock job match details
 * 
 * In a real implementation, this would call the job matching worker
 */
function generateMockJobMatchDetails(job: any) {
  // Extract skills from job description
  const jobSkills = extractSkillsFromJob(job);
  
  // Mock matched and missing skills
  const matchedSkills = jobSkills.slice(0, Math.floor(jobSkills.length * 0.7));
  const missingSkills = jobSkills.slice(Math.floor(jobSkills.length * 0.7));
  
  // Calculate scores
  const skillsMatchScore = matchedSkills.length / jobSkills.length;
  const experienceMatchScore = Math.random() * 0.3 + 0.5; // Random score between 0.5 and 0.8
  const educationMatchScore = Math.random() * 0.3 + 0.6; // Random score between 0.6 and 0.9
  const keywordMatchScore = Math.random() * 0.3 + 0.5; // Random score between 0.5 and 0.8
  
  // Calculate overall score (weighted average)
  const overallMatchScore = 
    skillsMatchScore * 0.4 + 
    experienceMatchScore * 0.3 + 
    educationMatchScore * 0.2 + 
    keywordMatchScore * 0.1;
  
  // Generate recommendations
  const recommendations = generateRecommendations(missingSkills, job);
  
  return {
    overallMatchScore,
    skillsMatchScore,
    experienceMatchScore,
    educationMatchScore,
    keywordMatchScore,
    matchedSkills,
    missingSkills,
    recommendations
  };
}

/**
 * Extract skills from job
 */
function extractSkillsFromJob(job: any): string[] {
  // In a real implementation, this would extract skills from the job description
  // For now, we'll return mock skills based on the job title
  
  const commonSkills = [
    'Communication',
    'Problem Solving',
    'Teamwork',
    'Time Management',
    'Adaptability'
  ];
  
  let jobSpecificSkills: string[] = [];
  
  if (job.title.toLowerCase().includes('software') || job.title.toLowerCase().includes('developer') || job.title.toLowerCase().includes('engineer')) {
    jobSpecificSkills = [
      'JavaScript',
      'TypeScript',
      'React',
      'Node.js',
      'SQL',
      'Git',
      'CI/CD',
      'AWS',
      'Docker',
      'REST API'
    ];
  } else if (job.title.toLowerCase().includes('data')) {
    jobSpecificSkills = [
      'Python',
      'SQL',
      'Data Analysis',
      'Machine Learning',
      'Statistics',
      'Data Visualization',
      'Pandas',
      'NumPy',
      'Tableau',
      'Power BI'
    ];
  } else if (job.title.toLowerCase().includes('manager') || job.title.toLowerCase().includes('lead')) {
    jobSpecificSkills = [
      'Leadership',
      'Project Management',
      'Strategic Planning',
      'Team Building',
      'Budgeting',
      'Performance Management',
      'Conflict Resolution',
      'Decision Making',
      'Delegation',
      'Mentoring'
    ];
  } else if (job.title.toLowerCase().includes('marketing')) {
    jobSpecificSkills = [
      'Digital Marketing',
      'Social Media',
      'Content Creation',
      'SEO',
      'SEM',
      'Analytics',
      'Email Marketing',
      'Brand Management',
      'Market Research',
      'Campaign Management'
    ];
  } else {
    jobSpecificSkills = [
      'Microsoft Office',
      'Customer Service',
      'Research',
      'Analysis',
      'Reporting',
      'Presentation',
      'Organization',
      'Multitasking',
      'Attention to Detail',
      'Critical Thinking'
    ];
  }
  
  return [...commonSkills, ...jobSpecificSkills];
}

/**
 * Generate recommendations based on missing skills and job
 */
function generateRecommendations(missingSkills: string[], job: any): string[] {
  const recommendations = [
    `Add the following skills to your profile: ${missingSkills.slice(0, 3).join(', ')}`,
    'Highlight relevant experience that demonstrates your expertise in the required skills',
    'Tailor your resume summary to match the job description',
    'Include specific achievements that align with the job requirements'
  ];
  
  if (job.title.toLowerCase().includes('senior') || job.title.toLowerCase().includes('lead')) {
    recommendations.push('Emphasize leadership experience and team management skills');
  }
  
  if (job.title.toLowerCase().includes('remote')) {
    recommendations.push('Highlight experience working remotely and using collaboration tools');
  }
  
  return recommendations;
}
