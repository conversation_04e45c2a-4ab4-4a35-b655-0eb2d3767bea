// @ts-nocheck
// File: src/routes/dashboard/automation/[id]/+page.server.ts
import { redirect } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { getUserFromToken } from '$lib/server/auth.js';
import type { PageServerLoad } from '../../$types.js';

// Using the shared Prisma client from $lib/server/prisma

export const load = async ({ params, cookies, locals }: Parameters<PageServerLoad>[0]) => {
  const user = getUserFromToken(cookies);
  const { id } = params;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  locals.user = user;

  // Get the automation run with related data
  const automationRun = await prisma.automationRun.findFirst({
    where: {
      id,
      OR: [
        { userId: user.id },
        {
          profile: {
            team: {
              members: {
                some: { userId: user.id },
              },
            },
          },
        },
      ],
    },
    include: {
      profile: {
        include: {
          data: true,
          resumes: {
            include: {
              document: true
            }
          }
        }
      },
      jobs: {
        orderBy: {
          createdAt: 'desc'
        }
      },
    },
  });

  if (!automationRun) {
    throw redirect(302, '/dashboard/automation');
  }

  return {
    user,
    automationRun,
  };
};
