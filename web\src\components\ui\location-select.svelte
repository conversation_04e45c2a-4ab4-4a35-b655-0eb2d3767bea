<script lang="ts">
  import * as Command from '$lib/components/ui/command/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Check, ChevronsUpDown } from 'lucide-svelte';
  import * as Popover from '$lib/components/ui/popover/index.js';
  import { cn } from '$lib/utils';

  // Props using Svelte 5 syntax
  const {
    value = '',
    options = [],
    placeholder = 'Select...',
    searchPlaceholder = 'Search...',
    disabled = false,
    required = false,
    name = '',
    id = '',
    className = '',
    onChange = undefined,
  } = $props<{
    value?: string;
    options?: { value: string; label: string }[];
    placeholder?: string;
    searchPlaceholder?: string;
    disabled?: boolean;
    required?: boolean;
    name?: string;
    id?: string;
    className?: string;
    onChange?: (value: string) => void;
  }>();

  // State variables
  let currentValue = $state(value);
  let open = $state(false);
  let searchTerm = $state('');

  // Derived values
  const filteredOptions = $derived(
    searchTerm
      ? options.filter((option: { label: string }) =>
          option.label.toLowerCase().includes(searchTerm.toLowerCase())
        )
      : options
  );

  const selectedOption = $derived(
    options.find((option: { value: string }) => option.value === currentValue)
  );

  // Handle selection
  function handleSelect(option: { value: string; label: string }) {
    currentValue = option.value;
    if (onChange) {
      onChange(option.value);
    }
    open = false;
    searchTerm = '';
  }
</script>

<div class={cn('w-full', className)}>
  <Popover.Root bind:open>
    <Popover.Trigger>
      <Button
        variant="outline"
        role="combobox"
        aria-expanded={open}
        class="w-full justify-between"
        {disabled}>
        {selectedOption ? selectedOption.label : placeholder}
        <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
      </Button>
    </Popover.Trigger>
    <Popover.Content class="w-full p-0">
      <div style="min-width: 300px;">
        <Command.Root>
          <Command.Input placeholder={searchPlaceholder} bind:value={searchTerm} class="h-9" />
          {#if filteredOptions.length === 0}
            <div class="py-6 text-center text-sm">No results found.</div>
          {:else}
            <Command.List>
              <Command.Empty>No results found.</Command.Empty>
              <Command.Group>
                {#each filteredOptions as option (option.value)}
                  <Command.Item
                    value={option.label}
                    onSelect={() => handleSelect(option)}
                    class="flex items-center">
                    <Check
                      class={cn(
                        'mr-2 h-4 w-4',
                        currentValue === option.value ? 'opacity-100' : 'opacity-0'
                      )} />
                    {option.label}
                  </Command.Item>
                {/each}
              </Command.Group>
            </Command.List>
          {/if}
        </Command.Root>
      </div>
    </Popover.Content>
  </Popover.Root>

  <!-- Hidden input for form submission -->
  <input type="hidden" {name} {id} value={currentValue} {required} />
</div>
