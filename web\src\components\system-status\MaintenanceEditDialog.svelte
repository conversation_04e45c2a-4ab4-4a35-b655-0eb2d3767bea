<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import * as Accordion from '$lib/components/ui/accordion/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import { Checkbox } from '$lib/components/ui/checkbox/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import * as ScrollArea from '$lib/components/ui/scroll-area/index.js';
  import { History, MessageSquare } from 'lucide-svelte';
  import StatusTag from '$components/system-status/StatusTag.svelte';
  import SeverityBadge from '$components/system-status/SeverityBadge.svelte';
  import { enhance } from '$app/forms';

  import type { StatusTagType } from '$lib/types';
  import SuperDebug from 'sveltekit-superforms/client/SuperDebug.svelte';

  // Props
  export let open: boolean;
  export let editForm: any;
  export let editErrors: any;
  export let serviceOptions: Array<{ value: string; label: string }>;
  export let eventHistory: Array<any>;
  export let onClose: () => void;
  export let onSubmit: () => void;
  export let resetForm: () => void;
  export let onOpenHistory: () => void;
  export let onOpenAddUpdate: () => void;
</script>

<Dialog.Root bind:open>
  <Dialog.Overlay />
  <Dialog.Content class="sm:max-w-[600px]">
    <Dialog.Header>
      <Dialog.Title>Edit Maintenance Event</Dialog.Title>
      <Dialog.Description>Update the maintenance event details.</Dialog.Description>
    </Dialog.Header>

    <form
      method="POST"
      action="?/update"
      id="edit-maintenance-form"
      use:enhance
      on:submit|preventDefault={onSubmit}>
      <input type="hidden" name="id" bind:value={$editForm.id} />
      <!-- Ensure all required fields are included in the form submission -->
      <input type="hidden" name="affectedServices" value={$editForm.affectedServices} />

      <div class="grid gap-4 py-4">
        <div class="grid gap-2">
          <Label for="edit-title">Title</Label>
          <Input id="edit-title" bind:value={$editForm.title} placeholder="Database Maintenance" />
          {#if $editErrors.title}<p class="text-sm text-red-500">{$editErrors.title}</p>{/if}
        </div>

        <div class="grid gap-2">
          <Label for="edit-description">Description</Label>
          <Textarea bind:value={$editForm.description} />
          {#if $editErrors.description}<p class="text-sm text-red-500">
              {$editErrors.description}
            </p>{/if}
        </div>

        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div class="grid gap-2">
            <Label for="edit-startTime">Start Time</Label>
            <Input id="edit-startTime" type="datetime-local" bind:value={$editForm.startTime} />
            {#if $editErrors.startTime}<p class="text-sm text-red-500">
                {$editErrors.startTime}
              </p>{/if}
          </div>

          <div class="grid gap-2">
            <Label for="edit-endTime">End Time</Label>
            <Input id="edit-endTime" type="datetime-local" bind:value={$editForm.endTime} />
            {#if $editErrors.endTime}<p class="text-sm text-red-500">
                {$editErrors.endTime}
              </p>{/if}
          </div>
        </div>

        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div class="grid gap-2">
            <Label for="edit-status">Current Status</Label>
            <div class="flex items-center gap-2">
              <Select.Root type="single" bind:value={$editForm.status}>
                <Select.SelectTrigger class="w-full">
                  <Select.SelectValue placeholder="Select status" />
                </Select.SelectTrigger>
                <Select.SelectContent class="w-full">
                  <Select.SelectItem value="scheduled">Scheduled</Select.SelectItem>
                  <Select.SelectItem value="in-progress">In Progress</Select.SelectItem>
                  <Select.SelectItem value="completed">Completed</Select.SelectItem>
                  <Select.SelectItem value="cancelled">Cancelled</Select.SelectItem>
                </Select.SelectContent>
              </Select.Root>
              <StatusTag status={$editForm.status as StatusTagType} />
            </div>
          </div>

          <div class="grid gap-2">
            <Label for="edit-severity">Severity</Label>
            <div class="flex items-center gap-2">
              <Select.Root type="single" bind:value={$editForm.severity}>
                <Select.SelectTrigger class="w-full">
                  <Select.SelectValue placeholder="Select severity" />
                </Select.SelectTrigger>
                <Select.SelectContent class="w-full">
                  <Select.SelectItem value="info">Information</Select.SelectItem>
                  <Select.SelectItem value="maintenance">Maintenance</Select.SelectItem>
                  <Select.SelectItem value="minor">Minor Outage</Select.SelectItem>
                  <Select.SelectItem value="major">Major Outage</Select.SelectItem>
                  <Select.SelectItem value="critical">Critical Outage</Select.SelectItem>
                </Select.SelectContent>
              </Select.Root>
              <SeverityBadge severity={$editForm.severity} />
            </div>
          </div>
        </div>

        <div class="grid gap-2">
          <Label for="edit-affectedServices">Affected Services</Label>
          <Select.Root
            type="single"
            bind:value={$editForm.affectedServices[0]}
            onValueChange={(value) => {
              // Ensure we always have at least one service
              if (value) {
                $editForm.affectedServices = [value];
              } else {
                // Default to first service if none selected
                $editForm.affectedServices = [serviceOptions[0].value];
              }
            }}>
            <Select.SelectTrigger class="w-full">
              <Select.SelectValue placeholder="Select a service" />
            </Select.SelectTrigger>
            <Select.SelectContent class="w-full">
              {#each serviceOptions as option}
                <Select.SelectItem value={option.value}>{option.label}</Select.SelectItem>
              {/each}
            </Select.SelectContent>
          </Select.Root>
          {#if $editErrors.affectedServices}<p class="text-sm text-red-500">
              {$editErrors.affectedServices}
            </p>{/if}
        </div>

        <div class="flex items-center justify-between">
          <h3 class="text-sm font-medium">Comments & Status Updates</h3>
          <Button type="button" variant="outline" class="gap-2" onclick={onOpenAddUpdate}>
            <MessageSquare class="h-4 w-4" />
            Add Status Update
          </Button>
        </div>

        <ScrollArea.Root orientation="vertical" class="!mb-0 flex h-[125px] flex-col space-y-4">
          {#if eventHistory && eventHistory.length > 0}
            <div class="max-h-60 rounded-md border p-3">
              <div class="space-y-3">
                {#each eventHistory as item}
                  <div class="relative border-l-2 border-gray-200 pl-4 dark:border-gray-700">
                    <div
                      class="absolute -left-1.5 top-1 h-3 w-3 rounded-full bg-gray-200 dark:bg-gray-700">
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-2">
                        {#if item.changeType === 'status_change' && item.newStatus}
                          <StatusTag status={item.newStatus as StatusTagType} />
                        {:else if item.changeType === 'comment'}
                          <span class="text-xs font-medium">Comment</span>
                        {:else}
                          <span class="text-xs font-medium">Update</span>
                        {/if}
                        <p class="text-xs font-medium">
                          {new Date(item.createdAt).toLocaleString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                          })}
                        </p>
                      </div>
                    </div>
                    {#if item.comment}
                      <p class="mt-1 text-sm">{item.comment}</p>
                    {/if}
                  </div>
                {/each}
              </div>
            </div>
          {/if}
        </ScrollArea.Root>

        <div class="mt-4 flex items-center space-x-2">
          <Checkbox id="edit-sendNotification" bind:checked={$editForm.sendNotification} />
          <Label for="edit-sendNotification" class="text-sm font-normal">
            Send notification to all users about this update
          </Label>
        </div>
      </div>

      {#if import.meta.env.DEV}
        <Accordion.Root type="single" class="mb-4">
          <Accordion.Item value="edit-debug">
            <Accordion.Trigger
              class="flex w-full items-center justify-between rounded border p-3 text-left">
              <h3 class="text-sm font-medium">SuperForm Debug</h3>
            </Accordion.Trigger>
            <Accordion.Content class="rounded-b border border-t-0 p-4">
              <SuperDebug data={$editForm} />
            </Accordion.Content>
          </Accordion.Item>
        </Accordion.Root>
      {/if}

      <Dialog.Footer>
        <div class="flex w-full items-center justify-between">
          <Button type="button" variant="outline" size="sm" class="gap-1" onclick={onOpenHistory}>
            <History class="h-4 w-4" />
            View History
          </Button>

          <div class="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onclick={() => {
                resetForm();
                onClose();
              }}>
              Cancel
            </Button>
            <Button type="submit">Update</Button>
          </div>
        </div>
      </Dialog.Footer>
    </form>
  </Dialog.Content>
</Dialog.Root>
