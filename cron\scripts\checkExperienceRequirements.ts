import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { CircuitState } from "../utils/improvedImprovedCircuitBreaker";

// Initialize Prisma client
const prisma = new PrismaClient();

async function checkExperienceRequirements() {
  logger.info('🔍 Checking experience requirements in the database...');

  try {
    // Get jobs with experience requirements
    const jobs = await prisma.jobListing.findMany({
      where: {
        experienceRequirements: {
          not: null
        }
      },
      select: {
        id: true,
        title: true,
        company: true,
        yearsOfExperience: true,
        experienceRequirements: true
      },
      take: 5 // Limit to 5 jobs for brevity
    });

    logger.info(`📋 Found ${jobs.length} jobs with experience requirements`);
    
    // Process each job
    for (const job of jobs) {
      logger.info(`📝 Job: ${job.title} @ ${job.company} (ID: ${job.id})`);
      logger.info(`⏳ Years of Experience (simple): ${job.yearsOfExperience.join(', ')}`);
      
      // Parse the experience requirements
      try {
        const expReqs = JSON.parse(job.experienceRequirements as string);
        logger.info(`⏳ Detailed Experience Requirements (${expReqs.length}):`);
        
        expReqs.forEach((req: any) => {
          if (req.skill) {
            logger.info(`  - ${req.years} years required for ${req.skill}`);
          } else {
            logger.info(`  - ${req.years} years of general experience`);
          }
        });
      } catch (error) {
        logger.error(`❌ Error parsing experience requirements: ${error.message}`);
      }
      
      logger.info('-----------------------------------');
    }
  } catch (error) {
    logger.error(`❌ Error: ${error.message}`);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
checkExperienceRequirements()
  .then(() => {
    logger.info('✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    logger.error(`❌ Script failed: ${error.message}`);
    process.exit(1);
  });
