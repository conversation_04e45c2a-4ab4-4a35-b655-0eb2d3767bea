<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Save, X } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';

  export let open = false;
  export let title = '';
  export let fieldValue = '';
  export let fieldType = 'notes'; // 'notes' or 'nextAction'
  export let applicationId = '';
  export let onClose = () => {};
  export let onSave: (value: string) => void;

  let editedValue = '';

  $: if (open) {
    editedValue = fieldValue || '';
  }

  async function handleSave() {
    try {
      // Make an API call to save the field
      const response = await fetch(`/api/applications/${applicationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          [fieldType]: editedValue,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update ${fieldType}: ${response.statusText}`);
      }

      // Call the onSave callback with the updated value
      onSave(editedValue);

      // Close the modal
      open = false;

      // Show success message
      toast.success(`${title} updated successfully`);
    } catch (err) {
      console.error(`Error updating ${fieldType}:`, err);
      toast.error(`Failed to update ${fieldType}`);
    }
  }

  function handleCancel() {
    open = false;
    onClose();
  }
</script>

<Dialog.Root bind:open onOpenChange={handleCancel}>
  <Dialog.Portal>
    <Dialog.Overlay
      class="bg-background/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 backdrop-blur-sm" />
    <Dialog.Content
      class="bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 sm:rounded-lg md:w-full">
      <Dialog.Header>
        <Dialog.Title class="text-lg font-semibold">{title}</Dialog.Title>
        <Dialog.Description class="text-muted-foreground text-sm">
          Make changes to the {fieldType === 'notes' ? 'notes' : 'next action'} for this application.
        </Dialog.Description>
      </Dialog.Header>

      <div class="grid gap-4 py-4">
        <div class="grid gap-2">
          <textarea
            bind:value={editedValue}
            class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[150px] w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm focus-visible:outline-none focus-visible:ring-1"
            placeholder={fieldType === 'notes'
              ? 'Add notes about this application...'
              : "What's the next step for this application?"}></textarea>
        </div>
      </div>

      <Dialog.Footer class="flex items-center justify-end space-x-2">
        <Button variant="outline" onclick={handleCancel}>
          <X class="mr-1.5 h-4 w-4" />
          Cancel
        </Button>
        <Button variant="default" onclick={handleSave}>
          <Save class="mr-1.5 h-4 w-4" />
          Save Changes
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
