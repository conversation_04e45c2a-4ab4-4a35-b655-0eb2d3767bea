<script lang="ts">
  import { type SkillsSchema } from '$lib/validators/profile';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Edit, Plus } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import SkillsModal from './SkillsModal.svelte';

  // Props
  const {
    data,
    onSave,
    disabled = false,
  } = $props<{
    data: SkillsSchema;
    onSave: (data: SkillsSchema) => Promise<boolean>;
    disabled?: boolean;
  }>();

  // Modal state
  let modalOpen = $state(false);

  // Open edit modal
  function openEditModal() {
    modalOpen = true;
  }

  // Handle modal save
  async function handleSave(skills: string[]): Promise<boolean> {
    try {
      const updatedData: SkillsSchema = {
        skills: skills,
      };

      const success = await onSave(updatedData);
      return success;
    } catch (error) {
      console.error('Error saving skills:', error);
      toast.error('Failed to save skills');
      return false;
    }
  }
</script>

<div class="rounded-lg border p-6">
  <div class="flex items-center justify-between">
    <h2 class="text-xl font-semibold">Skills</h2>
    <Button variant="ghost" size="sm" onclick={openEditModal} {disabled}>
      <Edit class="mr-2 h-4 w-4" />
      Edit
    </Button>
  </div>

  <div class="mt-4">
    {#if data?.skills && data.skills.length > 0}
      <div class="flex flex-wrap gap-2">
        {#each data.skills as skill}
          <span class="bg-primary/10 text-primary rounded-full px-3 py-1 text-sm">
            {skill}
          </span>
        {/each}
      </div>
    {:else}
      <div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8">
        <p class="text-muted-foreground text-center">No skills added yet</p>
        <Button variant="outline" class="mt-4" onclick={openEditModal} {disabled}>
          <Plus class="mr-2 h-4 w-4" />
          Add Skills
        </Button>
      </div>
    {/if}
  </div>
</div>

<!-- Skills Modal -->
<SkillsModal
  open={modalOpen}
  skills={data?.skills || []}
  onClose={() => (modalOpen = false)}
  onSave={handleSave}
  {disabled} />
