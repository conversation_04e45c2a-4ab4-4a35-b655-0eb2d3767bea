<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { X } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import type { ProfileData } from '$lib/types/profile';

  // Props
  const { profileData, onSave, onCancel } = $props<{
    profileData: ProfileData;
    onSave: (data: Partial<ProfileData>) => Promise<boolean>;
    onCancel: () => void;
  }>();

  // Form data
  let valueInRole = $state<string[]>(profileData.jobPreferences?.valueInRole || []);
  let workEnvironment = $state(profileData.jobPreferences?.workEnvironment || '');

  // Common values
  const commonValues = [
    'Work-Life Balance',
    'Career Growth',
    'Impactful Work',
    'Recognition & Reward',
    'Transparency & Communication',
    'Diversity & Inclusion',
    'Innovation',
    'Stability',
    'Collaboration',
    'Autonomy',
    'Learning Opportunities',
    'Competitive Compensation',
  ];

  // Toggle value
  function toggleValue(value: string) {
    if (valueInRole.includes(value)) {
      valueInRole = valueInRole.filter((v) => v !== value);
    } else {
      if (valueInRole.length < 3) {
        valueInRole = [...valueInRole, value];
      } else {
        toast.error('You can select up to 3 values');
      }
    }
  }

  // Handle form submission
  async function handleSubmit() {
    try {
      // Prepare data
      const updatedData: Partial<ProfileData> = {
        jobPreferences: {
          ...profileData.jobPreferences,
          valueInRole,
          workEnvironment,
        },
      };

      // Save data
      const success = await onSave(updatedData);
      if (success) {
        toast.success('Job values updated successfully');
      }
    } catch (error) {
      console.error('Error saving job values:', error);
      toast.error('Failed to save job values');
    }
  }
</script>

<form on:submit|preventDefault={handleSubmit} class="space-y-6">
  <div class="space-y-2">
    <Label>What do you value in a new role?</Label>
    <p class="text-muted-foreground text-sm">Select up to 3 values that are most important to you</p>
    <div class="mt-2 grid grid-cols-2 gap-2 sm:grid-cols-3">
      {#each commonValues as value}
        <Button
          type="button"
          variant={valueInRole.includes(value) ? 'default' : 'outline'}
          size="sm"
          class="justify-start"
          on:click={() => toggleValue(value)}>
          {value}
        </Button>
      {/each}
    </div>

    {#if valueInRole.length > 0}
      <div class="mt-4">
        <Label>Selected Values</Label>
        <div class="mt-2 flex flex-wrap gap-2">
          {#each valueInRole as value}
            <Badge variant="secondary" class="flex items-center gap-1">
              {value}
              <button
                type="button"
                class="ml-1 rounded-full p-0.5 hover:bg-primary/20"
                on:click={() => toggleValue(value)}>
                <X class="h-3 w-3" />
              </button>
            </Badge>
          {/each}
        </div>
      </div>
    {/if}
  </div>

  <div class="space-y-2">
    <Label for="workEnvironment">Work Environment Preferences</Label>
    <Textarea
      id="workEnvironment"
      bind:value={workEnvironment}
      placeholder="Describe your ideal work environment..."
      rows={4} />
    <p class="text-muted-foreground text-xs">
      For example: "I thrive in collaborative environments with clear goals and regular feedback."
    </p>
  </div>

  <div class="flex justify-end space-x-2 pt-4">
    <Button variant="outline" type="button" on:click={onCancel}>Cancel</Button>
    <Button type="submit">Save Changes</Button>
  </div>
</form>
