<script lang="ts">
  import { FlexRender } from '$lib/components/ui/data-table';
  import { renderComponent } from '$lib/components/ui/data-table/render-helpers';
  import { browser } from '$app/environment';
  import {
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
  } from '@tanstack/table-core';
  import DataTableCheckbox from '$lib/components/ui/checkbox/checkbox.svelte';
  import DataTableRowActions from './data-table-row-actions.svelte';
  import DataTablePagination from './data-table-pagination.svelte';
  import DataTableToolbar from './data-table-toolbar.svelte';
  import DataTableTitleCell from './data-table-title-cell.svelte';
  import * as TableUI from '$lib/components/ui/table';

  // Use $props for data and filters - properly destructure with reactivity
  const {
    data = [],
    searchTerm = '',
    typeFilter: propTypeFilter = [],
    sourceFilter: propSourceFilter = [],
    pagination: propPagination = { pageIndex: 0, pageSize: 10 },
  } = $props<{
    data?: any[];
    searchTerm?: string;
    typeFilter?: string[];
    sourceFilter?: string[];
    pagination?: { pageIndex: number; pageSize: number };
  }>();

  // State variables
  let sorting = $state([{ id: 'createdAt', desc: true }]);
  let columnFilters = $state([]);
  let typeFilterState = $state(propTypeFilter);
  let sourceFilterState = $state(propSourceFilter);
  let rowSelection = $state<Record<string, boolean>>({});
  let pagination = $state(propPagination);
  let tableInstance = $state(null);
  let filteredDataState = $state([]);

  // Initialize all columns as visible by default
  let columnVisibility = $state<Record<string, boolean>>({
    document: true,
    type: true,
    createdAt: true,
    updatedAt: true,
    select: true,
    actions: true,
  });

  // Function to filter data based on search term
  function filterData(data: any[], searchTerm: string): any[] {
    if (!searchTerm || searchTerm.trim() === '') {
      return data;
    }

    const term = searchTerm.toLowerCase().trim();
    return data.filter((doc: any) => {
      // Search in document label/title
      if (doc.label && doc.label.toLowerCase().includes(term)) {
        return true;
      }

      // Search in document type
      if (doc.type && doc.type.toLowerCase().includes(term)) {
        return true;
      }

      // Search in document content if available
      if (doc.content && doc.content.toLowerCase().includes(term)) {
        return true;
      }

      return false;
    });
  }

  // Function to update column filters
  function updateColumnFilters() {
    // Create a new array to trigger reactivity
    const newFilters = [];

    // Add type filter if active
    if (typeFilterState && typeFilterState.length > 0) {
      newFilters.push({
        id: 'type',
        value: typeFilterState,
      });
    }

    // Add source filter if active
    if (sourceFilterState && sourceFilterState.length > 0) {
      newFilters.push({
        id: 'source',
        value: sourceFilterState,
      });
    }

    // Update column filters
    columnFilters = newFilters;

    // Update source filter in document column meta
    if (tableInstance) {
      const documentColumn = tableInstance.getColumn('document');
      if (documentColumn && documentColumn.columnDef) {
        // Initialize meta if it doesn't exist
        if (!documentColumn.columnDef.meta) {
          documentColumn.columnDef.meta = {};
        }

        // Now safely update meta
        documentColumn.columnDef.meta = {
          ...documentColumn.columnDef.meta,
          sourceFilter: sourceFilterState || [],
        };
      }
    }
  }

  // Function to handle document deletion
  function handleDeleted(id: string) {
    console.log('Document deleted:', id);

    // Dispatch an event to notify other components
    const event = new CustomEvent('documentDeleted', { detail: id });
    document.dispatchEvent(event);
  }

  // Define createSvelteTable function
  function createSvelteTable(options: any) {
    const {
      data,
      columns,
      state,
      onSortingChange,
      onColumnFiltersChange,
      onRowSelectionChange,
      onPaginationChange,
      onColumnVisibilityChange,
      getCoreRowModel,
      getFilteredRowModel,
      getPaginationRowModel,
      getSortedRowModel,
      enableRowSelection,
    } = options;

    // Create a basic table instance
    const table = {
      data,
      columns,
      state,
      onSortingChange,
      onColumnFiltersChange,
      onRowSelectionChange,
      onPaginationChange,
      onColumnVisibilityChange,
      getCoreRowModel,
      getFilteredRowModel,
      getPaginationRowModel,
      getSortedRowModel,
      enableRowSelection,

      // Methods
      getHeaderGroups: () => {
        return [
          {
            headers: columns.map((column: any) => ({
              column: {
                columnDef: column,
                id: column.id,
                getContext: () => ({}),
              },
              colSpan: 1,
              isPlaceholder: false,
              id: column.id,
              getContext: () => ({}),
            })),
          },
        ];
      },

      getRowModel: () => {
        // Ensure data is an array before mapping
        const safeData = Array.isArray(data) ? data : [];

        return {
          rows: safeData.map((row, index) => ({
            id: row.id || `row-${index}`,
            original: row,
            getVisibleCells: () => {
              return columns.map((column: any) => ({
                column: {
                  columnDef: column,
                  id: column.id,
                },
                getContext: () => ({
                  row: {
                    original: row,
                    id: row.id,
                    getValue: (id: string) => row[id],
                  },
                }),
              }));
            },
          })),
        };
      },

      getAllColumns: () => {
        return columns;
      },

      getColumn: (id: string) => {
        return columns.find((column: any) => column.id === id);
      },

      getState: () => {
        return state;
      },

      setState: (newState: any) => {
        Object.assign(state, newState);

        // Call appropriate change handlers
        if (newState.sorting && onSortingChange) {
          onSortingChange(newState.sorting);
        }

        if (newState.columnFilters && onColumnFiltersChange) {
          onColumnFiltersChange(newState.columnFilters);
        }

        if (newState.rowSelection && onRowSelectionChange) {
          onRowSelectionChange(newState.rowSelection);
        }

        if (newState.pagination && onPaginationChange) {
          onPaginationChange(newState.pagination);
        }

        if (newState.columnVisibility && onColumnVisibilityChange) {
          onColumnVisibilityChange(newState.columnVisibility);
        }
      },

      setColumnVisibility: (visibility: any) => {
        if (onColumnVisibilityChange) {
          onColumnVisibilityChange(visibility);
        }
      },

      getIsAllPageRowsSelected: () => {
        // Ensure data is an array
        const safeData = Array.isArray(data) ? data : [];
        return safeData.length > 0 && safeData.every((row) => state.rowSelection[row.id]);
      },

      getIsSomePageRowsSelected: () => {
        // Ensure data is an array
        const safeData = Array.isArray(data) ? data : [];
        return safeData.some((row) => state.rowSelection[row.id]);
      },

      toggleAllPageRowsSelected: (value: boolean) => {
        const newSelection = { ...state.rowSelection };
        // Ensure data is an array
        const safeData = Array.isArray(data) ? data : [];

        safeData.forEach((row) => {
          if (value) {
            newSelection[row.id] = true;
          } else {
            delete newSelection[row.id];
          }
        });
        onRowSelectionChange(newSelection);
      },
    };

    return table;
  }

  // Initialize table
  function initializeTable() {
    if (browser) {
      // Ensure filteredDataState is an array
      const safeData = Array.isArray(filteredDataState) ? filteredDataState : [];

      console.log('Initializing table with data:', safeData.length, 'items');

      // Create the table instance even if there's no data
      tableInstance = createSvelteTable({
        data: safeData,
        columns: [
          // Select column
          {
            id: 'select',
            header: () => {
              return renderComponent(DataTableCheckbox, {
                checked:
                  filteredDataState.length > 0 &&
                  filteredDataState.every((row) => rowSelection[row.id]),
                indeterminate:
                  filteredDataState.some((row) => rowSelection[row.id]) &&
                  !(
                    filteredDataState.length > 0 &&
                    filteredDataState.every((row) => rowSelection[row.id])
                  ),
                onCheckedChange: (value) => {
                  toggleAllRowsSelection(!!value);
                },
                'aria-label': 'Select all',
              });
            },
            cell: ({ row }) => {
              return renderComponent(DataTableCheckbox, {
                checked: rowSelection[row.original.id] || false,
                onCheckedChange: (value) => {
                  toggleRowSelection(row.original.id, !!value);
                },
                'aria-label': 'Select row',
                class: 'translate-y-[2px]',
              });
            },
            enableSorting: false,
            width: '15px',
          },
          // Document column
          {
            accessorKey: 'label',
            header: 'Document Name',
            id: 'document',
            enableSorting: true,
            width: '60%',
            minWidth: '200px',
            meta: {
              sourceFilter: sourceFilterState || [],
            },
            cell: ({ row }) => {
              return renderComponent(DataTableTitleCell, { document: row.original });
            },
          },
          // Type column
          {
            accessorKey: 'type',
            header: 'Type',
            id: 'type',
            enableSorting: true,
            width: '120px',
            cell: ({ row }) => row.getValue('type'),
          },
          // Created column
          {
            accessorKey: 'createdAt',
            header: 'Created',
            id: 'createdAt',
            enableSorting: true,
            sortingFn: 'datetime',
            sortDescFirst: true,
            width: '190px',
            cell: ({ row }) => {
              const dateStr = row.getValue('createdAt');
              if (!dateStr) return 'N/A';

              const date = new Date(dateStr);
              if (isNaN(date.getTime())) return 'Invalid date';

              return new Intl.DateTimeFormat('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
              }).format(date);
            },
          },
          // Updated column
          {
            accessorKey: 'updatedAt',
            header: 'Edited',
            id: 'updatedAt',
            enableSorting: true,
            sortingFn: 'datetime',
            sortDescFirst: true,
            width: '190px',
            cell: ({ row }) => {
              const dateStr = row.getValue('updatedAt');
              if (!dateStr) return 'N/A';

              const date = new Date(dateStr);
              if (isNaN(date.getTime())) return 'Invalid date';

              return new Intl.DateTimeFormat('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
              }).format(date);
            },
          },
          // Actions column
          {
            id: 'actions',
            width: '10%',
            minWidth: '60px',
            cell: ({ row }) => {
              return renderComponent(DataTableRowActions, {
                row: row.original,
                on_deleted: (id) => handleDeleted(id),
              });
            },
          },
        ],
        state: {
          sorting,
          columnFilters,
          rowSelection,
          pagination,
          columnVisibility,
          globalFilter: '',
        },
        onSortingChange: (updater: any) => {
          sorting = typeof updater === 'function' ? updater(sorting) : updater;
        },
        onColumnFiltersChange: (updater: any) => {
          columnFilters = typeof updater === 'function' ? updater(columnFilters) : updater;
        },
        onRowSelectionChange: (updater: any) => {
          rowSelection = typeof updater === 'function' ? updater(rowSelection) : updater;
        },
        onPaginationChange: (updater: any) => {
          pagination = typeof updater === 'function' ? updater(pagination) : updater;
        },
        onColumnVisibilityChange: (updater: any) => {
          columnVisibility = typeof updater === 'function' ? updater(columnVisibility) : updater;
        },
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        enableRowSelection: true,
      });
    }
  }

  // Process data without using effects
  function processData() {
    // Ensure data is an array before processing
    const dataArray = Array.isArray(data) ? data : [];

    // Process the data
    const processed = dataArray.map((doc) => ({
      ...doc,
      id: doc.id ?? `temp-${Math.random().toString(36).substring(2, 9)}`,
      label: doc.label ?? 'Untitled Document',
      type: doc.type ?? 'document',
      createdAt: doc.createdAt ?? new Date().toISOString(),
      updatedAt: doc.updatedAt ?? doc.createdAt ?? new Date().toISOString(),
      source: doc.source ?? 'uploaded',
    }));

    return processed;
  }

  // Get selected row count
  function getSelectedRowCount() {
    return Object.keys(rowSelection).length;
  }

  // Toggle row selection
  function toggleRowSelection(id: string, selected?: boolean) {
    // Update rowSelection directly
    const newRowSelection = { ...rowSelection };

    if (selected !== undefined) {
      if (selected) {
        newRowSelection[id] = true;
      } else {
        delete newRowSelection[id];
      }
    } else {
      if (newRowSelection[id]) {
        delete newRowSelection[id];
      } else {
        newRowSelection[id] = true;
      }
    }

    rowSelection = newRowSelection;
  }

  // Toggle all rows selection
  function toggleAllRowsSelection(selected: boolean) {
    const newRowSelection = { ...rowSelection };

    // Ensure filteredData is an array
    const safeData = Array.isArray(filteredDataState) ? filteredDataState : [];

    safeData.forEach((row) => {
      if (selected) {
        newRowSelection[row.id] = true;
      } else {
        delete newRowSelection[row.id];
      }
    });

    rowSelection = newRowSelection;
  }

  // Handle search change
  function handleSearchChange(value: string) {
    // Dispatch event to parent component
    dispatchEvent(new CustomEvent('searchChange', { detail: value }));

    // Update filtered data
    updateTable();
  }

  // Handle pagination change
  function handlePaginationChange(value: any) {
    pagination = value;

    // Dispatch event to parent component
    dispatchEvent(new CustomEvent('paginationChange', { detail: value }));
  }

  // Update table data
  function updateTable() {
    // Filter data based on search term
    let filtered = filterData(data, searchTerm);

    // Apply column filters
    const applyColumnFilters = (data: any[]) => {
      if (!columnFilters.length) return data;

      return data.filter((row) => {
        return columnFilters.every((filter) => {
          const value = row[filter.id];
          if (Array.isArray(filter.value)) {
            return filter.value.length === 0 || filter.value.includes(value);
          }
          return true;
        });
      });
    };

    filtered = applyColumnFilters(filtered);

    // Update state
    filteredDataState = filtered;

    // Initialize table if not already initialized
    if (!tableInstance) {
      initializeTable();
    } else if (tableInstance.setState) {
      // Update table data
      tableInstance.data = filtered;
    }
  }

  // Create a custom handler for filter changes
  function handleTypeFilterChange(values: string[]) {
    typeFilterState = values;
    dispatchEvent(new CustomEvent('typeFilterChange', { detail: values }));
    updateTable();
  }

  function handleSourceFilterChange(values: string[]) {
    sourceFilterState = values;
    dispatchEvent(new CustomEvent('sourceFilterChange', { detail: values }));
    updateTable();
  }

  // Initialize the component
  if (browser) {
    // Update filtered data initially
    filteredDataState = filterData(data, searchTerm);

    // Initialize table
    initializeTable();

    // Update column filters
    updateColumnFilters();
  }

  // This is needed to make TypeScript happy
  export type DataTableProps = {
    data: any[];
    searchTerm?: string;
    typeFilter?: string[];
    sourceFilter?: string[];
    pagination?: { pageIndex: number; pageSize: number };
  };
</script>

{#if tableInstance}
  <div class="w-full space-y-4">
    <DataTableToolbar
      tableModel={tableInstance}
      data={processData()}
      {searchTerm}
      typeFilter={typeFilterState}
      sourceFilter={sourceFilterState}
      onSearchChange={(value) => handleSearchChange(value)}
      onTypeFilterChange={(values) => handleTypeFilterChange(values)}
      onSourceFilterChange={(values) => handleSourceFilterChange(values)} />
    <div class="border-border w-full rounded-md border">
      <TableUI.Root class="w-full">
        <TableUI.Header>
          {#each tableInstance.getHeaderGroups() as headerGroup}
            <TableUI.Row>
              {#each headerGroup.headers as header}
                <TableUI.Head
                  colSpan={header.colSpan}
                  style={header.column.columnDef.width
                    ? `width: ${header.column.columnDef.width}; min-width: ${header.column.columnDef.minWidth || '50px'};`
                    : ''}>
                  {#if !header.isPlaceholder}
                    <FlexRender
                      content={header.column.columnDef.header}
                      context={header.getContext ? header.getContext() : {}} />
                  {/if}
                </TableUI.Head>
              {/each}
            </TableUI.Row>
          {/each}
        </TableUI.Header>
        <TableUI.Body>
          {#if tableInstance.getRowModel().rows.length}
            {#each tableInstance.getRowModel().rows as row}
              <TableUI.Row class={rowSelection[row.id] ? 'bg-muted' : ''}>
                {#each row.getVisibleCells() as cell}
                  <TableUI.Cell
                    style={cell.column.columnDef.width
                      ? `width: ${cell.column.columnDef.width}; min-width: ${cell.column.columnDef.minWidth || '50px'};`
                      : ''}>
                    <FlexRender content={cell.column.columnDef.cell} context={cell.getContext()} />
                  </TableUI.Cell>
                {/each}
              </TableUI.Row>
            {/each}
          {:else}
            <TableUI.Row>
              <TableUI.Cell colspan={tableInstance.getAllColumns().length} class="h-24 text-center">
                No results.
              </TableUI.Cell>
            </TableUI.Row>
          {/if}
        </TableUI.Body>
      </TableUI.Root>
    </div>
    <DataTablePagination
      tableModel={tableInstance}
      selectedRowCount={getSelectedRowCount()}
      totalRowCount={filteredDataState.length}
      onPaginationChange={handlePaginationChange} />
  </div>
{:else}
  <div class="flex h-24 items-center justify-center">
    <p>Loading...</p>
  </div>
{/if}
