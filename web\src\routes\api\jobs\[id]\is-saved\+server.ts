import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ params, locals }) => {
  // Get the user from the session
  const user = locals.user;

  if (!user) {
    // If user is not authenticated, return false instead of 401
    return json({
      success: true,
      isSaved: false,
    });
  }

  try {
    const jobId = params.id;

    // Check if the job is saved
    const savedJob = await prisma.savedJob.findUnique({
      where: {
        userId_jobId: {
          userId: user.id,
          jobId: jobId,
        },
      },
    });

    return json({
      success: true,
      isSaved: !!savedJob,
    });
  } catch (error) {
    console.error('Error checking if job is saved:', error);
    return json({ error: 'Failed to check if job is saved' }, { status: 500 });
  }
};
