// scraper/utils/prismaClient.ts
// Centralized Prisma client management for the scraper service

import { PrismaClient } from "@prisma/client";
import { logger } from "./logger";

// Global singleton Prisma client instance
let globalPrismaClient: PrismaClient | null = null;

/**
 * Initialize the global Prisma client
 * This should be called once at application startup
 */
export async function initializePrismaClient(): Promise<PrismaClient> {
  if (globalPrismaClient) {
    logger.info(
      "✅ Prisma client already initialized, returning existing instance"
    );
    return globalPrismaClient;
  }

  try {
    logger.info("🔄 Initializing global Prisma client...");

    globalPrismaClient = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
      log: [
        { level: "warn", emit: "event" },
        { level: "error", emit: "event" },
      ],
      // Connection pool configuration optimized for scraper workload
      __internal: {
        engine: {
          connectionLimit: 5, // Reasonable limit for scraper service
        },
      },
    });

    // Add event listeners for logging
    globalPrismaClient.$on("warn", (e) => {
      logger.warn(`Prisma warning: ${e.message}`);
    });

    globalPrismaClient.$on("error", (e) => {
      logger.error(`Prisma error: ${e.message}`);
    });

    // Test the connection
    await globalPrismaClient.$connect();

    logger.info(
      "✅ Global Prisma client initialized and connected successfully"
    );
    return globalPrismaClient;
  } catch (error) {
    logger.error("❌ Failed to initialize global Prisma client:", error);
    globalPrismaClient = null;
    throw new Error("Failed to initialize Prisma client");
  }
}

/**
 * Get the global Prisma client instance
 * @returns The global Prisma client
 */
export function getPrismaClient(): PrismaClient {
  if (!globalPrismaClient) {
    logger.error(
      "❌ Prisma client not initialized. Call initializePrismaClient() first."
    );
    throw new Error(
      "Prisma client not initialized. Call initializePrismaClient() first."
    );
  }

  return globalPrismaClient;
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use getPrismaClient() instead
 */
export async function getPrismaClientAsync(
  schema?: string
): Promise<PrismaClient> {
  if (!globalPrismaClient) {
    logger.warn("⚠️ Prisma client not initialized, initializing now...");
    return await initializePrismaClient();
  }

  return globalPrismaClient;
}

/**
 * Execute a raw query on the database
 * @param query The SQL query to execute
 * @param params Parameters for the query
 * @param schema The schema to use for the query
 * @returns The query result
 */
export async function executeRawQuery(
  query: string,
  params: any[] = [],
  schema: string = "web"
): Promise<any> {
  const client = getPrismaClient();

  try {
    // First set the search path
    await client.$executeRawUnsafe(`SET search_path TO "${schema}"`);

    // Then execute the actual query
    return await client.$queryRawUnsafe(query, ...params);
  } catch (error) {
    logger.error(`❌ Error executing raw query in schema ${schema}:`, error);
    throw error;
  }
}

/**
 * Disconnect the global Prisma client
 * Call this when shutting down the application to properly close connections
 */
export async function disconnectPrisma(): Promise<void> {
  if (globalPrismaClient) {
    try {
      await globalPrismaClient.$disconnect();
      logger.info("✅ Global Prisma client disconnected successfully");
      globalPrismaClient = null;
    } catch (error) {
      logger.error("❌ Error disconnecting global Prisma client:", error);
    }
  } else {
    logger.info("ℹ️ No Prisma client to disconnect");
  }
}

// Handle application shutdown to properly close database connections
process.on("SIGINT", async () => {
  logger.info("Received SIGINT signal, disconnecting Prisma clients");
  await disconnectPrisma();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  logger.info("Received SIGTERM signal, disconnecting Prisma clients");
  await disconnectPrisma();
  process.exit(0);
});

// Export default client for backward compatibility
export default getPrismaClient;
