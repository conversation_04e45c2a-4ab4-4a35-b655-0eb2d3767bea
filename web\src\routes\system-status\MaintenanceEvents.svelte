<script lang="ts">
  import * as Accordion from '$lib/components/ui/accordion/index.js';
  import MaintenanceAccordion from './MaintenanceAccordion.svelte';
  import type { Incident } from './types';

  // Props
  const { recentIncidents } = $props<{
    recentIncidents: Incident[];
  }>();
</script>

<div class="max-auto container mb-8 px-8">
  <h2 class="mb-4 text-xl font-semibold">Recent Notices</h2>

  {#if recentIncidents.length > 0}
    <Accordion.Root type="multiple" class="w-full space-y-4">
      {#each recentIncidents as incident, i}
        <MaintenanceAccordion {incident} index={i} />
      {/each}
    </Accordion.Root>
  {:else}
    <div class="rounded-lg border p-6 text-center">
      <div class="flex items-center justify-center">
        <div class="mr-2 h-4 w-4 rounded-full bg-green-500"></div>
        <p class="text-muted-foreground">No notices reported for the past 7 days</p>
      </div>
    </div>
  {/if}
</div>
