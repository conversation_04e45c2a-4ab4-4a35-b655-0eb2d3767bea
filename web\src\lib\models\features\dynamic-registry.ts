// src/lib/models/features/dynamic-registry.ts
/**
 * This file serves as a bridge between the old static registry and the new dynamic database approach.
 * It provides the same interface as the old registry, but fetches data from the database instead.
 */

import { FeatureCategory } from './features';
import type { Feature, FeatureLimit } from './types';
import { browser } from '$app/environment';

// Cache for features and limits
let featuresCache: Feature[] = [];
let limitsCache: Record<string, FeatureLimit> = {};
let lastFetchTime = 0;
const CACHE_TTL = 60 * 1000; // 1 minute

/**
 * Fetch features from the database
 * @returns A promise that resolves to an array of features
 */
async function fetchFeatures(): Promise<Feature[]> {
  try {
    // Check if cache is valid
    const now = Date.now();
    if (featuresCache.length > 0 && now - lastFetchTime < CACHE_TTL) {
      return featuresCache;
    }

    // Skip the fetch on server-side rendering to avoid issues
    // We'll only fetch on the client side
    if (!browser) {
      console.log('Skipping features fetch on server side');
      return [];
    }

    // Fetch features from the API
    const response = await fetch('/api/admin/features', {
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error('Failed to fetch features');
    }

    const data = await response.json();
    featuresCache = data.features ?? [];
    lastFetchTime = now;

    // Update limits cache
    limitsCache = {};
    for (const feature of featuresCache) {
      if (feature.limits) {
        for (const limit of feature.limits) {
          limitsCache[limit.id] = limit;
        }
      }
    }

    return featuresCache;
  } catch (error) {
    console.error('Error fetching features:', error);
    return [];
  }
}

/**
 * Get all features
 * @returns A promise that resolves to an array of features
 */
export async function getAllFeatures(): Promise<Feature[]> {
  return await fetchFeatures();
}

/**
 * Get a feature by its ID
 * @param id Feature ID
 * @returns A promise that resolves to a feature or undefined if not found
 */
export async function getFeatureByIdAsync(id: string): Promise<Feature | undefined> {
  const features = await fetchFeatures();
  return features.find((feature) => feature.id === id);
}

/**
 * Get features by category
 * @param category Feature category
 * @returns A promise that resolves to an array of features in the category
 */
export async function getFeaturesByCategoryAsync(category: FeatureCategory): Promise<Feature[]> {
  const features = await fetchFeatures();
  return features.filter((feature) => feature.category === category);
}

/**
 * Get a feature limit by its ID
 * @param id Limit ID
 * @returns A promise that resolves to a feature limit or undefined if not found
 */
export async function getFeatureLimitByIdAsync(id: string): Promise<FeatureLimit | undefined> {
  await fetchFeatures(); // Ensure limits cache is updated
  return limitsCache[id];
}

/**
 * Get all limits for a feature
 * @param featureId Feature ID
 * @returns A promise that resolves to an array of feature limits
 */
export async function getLimitsForFeatureAsync(featureId: string): Promise<FeatureLimit[]> {
  const feature = await getFeatureByIdAsync(featureId);
  return feature?.limits || [];
}

// Synchronous versions for backward compatibility
// These will use the cache and may return stale or empty data if the cache is not yet populated

/**
 * Get all features (synchronous version)
 * @returns An array of features from the cache
 */
export const FEATURES: Feature[] = [];

/**
 * Get all feature limits (synchronous version)
 * @returns A record of feature limits from the cache
 */
export const FEATURE_LIMITS: Record<string, FeatureLimit> = {};

/**
 * Get a feature by its ID (synchronous version)
 * @param id Feature ID
 * @returns A feature or undefined if not found in the cache
 */
export function getFeatureById(id: string): Feature | undefined {
  return featuresCache.find((feature) => feature.id === id);
}

/**
 * Get features by category (synchronous version)
 * @param category Feature category
 * @returns An array of features in the category from the cache
 */
export function getFeaturesByCategory(category: FeatureCategory): Feature[] {
  return featuresCache.filter((feature) => feature.category === category);
}

/**
 * Get a feature limit by its ID (synchronous version)
 * @param id Limit ID
 * @returns A feature limit or undefined if not found in the cache
 */
export function getFeatureLimitById(id: string): FeatureLimit | undefined {
  return limitsCache[id];
}

/**
 * Get all limits for a feature (synchronous version)
 * @param featureId Feature ID
 * @returns An array of feature limits from the cache
 */
export function getLimitsForFeature(featureId: string): FeatureLimit[] {
  const feature = getFeatureById(featureId);
  return feature?.limits || [];
}

// Initialize the cache
fetchFeatures().then((features) => {
  // Update the FEATURES array
  FEATURES.length = 0;
  FEATURES.push(...features);

  // Update the FEATURE_LIMITS object
  for (const key in limitsCache) {
    FEATURE_LIMITS[key] = limitsCache[key];
  }
});
