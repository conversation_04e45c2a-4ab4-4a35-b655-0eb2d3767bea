/**
 * Seed Feature Limits Script
 * 
 * This script seeds feature limits to the database.
 * It uses the feature limits defined in feature-limits.ts.
 */

import { prisma, FEATURE_LIMITS, FEATURE_TO_LIMITS_MAP } from './feature-limits';

// Define interfaces for type safety
interface Feature {
  id: string;
  name: string;
  description?: string;
  category?: string;
  icon?: string;
  beta?: boolean;
}

interface FeatureLimit {
  id: string;
  featureId: string;
  name: string;
  description: string;
  defaultValue: string;
  type: string;
  unit: string | null;
  resetDay: number | null;
}

async function main(): Promise<void> {
  console.log('Starting feature limits seeding...');
  
  let featuresCreated = 0;
  let featuresUpdated = 0;
  let limitsCreated = 0;
  let limitsUpdated = 0;
  let errors = 0;
  
  try {
    // Get all features from the database
    const dbFeatures = await prisma.feature.findMany();
    console.log(`Found ${dbFeatures.length} features in the database`);
    
    // Process each feature
    for (const [featureId, limitIds] of Object.entries(FEATURE_TO_LIMITS_MAP)) {
      try {
        // Check if the feature exists in the database
        const existingFeature = dbFeatures.find(f => f.id === featureId);
        
        if (!existingFeature) {
          console.log(`Feature ${featureId} not found in database, skipping...`);
          continue;
        }
        
        console.log(`Processing feature: ${featureId} with ${limitIds.length} limits`);
        
        // Process each limit for this feature
        for (const limitId of limitIds) {
          const limit = FEATURE_LIMITS[limitId];
          
          if (!limit) {
            console.warn(`Limit ${limitId} not found in FEATURE_LIMITS, skipping...`);
            continue;
          }
          
          // Check if the limit already exists
          const existingLimit = await prisma.featureLimit.findUnique({
            where: { id: limitId },
          });
          
          if (!existingLimit) {
            // Create the limit
            await prisma.featureLimit.create({
              data: {
                id: limitId,
                featureId: featureId,
                name: limit.name,
                description: limit.description || '',
                defaultValue: limit.defaultValue.toString(),
                type: limit.type,
                unit: limit.unit || null,
                resetDay: limit.resetDay || null,
              },
            });
            console.log(`Created feature limit: ${limit.name} for feature ${featureId}`);
            limitsCreated++;
          } else {
            // Update the limit
            await prisma.featureLimit.update({
              where: { id: limitId },
              data: {
                name: limit.name,
                description: limit.description || '',
                defaultValue: limit.defaultValue.toString(),
                type: limit.type,
                unit: limit.unit || null,
                resetDay: limit.resetDay || null,
              },
            });
            console.log(`Updated feature limit: ${limit.name} for feature ${featureId}`);
            limitsUpdated++;
          }
        }
      } catch (error) {
        console.error(`Error processing feature ${featureId}:`, error);
        errors++;
      }
    }
    
    console.log('\nFeature limits seeding completed!');
    console.log(`Features created: ${featuresCreated}`);
    console.log(`Features updated: ${featuresUpdated}`);
    console.log(`Limits created: ${limitsCreated}`);
    console.log(`Limits updated: ${limitsUpdated}`);
    console.log(`Errors: ${errors}`);
    
  } catch (error) {
    console.error('Error seeding feature limits:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
