<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import { Save } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import type { EmploymentInfoData } from './EmploymentInfoForm.svelte';

  // Props
  const { open, data, onClose, onSave } = $props<{
    open: boolean;
    data: EmploymentInfoData;
    onClose: () => void;
    onSave: (data: EmploymentInfoData) => Promise<boolean>;
  }>();

  // Initialize form data
  let formData = $state<EmploymentInfoData>({
    ethnicity: data?.ethnicity || '',
    authorizedUS: data?.authorizedUS || false,
    authorizedCanada: data?.authorizedCanada || false,
    authorizedUK: data?.authorizedUK || false,
    requireSponsorship: data?.requireSponsorship || false,
    disability: data?.disability || false,
    lgbtq: data?.lgbtq || false,
    gender: data?.gender || '',
    veteran: data?.veteran || false,
  });

  // Form state
  let submitting = $state(false);

  // Reset form when modal opens
  $effect(() => {
    if (open) {
      formData = {
        ethnicity: data?.ethnicity || '',
        authorizedUS: data?.authorizedUS || false,
        authorizedCanada: data?.authorizedCanada || false,
        authorizedUK: data?.authorizedUK || false,
        requireSponsorship: data?.requireSponsorship || false,
        disability: data?.disability || false,
        lgbtq: data?.lgbtq || false,
        gender: data?.gender || '',
        veteran: data?.veteran || false,
      };
    }
  });

  // Ethnicity options
  const ethnicityOptions = [
    { value: 'american_indian', label: 'American Indian or Alaska Native' },
    { value: 'asian', label: 'Asian' },
    { value: 'black', label: 'Black or African American' },
    { value: 'hispanic', label: 'Hispanic or Latino' },
    { value: 'native_hawaiian', label: 'Native Hawaiian or Other Pacific Islander' },
    { value: 'white', label: 'White' },
    { value: 'two_or_more', label: 'Two or More Races' },
    { value: 'prefer_not_to_say', label: 'Prefer not to say' },
  ];

  // Gender options
  const genderOptions = [
    { value: 'male', label: 'Male' },
    { value: 'female', label: 'Female' },
    { value: 'non_binary', label: 'Non-binary' },
    { value: 'other', label: 'Other' },
    { value: 'prefer_not_to_say', label: 'Prefer not to say' },
  ];

  // Handle form submission
  async function handleSubmit() {
    // Set submitting state
    submitting = true;

    try {
      // Save employment information
      const success = await onSave(formData);
      if (success) {
        toast.success('Employment information updated successfully');
        onClose();
      }
    } catch (error) {
      console.error('Error saving employment information:', error);
      toast.error('Failed to save employment information');
    } finally {
      submitting = false;
    }
  }

  // Toggle boolean values
  function toggleValue(field: keyof EmploymentInfoData) {
    formData[field] = !formData[field as keyof typeof formData];
  }
</script>

<Dialog.Root {open} onOpenChange={(isOpen) => !isOpen && onClose()}>
  <Dialog.Content class="sm:max-w-[600px]">
    <Dialog.Header>
      <Dialog.Title>Edit Employment Information</Dialog.Title>
      <Dialog.Description>
        Update your employment information to help with job applications.
      </Dialog.Description>
    </Dialog.Header>

    <div class="max-h-[60vh] overflow-y-auto">
      <div class="grid gap-4 py-4">
        <div class="grid gap-2">
          <Label>What is your ethnicity?</Label>
          <div class="flex flex-wrap gap-2">
            {#each ethnicityOptions as option}
              <Button
                type="button"
                variant={formData.ethnicity === option.value ? 'default' : 'outline'}
                class="flex-grow-0"
                onclick={() => (formData.ethnicity = option.value)}>
                {option.label}
              </Button>
            {/each}
          </div>
        </div>

        <div class="grid gap-2">
          <Label>Are you authorized to work in the US?</Label>
          <div class="flex items-center space-x-2">
            <Button
              type="button"
              variant={formData.authorizedUS ? 'default' : 'outline'}
              onclick={() => toggleValue('authorizedUS')}>
              {formData.authorizedUS ? 'Yes' : 'No'}
            </Button>
          </div>
        </div>

        <div class="grid gap-2">
          <Label>Are you authorized to work in Canada?</Label>
          <div class="flex items-center space-x-2">
            <Button
              type="button"
              variant={formData.authorizedCanada ? 'default' : 'outline'}
              onclick={() => toggleValue('authorizedCanada')}>
              {formData.authorizedCanada ? 'Yes' : 'No'}
            </Button>
          </div>
        </div>

        <div class="grid gap-2">
          <Label>Are you authorized to work in the United Kingdom?</Label>
          <div class="flex items-center space-x-2">
            <Button
              type="button"
              variant={formData.authorizedUK ? 'default' : 'outline'}
              onclick={() => toggleValue('authorizedUK')}>
              {formData.authorizedUK ? 'Yes' : 'No'}
            </Button>
          </div>
        </div>

        <div class="grid gap-2">
          <Label>Will you now or in the future require sponsorship for employment visa status?</Label>
          <div class="flex items-center space-x-2">
            <Button
              type="button"
              variant={formData.requireSponsorship ? 'default' : 'outline'}
              onclick={() => toggleValue('requireSponsorship')}>
              {formData.requireSponsorship ? 'Yes' : 'No'}
            </Button>
          </div>
        </div>

        <div class="grid gap-2">
          <Label>Do you have a disability?</Label>
          <div class="flex items-center space-x-2">
            <Button
              type="button"
              variant={formData.disability ? 'default' : 'outline'}
              onclick={() => toggleValue('disability')}>
              {formData.disability ? 'Yes' : 'No'}
            </Button>
          </div>
        </div>

        <div class="grid gap-2">
          <Label>Do you identify as LGBTQ+?</Label>
          <div class="flex items-center space-x-2">
            <Button
              type="button"
              variant={formData.lgbtq ? 'default' : 'outline'}
              onclick={() => toggleValue('lgbtq')}>
              {formData.lgbtq ? 'Yes' : 'No'}
            </Button>
          </div>
        </div>

        <div class="grid gap-2">
          <Label>What is your gender?</Label>
          <div class="flex flex-wrap gap-2">
            {#each genderOptions as option}
              <Button
                type="button"
                variant={formData.gender === option.value ? 'default' : 'outline'}
                class="flex-grow-0"
                onclick={() => (formData.gender = option.value)}>
                {option.label}
              </Button>
            {/each}
          </div>
        </div>

        <div class="grid gap-2">
          <Label>Are you a veteran?</Label>
          <div class="flex items-center space-x-2">
            <Button
              type="button"
              variant={formData.veteran ? 'default' : 'outline'}
              onclick={() => toggleValue('veteran')}>
              {formData.veteran ? 'Yes' : 'No'}
            </Button>
          </div>
        </div>
      </div>
    </div>

    <Dialog.Footer>
      <Button variant="outline" onclick={onClose}>Cancel</Button>
      <Button onclick={handleSubmit} disabled={submitting}>
        {#if submitting}
          <span class="mr-2">Saving...</span>
        {:else}
          <Save class="mr-2 h-4 w-4" />
          Save Changes
        {/if}
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
