<script lang="ts">
  import { ShieldAlert } from 'lucide-svelte';
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';

  // Check if user has admin access
  let isAdmin = false;
  let isLoading = true;

  // Check user role on mount
  onMount(async () => {
    try {
      const response = await fetch('/api/user/me');
      if (response.ok) {
        const userData = await response.json();
        isAdmin = userData.isAdmin === true;
      }
    } catch (error) {
      console.error('Error checking user role:', error);
    } finally {
      isLoading = false;

      // Redirect if not admin
      if (!isAdmin) {
        goto('/dashboard');
      }
    }
  });
</script>

{#if isLoading}
  <div class="container mx-auto py-8">
    <div class="flex h-64 items-center justify-center">
      <div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent">
      </div>
    </div>
  </div>
{:else if isAdmin}
  <div class="w-full">
    <slot />
  </div>
{:else}
  <div class="container mx-auto py-8">
    <div class="flex h-64 flex-col items-center justify-center">
      <ShieldAlert class="mb-4 h-16 w-16 text-red-500" />
      <h2 class="mb-2 text-2xl font-bold">Access Denied</h2>
      <p class="text-muted-foreground mb-4">You don't have permission to access email settings.</p>
      <a href="/dashboard" class="text-primary hover:underline">Return to Dashboard</a>
    </div>
  </div>
{/if}
