<script lang="ts">
  export let profiles: any[] = [];
  export let selectedProfileId: string | null = null;

  import * as Select from '$lib/components/ui/select';
</script>

<Select.Root
  type="single"
  value={selectedProfileId}
  onValueChange={(name: string) => {
    selectedProfileId = name;
  }}>
  <Select.Trigger class="w-50 bg-foreground text-background px-3 py-2">
    <Select.Value
      placeholder={profiles.find((o) => o.name === selectedProfileId)?.name || 'Select Profile'} />
  </Select.Trigger>
  <Select.Content class="max-h-60">
    {#each profiles as option}
      <Select.Item value={option.id}>{option.name}</Select.Item>
    {/each}
  </Select.Content>
</Select.Root>
