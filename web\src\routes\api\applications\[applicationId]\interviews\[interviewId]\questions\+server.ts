/**
 * API endpoints for managing interview questions for an interview stage
 */

import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';

/**
 * Get all questions for an interview stage
 * GET /api/applications/:applicationId/interviews/:interviewId/questions
 */
export async function GET({ params, locals }) {
  // Ensure user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { applicationId, interviewId } = params;

  try {
    // Verify the application belongs to the user
    const application = await prisma.application.findUnique({
      where: {
        id: applicationId,
        userId: locals.user.id,
      },
    });

    if (!application) {
      return json({ error: 'Application not found' }, { status: 404 });
    }

    // Verify the interview stage belongs to the application
    const interviewStage = await prisma.interviewStage.findUnique({
      where: {
        id: interviewId,
        applicationId,
      },
    });

    if (!interviewStage) {
      return json({ error: 'Interview stage not found' }, { status: 404 });
    }

    // Get all questions for the interview stage
    const questions = await prisma.interviewQuestion.findMany({
      where: {
        interviewStageId: interviewId,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    return json({ questions });
  } catch (error) {
    console.error('Error fetching interview questions:', error);
    return json({ error: 'Failed to fetch interview questions' }, { status: 500 });
  }
}

/**
 * Create a new question for an interview stage
 * POST /api/applications/:applicationId/interviews/:interviewId/questions
 */
export async function POST({ request, params, locals }) {
  // Ensure user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { applicationId, interviewId } = params;

  try {
    // Verify the application belongs to the user
    const application = await prisma.application.findUnique({
      where: {
        id: applicationId,
        userId: locals.user.id,
      },
    });

    if (!application) {
      return json({ error: 'Application not found' }, { status: 404 });
    }

    // Verify the interview stage belongs to the application
    const interviewStage = await prisma.interviewStage.findUnique({
      where: {
        id: interviewId,
        applicationId,
      },
    });

    if (!interviewStage) {
      return json({ error: 'Interview stage not found' }, { status: 404 });
    }

    // Parse the request body
    const body = await request.json();
    const { question, category, difficulty, userResponse, userConfidence, notes } = body;

    // Validate required fields
    if (!question || !category) {
      return json({ error: 'Question and category are required' }, { status: 400 });
    }

    // Create the interview question
    const interviewQuestion = await prisma.interviewQuestion.create({
      data: {
        interviewStageId: interviewId,
        question,
        category,
        difficulty,
        userResponse,
        userConfidence,
        notes,
      },
    });

    return json({ interviewQuestion }, { status: 201 });
  } catch (error) {
    console.error('Error creating interview question:', error);
    return json({ error: 'Failed to create interview question' }, { status: 500 });
  }
}
