import { json } from '@sveltejs/kit';
import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';

// GET - Validate a referral code
export const GET: RequestHandler = async ({ url }) => {
  const referralCode = url.searchParams.get('code');
  
  if (!referralCode) {
    return json({ error: 'Referral code is required' }, { status: 400 });
  }

  try {
    // Find user with this referral code
    const referrer = await prisma.user.findUnique({
      where: { referralCode: referralCode.toUpperCase() },
      select: {
        id: true,
        name: true,
        email: true,
        referralCode: true,
        createdAt: true,
      },
    });

    if (!referrer) {
      return json({ 
        valid: false, 
        error: 'Invalid referral code' 
      }, { status: 404 });
    }

    return json({
      valid: true,
      referrer: {
        name: referrer.name,
        referralCode: referrer.referralCode,
      },
    });
  } catch (error) {
    console.error('Error validating referral code:', error);
    return json({ error: 'Failed to validate referral code' }, { status: 500 });
  }
};
