// @ts-nocheck
import { redirect, fail } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { getUserFromToken } from '$lib/server/auth.js';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import type { PageServerLoad, Actions } from './$types.js';

// Define the schema with Zod for validation
const inviteSchema = z.object({
  email: z.string().email('Invalid email address'),
  role: z.enum(['member', 'admin']).default('member'),
});

const teamSchema = z.object({
  name: z.string().min(1, 'Team name is required'),
});

export const load = async ({ locals }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user || !user.email) {
    throw redirect(302, '/auth/sign-in');
  }

  // Get user data
  const userData = await prisma.user.findUnique({
    where: { email: user.email },
    include: {
      Team: {
        include: {
          members: {
            include: {
              user: true,
            },
          },
        },
      },
      TeamMember: {
        include: {
          team: {
            include: {
              owner: true,
              members: {
                include: {
                  user: true,
                },
              },
            },
          },
        },
      },
    },
  });

  if (!userData) {
    throw redirect(302, '/auth/sign-in');
  }

  locals.user = userData;

  // Get all teams the user is part of
  const ownedTeams = userData.Team || [];
  const memberTeams = userData.TeamMember?.map((tm) => tm.team) || [];

  // Combine and deduplicate teams
  const allTeams = [...ownedTeams, ...memberTeams];
  const uniqueTeams = Array.from(new Map(allTeams.map((team) => [team.id, team])).values());

  // Create the invite form
  const inviteForm = await superValidate(zod(inviteSchema));

  // Create the team form
  const teamForm = await superValidate(zod(teamSchema));

  return {
    user: userData,
    teams: uniqueTeams,
    inviteForm,
    teamForm,
  };
};

export const actions = {
  invite: async ({ request, cookies, locals }: import('./$types').RequestEvent) => {
    const tokenData = getUserFromToken(cookies);

    if (!tokenData || !tokenData.email) {
      throw redirect(302, '/auth/sign-in');
    }

    // Get user data
    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email },
    });

    if (!userData) {
      throw redirect(302, '/auth/sign-in');
    }

    const form = await superValidate(request, zod(inviteSchema));

    if (!form.valid) {
      return fail(400, { inviteForm: form });
    }

    try {
      // In a real implementation, you would send an invitation email
      // For now, we'll just return success
      return { inviteForm: form, success: true };
    } catch (error) {
      console.error('Error inviting team member:', error);
      return fail(500, { inviteForm: form, error: 'Failed to invite team member' });
    }
  },

  createTeam: async ({ request, cookies, locals }: import('./$types').RequestEvent) => {
    const tokenData = getUserFromToken(cookies);

    if (!tokenData || !tokenData.email) {
      throw redirect(302, '/auth/sign-in');
    }

    // Get user data
    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email },
    });

    if (!userData) {
      throw redirect(302, '/auth/sign-in');
    }

    const form = await superValidate(request, zod(teamSchema));

    if (!form.valid) {
      return fail(400, { teamForm: form });
    }

    try {
      // Create a new team
      const newTeam = await prisma.team.create({
        data: {
          name: form.data.name,
          ownerId: userData.id,
        },
      });

      return { teamForm: form, success: true, team: newTeam };
    } catch (error) {
      console.error('Error creating team:', error);
      return fail(500, { teamForm: form, error: 'Failed to create team' });
    }
  },

  removeTeamMember: async ({ request, cookies, locals }: import('./$types').RequestEvent) => {
    const tokenData = getUserFromToken(cookies);

    if (!tokenData || !tokenData.email) {
      throw redirect(302, '/auth/sign-in');
    }

    // Get user data
    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email },
    });

    if (!userData) {
      throw redirect(302, '/auth/sign-in');
    }

    const data = await request.formData();
    const teamId = data.get('teamId')?.toString();
    const memberId = data.get('memberId')?.toString();

    if (!teamId || !memberId) {
      return fail(400, { error: 'Missing required fields' });
    }

    try {
      // Check if user is team owner
      const team = await prisma.team.findUnique({
        where: { id: teamId },
      });

      if (!team || team.ownerId !== userData.id) {
        return fail(403, { error: 'You do not have permission to remove team members' });
      }

      // Remove team member
      await prisma.teamMember.delete({
        where: {
          id: memberId,
        },
      });

      return { success: true };
    } catch (error) {
      console.error('Error removing team member:', error);
      return fail(500, { error: 'Failed to remove team member' });
    }
  },
};
;null as any as Actions;