// src/lib/server/notification-triggers/system-triggers.ts
import { prisma } from '$lib/server/prisma';
import {
  sendGlobalNotification,
  sendNotificationToUser,
  NotificationType,
  NotificationPriority,
} from '$lib/server/notification-triggers/notification-service';
import { logger } from '$lib/server/logger';

/**
 * Send a system maintenance notification to all users
 */
export async function notifySystemMaintenance(
  startTime: Date,
  endTime: Date,
  details: string
): Promise<boolean> {
  try {
    // Format dates
    const startTimeStr = startTime.toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      timeZoneName: 'short',
    });

    const endTimeStr = endTime.toLocaleString('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      timeZoneName: 'short',
    });

    // Create notification
    const result = await sendGlobalNotification({
      title: 'Scheduled Maintenance',
      message: `The system will be undergoing maintenance from ${startTimeStr} to ${endTimeStr}. ${details}`,
      type: NotificationType.SYSTEM,
      priority: NotificationPriority.HIGH,
      metadata: {
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        details,
      },
    });

    return result;
  } catch (error) {
    logger.error('Error sending system maintenance notification:', error);
    return false;
  }
}

/**
 * Send a new feature announcement to all users
 */
export async function notifyNewFeature(
  featureName: string,
  description: string,
  url?: string
): Promise<boolean> {
  try {
    // Create notification
    const result = await sendGlobalNotification({
      title: 'New Feature Available',
      message: `We've just launched ${featureName}! ${description}`,
      url: url || '/dashboard',
      type: NotificationType.SYSTEM,
      priority: NotificationPriority.MEDIUM,
      metadata: {
        featureName,
        description,
      },
    });

    return result;
  } catch (error) {
    logger.error('Error sending new feature notification:', error);
    return false;
  }
}

/**
 * Send a system update notification to all users
 */
export async function notifySystemUpdate(
  version: string,
  description: string,
  changes: string[]
): Promise<boolean> {
  try {
    // Create notification
    const result = await sendGlobalNotification({
      title: `System Update: v${version}`,
      message: description,
      type: NotificationType.SYSTEM,
      priority: NotificationPriority.MEDIUM,
      metadata: {
        version,
        description,
        changes,
      },
    });

    return result;
  } catch (error) {
    logger.error('Error sending system update notification:', error);
    return false;
  }
}

/**
 * Send a security alert to all users
 */
export async function notifySecurityAlert(
  title: string,
  message: string,
  severity: 'low' | 'medium' | 'high' | 'critical'
): Promise<boolean> {
  try {
    // Map severity to priority
    let priority;
    switch (severity) {
      case 'critical':
        priority = NotificationPriority.URGENT;
        break;
      case 'high':
        priority = NotificationPriority.HIGH;
        break;
      case 'medium':
        priority = NotificationPriority.MEDIUM;
        break;
      case 'low':
      default:
        priority = NotificationPriority.LOW;
        break;
    }

    // Create notification
    const result = await sendGlobalNotification({
      title: `Security Alert: ${title}`,
      message,
      type: NotificationType.SYSTEM,
      priority,
      metadata: {
        severity,
      },
    });

    return result;
  } catch (error) {
    logger.error('Error sending security alert notification:', error);
    return false;
  }
}

/**
 * Send a notification to users with expiring subscriptions
 */
export async function notifySubscriptionExpiring(
  userId: string,
  planName: string,
  expiryDate: Date,
  daysRemaining: number
): Promise<boolean> {
  try {
    // Get user notification preferences from user preferences
    const userRecord = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        preferences: true,
      },
    });

    // Check if user has disabled notifications in preferences
    const userPrefs = (userRecord?.preferences as any) || {};
    const notificationPrefs = userPrefs.notifications || {};

    if (notificationPrefs.platform && notificationPrefs.platform.browser === false) {
      logger.info(`User ${userId} has disabled browser notifications in preferences`);
      return false;
    }

    // Also check NotificationSettings for backward compatibility
    const settings = await prisma.notificationSettings.findUnique({
      where: { userId },
    });

    if (settings && !settings.browserEnabled) {
      logger.info(`User ${userId} has disabled browser notifications in settings`);
      return false;
    }

    // Format date
    const expiryDateStr = expiryDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    // Create notification
    const result = await sendNotificationToUser(userId, {
      title: 'Subscription Expiring Soon',
      message: `Your ${planName} subscription will expire on ${expiryDateStr} (${daysRemaining} days remaining). Renew now to avoid interruption of service.`,
      url: '/dashboard/settings/billing',
      type: NotificationType.SYSTEM,
      priority: daysRemaining <= 3 ? NotificationPriority.HIGH : NotificationPriority.MEDIUM,
      metadata: {
        planName,
        expiryDate: expiryDate.toISOString(),
        daysRemaining,
      },
    });

    return result;
  } catch (error) {
    logger.error('Error sending subscription expiring notification:', error);
    return false;
  }
}
