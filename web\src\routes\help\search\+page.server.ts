// src/routes/help/search/+page.server.ts
import type { PageServerLoad } from './$types';
import { searchHelpArticles, getHelpArticles } from '$lib/sanity/client';

export const load = (async ({ url }) => {
  const query = url.searchParams.get('q') || '';

  try {
    // Search for articles
    const searchResults = query ? await searchHelpArticles(query, 20) : [];

    // Fetch all articles to build the sidebar categories
    const allArticles = await getHelpArticles();

    // Group articles by category for the sidebar
    const articlesByCategory = allArticles.reduce((acc, article) => {
      if (!acc[article.category]) {
        acc[article.category] = {
          name: getCategoryName(article.category),
          slug: article.category,
          icon: getCategoryIcon(article.category),
          articles: [],
        };
      }

      acc[article.category].articles.push({
        id: article._id,
        title: article.title,
        slug: article.slug.current,
      });

      return acc;
    }, {});

    // Convert to array and sort categories
    const categories = Object.values(articlesByCategory).sort((a, b) =>
      a.name.localeCompare(b.name)
    );

    // Format search results for the component
    const formattedSearchResults = searchResults.map((article) => ({
      ...article,
      id: article._id,
      slug: article.slug.current,
      excerpt: article.description,
      category: {
        id: article.category,
        name: getCategoryName(article.category),
        slug: article.category,
        icon: getCategoryIcon(article.category),
      },
      tags:
        article.tags?.map((tag) => ({
          id: tag,
          name: tag,
          slug: tag.toLowerCase().replace(/\s+/g, '-'),
        })) || [],
    }));

    return {
      query,
      searchResults: formattedSearchResults,
      categories,
      resultCount: searchResults.length,
    };
  } catch (error) {
    console.error('Error searching help articles:', error);
    return {
      query,
      searchResults: [],
      categories: [],
      resultCount: 0,
    };
  }
}) satisfies PageServerLoad;

// Helper function to get category name from slug
function getCategoryName(slug: string): string {
  const categoryMap = {
    'getting-started': 'Getting Started',
    'auto-apply': 'Using Auto Apply',
    'account-billing': 'Account & Billing',
    troubleshooting: 'Troubleshooting',
    'privacy-security': 'Privacy & Security',
  };

  return categoryMap[slug as keyof typeof categoryMap] || slug;
}

// Helper function to get category icon from slug
function getCategoryIcon(slug: string): string {
  const iconMap = {
    'getting-started': 'BookOpen',
    'auto-apply': 'FileText',
    'account-billing': 'CreditCard',
    troubleshooting: 'HelpCircle',
    'privacy-security': 'Shield',
  };

  return iconMap[slug as keyof typeof iconMap] || 'HelpCircle';
}
