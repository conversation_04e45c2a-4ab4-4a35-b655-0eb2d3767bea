doctype html
html
  head
    meta(name='viewport', content='width=device-width')
    meta(http-equiv='Content-Type', content='text/html; charset=UTF-8')
    title= subject || 'Message from Hirli'
    style.
      /* Base styles */
      body {
        font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
        font-size: 16px;
        line-height: 1.5;
        color: #333;
        margin: 0;
        padding: 0;
        background-color: #f5f5f5;
      }
      .container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }
      .header {
        text-align: center;
        padding: 20px 0;
        border-bottom: 1px solid #eee;
      }
      .header img {
        max-width: 200px;
        height: auto;
      }
      .content {
        padding: 30px 0;
      }
      .footer {
        text-align: center;
        padding: 20px 0;
        border-top: 1px solid #eee;
        font-size: 12px;
        color: #777;
      }
      h1 {
        color: #333;
        font-size: 24px;
        margin-bottom: 20px;
      }
      h2 {
        color: #555;
        font-size: 20px;
        margin-bottom: 15px;
      }
      p {
        margin-bottom: 15px;
      }
      a {
        color: #0066cc;
        text-decoration: none;
      }
      a:hover {
        text-decoration: underline;
      }
      .signature {
        font-weight: 500;
        color: #333;
        margin-top: 5px;
      }
      .button {
        display: inline-block;
        padding: 12px 24px;
        background-color: #0066cc;
        color: #ffffff !important;
        text-decoration: none;
        border-radius: 4px;
        margin: 15px 0;
        font-weight: bold;
      }
      .button:hover {
        background-color: #0052a3;
        text-decoration: none;
      }
      .info-box {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 15px;
        margin: 15px 0;
      }
      .highlight {
        background-color: #fffde7;
        padding: 2px 5px;
        border-radius: 2px;
      }
      .card {
        border: 1px solid #eee;
        border-radius: 4px;
        padding: 15px;
        margin: 15px 0;
      }
      .card-header {
        font-weight: bold;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
      }

      /* Responsive styles */
      @media only screen and (max-width: 620px) {
        .container {
          width: 100% !important;
        }
      }

  body
    div.container
      //- Include header
      include ../partials/header

      //- Main content
      div.content
        block content
          p This is a default message.

      //- Include footer
      include ../partials/footer
