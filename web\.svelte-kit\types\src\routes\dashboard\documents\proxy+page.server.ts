// @ts-nocheck
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';
import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types.js';

// Helper function to format file URLs based on document type
function formatFileUrl(doc) {
  if (!doc.fileUrl) return '';

  console.log(`Formatting URL for document ${doc.id}: ${doc.fileUrl}`);

  // If it's a placeholder URL, return it as is
  if (doc.fileUrl === '/placeholder.pdf' || doc.fileUrl.includes('placeholder')) {
    console.log(`Returning placeholder URL: ${doc.fileUrl}`);
    return doc.fileUrl;
  }

  // If the URL already starts with http:// or https://, return it as is
  if (doc.fileUrl.startsWith('http://') || doc.fileUrl.startsWith('https://')) {
    console.log(`Returning absolute URL: ${doc.fileUrl}`);
    return doc.fileUrl;
  }

  // If the URL already includes /uploads/, return it as is
  if (doc.fileUrl.includes('/uploads/')) {
    console.log(`URL already includes /uploads/: ${doc.fileUrl}`);
    return doc.fileUrl;
  }

  // If the URL already starts with a slash
  if (doc.fileUrl.startsWith('/')) {
    // Remove the leading slash for consistency
    const urlWithoutLeadingSlash = doc.fileUrl.substring(1);

    // Determine the appropriate folder based on document type
    let folder = 'documents';
    if (doc.type === 'resume') folder = 'resumes';
    else if (doc.type === 'cover_letter') folder = 'cover-letters';
    else if (doc.type === 'reference') folder = 'references';

    const formattedUrl = `/uploads/${folder}/${urlWithoutLeadingSlash}`;
    console.log(`Formatted URL with leading slash: ${formattedUrl}`);
    return formattedUrl;
  }

  // If the URL doesn't start with a slash, add the appropriate prefix
  // based on the document type
  let folder = 'documents';
  if (doc.type === 'resume') folder = 'resumes';
  else if (doc.type === 'cover_letter') folder = 'cover-letters';
  else if (doc.type === 'reference') folder = 'references';

  const formattedUrl = `/uploads/${folder}/${doc.fileUrl}`;
  console.log(`Formatted URL without leading slash: ${formattedUrl}`);
  return formattedUrl;
}

// Using the shared Prisma client from $lib/server/prisma

export const load = async ({ locals }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;
  if (!user || !user.id) throw redirect(302, '/auth/sign-in');

  console.log('User ID:', user.id);

  // Get all profiles for user (or team-based)
  const profiles = await prisma.profile.findMany({
    where: {
      OR: [{ userId: user.id }, { team: { members: { some: { userId: user.id } } } }],
    },
    select: {
      id: true,
      name: true,
    },
  });

  console.log(`Found ${profiles.length} profiles for user ${user.id}`);

  // Get documents owned by the user
  console.log(`Fetching documents for user ${user.id}...`);
  const documents = await prisma.document.findMany({
    where: {
      userId: user.id,
    },
    include: {
      profile: true,
      resume: {
        include: {
          optimization: true,
          jobSearches: {
            take: 1,
            orderBy: { createdAt: 'desc' },
          },
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  console.log(`Found ${documents.length} documents for user ${user.id}:`, documents);

  // Process documents to include resume data when available
  const processedDocuments = documents.map((doc) => {
    const resume = doc.resume;
    const jobSearch = resume?.jobSearches?.[0] ?? null;

    const formattedUrl = formatFileUrl(doc);
    console.log(`Document ${doc.id}: Original URL: ${doc.fileUrl}, Formatted URL: ${formattedUrl}`);

    return {
      id: doc.id,
      label: doc.label ?? 'Untitled Document',
      fileUrl: formattedUrl,
      fileName: doc.fileName ?? doc.fileUrl?.split('/').pop() ?? 'unknown.pdf',
      type: doc.type ?? 'document',
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt,
      isDefault: doc.isDefault ?? false,
      isParsed: resume?.isParsed ?? false,
      parsedAt: resume?.parsedAt ?? null,
      // Determine the source based on the document's characteristics
      source:
        // If the document has a placeholder URL, it was created from scratch
        doc.fileUrl === '/placeholder.pdf'
          ? 'created'
          : // If the document is in the 'resumes' storage location, it was uploaded
            doc.storageLocation === 'resumes' && doc.fileName
            ? 'uploaded'
            : // If the document's filename includes 'generated', it was generated
              doc.fileName?.includes('generated')
              ? 'generated'
              : // For documents created from scratch but not yet generated
                doc.fileUrl?.startsWith('/placeholder') || !doc.fileName
                ? 'created'
                : // Default to 'uploaded' for any other case
                  'uploaded',
      score: resume?.score ?? null,
      profile: doc.profile ?? null,
      jobSearch,
    };
  });

  const result = {
    profiles,
    documents: processedDocuments,
  };

  console.log('Server returning data:', {
    profilesCount: profiles.length,
    documentsCount: processedDocuments.length,
  });

  return result;
};
