// @ts-nocheck
// Verifies token on page load
import type { PageServerLoad } from './$types.js';
import { prisma } from '$lib/server/prisma';
import { redirect } from '@sveltejs/kit';

// Using the shared Prisma client from $lib/server/prisma

export const load = async ({ url }: Parameters<PageServerLoad>[0]) => {
  const token = url.searchParams.get('token');

  if (!token) throw redirect(302, '/auth/forgot-password');

  const record = await prisma.passwordResetToken.findUnique({
    where: { token },
  });

  if (!record || record.expiresAt < new Date()) {
    throw redirect(302, '/auth/forgot-password');
  }

  return { token };
};
