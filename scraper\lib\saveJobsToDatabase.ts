// ───────────────────────────────────────────────────────────────────────────
//  saveJobsToDatabase.ts
// ───────────────────────────────────────────────────────────────────────────
import { PrismaClient } from "@prisma/client";

interface FastJob {
  platform: string;
  jobId: string;
  title: string;
  company: string;
  location: string;
  url: string; // @unique in Prisma model
  lastCheckedAt: Date;
  fingerprint?: string; // Optional computed field
}

function chunk<T>(arr: T[], size = 1_000): T[][] {
  const out: T[][] = [];
  for (let i = 0; i < arr.length; i += size) out.push(arr.slice(i, i + size));
  return out;
}

/**
 * Generate a fingerprint for a job to identify duplicates
 * @param job FastJob object
 * @returns Fingerprint string
 */
function generateJobFingerprint(job: FastJob): string {
  // Normalize strings for consistent comparison
  const normalizedTitle = job.title?.toLowerCase().trim() || "";
  const normalizedCompany = job.company?.toLowerCase().trim() || "";
  const normalizedLocation = job.location?.toLowerCase().trim() || "";

  // Create a more robust fingerprint using company, title and location
  // This will consider jobs with the same title at the same company in the same location as duplicates
  // even if they have different URLs
  return `${normalizedCompany}|${normalizedTitle}|${normalizedLocation}`;
}

/**
 * Inserts new rows into job_listing.
 * Existing rows (same `url`) are silently skipped by Postgres.
 * When useFingerprintDeduplication is true, jobs with the same company and title
 * are considered duplicates even if they have different URLs.
 *
 * @param jobs                      array of FastJob objects
 * @param chunkSize                 batch size for createMany (default 25)
 * @param useFingerprintDeduplication whether to use fingerprint-based de-duplication (default true)
 * @param forceInsert               whether to force insert all jobs (default false)
 * @returns                         number of rows actually inserted
 */
export async function saveJobsToDatabase(
  jobs: FastJob[],
  chunkSize = 100, // Increased from 25 to 100 for better performance
  useFingerprintDeduplication = true,
  forceInsert = false
): Promise<number> {
  // Check for empty input
  if (!jobs || jobs.length === 0) {
    console.info("No jobs to save.");
    return 0;
  }

  const prisma = new PrismaClient();

  /* 1 ─ de‑dupe the batch in memory */
  let uniqueJobs;

  if (useFingerprintDeduplication && !forceInsert) {
    // Generate fingerprints for all jobs
    const jobsWithFingerprints = jobs.map((job) => ({
      ...job,
      fingerprint: generateJobFingerprint(job),
    }));

    // De-dupe based on fingerprint (same company + title + location)
    uniqueJobs = Array.from(
      new Map(jobsWithFingerprints.map((j) => [j.fingerprint, j])).values()
    );

    console.info(
      `Using fingerprint de-duplication: ${jobs.length} jobs → ${uniqueJobs.length} unique jobs`
    );
  } else {
    // Traditional de-duplication by URL
    uniqueJobs = Array.from(new Map(jobs.map((j) => [j.url, j])).values());
  }

  /* 2 - Check for existing jobs in database before inserting */
  if (!forceInsert) {
    // Generate fingerprints for all jobs once
    const jobsWithFingerprints = uniqueJobs.map((job) => ({
      ...job,
      fingerprint: generateJobFingerprint(job),
    }));

    // For large batches, use a more efficient approach
    if (jobsWithFingerprints.length > 50) {
      // Get all unique companies, titles, and locations to minimize query size
      const companies = new Set(
        jobsWithFingerprints.map((job) => job.company.toLowerCase().trim())
      );
      const titles = new Set(
        jobsWithFingerprints.map((job) => job.title.toLowerCase().trim())
      );
      const locations = new Set(
        jobsWithFingerprints.map((job) => job.location.toLowerCase().trim())
      );

      // Find existing jobs that match any of our potential combinations
      // This is more efficient than checking each job individually
      const existingJobs = await prisma.jobListing.findMany({
        where: {
          AND: [
            { company: { in: Array.from(companies), mode: "insensitive" } },
            { title: { in: Array.from(titles), mode: "insensitive" } },
            { location: { in: Array.from(locations), mode: "insensitive" } },
          ],
        },
        select: { title: true, company: true, location: true },
      });

      // Create fingerprints for existing jobs
      const existingFingerprints = new Set(
        existingJobs.map((job) => generateJobFingerprint(job as any))
      );

      // Filter out jobs that already exist in the database
      uniqueJobs = jobsWithFingerprints.filter(
        (job) => !existingFingerprints.has(job.fingerprint)
      );
    } else {
      // For smaller batches, use the original approach
      const existingJobs = await prisma.jobListing.findMany({
        where: {
          OR: jobsWithFingerprints.map((job) => ({
            AND: [
              { title: { equals: job.title, mode: "insensitive" } },
              { company: { equals: job.company, mode: "insensitive" } },
              { location: { equals: job.location, mode: "insensitive" } },
            ],
          })),
        },
        select: { title: true, company: true, location: true },
      });

      // Create fingerprints for existing jobs
      const existingFingerprints = new Set(
        existingJobs.map((job) => generateJobFingerprint(job as any))
      );

      // Filter out jobs that already exist in the database
      uniqueJobs = jobsWithFingerprints.filter(
        (job) => !existingFingerprints.has(job.fingerprint)
      );
    }

    console.info(
      `Database de-duplication: ${uniqueJobs.length} jobs after filtering existing entries`
    );
  }

  /* 2 ─ split into manageable INSERT batches */
  const batches = chunk(uniqueJobs, chunkSize);

  let inserted = 0;

  for (const batch of batches) {
    const { count } = await prisma.job_listing.createMany({
      data: batch.map((j: FastJob) => ({
        platform: j.platform,
        jobId: j.jobId,
        title: j.title,
        company: j.company,
        location: j.location,
        url: j.url,
        lastCheckedAt: j.lastCheckedAt,
        isProcessing: false,
        isActive: true,
      })),
      skipDuplicates: !forceInsert, // Skip duplicates only if not forcing insert
    });

    inserted += count;
  }

  console.info(
    `📦  Inserted ${inserted} new rows (batch size ${chunkSize}, ` +
      `${uniqueJobs.length - inserted} duplicates skipped).`
  );

  return inserted;
}
