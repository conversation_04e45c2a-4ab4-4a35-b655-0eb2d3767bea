<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import type { SystemMetrics } from './types';

  // Props
  const { metrics } = $props<{
    metrics: SystemMetrics;
  }>();
</script>

<Card.Root>
  <Card.Content>
    <div class="grid gap-4 md:grid-cols-4">
      <div class="flex flex-col items-center justify-center rounded-lg border p-4">
        <div class="text-2xl font-bold">{metrics.uptime.toFixed(2)}%</div>
        <p class="text-muted-foreground text-sm">Uptime (30 days)</p>
      </div>
      <div class="flex flex-col items-center justify-center rounded-lg border p-4">
        <div class="text-2xl font-bold">{metrics.emailDeliveryRate.toFixed(1)}%</div>
        <p class="text-muted-foreground text-sm">Email Delivery Rate</p>
      </div>
      <div class="flex flex-col items-center justify-center rounded-lg border p-4">
        <div class="text-2xl font-bold">{metrics.apiResponseTime}ms</div>
        <p class="text-muted-foreground text-sm">API Response Time</p>
      </div>
      <div class="flex flex-col items-center justify-center rounded-lg border p-4">
        <div class="text-2xl font-bold">{metrics.jobSuccessRate.toFixed(1)}%</div>
        <p class="text-muted-foreground text-sm">Job Success Rate</p>
      </div>
    </div>
  </Card.Content>
</Card.Root>
