import { json } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';
import { OpenAI } from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { resumeId, jobId } = await request.json();

    if (!resumeId || !jobId) {
      return json({ error: 'Resume ID and Job ID are required' }, { status: 400 });
    }

    // Get the resume
    const resume = await prisma.document.findUnique({
      where: {
        id: resumeId,
        userId: locals.user.id
      },
      include: {
        resume: true
      }
    });

    if (!resume) {
      return json({ error: 'Resume not found' }, { status: 404 });
    }

    // Get the job
    const job = await prisma.jobListing.findUnique({
      where: {
        id: jobId
      },
      select: {
        title: true,
        company: true,
        description: true,
        requirements: true
      }
    });

    if (!job) {
      return json({ error: 'Job not found' }, { status: 404 });
    }

    // Generate job-specific ATS analysis
    const analysis = await generateJobSpecificATSAnalysis(resume, job);

    // Save the analysis to the database
    const savedAnalysis = await prisma.atsAnalysis.create({
      data: {
        userId: locals.user.id,
        resumeId,
        jobId,
        overallScore: analysis.overallScore,
        keywordScore: analysis.keywordScore,
        formatScore: analysis.formatScore,
        contentScore: analysis.contentScore,
        readabilityScore: analysis.readabilityScore,
        keywordMatches: analysis.keywordMatches,
        missingKeywords: analysis.missingKeywords,
        formatIssues: analysis.formatIssues,
        contentSuggestions: analysis.contentSuggestions,
        readabilitySuggestions: analysis.readabilitySuggestions,
        jobSpecific: true,
        jobTitle: job.title,
        company: job.company
      }
    });

    // Update feature usage
    await prisma.featureUsage.upsert({
      where: {
        userId_featureId_limitId: {
          userId: locals.user.id,
          featureId: 'ats_optimization',
          limitId: 'ats_job_specific_scans_monthly'
        }
      },
      update: {
        usage: {
          increment: 1
        }
      },
      create: {
        userId: locals.user.id,
        featureId: 'ats_optimization',
        limitId: 'ats_job_specific_scans_monthly',
        usage: 1
      }
    });

    return json({ analysis: savedAnalysis });
  } catch (error) {
    console.error('Error generating job-specific ATS analysis:', error);
    return json({ error: 'Failed to generate job-specific ATS analysis' }, { status: 500 });
  }
};

export const GET: RequestHandler = async ({ url, locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const resumeId = url.searchParams.get('resumeId');
    const jobId = url.searchParams.get('jobId');

    if (!resumeId || !jobId) {
      return json({ error: 'Resume ID and Job ID are required' }, { status: 400 });
    }

    // Get the latest job-specific ATS analysis
    const analysis = await prisma.atsAnalysis.findFirst({
      where: {
        userId: locals.user.id,
        resumeId,
        jobId,
        jobSpecific: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!analysis) {
      return json({ error: 'No job-specific analysis found' }, { status: 404 });
    }

    return json({ analysis });
  } catch (error) {
    console.error('Error fetching job-specific ATS analysis:', error);
    return json({ error: 'Failed to fetch job-specific ATS analysis' }, { status: 500 });
  }
};

// Helper function to generate job-specific ATS analysis
async function generateJobSpecificATSAnalysis(resume: any, job: any): Promise<any> {
  try {
    // Extract resume content
    const resumeContent = resume.resume ? JSON.stringify(resume.resume) : '';
    const resumeText = resume.content || '';

    // Prepare the prompt
    const prompt = `Analyze this resume for ATS compatibility specifically for this job:

Resume Content:
${resumeContent}

Plain Text:
${resumeText}

Job Title: ${job.title}
Company: ${job.company}
Job Description: ${job.description || ''}
Requirements: ${job.requirements || ''}

Please analyze how well this resume matches the job description.

Provide a detailed analysis with the following scores (0-100):
1. Overall Score
2. Keyword Score
3. Format Score
4. Content Score
5. Readability Score

Also provide:
- Keyword matches found
- Missing important keywords
- Format issues
- Content suggestions
- Readability suggestions

Return the analysis as a JSON object with these fields.
`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: 'You are an expert ATS (Applicant Tracking System) analyzer.' },
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 2000,
      response_format: { type: 'json_object' }
    });

    // Parse the response
    const content = response.choices[0]?.message?.content || '';
    const analysisResult = JSON.parse(content);

    // Ensure all required fields are present
    return {
      overallScore: analysisResult.overallScore || 0,
      keywordScore: analysisResult.keywordScore || 0,
      formatScore: analysisResult.formatScore || 0,
      contentScore: analysisResult.contentScore || 0,
      readabilityScore: analysisResult.readabilityScore || 0,
      keywordMatches: analysisResult.keywordMatches || [],
      missingKeywords: analysisResult.missingKeywords || [],
      formatIssues: analysisResult.formatIssues || [],
      contentSuggestions: analysisResult.contentSuggestions || [],
      readabilitySuggestions: analysisResult.readabilitySuggestions || []
    };
  } catch (error) {
    console.error('Error in job-specific ATS analysis generation:', error);
    
    // Return default values if analysis fails
    return {
      overallScore: 70,
      keywordScore: 65,
      formatScore: 75,
      contentScore: 70,
      readabilityScore: 80,
      keywordMatches: ['resume', 'experience', 'skills'],
      missingKeywords: ['specific technical skills', 'certifications'],
      formatIssues: ['Consider using a more ATS-friendly format'],
      contentSuggestions: ['Add more quantifiable achievements'],
      readabilitySuggestions: ['Use more bullet points for better readability']
    };
  }
}
