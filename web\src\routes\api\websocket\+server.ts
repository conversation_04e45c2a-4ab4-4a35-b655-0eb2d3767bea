import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getRedisClient } from '$lib/server/redis';

/**
 * Send a WebSocket message via Redis
 * 
 * This endpoint allows server-side code to send WebSocket messages
 * by publishing to a Redis channel that the WebSocket server is
 * subscribed to.
 */
export const POST: RequestHandler = async ({ request }) => {
  try {
    // Get the message from the request body
    const data = await request.json();
    
    // Get Redis client
    const redis = await getRedisClient();
    if (!redis) {
      return json({ error: 'Redis client not available' }, { status: 500 });
    }
    
    // Add timestamp if not provided
    if (!data.timestamp) {
      data.timestamp = new Date().toISOString();
    }
    
    // Publish to the websocket::broadcast channel
    await redis.publish('websocket::broadcast', JSON.stringify(data));
    
    return json({ success: true, message: 'WebSocket message sent' });
  } catch (error) {
    console.error('Error sending WebSocket message:', error);
    return json({ error: 'Failed to send WebSocket message' }, { status: 500 });
  }
};

/**
 * Get WebSocket server status
 */
export const GET: RequestHandler = async () => {
  return json({
    status: 'WebSocket server is running',
    timestamp: new Date().toISOString(),
  });
};
