// src/routes/api/help/tags/+server.ts
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { prisma } from '$lib/server/prisma';

// Get all help tags
export const GET: RequestHandler = async ({ url }) => {
  try {
    const includeArticleCount = url.searchParams.get('includeArticleCount') === 'true';

    // Get tags from the database using Prisma models
    const tags = await prisma.helpTag.findMany({
      orderBy: {
        name: 'asc',
      },
      include: {
        _count: includeArticleCount ? {
          select: {
            articles: true,
          },
        } : undefined,
      },
    });

    // Format the response
    const formattedTags = tags.map((tag) => ({
      ...tag,
      articleCount: includeArticleCount ? tag._count.articles : undefined,
      _count: undefined,
    }));

    return json(formattedTags);
  } catch (error) {
    console.error('Error fetching help tags:', error);
    return json({ error: 'Failed to fetch help tags' }, { status: 500 });
  }
};

// Create a new help tag (admin only)
export const POST: RequestHandler = async ({ request, locals }) => {
  const user = locals.user;
  if (!user || user.role !== 'ADMIN') {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { name, slug } = await request.json();

    // Create the tag
    const tag = await prisma.helpTag.create({
      data: {
        name,
        slug,
      },
    });

    return json(tag);
  } catch (error) {
    console.error('Error creating help tag:', error);
    return json({ error: 'Failed to create help tag' }, { status: 500 });
  }
};
