// File: workers/utils/enhanced-prisma.ts
import { PrismaClient } from "@prisma/client";
import * as dotenv from "dotenv";
dotenv.config();

// Connection settings
const CONNECTION_SETTINGS = {
  MAX_RETRIES: 5,
  RETRY_DELAY_MS: 1000,
  CIRCUIT_BREAKER_THRESHOLD: 3,
  CIRCUIT_BREAKER_RESET_TIME_MS: 30000,
  CONNECTION_TIMEOUT_MS: 10000,
};

// Circuit breaker state
type CircuitBreakerState = "CLOSED" | "OPEN" | "HALF_OPEN";

class DatabaseConnectionManager {
  private client: PrismaClient | null = null;
  private failureCount = 0;
  private circuitBreakerState: CircuitBreakerState = "CLOSED";
  private lastFailureTime = 0;
  private isConnecting = false;
  private connectionPromise: Promise<PrismaClient> | null = null;

  constructor() {
    this.initializeClient();
  }

  private initializeClient() {
    console.log(`[prisma] Available database URLs:`);
    console.log(
      `[prisma] DATABASE_URL: ${
        process.env.DATABASE_URL ? "defined" : "undefined"
      }`
    );
    console.log(
      `[prisma] EXTERNAL_DATABASE_URL: ${
        process.env.EXTERNAL_DATABASE_URL ? "defined" : "undefined"
      }`
    );

    const isProd = process.env.NODE_ENV === "production";

    let databaseUrl = isProd
      ? process.env.DATABASE_URL
      : process.env.EXTERNAL_DATABASE_URL;

    if (!databaseUrl || databaseUrl.includes("${")) {
      console.error(`[prisma] ❌ No valid database URL found!`);
      throw new Error(
        "No valid database URL found. Please set DATABASE_URL or EXTERNAL_DATABASE_URL."
      );
    }

    try {
      const dbUrl = new URL(databaseUrl);
      const hiddenUrl = `${dbUrl.protocol}//${dbUrl.username}:****@${dbUrl.hostname}:${dbUrl.port}${dbUrl.pathname}`;
      console.log(`[prisma] Using database URL: ${hiddenUrl}`);
    } catch (error) {
      console.error(`[prisma] ❌ Invalid database URL format:`, error);
    }

    try {
      this.client = new PrismaClient({
        datasources: {
          db: {
            url: databaseUrl,
          },
        },
        log: ["error", "warn"],
        errorFormat: "pretty",
        // Add connection pool configuration
        // These settings help manage connection limits
        __internal: {
          engine: {
            connectionLimit: 5, // Limit connections to avoid overwhelming the DB
          },
        },
      });

      // Add event listeners for connection issues
      if (this.client.$on) {
        this.client.$on("error", (e: any) => {
          console.error(`[prisma] Prisma client error: ${e.message}`);
          this.handleConnectionFailure();
        });
      }

      console.log("[prisma] Prisma client initialized");
    } catch (error) {
      console.error("[prisma] Failed to initialize Prisma client:", error);
      this.handleConnectionFailure();
    }
  }

  /**
   * Get the Prisma client instance with connection retry logic
   */
  async getClient(): Promise<PrismaClient> {
    // If circuit breaker is open, check if it's time to try again
    if (this.circuitBreakerState === "OPEN") {
      const now = Date.now();
      if (
        now - this.lastFailureTime >
        CONNECTION_SETTINGS.CIRCUIT_BREAKER_RESET_TIME_MS
      ) {
        console.log(
          "[prisma] Circuit breaker reset time reached, setting to HALF_OPEN"
        );
        this.circuitBreakerState = "HALF_OPEN";
      } else {
        throw new Error("Database connection circuit breaker is open");
      }
    }

    // If we're already connecting, return the existing promise
    if (this.isConnecting && this.connectionPromise) {
      return this.connectionPromise;
    }

    // If client exists and circuit breaker is closed, return it
    if (this.client && this.circuitBreakerState === "CLOSED") {
      try {
        // Test the connection with a simple query that returns a properly typed result
        await this.client.$queryRaw`SELECT 1 as connection_test`;
        return this.client;
      } catch (error) {
        console.error("[prisma] Connection test failed:", error);
        // Continue to reconnection logic
      }
    }

    // Set connecting flag and create connection promise
    this.isConnecting = true;
    this.connectionPromise = this.connectWithRetry();

    try {
      // Wait for connection
      const client = await this.connectionPromise;
      this.isConnecting = false;
      return client;
    } catch (error) {
      this.isConnecting = false;
      this.connectionPromise = null;
      throw error;
    }
  }

  /**
   * Connect to the database with retry logic
   */
  private async connectWithRetry(): Promise<PrismaClient> {
    let attempts = 0;

    while (attempts < CONNECTION_SETTINGS.MAX_RETRIES) {
      try {
        if (!this.client) {
          this.initializeClient();
          if (!this.client) {
            throw new Error("Failed to initialize Prisma client");
          }
        }

        // Test connection with a simple query that returns a properly typed result
        await this.client.$queryRaw`SELECT 1 as connection_test`;

        console.log("[prisma] Database connection successful");

        // Reset failure count on successful connection
        this.failureCount = 0;
        this.circuitBreakerState = "CLOSED";

        return this.client;
      } catch (error) {
        attempts++;
        console.error(
          `[prisma] Database connection attempt ${attempts} failed:`,
          error
        );

        // Try to disconnect and reconnect
        if (this.client) {
          try {
            await this.client.$disconnect();
            await this.client.$connect();
          } catch (reconnectError) {
            console.error("[prisma] Reconnection failed:", reconnectError);
          }
        }

        if (attempts >= CONNECTION_SETTINGS.MAX_RETRIES) {
          this.handleConnectionFailure();
          throw new Error(
            `Database connection failed after ${attempts} attempts`
          );
        }

        // Wait before retrying with exponential backoff
        const delay = Math.min(
          CONNECTION_SETTINGS.RETRY_DELAY_MS * Math.pow(2, attempts - 1),
          10000
        );
        console.log(
          `[prisma] Waiting ${delay}ms before retry ${attempts + 1}/${CONNECTION_SETTINGS.MAX_RETRIES}`
        );
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    throw new Error("Database connection failed");
  }

  /**
   * Handle connection failure and update circuit breaker state
   */
  private handleConnectionFailure() {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    console.warn(`[prisma] Connection failure count: ${this.failureCount}`);

    if (this.failureCount >= CONNECTION_SETTINGS.CIRCUIT_BREAKER_THRESHOLD) {
      console.error(
        "[prisma] Circuit breaker threshold reached, opening circuit"
      );
      this.circuitBreakerState = "OPEN";
    }
  }

  /**
   * Disconnect the Prisma client
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      try {
        await this.client.$disconnect();
        console.log("[prisma] Prisma client disconnected");
      } catch (error) {
        console.error("[prisma] Error disconnecting Prisma client:", error);
      }
    }
  }
}

// Create a singleton instance
const dbManager = new DatabaseConnectionManager();

// Export the enhanced Prisma client
export const prisma = await dbManager.getClient();

/**
 * Ensure database connection is established
 * @returns Promise resolving to true if connection successful, false otherwise
 */
export async function ensureDatabaseConnection(): Promise<boolean> {
  try {
    const client = await dbManager.getClient();
    await client.$queryRaw`SELECT 1 as connection_test`;
    return true;
  } catch (error) {
    console.error("Database connection error:", error);
    return false;
  }
}

/**
 * Disconnect the Prisma client
 */
export async function disconnectPrisma(): Promise<void> {
  return dbManager.disconnect();
}

// Handle application shutdown
process.on("SIGINT", async () => {
  console.log("[prisma] Received SIGINT signal, disconnecting Prisma client");
  await disconnectPrisma();
});

process.on("SIGTERM", async () => {
  console.log("[prisma] Received SIGTERM signal, disconnecting Prisma client");
  await disconnectPrisma();
});
