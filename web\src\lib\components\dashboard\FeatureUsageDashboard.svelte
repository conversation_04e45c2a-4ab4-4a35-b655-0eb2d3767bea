<!--
  Feature Usage Dashboard

  This component displays the user's feature usage in a user-friendly way.
  It shows progress bars for feature limits and provides a summary of usage.
-->
<script lang="ts">
  import { onMount } from 'svelte';
  import {
    getUserFeatureUsageWithPlanLimits,
    getFeatureUsageSummary,
  } from '$lib/services/feature-service';
  import type {
    FeatureWithDetailedUsage,
    UserFeatureUsageWithPlan,
  } from '$lib/services/feature-service';
  import { FeatureCategory, FeatureAccessLevel } from '$lib/models/features/features';
  import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
  } from '$lib/components/ui/card';
  import { Progress } from '$lib/components/ui/progress';
  import { Badge } from '$lib/components/ui/badge';
  import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
  } from '$lib/components/ui/accordion';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
  import { AlertCircleIcon } from 'lucide-svelte';

  // State
  let loading = true;
  let error: string | null = null;
  let usageData: UserFeatureUsageWithPlan | null = null;
  let usageSummary: any = null;
  let featuresByCategory: Record<string, FeatureWithDetailedUsage[]> = {};
  let categories: string[] = [];

  // Format a limit value for display
  function formatLimitValue(value: number | 'unlimited', unit?: string): string {
    if (value === 'unlimited') return 'Unlimited';
    return unit ? `${value} ${unit}` : `${value}`;
  }

  // Format a percentage for display (used in getProgressColor)

  // Get the color for a progress bar based on usage percentage
  function getProgressColor(percentUsed?: number): string {
    if (percentUsed === undefined) return 'bg-primary';
    if (percentUsed >= 90) return 'bg-destructive';
    if (percentUsed >= 70) return 'bg-warning';
    return 'bg-primary';
  }

  // Get the access level badge color
  function getAccessLevelColor(accessLevel: FeatureAccessLevel): string {
    switch (accessLevel) {
      case FeatureAccessLevel.Included:
        return 'bg-primary';
      case FeatureAccessLevel.Limited:
        return 'bg-warning';
      case FeatureAccessLevel.Unlimited:
        return 'bg-success';
      case FeatureAccessLevel.NotIncluded:
        return 'bg-destructive';
      default:
        return 'bg-muted';
    }
  }

  // Format the access level for display
  function formatAccessLevel(accessLevel: FeatureAccessLevel): string {
    switch (accessLevel) {
      case FeatureAccessLevel.Included:
        return 'Included';
      case FeatureAccessLevel.Limited:
        return 'Limited';
      case FeatureAccessLevel.Unlimited:
        return 'Unlimited';
      case FeatureAccessLevel.NotIncluded:
        return 'Not Included';
      default:
        return 'Unknown';
    }
  }

  // Format a category name for display
  function formatCategoryName(category: string): string {
    return category
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // Load the usage data
  async function loadUsageData() {
    loading = true;
    error = null;

    try {
      // Get the usage data
      usageData = await getUserFeatureUsageWithPlanLimits();

      if (usageData.error) {
        error = usageData.error;
      } else if (usageData.features.length === 0) {
        error = 'No feature usage data available.';
      } else {
        // Group features by category
        featuresByCategory = {};
        usageData.features.forEach((feature) => {
          const category = feature.category || 'other';
          if (!featuresByCategory[category]) {
            featuresByCategory[category] = [];
          }
          featuresByCategory[category].push(feature);
        });

        // Sort categories
        categories = Object.keys(featuresByCategory).sort((a, b) => {
          // Put core category first
          if (a === FeatureCategory.Core) return -1;
          if (b === FeatureCategory.Core) return 1;
          return a.localeCompare(b);
        });
      }

      // Get the usage summary
      usageSummary = await getFeatureUsageSummary();
    } catch (err) {
      console.error('Error loading feature usage data:', err);
      error = err.message || 'Failed to load feature usage data.';
    } finally {
      loading = false;
    }
  }

  onMount(() => {
    loadUsageData();
  });
</script>

<div class="space-y-6">
  <!-- Header -->
  <div class="flex flex-col gap-2">
    <h2 class="text-3xl font-bold tracking-tight">Feature Usage</h2>
    <p class="text-muted-foreground">
      Track your feature usage and limits based on your subscription plan.
    </p>
  </div>

  <!-- Loading state -->
  {#if loading}
    <div class="space-y-4">
      <Skeleton class="h-[200px] w-full" />
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Skeleton class="h-[150px] w-full" />
        <Skeleton class="h-[150px] w-full" />
        <Skeleton class="h-[150px] w-full" />
      </div>
    </div>
  {:else if error}
    <!-- Error state -->
    <Alert variant="destructive">
      <AlertCircleIcon class="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{error}</AlertDescription>
    </Alert>
  {:else}
    <!-- Usage summary -->
    {#if usageSummary}
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader class="pb-2">
            <CardTitle class="text-sm font-medium">Total Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{usageSummary.totalFeatures}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader class="pb-2">
            <CardTitle class="text-sm font-medium">Features Used</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{usageSummary.featuresUsed}</div>
            <p class="text-muted-foreground text-xs">
              {Math.round((usageSummary.featuresUsed / usageSummary.totalFeatures) * 100)}% of total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader class="pb-2">
            <CardTitle class="text-sm font-medium">Features with Limits</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{usageSummary.featuresWithLimits}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader class="pb-2">
            <CardTitle class="text-sm font-medium">Features at Limit</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{usageSummary.featuresAtLimit}</div>
            {#if usageSummary.featuresAtLimit > 0}
              <p class="text-destructive text-xs">Consider upgrading your plan</p>
            {/if}
          </CardContent>
        </Card>
      </div>
    {/if}

    <!-- Features by category -->
    <Accordion type="single" class="w-full">
      {#each categories as category, i}
        <AccordionItem value={category} open={i === 0}>
          <AccordionTrigger>
            <div class="flex items-center gap-2">
              <span>{formatCategoryName(category)}</span>
              <Badge variant="outline">{featuresByCategory[category].length}</Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <div class="grid grid-cols-1 gap-4 pt-4 md:grid-cols-2">
              {#each featuresByCategory[category] as feature}
                <Card>
                  <CardHeader>
                    <div class="flex items-start justify-between">
                      <CardTitle>{feature.name}</CardTitle>
                      <Badge variant="outline" class={getAccessLevelColor(feature.accessLevel)}>
                        {formatAccessLevel(feature.accessLevel)}
                      </Badge>
                    </div>
                    <CardDescription>{feature.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {#if feature.limits && feature.limits.length > 0}
                      <div class="space-y-4">
                        {#each feature.limits as limit}
                          <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                              <span>{limit.name}</span>
                              <span>
                                {limit.used} / {formatLimitValue(limit.value, limit.unit)}
                              </span>
                            </div>
                            {#if limit.value !== 'unlimited' && typeof limit.value === 'number'}
                              <Progress
                                value={limit.percentUsed || 0}
                                max={100}
                                class={getProgressColor(limit.percentUsed)} />
                            {:else}
                              <div class="text-muted-foreground text-xs">Unlimited usage</div>
                            {/if}
                          </div>
                        {/each}
                      </div>
                    {:else}
                      <div class="text-muted-foreground text-sm">
                        No usage limits for this feature.
                      </div>
                    {/if}
                  </CardContent>
                </Card>
              {/each}
            </div>
          </AccordionContent>
        </AccordionItem>
      {/each}
    </Accordion>
  {/if}
</div>
