<script lang="ts">
  import type { WorkExperienceSchema } from '$lib/validators/profile';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import * as Checkbox from '$lib/components/ui/checkbox/index.js';
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import * as AlertDialog from '$lib/components/ui/alert-dialog/index.js';
  import { Plus, Edit, Trash2, Briefcase } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';

  // Props
  const {
    data,
    onSave,
    disabled = false,
  } = $props<{
    data: WorkExperienceSchema[];
    onSave: (data: WorkExperienceSchema[]) => Promise<boolean>;
    disabled?: boolean;
  }>();

  // Form data
  let formData = $state({
    company: '',
    title: '',
    location: '',
    startDate: '',
    endDate: '',
    current: false,
    description: '',
  });

  // Form errors
  let errors = $state<Record<string, string>>({});
  let submitting = $state(false);

  // State
  let experiences = $state<WorkExperienceSchema[]>(data || []);
  let dialogOpen = $state(false);
  let deleteDialogOpen = $state(false);
  let editingIndex = $state<number | null>(null);
  let deletingIndex = $state<number | null>(null);

  // Open dialog for adding new experience
  function addExperience() {
    // Reset form data
    formData = {
      company: '',
      title: '',
      location: '',
      startDate: '',
      endDate: '',
      current: false,
      description: '',
    };
    editingIndex = null;
    dialogOpen = true;
  }

  // Open dialog for editing experience
  function editExperience(index: number) {
    const experience = experiences[index];
    // Set form data to experience values
    formData = {
      company: experience.company || '',
      title: experience.title || '',
      location: experience.location || '',
      startDate: experience.startDate || '',
      endDate: experience.endDate || '',
      current: experience.current || false,
      description: experience.description || '',
    };
    editingIndex = index;
    dialogOpen = true;
  }

  // Open dialog for deleting experience
  function confirmDeleteExperience(index: number) {
    deletingIndex = index;
    deleteDialogOpen = true;
  }

  // Delete experience
  function deleteExperience() {
    if (deletingIndex !== null) {
      experiences = experiences.filter((_, i) => i !== deletingIndex);
      saveExperiences();
      deleteDialogOpen = false;
      deletingIndex = null;
    }
  }

  // Handle form submission
  async function handleSubmit() {
    // Validate form
    if (!formData.company || !formData.title || !formData.startDate) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Create new experience object
    const newExperience: WorkExperienceSchema = {
      ...formData,
    };

    // Update or add experience
    if (editingIndex !== null) {
      experiences[editingIndex] = newExperience;
    } else {
      experiences = [...experiences, newExperience];
    }

    // Save experiences
    await saveExperiences();

    // Close dialog
    dialogOpen = false;
  }

  // Save experiences to server
  async function saveExperiences() {
    const success = await onSave(experiences);
    if (success) {
      toast.success('Work experience updated successfully');
    }
    return success;
  }

  // Format date for display
  function formatDate(dateString: string | undefined): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
    });
  }

  // Format date range for display
  function formatDateRange(
    startDate: string | undefined,
    endDate: string | undefined,
    current: boolean
  ): string {
    const start = formatDate(startDate);
    const end = current ? 'Present' : formatDate(endDate);

    return `${start} - ${end}`;
  }
</script>

<div class="rounded-lg border p-6">
  <div class="flex items-center justify-between">
    <h2 class="text-xl font-semibold">Work Experience</h2>
    <Button variant="outline" size="sm" onclick={addExperience} {disabled}>
      <Plus class="mr-2 h-4 w-4" />
      Add Experience
    </Button>
  </div>

  <div class="mt-4">
    {#if experiences.length > 0}
      <div class="space-y-4">
        {#each experiences as experience, index}
          <div class="flex items-start justify-between rounded-md border p-4">
            <div class="flex-1">
              <div class="flex items-center">
                <Briefcase class="mr-2 h-5 w-5 text-blue-500" />
                <h3 class="font-medium">{experience.title}</h3>
              </div>
              <p class="text-muted-foreground">{experience.company}</p>
              <p class="text-muted-foreground text-sm">
                {formatDateRange(experience.startDate, experience.endDate, experience.current)}
              </p>
              {#if experience.location}
                <p class="text-muted-foreground text-sm">{experience.location}</p>
              {/if}
              {#if experience.description}
                <p class="mt-2 text-sm">{experience.description}</p>
              {/if}
            </div>
            <div class="flex space-x-2">
              <Button variant="ghost" size="icon" onclick={() => editExperience(index)} {disabled}>
                <Edit class="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onclick={() => confirmDeleteExperience(index)}
                {disabled}>
                <Trash2 class="h-4 w-4" />
              </Button>
            </div>
          </div>
        {/each}
      </div>
    {:else}
      <div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8">
        <Briefcase class="text-muted-foreground mb-2 h-10 w-10" />
        <p class="text-muted-foreground text-center">No work experience added yet</p>
        <Button variant="outline" class="mt-4" onclick={addExperience} {disabled}>
          <Plus class="mr-2 h-4 w-4" />
          Add Experience
        </Button>
      </div>
    {/if}
  </div>
</div>

<!-- Add/Edit Experience Dialog -->
<Dialog.Root bind:open={dialogOpen}>
  <Dialog.Content class="sm:max-w-[600px]">
    <Dialog.Header>
      <Dialog.Title>{editingIndex !== null ? 'Edit Experience' : 'Add Experience'}</Dialog.Title>
      <Dialog.Description>
        {editingIndex !== null
          ? 'Update your work experience details.'
          : 'Add a new work experience to your profile.'}
      </Dialog.Description>
    </Dialog.Header>

    <form method="POST" class="space-y-4">
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div class="space-y-2">
          <Label for="company">Company *</Label>
          <Input id="company" bind:value={formData.company} />
          {#if errors.company}
            <p class="text-destructive text-sm">{errors.company}</p>
          {/if}
        </div>
        <div class="space-y-2">
          <Label for="title">Job Title *</Label>
          <Input id="title" bind:value={formData.title} />
          {#if errors.title}
            <p class="text-destructive text-sm">{errors.title}</p>
          {/if}
        </div>
        <div class="space-y-2">
          <Label for="location">Location</Label>
          <Input id="location" bind:value={formData.location} />
        </div>
        <div class="space-y-2">
          <Label for="startDate">Start Date *</Label>
          <Input id="startDate" type="month" bind:value={formData.startDate} />
          {#if errors.startDate}
            <p class="text-destructive text-sm">{errors.startDate}</p>
          {/if}
        </div>
        <div class="space-y-2">
          <div class="flex items-center space-x-2">
            <Checkbox.Root
              id="current"
              checked={formData.current}
              onCheckedChange={(checked) => (formData.current = checked)} />
            <Label for="current">I currently work here</Label>
          </div>
        </div>
        <div class="space-y-2">
          <Label for="endDate">End Date</Label>
          <Input
            id="endDate"
            type="month"
            bind:value={formData.endDate}
            disabled={formData.current} />
        </div>
      </div>

      <div class="space-y-2">
        <Label for="description">Description</Label>
        <Textarea
          id="description"
          bind:value={formData.description}
          rows={4}
          placeholder="Describe your responsibilities and achievements..." />
      </div>

      <Dialog.Footer>
        <Button variant="outline" type="button" onclick={() => (dialogOpen = false)}>Cancel</Button>
        <Button type="button" onclick={handleSubmit} disabled={submitting}>
          {#if submitting}
            Saving...
          {:else}
            Save
          {/if}
        </Button>
      </Dialog.Footer>
    </form>
  </Dialog.Content>
</Dialog.Root>

<!-- Delete Confirmation Dialog -->
<AlertDialog.Root bind:open={deleteDialogOpen}>
  <AlertDialog.Content>
    <AlertDialog.Header>
      <AlertDialog.Title>Delete Work Experience</AlertDialog.Title>
      <AlertDialog.Description>
        Are you sure you want to delete this work experience? This action cannot be undone.
      </AlertDialog.Description>
    </AlertDialog.Header>
    <AlertDialog.Footer>
      <AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
      <AlertDialog.Action onclick={deleteExperience}>Delete</AlertDialog.Action>
    </AlertDialog.Footer>
  </AlertDialog.Content>
</AlertDialog.Root>
