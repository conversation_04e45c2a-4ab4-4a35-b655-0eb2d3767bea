/**
 * Keyboard Shortcuts Store
 *
 * This file provides a Svelte store for managing keyboard shortcuts.
 * It handles activating/deactivating shortcuts based on the current page.
 */

import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';
import { page } from '$app/stores';
import { ShortcutPage } from '$lib/types/keyboard-shortcuts';
import { getShortcutsForPage } from './shortcuts-registry';

// Store for the current active page (for shortcuts)
export const currentShortcutPage = writable<ShortcutPage>(ShortcutPage.GLOBAL);

// Store for active shortcut groups
export const activeShortcutGroups = derived([currentShortcutPage], ([$currentShortcutPage]) =>
  getShortcutsForPage($currentShortcutPage)
);

// Store for all active shortcuts (flattened)
export const activeShortcuts = derived([activeShortcutGroups], ([$activeShortcutGroups]) =>
  $activeShortcutGroups.flatMap((group) => group.shortcuts)
);

// Helper to determine the current page from the URL
export function determineCurrentPage(path: string): ShortcutPage {
  if (path.includes('/dashboard/jobs')) return ShortcutPage.JOBS;
  if (path.includes('/dashboard/applications')) return ShortcutPage.APPLICATIONS;
  if (path.includes('/dashboard/resumes')) return ShortcutPage.RESUMES;
  if (path.includes('/dashboard/documents')) return ShortcutPage.DOCUMENTS;
  if (path.includes('/dashboard/tracker')) return ShortcutPage.TRACKER;
  if (path.includes('/dashboard/automation')) return ShortcutPage.AUTOMATION;
  if (path.includes('/dashboard/matches')) return ShortcutPage.MATCHES;
  if (path.includes('/dashboard/settings/admin')) return ShortcutPage.ADMIN;
  if (path.includes('/dashboard/settings')) return ShortcutPage.SETTINGS;
  if (path.includes('/system-status')) return ShortcutPage.SYSTEM_STATUS;
  if (path.includes('/dashboard/notifications')) return ShortcutPage.NOTIFICATIONS;
  if (path.includes('/dashboard')) return ShortcutPage.DASHBOARD;
  return ShortcutPage.GLOBAL;
}

// Initialize keyboard shortcuts handler
let shortcutsInitialized = false;
let cleanupFunction: (() => void) | null = null;

export function initKeyboardShortcuts() {
  if (!browser || shortcutsInitialized) return;

  // Set up page change listener
  const unsubscribePage = page.subscribe((pageData) => {
    // Use URL pathname to determine current page
    if (pageData && pageData.url) {
      const pathname = pageData.url.pathname;
      const currentPage = determineCurrentPage(pathname);
      currentShortcutPage.set(currentPage);
    }
  });

  // Set up keyboard event listener
  const handleKeyDown = (event: KeyboardEvent) => {
    // Only process if Alt key is pressed (or Windows/Command key)
    if (!event.altKey && !event.metaKey) return;

    const key = event.key.toLowerCase();
    // Determine the modifier key (Alt or Meta/Win)
    const modifierKey = event.altKey
      ? 'Alt'
      : event.metaKey
        ? /Mac|iPhone|iPad|iPod/.test(navigator.userAgent)
          ? '⌘'
          : 'Win'
        : '';
    const shortcutKey = `${modifierKey}+${key.toUpperCase()}`;

    // Find matching shortcut
    activeShortcuts.subscribe((shortcuts) => {
      const matchingShortcut = shortcuts.find(
        (s) => s.keys.toLowerCase() === shortcutKey.toLowerCase()
      );

      if (matchingShortcut) {
        event.preventDefault();
        matchingShortcut.handler(event);
      }
    })();
  };

  // Add event listener
  document.addEventListener('keydown', handleKeyDown);

  // Set cleanup function
  cleanupFunction = () => {
    document.removeEventListener('keydown', handleKeyDown);
    unsubscribePage();
  };

  shortcutsInitialized = true;
}

export function cleanupKeyboardShortcuts() {
  if (cleanupFunction) {
    cleanupFunction();
    cleanupFunction = null;
    shortcutsInitialized = false;
  }
}
