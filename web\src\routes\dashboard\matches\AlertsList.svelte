<script lang="ts">
  import * as Card from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import { MoreVertical, Bell, BellOff, Edit, Trash2, Plus } from 'lucide-svelte';
  import * as AlertDialog from '$lib/components/ui/alert-dialog';
  import { toast } from 'svelte-sonner';

  export let alerts: any[] = [];
  export let onCreateAlert: () => void = () => {};

  // UI state
  let showDeleteDialog = false;
  let currentAlert: any = null;

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get frequency text
  const getFrequencyText = (frequency: string) => {
    switch (frequency) {
      case 'daily':
        return 'Daily';
      case 'weekly':
        return 'Weekly';
      case 'monthly':
        return 'Monthly';
      default:
        return 'As needed';
    }
  };

  // Handle edit alert
  const handleEditAlert = (alert: any) => {
    currentAlert = alert;
    // We'll implement this in a future update
    toast.info('Edit functionality will be available soon');
  };

  // Handle delete alert
  const handleDeleteAlert = (alert: any) => {
    currentAlert = alert;
    showDeleteDialog = true;
  };

  // Handle toggle alert
  const handleToggleAlert = async (alert: any) => {
    try {
      const response = await fetch('/api/job-alerts', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: alert.id,
          enabled: !alert.enabled,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update job alert');
      }

      // Update the alert in the list
      alerts = alerts.map((a) => (a.id === alert.id ? { ...a, enabled: !a.enabled } : a));

      toast.success(`Alert ${alert.enabled ? 'disabled' : 'enabled'} successfully`);
    } catch (error) {
      console.error('Error updating job alert:', error);
      toast.error('Failed to update job alert');
    }
  };

  // Handle confirm delete
  const handleConfirmDelete = async () => {
    if (!currentAlert) return;

    try {
      const response = await fetch('/api/job-alerts', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: currentAlert.id,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete job alert');
      }

      // Remove the alert from the list
      alerts = alerts.filter((a) => a.id !== currentAlert.id);

      showDeleteDialog = false;
      currentAlert = null;

      toast.success('Alert deleted successfully');
    } catch (error) {
      console.error('Error deleting job alert:', error);
      toast.error('Failed to delete job alert');
    }
  };

  // Format search params for display
  const formatSearchParams = (params: any) => {
    if (!params) return 'All jobs';

    const parts = [];
    if (params.keywords) parts.push(`Keywords: ${params.keywords}`);
    if (params.location) parts.push(`Location: ${params.location}`);

    // Format job type with proper capitalization
    if (params.jobType) {
      const jobTypeMap = {
        full_time: 'Full-time',
        part_time: 'Part-time',
        contract: 'Contract',
        temporary: 'Temporary',
        internship: 'Internship',
      };
      parts.push(`Job Type: ${jobTypeMap[params.jobType] || params.jobType}`);
    }

    if (params.remote) parts.push('Remote Only');

    return parts.length > 0 ? parts.join(', ') : 'All jobs';
  };

  // Get badge color based on alert status
  const getStatusBadgeVariant = (enabled: boolean) => {
    return enabled ? 'default' : 'secondary';
  };

  // Get the count of matching jobs (placeholder for now)
  const getMatchingJobsCount = (alert: any) => {
    // This would ideally come from the API
    return Math.floor(Math.random() * 20); // Placeholder
  };
</script>

<div>
  {#if alerts.length === 0}
    <div class="rounded-lg border p-6 text-center">
      <div
        class="bg-foreground mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full">
        <Bell class="text-background h-6 w-6" />
      </div>
      <h3 class="mb-2 text-lg font-medium">No job alerts yet</h3>
      <p class="text-muted-foreground mx-auto mb-4 max-w-md">
        Create job alerts to get notified when new jobs matching your criteria are available. You'll
        receive email notifications based on your selected frequency.
      </p>
      <Button class="mt-6" variant="default" onclick={onCreateAlert}>
        <Plus class="mr-2 h-4 w-4" />
        Create Your First Alert
      </Button>
    </div>
  {:else}
    <div class="space-y-4">
      {#each alerts as alert (alert.id)}
        <Card.Root>
          <Card.Header class="p-4">
            <div class="flex items-start justify-between">
              <div>
                <Card.Title class="flex items-center gap-2">
                  {alert.name}
                  <Badge variant={getStatusBadgeVariant(alert.enabled)}>
                    {alert.enabled ? 'Active' : 'Disabled'}
                  </Badge>
                </Card.Title>
                <Card.Description class="mt-1">
                  {formatSearchParams(alert.searchParams)}
                </Card.Description>
                <div class="mt-2">
                  <Badge variant="outline" class="text-xs">
                    {getMatchingJobsCount(alert)} matching jobs
                  </Badge>
                </div>
              </div>
              <DropdownMenu.Root>
                <DropdownMenu.Trigger>
                  <Button variant="ghost" size="icon" class="h-8 w-8">
                    <MoreVertical class="h-4 w-4" />
                    <span class="sr-only">Open menu</span>
                  </Button>
                </DropdownMenu.Trigger>
                <DropdownMenu.Content align="end">
                  <DropdownMenu.Item
                    onclick={() => handleToggleAlert(alert)}
                    class="flex items-center gap-2">
                    {#if alert.enabled}
                      <BellOff class="h-4 w-4" />
                      <span>Disable Alert</span>
                    {:else}
                      <Bell class="h-4 w-4" />
                      <span>Enable Alert</span>
                    {/if}
                  </DropdownMenu.Item>
                  <DropdownMenu.Item
                    onclick={() => handleEditAlert(alert)}
                    class="flex items-center gap-2">
                    <Edit class="h-4 w-4" />
                    <span>Edit Alert</span>
                  </DropdownMenu.Item>
                  <DropdownMenu.Separator />
                  <DropdownMenu.Item
                    onclick={() => handleDeleteAlert(alert)}
                    class="flex items-center gap-2 text-red-600">
                    <Trash2 class="h-4 w-4" />
                    <span>Delete Alert</span>
                  </DropdownMenu.Item>
                </DropdownMenu.Content>
              </DropdownMenu.Root>
            </div>
          </Card.Header>
          <Card.Content class="p-4 pt-0">
            <div class="flex flex-wrap gap-x-6 gap-y-2 text-sm text-gray-500">
              <div class="flex items-center gap-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round">
                  <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"
                  ></path>
                  <path d="M12 6v6l4 2"></path>
                </svg>
                <span class="font-medium">Frequency:</span>
                {getFrequencyText(alert.frequency)}
              </div>
              <div class="flex items-center gap-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                <span class="font-medium">Created:</span>
                {formatDate(alert.createdAt)}
              </div>
              {#if alert.lastSentAt}
                <div class="flex items-center gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round">
                    <path d="M22 2L11 13"></path>
                    <path d="M22 2l-7 20-4-9-9-4 20-7z"></path>
                  </svg>
                  <span class="font-medium">Last sent:</span>
                  {formatDate(alert.lastSentAt)}
                </div>
              {/if}
            </div>
          </Card.Content>
          <Card.Footer class="p-4 pt-0">
            <div class="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                class="text-xs"
                onclick={() => (window.location.href = '/dashboard/jobs')}>
                View Matching Jobs
              </Button>
            </div>
          </Card.Footer>
        </Card.Root>
      {/each}
    </div>
  {/if}

  <!-- Delete Alert Dialog -->
  <AlertDialog.Root open={showDeleteDialog}>
    <AlertDialog.Content>
      <AlertDialog.Header>
        <AlertDialog.Title>Delete Job Alert</AlertDialog.Title>
        <AlertDialog.Description>
          Are you sure you want to delete this job alert? This action cannot be undone.
        </AlertDialog.Description>
      </AlertDialog.Header>
      <AlertDialog.Footer>
        <AlertDialog.Cancel onclick={() => (showDeleteDialog = false)}>Cancel</AlertDialog.Cancel>
        <AlertDialog.Action onclick={handleConfirmDelete}>Delete</AlertDialog.Action>
      </AlertDialog.Footer>
    </AlertDialog.Content>
  </AlertDialog.Root>
</div>
