// @ts-nocheck
/**
 * Admin Settings Page Server
 *
 * This module handles server-side logic for the admin settings page.
 */

import { redirect } from '@sveltejs/kit';
import { dev } from '$app/environment';
import { prisma } from '$lib/server/prisma';
import type { PageServerLoad } from './$types';

export const load = async ({ locals }: Parameters<PageServerLoad>[0]) => {
  // In development mode, allow access to the page
  if (dev) {
    return {};
  }

  // Get user from session
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  // Check if user is an admin
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
    select: { isAdmin: true, role: true },
  });

  // Allow access if either isAdmin is true OR role is 'admin'
  if (!userData || (!userData.isAdmin && userData.role !== 'admin')) {
    throw redirect(302, '/dashboard/settings');
  }

  return {};
};
