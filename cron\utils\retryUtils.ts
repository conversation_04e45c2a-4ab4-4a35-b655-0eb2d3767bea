// cron/utils/retryUtils.ts
// Utility functions for retrying operations

import { logger } from "./logger";

/**
 * Options for the retry function
 */
export interface RetryOptions {
  /** Maximum number of retry attempts */
  maxRetries: number;
  
  /** Delay between retries in milliseconds */
  delayMs: number;
  
  /** Whether to use exponential backoff (default: true) */
  backoff?: boolean;
  
  /** Maximum delay in milliseconds (default: 30000) */
  maxDelayMs?: number;
  
  /** Function to determine if an error is retryable */
  isRetryable?: (error: any) => boolean;
  
  /** Operation name for logging */
  operationName?: string;
}

/**
 * Execute an operation with retry logic
 * @param operation The operation to execute
 * @param options Retry options
 * @returns The result of the operation
 * @throws The last error encountered if all retries fail
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions
): Promise<T> {
  const {
    maxRetries,
    delayMs,
    backoff = true,
    maxDelayMs = 30000,
    isRetryable = () => true,
    operationName = "operation"
  } = options;
  
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // If we've reached the maximum number of retries, or the error is not retryable, throw
      if (attempt > maxRetries || !isRetryable(error)) {
        if (attempt > maxRetries) {
          logger.error(`❌ ${operationName} failed after ${maxRetries} retries:`, error);
        } else {
          logger.error(`❌ ${operationName} failed with non-retryable error:`, error);
        }
        throw error;
      }
      
      // Calculate delay with exponential backoff if enabled
      let delay = delayMs;
      if (backoff) {
        delay = Math.min(delayMs * Math.pow(2, attempt - 1), maxDelayMs);
      }
      
      logger.warn(
        `⚠️ ${operationName} failed (attempt ${attempt}/${maxRetries + 1}), retrying in ${delay}ms:`,
        error
      );
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  // This should never happen due to the throw in the loop
  throw lastError;
}

/**
 * Common retry policies for different types of operations
 */
export const retryPolicies = {
  // For database operations
  database: {
    maxRetries: 3,
    delayMs: 1000,
    backoff: true,
    isRetryable: (error: any) => {
      // Retry on connection errors or deadlocks
      return (
        error.code === 'P1001' || // Prisma connection error
        error.code === 'P1008' || // Operations timeout
        error.code === 'P1017' || // Server closed the connection
        error.code === 'P2034'    // Transaction deadlock
      );
    },
  },
  
  // For network operations
  network: {
    maxRetries: 5,
    delayMs: 2000,
    backoff: true,
    isRetryable: (error: any) => {
      // Retry on network errors, timeouts, and 5xx server errors
      return (
        error.code === 'ECONNRESET' ||
        error.code === 'ETIMEDOUT' ||
        error.code === 'ECONNREFUSED' ||
        (error.response && error.response.status >= 500 && error.response.status < 600)
      );
    },
  },
  
  // For scraping operations
  scraping: {
    maxRetries: 3,
    delayMs: 5000,
    backoff: true,
    isRetryable: (error: any) => {
      // Retry on timeouts, navigation errors, and certain status codes
      return (
        error.name === 'TimeoutError' ||
        error.message?.includes('Navigation timeout') ||
        error.message?.includes('net::ERR_') ||
        (error.response && (
          error.response.status === 429 || // Too many requests
          error.response.status >= 500 && error.response.status < 600 // Server errors
        ))
      );
    },
  },
};

export default withRetry;
