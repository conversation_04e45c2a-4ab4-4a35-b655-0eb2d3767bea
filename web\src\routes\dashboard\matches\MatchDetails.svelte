<script lang="ts">
  import { Button } from '$lib/components/ui/button';

  export let selectedJob: any = null;
</script>

<div>
  <h2 class="mb-4 text-lg font-semibold">Job Details</h2>
  {#if selectedJob}
    <div>
      <h3 class="text-xl font-bold">{selectedJob.title}</h3>
      <p class="mt-1 text-lg text-gray-700">{selectedJob.company}</p>
      <p class="text-md mt-1 text-gray-600">{selectedJob.location}</p>

      {#if selectedJob.salary}
        <div class="mt-4">
          <h4 class="text-md font-semibold">Salary</h4>
          <p>{selectedJob.salary}</p>
        </div>
      {/if}

      {#if selectedJob.description}
        <div class="mt-4">
          <h4 class="text-md font-semibold">Description</h4>
          <div class="prose mt-2 max-w-none">
            {@html selectedJob.description}
          </div>
        </div>
      {/if}

      {#if selectedJob.requirements && selectedJob.requirements.length > 0}
        <div class="mt-4">
          <h4 class="text-md font-semibold">Requirements</h4>
          <ul class="mt-2 list-disc pl-5">
            {#each selectedJob.requirements as requirement}
              <li>{requirement}</li>
            {/each}
          </ul>
        </div>
      {/if}

      {#if selectedJob.benefits && selectedJob.benefits.length > 0}
        <div class="mt-4">
          <h4 class="text-md font-semibold">Benefits</h4>
          <ul class="mt-2 list-disc pl-5">
            {#each selectedJob.benefits as benefit}
              <li>{benefit}</li>
            {/each}
          </ul>
        </div>
      {/if}

      <div class="mt-6 flex space-x-4">
        <Button onclick={() => window.open(selectedJob.url, '_blank')}>
          View Original Posting
        </Button>
        {#if selectedJob.applyLink}
          <Button
            variant="outline"
            onclick={() => window.open(selectedJob.applyLink, '_blank')}>
            Apply Now
          </Button>
        {/if}
      </div>
    </div>
  {:else}
    <p class="text-gray-500">Select a job to view details</p>
  {/if}
</div>
