// cron/scripts/updateCompanyDomains.ts

import { batchUpdateCompanyDomains } from "../jobs/batchUpdateCompanyDomains";
import { logger } from "../utils/logger";
import { CircuitState } from "../utils/improvedImprovedCircuitBreaker";

logger.info("🚀 Starting manual company domain update");

batchUpdateCompanyDomains()
  .then(() => {
    logger.info("✅ Company domain update completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Error updating company domains:", error);
    process.exit(1);
  });
