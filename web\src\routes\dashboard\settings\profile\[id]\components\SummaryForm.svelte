<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import { toast } from 'svelte-sonner';
  import type { ProfileData } from '$lib/types/profile';

  // Props
  const { profileData, onSave, onCancel } = $props<{
    profileData: ProfileData;
    onSave: (data: Partial<ProfileData>) => Promise<boolean>;
    onCancel: () => void;
  }>();

  // Form data
  let summary = $state(profileData.summary || profileData.personalInfo?.summary || '');

  // Handle form submission
  async function handleSubmit() {
    try {
      // Prepare data
      const updatedData: Partial<ProfileData> = {
        summary,
        personalInfo: {
          ...profileData.personalInfo,
          summary,
        },
      };

      // Save data
      const success = await onSave(updatedData);
      if (success) {
        toast.success('Professional summary updated successfully');
      }
    } catch (error) {
      console.error('Error saving summary:', error);
      toast.error('Failed to save professional summary');
    }
  }
</script>

<form on:submit|preventDefault={handleSubmit} class="space-y-4">
  <div class="space-y-2">
    <Label for="summary">Professional Summary</Label>
    <Textarea
      id="summary"
      bind:value={summary}
      placeholder="Write a brief summary of your professional background, skills, and career goals..."
      rows={6}
    />
    <p class="text-muted-foreground text-xs">
      A good summary highlights your key skills, experience, and what you're looking for in your next
      role.
    </p>
  </div>

  <div class="flex justify-end space-x-2 pt-4">
    <Button variant="outline" type="button" on:click={onCancel}>Cancel</Button>
    <Button type="submit">Save Changes</Button>
  </div>
</form>
