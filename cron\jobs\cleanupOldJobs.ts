import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";

const prisma = new PrismaClient();

export async function cleanupOldJobs() {
  const startTime = Date.now();
  let success = false;
  let failureReason = "";
  let count = 0;

  try {
    const cutoff = new Date(Date.now() - 1000 * 60 * 60 * 24); // 24 hours ago

    const result = await prisma.jobListing.updateMany({
      where: {
        lastCheckedAt: { lt: cutoff },
        isActive: true,
      },
      data: {
        isActive: false,
        closedAt: new Date(),
      },
    });

    count = result.count;
    logger.info(`🧹 Marked ${result.count} jobs as inactive`);
    success = true;
  } catch (error) {
    failureReason = error instanceof Error ? error.message : String(error);
    logger.error(`❌ Error cleaning up old jobs:`, error);
  } finally {
    const duration = Date.now() - startTime;
    // Track this job in the global stats
    logger.jobStats({
      jobType: "cleanupOldJobs",
      duration,
      processed: count,
      succeeded: success ? count : 0,
      failed: success ? 0 : count,
      details: {
        error: failureReason || undefined,
      },
    });
  }

  return count;
}
