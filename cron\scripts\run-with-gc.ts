#!/usr/bin/env node

/**
 * This script runs a specified command with the --expose-gc flag to enable manual garbage collection.
 * If --expose-gc is not allowed, it will fall back to running without it.
 * Usage: tsx run-with-gc.ts <command>
 * Example: tsx run-with-gc.ts jobs/scrapeJobDetails.ts
 */

import { spawn } from "child_process";
import path from "path";

// Get the command to run from command line arguments
const args = process.argv.slice(2);

if (args.length === 0) {
  console.error("Error: No command specified");
  console.error("Usage: tsx run-with-gc.ts <command>");
  console.error("Example: tsx run-with-gc.ts jobs/scrapeJobDetails.ts");
  process.exit(1);
}

// Determine if we're in production
const isProduction = process.env.NODE_ENV === "production";

// In production, don't try to use --expose-gc since it's not allowed on Render.com
const command = "tsx";
const commandArgs = isProduction ? [...args] : ["--expose-gc", ...args];

console.log(
  `Running: ${command} ${commandArgs.join(" ")} (GC flag: ${!isProduction})`
);

// Spawn the process
const child = spawn(command, commandArgs, {
  stdio: "inherit",
  shell: true,
});

// Handle process exit
child.on("close", (code) => {
  process.exit(code || 0);
});

// Handle process errors
child.on("error", (err) => {
  console.error("Failed to start process:", err);
  process.exit(1);
});
