<script lang="ts">
  import UsageSummaryCard from './UsageSummaryCard.svelte';

  export let usageSummary: any;
  export let resumeUsage: { used: number; limit: number | null; remaining: number | null } | null =
    null;
</script>

<div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
  <UsageSummaryCard title="Total Features" value={usageSummary.totalFeatures} />

  <UsageSummaryCard
    title="Features Used"
    value={usageSummary.featuresUsed}
    subtitle={usageSummary.featuresUsed !== undefined &&
    usageSummary.totalFeatures !== undefined &&
    usageSummary.totalFeatures > 0
      ? `${Math.round((usageSummary.featuresUsed / usageSummary.totalFeatures) * 100)}% of total`
      : ''} />

  <UsageSummaryCard title="Features with Limits" value={usageSummary.featuresWithLimits} />

  <UsageSummaryCard
    title="Features at Limit"
    value={usageSummary.featuresAtLimit}
    warning="Consider upgrading your plan"
    showWarning={usageSummary.featuresAtLimit > 0} />

  {#if resumeUsage && resumeUsage.used !== undefined}
    <UsageSummaryCard
      title="Resume Submissions"
      value={resumeUsage.used}
      subtitle={resumeUsage.limit ? `${resumeUsage.remaining} remaining this month` : 'Unlimited'}
      warning="Consider upgrading your plan"
      showWarning={resumeUsage.limit && resumeUsage.remaining === 0} />
  {/if}
</div>
