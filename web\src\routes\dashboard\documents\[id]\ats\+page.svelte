<script lang="ts">
  import { goto } from '$app/navigation';
  import SEO from '$components/shared/SEO.svelte';
  import * as Card from '$lib/components/ui/card';
  import * as Tabs from '$lib/components/ui/tabs';
  import { ChevronLeft, FileText, Sparkles } from 'lucide-svelte';
  import UniversalDocumentViewer from '$lib/components/document/UniversalDocumentViewer.svelte';
  import ATSScoreCard from '$components/ai/ATSScoreCard.svelte';

  const { data } = $props<{
    data: {
      id: string;
      document: Document | null;
    };
  }>();

  interface Document {
    id: string;
    label: string;
    fileUrl: string;
    createdAt: string | Date;
    updatedAt?: string | Date;
    type: string;
    fileName?: string;
    score?: number | null;
    profile?: any;
    jobSearch?: any;
    isDefault?: boolean;
    source?: 'uploaded' | 'generated' | 'created';
    isParsed?: boolean;
    parsedAt?: string | Date;
    resume?: any;
  }

  let document = $state<Document | null>(data.document || null);
  let loading = $state(!document);
  let activeTab = $state('overview');
  let selectedJobId = $state('');
  let jobs = $state<any[]>([]);

  // Load jobs for job-specific analysis
  $effect(() => {
    if (!document) return;

    // Use a separate async function to avoid issues with $effect
    loadJobs();
  });

  async function loadJobs() {
    try {
      const response = await fetch('/api/jobs?limit=10');
      if (response.ok) {
        const responseData = await response.json();
        jobs = responseData.jobs || [];
      }
    } catch (error) {
      console.error('Error loading jobs:', error);
    }
  }

  // Handle job selection
  function handleJobSelect(event: Event) {
    const target = event.target as HTMLSelectElement;
    selectedJobId = target.value;
  }
</script>

<SEO title="ATS Optimization | Auto Apply" description="Optimize your resume for ATS systems" />

<div class="container mx-auto p-6">
  <div class="mb-6 flex items-center justify-between">
    <div>
      <div class="flex items-center">
        <button
          class="mr-2 inline-flex h-10 items-center justify-center rounded-md p-2 text-gray-700 hover:bg-gray-100"
          onclick={() => goto(`/dashboard/documents/${document?.id}`)}>
          <ChevronLeft class="h-5 w-5" />
        </button>
        <div>
          <h1 class="text-3xl font-bold">ATS Optimization</h1>
          <p class="text-gray-500">Analyze and optimize your resume for ATS systems</p>
        </div>
      </div>
    </div>
  </div>

  {#if loading}
    <div class="flex h-64 items-center justify-center">
      <p class="text-lg text-gray-500">Loading document...</p>
    </div>
  {:else if document}
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
      <!-- Document Viewer -->
      <div>
        <Card.Root>
          <Card.Header>
            <Card.Title class="flex items-center">
              <FileText class="mr-2 h-5 w-5" />
              {document.label}
            </Card.Title>
            <Card.Description>View your resume document</Card.Description>
          </Card.Header>
          <Card.Content>
            <div style="height: 600px;">
              <UniversalDocumentViewer {document} />
            </div>
          </Card.Content>
        </Card.Root>
      </div>

      <!-- ATS Analysis -->
      <div>
        <Tabs.Root value={activeTab} onValueChange={(value) => (activeTab = value)}>
          <Tabs.List class="grid w-full grid-cols-2">
            <Tabs.Trigger value="overview">General Analysis</Tabs.Trigger>
            <Tabs.Trigger value="job-specific">Job-Specific Analysis</Tabs.Trigger>
          </Tabs.List>

          <Tabs.Content value="overview" class="mt-4">
            <Card.Root>
              <Card.Header>
                <Card.Title class="flex items-center">
                  <Sparkles class="mr-2 h-5 w-5 text-blue-500" />
                  ATS Analysis
                </Card.Title>
                <Card.Description>See how your resume performs with ATS systems</Card.Description>
              </Card.Header>
              <Card.Content>
                <ATSScoreCard resumeId={document.resume?.id || document.id} />
              </Card.Content>
            </Card.Root>
          </Tabs.Content>

          <Tabs.Content value="job-specific" class="mt-4">
            <Card.Root>
              <Card.Header>
                <Card.Title class="flex items-center">
                  <Sparkles class="mr-2 h-5 w-5 text-blue-500" />
                  Job-Specific Analysis
                </Card.Title>
                <Card.Description>Analyze your resume against a specific job</Card.Description>
              </Card.Header>
              <Card.Content>
                <div class="mb-4">
                  <label for="job-select" class="mb-2 block text-sm font-medium">
                    Select a job to analyze against
                  </label>
                  <select
                    id="job-select"
                    class="w-full rounded-md border border-gray-300 p-2"
                    value={selectedJobId}
                    onchange={handleJobSelect}>
                    <option value="">Select a job...</option>
                    {#each jobs as job}
                      <option value={job.id}>{job.title} at {job.company}</option>
                    {/each}
                  </select>
                </div>

                {#if selectedJobId}
                  <ATSScoreCard
                    resumeId={document.resume?.id || document.id}
                    jobId={selectedJobId} />
                {:else}
                  <div class="rounded-md bg-blue-50 p-4 text-sm text-blue-700">
                    <p>Select a job to see how your resume matches the specific requirements.</p>
                  </div>
                {/if}
              </Card.Content>
            </Card.Root>
          </Tabs.Content>
        </Tabs.Root>
      </div>
    </div>
  {:else}
    <div class="flex h-64 items-center justify-center">
      <p class="text-lg text-red-500">Document not found</p>
    </div>
  {/if}
</div>
