// web/src/lib/server/healthMonitor.ts
// Centralized health monitoring service for consumer-facing services

import { logger } from './logger';
import { prisma } from './prisma';
import type { StatusType } from '../../routes/system-status/types';

export interface ServiceHealthData {
  service: string;
  status: StatusType;
  responseTime: number;
  details: {
    statusCode?: number;
    errorRate?: number;
    throughput?: number;
    availability?: number;
    lastError?: string;
    metrics?: Record<string, any>;
  };
  timestamp: string;
}

export interface HealthCheckResult {
  service: string;
  status: StatusType;
  responseTime: number;
  details: Record<string, any>;
}

// Service health check functions
export class HealthMonitor {
  private static instance: HealthMonitor;
  private healthCache = new Map<string, ServiceHealthData>();
  private lastUpdate = new Map<string, number>();
  private readonly CACHE_TTL = 30000; // 30 seconds

  static getInstance(): HealthMonitor {
    if (!HealthMonitor.instance) {
      HealthMonitor.instance = new HealthMonitor();
    }
    return HealthMonitor.instance;
  }

  /**
   * Check health of a specific consumer-facing service
   */
  async checkServiceHealth(serviceName: string, fetch: any): Promise<HealthCheckResult> {
    const startTime = performance.now();
    
    try {
      switch (serviceName.toLowerCase()) {
        case 'matches':
          return await this.checkMatchesService(fetch, startTime);
        case 'jobs':
          return await this.checkJobsService(fetch, startTime);
        case 'tracker':
          return await this.checkTrackerService(fetch, startTime);
        case 'documents':
          return await this.checkDocumentsService(fetch, startTime);
        case 'automation':
          return await this.checkAutomationService(fetch, startTime);
        case 'system':
          return await this.checkSystemService(fetch, startTime);
        case 'website':
          return await this.checkWebsiteService(fetch, startTime);
        default:
          throw new Error(`Unknown service: ${serviceName}`);
      }
    } catch (error) {
      const responseTime = Math.round(performance.now() - startTime);
      logger.error(`Health check failed for ${serviceName}:`, error);
      
      return {
        service: serviceName,
        status: 'outage',
        responseTime,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          statusCode: 503,
        },
      };
    }
  }

  /**
   * Check Matches service (job matching and recommendations)
   */
  private async checkMatchesService(fetch: any, startTime: number): Promise<HealthCheckResult> {
    try {
      // Check job matching API
      const response = await fetch('/api/jobs/matches', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });
      
      const responseTime = Math.round(performance.now() - startTime);
      const isHealthy = response.ok;
      
      return {
        service: 'matches',
        status: isHealthy ? 'operational' : 'degraded',
        responseTime,
        details: {
          statusCode: response.status,
          availability: isHealthy ? 100 : 0,
          endpoint: '/api/jobs/matches',
        },
      };
    } catch (error) {
      const responseTime = Math.round(performance.now() - startTime);
      return {
        service: 'matches',
        status: 'outage',
        responseTime,
        details: {
          error: error instanceof Error ? error.message : 'Service unavailable',
          statusCode: 503,
        },
      };
    }
  }

  /**
   * Check Jobs service (job search and listings)
   */
  private async checkJobsService(fetch: any, startTime: number): Promise<HealthCheckResult> {
    try {
      // Check job search API
      const response = await fetch('/api/jobs?limit=1', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });
      
      const responseTime = Math.round(performance.now() - startTime);
      const isHealthy = response.ok;
      
      return {
        service: 'jobs',
        status: isHealthy ? 'operational' : 'degraded',
        responseTime,
        details: {
          statusCode: response.status,
          availability: isHealthy ? 100 : 0,
          endpoint: '/api/jobs',
        },
      };
    } catch (error) {
      const responseTime = Math.round(performance.now() - startTime);
      return {
        service: 'jobs',
        status: 'outage',
        responseTime,
        details: {
          error: error instanceof Error ? error.message : 'Service unavailable',
          statusCode: 503,
        },
      };
    }
  }

  /**
   * Check Tracker service (application tracking)
   */
  private async checkTrackerService(fetch: any, startTime: number): Promise<HealthCheckResult> {
    try {
      // Check applications status API
      const response = await fetch('/api/applications/status', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });
      
      const responseTime = Math.round(performance.now() - startTime);
      const isHealthy = response.ok;
      
      let details: any = {
        statusCode: response.status,
        availability: isHealthy ? 100 : 0,
        endpoint: '/api/applications/status',
      };
      
      if (isHealthy) {
        const data = await response.json();
        details = {
          ...details,
          dailyApplications: data.dailyApplications || 0,
          successRate: data.successRate || 0,
          averageProcessingTime: data.averageProcessingTime || 0,
        };
      }
      
      return {
        service: 'tracker',
        status: isHealthy ? 'operational' : 'degraded',
        responseTime,
        details,
      };
    } catch (error) {
      const responseTime = Math.round(performance.now() - startTime);
      return {
        service: 'tracker',
        status: 'outage',
        responseTime,
        details: {
          error: error instanceof Error ? error.message : 'Service unavailable',
          statusCode: 503,
        },
      };
    }
  }

  /**
   * Check Documents service (resume and document management)
   */
  private async checkDocumentsService(fetch: any, startTime: number): Promise<HealthCheckResult> {
    try {
      // Check resume templates API
      const response = await fetch('/api/resume/templates', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });
      
      const responseTime = Math.round(performance.now() - startTime);
      const isHealthy = response.ok;
      
      return {
        service: 'documents',
        status: isHealthy ? 'operational' : 'degraded',
        responseTime,
        details: {
          statusCode: response.status,
          availability: isHealthy ? 100 : 0,
          endpoint: '/api/resume/templates',
        },
      };
    } catch (error) {
      const responseTime = Math.round(performance.now() - startTime);
      return {
        service: 'documents',
        status: 'outage',
        responseTime,
        details: {
          error: error instanceof Error ? error.message : 'Service unavailable',
          statusCode: 503,
        },
      };
    }
  }

  /**
   * Check Automation service (automated job application tools)
   */
  private async checkAutomationService(fetch: any, startTime: number): Promise<HealthCheckResult> {
    try {
      // Check worker service health
      const workerUrl = process.env.WORKER_API_URL || 'https://auto-apply-worker.onrender.com';
      const response = await fetch(`${workerUrl}/health`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });
      
      const responseTime = Math.round(performance.now() - startTime);
      const isHealthy = response.ok;
      
      return {
        service: 'automation',
        status: isHealthy ? 'operational' : 'degraded',
        responseTime,
        details: {
          statusCode: response.status,
          availability: isHealthy ? 100 : 0,
          endpoint: `${workerUrl}/health`,
        },
      };
    } catch (error) {
      const responseTime = Math.round(performance.now() - startTime);
      return {
        service: 'automation',
        status: 'outage',
        responseTime,
        details: {
          error: error instanceof Error ? error.message : 'Service unavailable',
          statusCode: 503,
        },
      };
    }
  }

  /**
   * Check System service (core system services)
   */
  private async checkSystemService(fetch: any, startTime: number): Promise<HealthCheckResult> {
    try {
      // Check database connectivity
      await prisma.$queryRaw`SELECT 1 as ping`;
      
      const responseTime = Math.round(performance.now() - startTime);
      
      return {
        service: 'system',
        status: 'operational',
        responseTime,
        details: {
          database: 'connected',
          availability: 100,
        },
      };
    } catch (error) {
      const responseTime = Math.round(performance.now() - startTime);
      return {
        service: 'system',
        status: 'outage',
        responseTime,
        details: {
          error: error instanceof Error ? error.message : 'Database unavailable',
          database: 'disconnected',
          availability: 0,
        },
      };
    }
  }

  /**
   * Check Website service (website and user interface)
   */
  private async checkWebsiteService(fetch: any, startTime: number): Promise<HealthCheckResult> {
    try {
      // Check main pages
      const response = await fetch('/', {
        method: 'GET',
        headers: { 'Content-Type': 'text/html' },
      });
      
      const responseTime = Math.round(performance.now() - startTime);
      const isHealthy = response.ok;
      
      return {
        service: 'website',
        status: isHealthy ? 'operational' : 'degraded',
        responseTime,
        details: {
          statusCode: response.status,
          availability: isHealthy ? 100 : 0,
          endpoint: '/',
        },
      };
    } catch (error) {
      const responseTime = Math.round(performance.now() - startTime);
      return {
        service: 'website',
        status: 'outage',
        responseTime,
        details: {
          error: error instanceof Error ? error.message : 'Website unavailable',
          statusCode: 503,
        },
      };
    }
  }

  /**
   * Update service status in database
   */
  async updateServiceStatus(serviceName: string, healthData: HealthCheckResult): Promise<void> {
    try {
      // Find or create service status record
      const existingService = await prisma.serviceStatus.findUnique({
        where: { name: serviceName },
      });

      if (existingService) {
        // Only update if status has changed
        if (existingService.status !== healthData.status) {
          await prisma.serviceStatus.update({
            where: { id: existingService.id },
            data: {
              status: healthData.status,
              lastCheckedAt: new Date(),
            },
          });

          // Record status change in history
          await prisma.serviceStatusHistory.create({
            data: {
              serviceId: existingService.id,
              status: healthData.status,
            },
          });

          logger.info(`Updated status for ${serviceName} to ${healthData.status}`);
        } else {
          // Just update the lastCheckedAt timestamp
          await prisma.serviceStatus.update({
            where: { id: existingService.id },
            data: {
              lastCheckedAt: new Date(),
            },
          });
        }
      }
    } catch (error) {
      logger.error(`Error updating service status for ${serviceName}:`, error);
    }
  }

  /**
   * Get cached health data or fetch fresh data
   */
  async getServiceHealth(serviceName: string, fetch: any): Promise<ServiceHealthData> {
    const now = Date.now();
    const lastUpdateTime = this.lastUpdate.get(serviceName) || 0;
    const cachedData = this.healthCache.get(serviceName);

    // Return cached data if it's still fresh
    if (cachedData && (now - lastUpdateTime) < this.CACHE_TTL) {
      return cachedData;
    }

    // Fetch fresh health data
    const healthResult = await this.checkServiceHealth(serviceName, fetch);
    
    const healthData: ServiceHealthData = {
      service: serviceName,
      status: healthResult.status,
      responseTime: healthResult.responseTime,
      details: healthResult.details,
      timestamp: new Date().toISOString(),
    };

    // Update cache
    this.healthCache.set(serviceName, healthData);
    this.lastUpdate.set(serviceName, now);

    // Update database
    await this.updateServiceStatus(serviceName, healthResult);

    return healthData;
  }

  /**
   * Get health data for all services
   */
  async getAllServicesHealth(fetch: any): Promise<Record<string, ServiceHealthData>> {
    const services = ['matches', 'jobs', 'tracker', 'documents', 'automation', 'system', 'website'];
    const healthData: Record<string, ServiceHealthData> = {};

    await Promise.all(
      services.map(async (service) => {
        try {
          healthData[service] = await this.getServiceHealth(service, fetch);
        } catch (error) {
          logger.error(`Error getting health for ${service}:`, error);
          healthData[service] = {
            service,
            status: 'unknown',
            responseTime: 0,
            details: { error: 'Health check failed' },
            timestamp: new Date().toISOString(),
          };
        }
      })
    );

    return healthData;
  }
}
