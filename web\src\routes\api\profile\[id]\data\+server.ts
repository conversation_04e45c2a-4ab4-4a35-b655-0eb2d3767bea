import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { parseProfileData } from '$lib/utils/profileHelpers';
import type { ProfileData } from '$lib/types/profile';

export const GET: RequestHandler = async ({ params, locals }) => {
  const user = locals.user;
  if (!user) return new Response('Unauthorized', { status: 401 });

  const profileId = params.id;

  try {
    // Get the profile with related data
    const profile = await prisma.profile.findUnique({
      where: { id: profileId },
      include: {
        data: true,
        documents: {
          include: {
            resume: true,
          },
        },
      },
    });

    if (!profile) {
      return new Response('Profile not found', { status: 404 });
    }

    // Check if the user has access to this profile
    if (profile.userId !== user.id) {
      return new Response('Unauthorized', { status: 403 });
    }

    // Check if any documents are being processed by the worker
    const hasDocumentBeingProcessed =
      (await prisma.workerProcess.findFirst({
        where: {
          status: 'processing',
          data: {
            path: ['profileId'],
            equals: profileId,
          },
        },
      })) !== null;

    // Get profile data from ProfileData first (for backward compatibility)
    let profileData: ProfileData = {};
    if (profile.data) {
      profileData = parseProfileData(profile.data);
    }

    // Check if there's a ParsedResume associated with this profile
    // First, try to find a ParsedResume directly associated with this profile
    let parsedResumes = await prisma.$queryRaw`
      SELECT * FROM "workers"."ParsedResume"
      WHERE "profileId" = ${profileId}
      ORDER BY "parsedAt" DESC
      LIMIT 1
    `;

    // If no ParsedResume is directly associated with the profile, check resumes associated with this profile
    if (!parsedResumes || !Array.isArray(parsedResumes) || parsedResumes.length === 0) {
      const resumeIds = profile.documents.filter((doc) => doc.resume).map((doc) => doc.resume.id);

      if (resumeIds.length > 0) {
        // Find the most recent ParsedResume for any of these resumes
        parsedResumes = await prisma.$queryRaw`
          SELECT * FROM "workers"."ParsedResume"
          WHERE "resumeId" IN (${resumeIds.join(',')})
          ORDER BY "parsedAt" DESC
          LIMIT 1
        `;

        // If we found a ParsedResume through a resume but it's not associated with this profile,
        // update it to associate it with this profile
        if (parsedResumes && Array.isArray(parsedResumes) && parsedResumes.length > 0) {
          const parsedResume = parsedResumes[0];

          if (!parsedResume.profileId) {
            try {
              await prisma.$executeRawUnsafe(`
                UPDATE "workers"."ParsedResume"
                SET "profileId" = '${profileId}'
                WHERE "id" = '${parsedResume.id}'
              `);
              console.log(`Associated ParsedResume ${parsedResume.id} with profile ${profileId}`);
            } catch (error) {
              console.error('Error associating ParsedResume with profile:', error);
            }
          }
        }
      }

      if (parsedResumes && Array.isArray(parsedResumes) && parsedResumes.length > 0) {
        const parsedResume = parsedResumes[0];

        // Merge the parsed resume data with the existing profile data
        // User-edited data takes precedence over parsed data
        const parsedData: Partial<ProfileData> = {
          fullName: parsedResume.name,
          email: parsedResume.email,
          phone: parsedResume.phone,
          location: parsedResume.location,
          summary: parsedResume.summary,
          website: parsedResume.website,
        };

        // Only use parsed data for fields that don't exist in profileData
        if (!profileData.fullName) profileData.fullName = parsedData.fullName;
        if (!profileData.email) profileData.email = parsedData.email;
        if (!profileData.phone) profileData.phone = parsedData.phone;
        if (!profileData.location) profileData.location = parsedData.location;
        if (!profileData.summary) profileData.summary = parsedData.summary;
        if (!profileData.website) profileData.website = parsedData.website;

        // Handle structured data
        if (
          parsedResume.education &&
          (!profileData.education || profileData.education.length === 0)
        ) {
          profileData.education = parsedResume.education;
        }

        if (
          parsedResume.experience &&
          (!profileData.workExperience || profileData.workExperience.length === 0)
        ) {
          profileData.workExperience = parsedResume.experience;
        }

        if (
          parsedResume.skills &&
          (!profileData.skills ||
            (Array.isArray(profileData.skills) && profileData.skills.length === 0))
        ) {
          profileData.skills = parsedResume.skills;
        }

        if (parsedResume.projects && (!profileData.projects || profileData.projects.length === 0)) {
          profileData.projects = parsedResume.projects;
        }

        if (
          parsedResume.certifications &&
          (!profileData.certifications || profileData.certifications.length === 0)
        ) {
          profileData.certifications = parsedResume.certifications;
        }

        if (
          parsedResume.languages &&
          (!profileData.languages || profileData.languages.length === 0)
        ) {
          profileData.languages = parsedResume.languages;
        }
      }
    }

    return json({
      data: profileData,
      isParsing: hasDocumentBeingProcessed,
    });
  } catch (error) {
    console.error('Error fetching profile data:', error);
    return json({ error: 'Failed to fetch profile data', details: String(error) }, { status: 500 });
  }
};

export const PUT: RequestHandler = async ({ params, request, locals }) => {
  const user = locals.user;
  if (!user) return new Response('Unauthorized', { status: 401 });

  const profileId = params.id;

  try {
    // Get the profile with documents and resumes
    const profile = await prisma.profile.findUnique({
      where: { id: profileId },
      include: {
        documents: {
          include: {
            resume: true,
          },
        },
      },
    });

    // Check if profile exists and belongs to the user
    if (!profile) {
      return json({ error: 'Profile not found' }, { status: 404 });
    }

    if (profile.userId !== user.id) {
      return json({ error: 'Unauthorized access to profile' }, { status: 403 });
    }

    // Get the request body
    const data = await request.json();

    // Update the profile data in ProfileData for backward compatibility
    await prisma.profileData.upsert({
      where: {
        profileId: profileId,
      },
      update: {
        data: JSON.stringify(data),
        updatedAt: new Date(),
      },
      create: {
        profileId: profileId,
        data: JSON.stringify(data),
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // Find all resumes associated with this profile
    const resumeIds = profile.documents.filter((doc) => doc.resume).map((doc) => doc.resume.id);

    if (resumeIds.length > 0) {
      // Find the most recent ParsedResume for any of these resumes
      const parsedResumes = await prisma.$queryRaw`
        SELECT * FROM "workers"."ParsedResume"
        WHERE "resumeId" IN (${resumeIds.join(',')})
        ORDER BY "parsedAt" DESC
        LIMIT 1
      `;

      if (parsedResumes && Array.isArray(parsedResumes) && parsedResumes.length > 0) {
        const parsedResume = parsedResumes[0];

        // Update the ParsedResume record with the new data
        // Only update fields that are present in the data
        const updateData: any = {};

        if (data.fullName !== undefined) updateData.name = data.fullName;
        if (data.email !== undefined) updateData.email = data.email;
        if (data.phone !== undefined) updateData.phone = data.phone;
        if (data.location !== undefined) updateData.location = data.location;
        if (data.summary !== undefined) updateData.summary = data.summary;
        if (data.website !== undefined) updateData.website = data.website;

        // Handle structured data
        if (data.education !== undefined) updateData.education = data.education;
        if (data.workExperience !== undefined) updateData.experience = data.workExperience;
        if (data.skills !== undefined) updateData.skills = data.skills;
        if (data.projects !== undefined) updateData.projects = data.projects;
        if (data.certifications !== undefined) updateData.certifications = data.certifications;
        if (data.languages !== undefined) updateData.languages = data.languages;

        // Only update if there are fields to update
        if (Object.keys(updateData).length > 0) {
          try {
            // Update the ParsedResume record
            const updateQuery = `
              UPDATE "workers"."ParsedResume"
              SET ${Object.entries(updateData)
                .map(([key, value]) => `"${key}" = '${JSON.stringify(value).replace(/'/g, "''")}'`)
                .join(', ')}
              WHERE "id" = '${parsedResume.id}'
            `;

            await prisma.$executeRawUnsafe(updateQuery);
            console.log(`Updated ParsedResume record ${parsedResume.id} with profile data`);
          } catch (error) {
            console.error('Error updating ParsedResume:', error);
            // Continue with the success response even if this fails
          }
        }
      }
    }

    return json({
      success: true,
      message: 'Profile data updated successfully',
    });
  } catch (error) {
    console.error('Error updating profile data:', error);
    return json(
      {
        error: 'Failed to update profile data',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
};
