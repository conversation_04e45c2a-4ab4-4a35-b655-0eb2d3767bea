# Base image with Node.js
FROM node:20-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV NODE_ENV=production

# Install curl for health check and wget for Ollama installation
RUN apt-get update && apt-get install -y curl wget \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Ollama
RUN wget https://ollama.com/download/ollama-linux-amd64 -O /usr/local/bin/ollama \
    && chmod +x /usr/local/bin/ollama

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application
COPY . .

# Build the application
RUN npm run build

# Expose API port
EXPOSE 3100

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 CMD curl -f http://localhost:3100/api/health || exit 1

# Create a startup script
RUN echo '#!/bin/sh \n\
# Start Ollama in the background \n\
ollama serve & \n\
# Wait for <PERSON>lla<PERSON> to start \n\
sleep 5 \n\
# Pull the Mistral model \n\
ollama pull mistral \n\
# Start the AI service \n\
node dist/index.js' > /app/start.sh && chmod +x /app/start.sh

# Start the AI service
CMD ["/app/start.sh"]
