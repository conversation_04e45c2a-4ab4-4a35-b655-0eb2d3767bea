/**
 * AI Service
 *
 * This service handles all AI-related functionality including:
 * - Resume AI suggestions
 * - AI Interview Coach
 * - ATS Optimization
 * - AI Job Matching
 */

import { toast } from 'svelte-sonner';

// Types for AI features
export type ResumeSuggestion = {
  id: string;
  section: string;
  originalContent: string;
  suggestion: string;
  reasoning?: string;
  applied: boolean;
  createdAt: string;
};

// Types will be defined below

export type InterviewQuestion = {
  id: string;
  question: string;
  category: string;
  difficulty?: number;
};

export type AICoachingSession = {
  id: string;
  jobTitle: string;
  company?: string;
  applicationId?: string;
  createdAt: string;
  updatedAt: string;
  status: 'in_progress' | 'completed' | 'cancelled';
  questions: {
    question: string;
    type: string;
  }[];
  responses: {
    questionIndex: number;
    response: string;
    timestamp: string;
  }[];
  feedback: {
    questionIndex: number;
    feedback: string;
    timestamp: string;
  }[];
};

export type ATSAnalysisResult = {
  id?: string;
  overallScore: number;
  keywordScore: number;
  formatScore: number;
  contentScore: number;
  readabilityScore: number;
  keywordMatches: string[];
  missingKeywords: string[];
  formatIssues: string[];
  contentSuggestions: string[];
  readabilitySuggestions: string[];
  createdAt?: string;
};

export type JobSpecificATSAnalysis = ATSAnalysisResult & {
  jobTitle: string;
  company: string;
  jobId: string;
  jobSpecific: boolean;
};

export type JobMatchDetails = {
  id?: string;
  overallMatchScore: number;
  skillsMatchScore: number;
  experienceMatchScore: number;
  educationMatchScore: number;
  keywordMatchScore: number;
  matchedSkills: string[];
  missingSkills: string[];
  strengthAreas: string[];
  improvementAreas: string[];
  recommendations: string[];
  jobTitle?: string;
  company?: string;
  createdAt?: string;
};

/**
 * Get AI suggestions for a resume section
 * @param resumeId - The ID of the resume
 * @param section - The section to get suggestions for (e.g., "summary", "experience", "skills")
 * @param content - The current content of the section
 */
export async function getResumeSuggestions(
  resumeId: string,
  section: string,
  content: string
): Promise<ResumeSuggestion[]> {
  try {
    const response = await fetch(`/api/ai/resume/suggestions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        resumeId,
        section,
        content,
      }),
    });

    if (!response.ok) {
      throw new Error(`Error getting resume suggestions: ${response.statusText}`);
    }

    const data = await response.json();
    return data.suggestions;
  } catch (error) {
    console.error('Error getting resume suggestions:', error);
    toast.error('Failed to get AI suggestions. Please try again.');
    return [];
  }
}

/**
 * Apply an AI suggestion to a resume section
 * @param suggestionId - The ID of the suggestion to apply
 */
export async function applyResumeSuggestion(suggestionId: string): Promise<boolean> {
  try {
    const response = await fetch(`/api/ai/resume/suggestions/${suggestionId}/apply`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error applying resume suggestion: ${response.statusText}`);
    }

    const data = await response.json();
    toast.success('AI suggestion applied successfully');
    return data.success;
  } catch (error) {
    console.error('Error applying resume suggestion:', error);
    toast.error('Failed to apply AI suggestion. Please try again.');
    return false;
  }
}

/**
 * Get ATS analysis for a resume
 * @param resumeId - The ID of the resume to analyze
 */
export async function getATSAnalysis(resumeId: string): Promise<ATSAnalysisResult | null> {
  try {
    // First check if there's an existing analysis
    const existingResponse = await fetch(`/api/ai/ats?resumeId=${resumeId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // If we have an existing analysis, return it
    if (existingResponse.ok) {
      const data = await existingResponse.json();
      return data.analysis;
    }

    // Otherwise, generate a new analysis
    const response = await fetch(`/api/ai/ats`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        resumeId,
      }),
    });

    if (!response.ok) {
      throw new Error(`Error getting ATS analysis: ${response.statusText}`);
    }

    const data = await response.json();
    return data.analysis;
  } catch (error) {
    console.error('Error getting ATS analysis:', error);
    toast.error('Failed to get ATS analysis. Please try again.');
    return null;
  }
}

/**
 * Get job-specific ATS analysis for a resume
 * @param resumeId - The ID of the resume to analyze
 * @param jobId - The ID of the job to analyze against
 */
export async function getJobSpecificATSAnalysis(
  resumeId: string,
  jobId: string
): Promise<JobSpecificATSAnalysis | null> {
  try {
    // First check if there's an existing analysis
    const existingResponse = await fetch(`/api/ai/ats?resumeId=${resumeId}&jobId=${jobId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // If we have an existing analysis, return it
    if (existingResponse.ok) {
      const data = await existingResponse.json();
      return data.analysis;
    }

    // Otherwise, generate a new analysis
    const response = await fetch(`/api/ai/ats/job`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        resumeId,
        jobId,
      }),
    });

    if (!response.ok) {
      throw new Error(`Error getting job-specific ATS analysis: ${response.statusText}`);
    }

    const data = await response.json();
    return data.analysis;
  } catch (error) {
    console.error('Error getting job-specific ATS analysis:', error);
    toast.error('Failed to get job-specific ATS analysis. Please try again.');
    return null;
  }
}

/**
 * Create a new AI interview coaching session
 * @param applicationId - The ID of the job application (optional)
 * @param jobTitle - The title of the job
 * @param company - The company name (optional)
 * @param jobDescription - The job description (optional)
 */
export async function createInterviewCoachingSession(
  jobTitle: string,
  applicationId?: string,
  company?: string,
  jobDescription?: string
): Promise<AICoachingSession | null> {
  try {
    const response = await fetch(`/api/ai/interview/sessions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        applicationId,
        jobTitle,
        company,
        jobDescription,
      }),
    });

    if (!response.ok) {
      throw new Error(`Error creating interview coaching session: ${response.statusText}`);
    }

    const data = await response.json();
    return data.session;
  } catch (error) {
    console.error('Error creating interview coaching session:', error);
    toast.error('Failed to create interview coaching session. Please try again.');
    return null;
  }
}

/**
 * Get interview coaching sessions
 */
export async function getInterviewCoachingSessions(): Promise<AICoachingSession[]> {
  try {
    const response = await fetch(`/api/ai/interview/sessions`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error getting interview coaching sessions: ${response.statusText}`);
    }

    const data = await response.json();
    return data.sessions;
  } catch (error) {
    console.error('Error getting interview coaching sessions:', error);
    toast.error('Failed to get interview coaching sessions. Please try again.');
    return [];
  }
}

/**
 * Get an interview coaching session by ID
 * @param sessionId - The ID of the session
 */
export async function getInterviewCoachingSession(
  sessionId: string
): Promise<AICoachingSession | null> {
  try {
    const response = await fetch(`/api/ai/interview/${sessionId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error getting interview coaching session: ${response.statusText}`);
    }

    const data = await response.json();
    return data.session;
  } catch (error) {
    console.error('Error getting interview coaching session:', error);
    toast.error('Failed to get interview coaching session. Please try again.');
    return null;
  }
}

/**
 * Submit a response to an interview question
 * @param sessionId - The ID of the session
 * @param questionIndex - The index of the question
 * @param response - The user's response
 */
export async function submitInterviewResponse(
  sessionId: string,
  questionIndex: number,
  response: string
): Promise<{ feedback: string; session: AICoachingSession } | null> {
  try {
    const apiResponse = await fetch(`/api/ai/interview/${sessionId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        questionIndex,
        response,
      }),
    });

    if (!apiResponse.ok) {
      throw new Error(`Error submitting interview response: ${apiResponse.statusText}`);
    }

    const data = await apiResponse.json();
    return {
      feedback: data.feedback,
      session: data.session,
    };
  } catch (error) {
    console.error('Error submitting interview response:', error);
    toast.error('Failed to submit interview response. Please try again.');
    return null;
  }
}

/**
 * Update the status of an interview coaching session
 * @param sessionId - The ID of the session
 * @param status - The new status
 */
export async function updateInterviewSessionStatus(
  sessionId: string,
  status: 'in_progress' | 'completed' | 'cancelled'
): Promise<AICoachingSession | null> {
  try {
    const response = await fetch(`/api/ai/interview/${sessionId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        status,
      }),
    });

    if (!response.ok) {
      throw new Error(`Error updating interview session status: ${response.statusText}`);
    }

    const data = await response.json();
    return data.session;
  } catch (error) {
    console.error('Error updating interview session status:', error);
    toast.error('Failed to update interview session status. Please try again.');
    return null;
  }
}

/**
 * Get job match details
 * @param profileId - The ID of the profile
 * @param jobId - The ID of the job
 */
export async function getJobMatchDetails(
  profileId: string,
  jobId: string
): Promise<JobMatchDetails | null> {
  try {
    // First check if there's an existing analysis
    const existingResponse = await fetch(
      `/api/ai/job-match?profileId=${profileId}&jobId=${jobId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    // If we have an existing analysis, return it
    if (existingResponse.ok) {
      const data = await existingResponse.json();
      return data.analysis;
    }

    // Otherwise, generate a new analysis
    const response = await fetch(`/api/ai/job-match`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        profileId,
        jobId,
      }),
    });

    if (!response.ok) {
      throw new Error(`Error getting job match details: ${response.statusText}`);
    }

    const data = await response.json();
    return data.analysis;
  } catch (error) {
    console.error('Error getting job match details:', error);
    toast.error('Failed to get job match details. Please try again.');
    return null;
  }
}
