<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Save, Linkedin, Github, Globe, Link } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import type { PortfolioLinksSchema } from '$lib/validators/profile';

  // Props
  const { open, data, onClose, onSave } = $props<{
    open: boolean;
    data: PortfolioLinksSchema;
    onClose: () => void;
    onSave: (data: PortfolioLinksSchema) => Promise<boolean>;
  }>();

  // Initialize form data
  let formData = $state<PortfolioLinksSchema>(
    data || {
      linkedinUrl: '',
      githubUrl: '',
      portfolioUrl: '',
      otherUrl: '',
    }
  );

  // Form state
  let submitting = $state(false);
  let errors = $state<Record<string, string>>({});

  // Reset form when modal opens
  $effect(() => {
    if (open) {
      formData = {
        linkedinUrl: data.linkedinUrl || '',
        githubUrl: data.githubUrl || '',
        portfolioUrl: data.portfolioUrl || '',
        otherUrl: data.otherUrl || '',
      };
      errors = {};
    }
  });

  // Handle form submission
  async function handleSubmit() {
    // Reset errors
    errors = {};

    // Set submitting state
    submitting = true;

    try {
      // Save portfolio links
      const success = await onSave(formData);
      if (success) {
        toast.success('Portfolio links updated successfully');
        onClose();
      }
    } catch (error) {
      console.error('Error saving portfolio links:', error);
      toast.error('Failed to save portfolio links');
    } finally {
      submitting = false;
    }
  }
</script>

<Dialog.Root open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
  <Dialog.Content class="sm:max-w-[500px]">
    <Dialog.Header>
      <Dialog.Title>Edit Portfolio & Links</Dialog.Title>
      <Dialog.Description>
        Update your professional links. Click save when you're done.
      </Dialog.Description>
    </Dialog.Header>

    <div class="grid gap-4 py-4">
      <div class="grid gap-2">
        <Label for="linkedinUrl">LinkedIn URL</Label>
        <div class="relative">
          <Linkedin
            class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
          <Input
            id="linkedinUrl"
            bind:value={formData.linkedinUrl}
            placeholder="https://linkedin.com/in/yourprofile"
            class="pl-10" />
        </div>
        {#if errors.linkedinUrl}
          <p class="text-destructive text-sm">{errors.linkedinUrl}</p>
        {/if}
      </div>

      <div class="grid gap-2">
        <Label for="githubUrl">GitHub URL</Label>
        <div class="relative">
          <Github class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
          <Input
            id="githubUrl"
            bind:value={formData.githubUrl}
            placeholder="https://github.com/yourusername"
            class="pl-10" />
        </div>
        {#if errors.githubUrl}
          <p class="text-destructive text-sm">{errors.githubUrl}</p>
        {/if}
      </div>

      <div class="grid gap-2">
        <Label for="portfolioUrl">Portfolio URL</Label>
        <div class="relative">
          <Globe class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
          <Input
            id="portfolioUrl"
            bind:value={formData.portfolioUrl}
            placeholder="https://yourportfolio.com"
            class="pl-10" />
        </div>
        {#if errors.portfolioUrl}
          <p class="text-destructive text-sm">{errors.portfolioUrl}</p>
        {/if}
      </div>

      <div class="grid gap-2">
        <Label for="otherUrl">Other URL</Label>
        <div class="relative">
          <Link class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
          <Input
            id="otherUrl"
            bind:value={formData.otherUrl}
            placeholder="https://example.com"
            class="pl-10" />
        </div>
        {#if errors.otherUrl}
          <p class="text-destructive text-sm">{errors.otherUrl}</p>
        {/if}
      </div>
    </div>

    <Dialog.Footer>
      <Button variant="outline" onclick={onClose}>Cancel</Button>
      <Button onclick={handleSubmit} disabled={submitting}>
        {#if submitting}
          <span class="mr-2">Saving...</span>
        {:else}
          <Save class="mr-2 h-4 w-4" />
          Save Changes
        {/if}
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
