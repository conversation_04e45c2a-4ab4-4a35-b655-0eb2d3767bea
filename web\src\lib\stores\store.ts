/**
 * Central Store Manager
 *
 * This file provides a centralized store for managing all application state.
 * It consolidates theme, UI state, features, search history, and notification preferences
 * into a single store with a consistent API.
 */

import { writable, derived, get } from '$lib/compat/store';
import { browser } from '$app/environment';

// Theme Types
export type ThemeMode = 'light' | 'dark' | 'system';

// UI Types
export type ViewMode = 'list' | 'grid' | 'compact';

// Notification Types
export type NotificationChannel = 'email' | 'push' | 'in_app';
export type NotificationType = 'job_alerts' | 'application_updates' | 'messages' | 'system';
export type NotificationPreference = {
  type: NotificationType;
  channels: Record<NotificationChannel, boolean>;
  enabled: boolean;
};

// Search Types
export type SearchItem = {
  id: string;
  query: string;
  timestamp: Date;
  favorite: boolean;
  category?: string;
};

// Feature Types
export type FeatureFlag = {
  id: string;
  name: string;
  enabled: boolean;
  description: string;
  requiresSubscription: boolean;
  beta?: boolean;
};

// Account Preferences Types
export type RegionalSettings = {
  language: string;
  timezone: string;
  dateFormat: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD';
  timeFormat: '12h' | '24h';
};

export type AccessibilitySettings = {
  theme: ThemeMode;
  highContrast: boolean;
  reducedMotion: boolean;
  largeText: boolean;
  screenReader: boolean;
  // UI Preferences
  sidebarCollapsed?: boolean;
  viewMode?: ViewMode;
};

export type PrivacySettings = {
  profileVisibility: 'public' | 'private' | 'connections';
  activityVisibility: 'public' | 'private' | 'connections';
  allowDataCollection: boolean;
  allowThirdPartySharing: boolean;
};

export type CookiePreferences = {
  functional: boolean;
  analytics: boolean;
  advertising: boolean;
};

export type AccountPreferences = {
  phone?: string;
  bio?: string;
  language: string;
  timezone: string;
  dateFormat: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD';
  timeFormat: '12h' | '24h';
  accessibility: AccessibilitySettings;
  privacy: PrivacySettings;
  cookiePreferences: CookiePreferences;
};

// Store state interface
interface StoreState {
  // Theme
  theme: ThemeMode;

  // UI State
  ui: {
    sidebarCollapsed: boolean;
    activeModals: Record<string, boolean>;
    lastViewedSection: string | null;
    viewMode: ViewMode;
  };

  // Feature Flags
  features: Record<string, FeatureFlag>;

  // Search History
  searchHistory: SearchItem[];

  // Notification Preferences
  notificationPreferences: Record<NotificationType, NotificationPreference>;

  // Account Preferences
  account: AccountPreferences;
}

// Initial state
const defaultState: StoreState = {
  theme: 'system',

  ui: {
    sidebarCollapsed: false,
    activeModals: {},
    lastViewedSection: null,
    viewMode: 'list',
  },

  features: {},

  searchHistory: [],

  notificationPreferences: {
    job_alerts: {
      type: 'job_alerts',
      channels: { email: true, push: true, in_app: true },
      enabled: true,
    },
    application_updates: {
      type: 'application_updates',
      channels: { email: true, push: true, in_app: true },
      enabled: true,
    },
    messages: {
      type: 'messages',
      channels: { email: true, push: true, in_app: true },
      enabled: true,
    },
    system: {
      type: 'system',
      channels: { email: true, push: false, in_app: true },
      enabled: true,
    },
  },

  // Default account preferences
  account: {
    phone: '',
    bio: '',
    language: 'en',
    timezone: 'UTC',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    accessibility: {
      theme: 'system',
      highContrast: false,
      reducedMotion: false,
      largeText: false,
      screenReader: false,
    },
    privacy: {
      profileVisibility: 'public',
      activityVisibility: 'public',
      allowDataCollection: true,
      allowThirdPartySharing: false,
    },
    cookiePreferences: {
      functional: true,
      analytics: true,
      advertising: false,
    },
  },
};

// Load initial state from localStorage
function loadInitialState(): StoreState {
  if (!browser) return defaultState;

  try {
    // Load theme
    const storedTheme = localStorage.getItem('theme');

    // Load UI state
    const storedUIState = localStorage.getItem('ui_state');

    // Load search history
    const storedSearchHistory = localStorage.getItem('search_history');

    // Load account preferences
    const storedAccountPreferences = localStorage.getItem('account_preferences');

    // Merge with default state
    return {
      ...defaultState,
      theme: (storedTheme as ThemeMode) || defaultState.theme,
      ui: storedUIState ? { ...defaultState.ui, ...JSON.parse(storedUIState) } : defaultState.ui,
      searchHistory: storedSearchHistory
        ? JSON.parse(storedSearchHistory).map((item: any) => ({
            ...item,
            timestamp: new Date(item.timestamp),
          }))
        : defaultState.searchHistory,
      account: storedAccountPreferences
        ? { ...defaultState.account, ...JSON.parse(storedAccountPreferences) }
        : defaultState.account,
    };
  } catch (error) {
    console.error('Failed to load state from localStorage:', error);
    return defaultState;
  }
}

// Create the store
export const store = writable(loadInitialState());

// Save to localStorage when updated
if (browser) {
  store.subscribe((state: StoreState) => {
    try {
      // Save theme
      localStorage.setItem('theme', state.theme);

      // Save UI state
      localStorage.setItem('ui_state', JSON.stringify(state.ui));

      // Save search history
      localStorage.setItem('search_history', JSON.stringify(state.searchHistory));

      // Save account preferences
      localStorage.setItem('account_preferences', JSON.stringify(state.account));
    } catch (error) {
      console.error('Failed to save state to localStorage:', error);
    }
  });
}

// Derived stores
export const effectiveTheme = derived(
  store,
  ($store: StoreState, set: (value: ThemeMode) => void) => {
    if (!browser) return set('light');

    if ($store.theme === 'system') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      set(prefersDark ? 'dark' : 'light');

      // Listen for system preference changes
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handler = (e: MediaQueryListEvent) => set(e.matches ? 'dark' : 'light');
      mediaQuery.addEventListener('change', handler);

      return () => mediaQuery.removeEventListener('change', handler);
    } else {
      set($store.theme);
    }
  },
  'light' // Initial value
);

// Apply theme changes
if (browser) {
  effectiveTheme.subscribe((theme: ThemeMode) => {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
      document.documentElement.style.colorScheme = 'dark';
    } else {
      document.documentElement.classList.remove('dark');
      document.documentElement.style.colorScheme = 'light';
    }
  });
}

// Helper functions
// Theme
export function setTheme(newTheme: ThemeMode): void {
  store.update((state: StoreState) => ({ ...state, theme: newTheme }));
}

export function toggleTheme(): void {
  store.update((state: StoreState) => {
    const currentTheme = state.theme;
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    return { ...state, theme: newTheme };
  });
}

export function initThemeFromServer(serverTheme: ThemeMode | null | undefined): void {
  if (!serverTheme) return;

  // Only update if there's no localStorage theme
  const currentState = get(store);
  if (currentState.theme === defaultState.theme) {
    setTheme(serverTheme);
  }
}

export function setViewMode(mode: ViewMode): void {
  store.update((state: StoreState) => ({
    ...state,
    ui: {
      ...state.ui,
      viewMode: mode,
    },
    account: {
      ...state.account,
      accessibility: {
        ...state.account.accessibility,
        viewMode: mode,
      },
    },
  }));
}

export function toggleModal(modalId: string, isOpen?: boolean): void {
  store.update((state: StoreState) => ({
    ...state,
    ui: {
      ...state.ui,
      activeModals: {
        ...state.ui.activeModals,
        [modalId]: isOpen !== undefined ? isOpen : !state.ui.activeModals[modalId],
      },
    },
  }));
}

// Search History
export function addSearchItem(query: string, category?: string): void {
  if (!query.trim()) return;

  const id = crypto.randomUUID();
  const newItem: SearchItem = {
    id,
    query: query.trim(),
    timestamp: new Date(),
    favorite: false,
    category,
  };

  store.update((state: StoreState) => {
    // Remove existing item with same query if exists
    const filtered = state.searchHistory.filter(
      (item: SearchItem) => item.query.toLowerCase() !== query.toLowerCase()
    );

    // Add new item at the beginning and limit the size
    const MAX_HISTORY_ITEMS = 20;
    return {
      ...state,
      searchHistory: [newItem, ...filtered].slice(0, MAX_HISTORY_ITEMS),
    };
  });
}

// Feature Flags
export function isFeatureEnabled(featureId: string): boolean {
  const state = get(store);
  return state.features[featureId]?.enabled || false;
}

// Notification Preferences
export function toggleNotificationType(type: NotificationType, enabled?: boolean): void {
  store.update((state: StoreState) => ({
    ...state,
    notificationPreferences: {
      ...state.notificationPreferences,
      [type]: {
        ...state.notificationPreferences[type],
        enabled: enabled !== undefined ? enabled : !state.notificationPreferences[type].enabled,
      },
    },
  }));
}

export function toggleNotificationChannel(
  type: NotificationType,
  channel: NotificationChannel,
  enabled?: boolean
): void {
  store.update((state: StoreState) => ({
    ...state,
    notificationPreferences: {
      ...state.notificationPreferences,
      [type]: {
        ...state.notificationPreferences[type],
        channels: {
          ...state.notificationPreferences[type].channels,
          [channel]:
            enabled !== undefined
              ? enabled
              : !state.notificationPreferences[type].channels[channel],
        },
      },
    },
  }));
}

// Account Preferences
export function updateAccountPreferences(preferences: Partial<AccountPreferences>): void {
  // Only update fields that are explicitly defined
  store.update((state: StoreState) => {
    const updatedAccount = { ...state.account };

    // Update top-level fields
    if (preferences.phone !== undefined) updatedAccount.phone = preferences.phone;
    if (preferences.bio !== undefined) updatedAccount.bio = preferences.bio;
    if (preferences.language !== undefined) updatedAccount.language = preferences.language;
    if (preferences.timezone !== undefined) updatedAccount.timezone = preferences.timezone;
    if (preferences.dateFormat !== undefined) updatedAccount.dateFormat = preferences.dateFormat;
    if (preferences.timeFormat !== undefined) updatedAccount.timeFormat = preferences.timeFormat;

    // Update nested objects if provided
    if (preferences.accessibility) {
      updatedAccount.accessibility = {
        ...updatedAccount.accessibility,
        ...preferences.accessibility,
      };
    }

    if (preferences.privacy) {
      updatedAccount.privacy = {
        ...updatedAccount.privacy,
        ...preferences.privacy,
      };
    }

    if (preferences.cookiePreferences) {
      updatedAccount.cookiePreferences = {
        ...updatedAccount.cookiePreferences,
        ...preferences.cookiePreferences,
      };
    }

    return {
      ...state,
      account: updatedAccount,
    };
  });
}

export function updateAccessibilitySettings(settings: Partial<AccessibilitySettings>): void {
  // Only update settings that are explicitly defined
  store.update((state: StoreState) => {
    // Skip update if no settings are provided
    if (Object.keys(settings).length === 0) return state;

    const updatedAccessibility = { ...state.account.accessibility };

    if (settings.theme !== undefined) updatedAccessibility.theme = settings.theme;
    if (settings.highContrast !== undefined)
      updatedAccessibility.highContrast = settings.highContrast;
    if (settings.reducedMotion !== undefined)
      updatedAccessibility.reducedMotion = settings.reducedMotion;
    if (settings.largeText !== undefined) updatedAccessibility.largeText = settings.largeText;
    if (settings.screenReader !== undefined)
      updatedAccessibility.screenReader = settings.screenReader;

    // UI Preferences
    if (settings.sidebarCollapsed !== undefined)
      updatedAccessibility.sidebarCollapsed = settings.sidebarCollapsed;
    if (settings.viewMode !== undefined) updatedAccessibility.viewMode = settings.viewMode;

    // Also update UI state if UI preferences are changed
    const updatedState = { ...state };

    if (settings.sidebarCollapsed !== undefined) {
      updatedState.ui = {
        ...updatedState.ui,
        sidebarCollapsed: settings.sidebarCollapsed,
      };
    }

    if (settings.viewMode !== undefined) {
      updatedState.ui = {
        ...updatedState.ui,
        viewMode: settings.viewMode,
      };
    }

    return {
      ...updatedState,
      account: {
        ...updatedState.account,
        accessibility: updatedAccessibility,
      },
    };
  });
}

export function updatePrivacySettings(settings: Partial<PrivacySettings>): void {
  // Only update settings that are explicitly defined
  store.update((state: StoreState) => {
    // Skip update if no settings are provided
    if (Object.keys(settings).length === 0) return state;

    const updatedPrivacy = { ...state.account.privacy };

    if (settings.profileVisibility !== undefined)
      updatedPrivacy.profileVisibility = settings.profileVisibility;
    if (settings.activityVisibility !== undefined)
      updatedPrivacy.activityVisibility = settings.activityVisibility;
    if (settings.allowDataCollection !== undefined)
      updatedPrivacy.allowDataCollection = settings.allowDataCollection;
    if (settings.allowThirdPartySharing !== undefined)
      updatedPrivacy.allowThirdPartySharing = settings.allowThirdPartySharing;

    return {
      ...state,
      account: {
        ...state.account,
        privacy: updatedPrivacy,
      },
    };
  });
}

export function updateCookiePreferences(preferences: Partial<CookiePreferences>): void {
  // Only update preferences that are explicitly defined
  store.update((state: StoreState) => {
    // Skip update if no preferences are provided
    if (Object.keys(preferences).length === 0) return state;

    const updatedPreferences = { ...state.account.cookiePreferences };

    if (preferences.functional !== undefined)
      updatedPreferences.functional = preferences.functional;
    if (preferences.analytics !== undefined) updatedPreferences.analytics = preferences.analytics;
    if (preferences.advertising !== undefined)
      updatedPreferences.advertising = preferences.advertising;

    return {
      ...state,
      account: {
        ...state.account,
        cookiePreferences: updatedPreferences,
      },
    };
  });
}

export function initAccountFromServer(serverAccount: any): void {
  if (!serverAccount) return;

  store.update((state: StoreState) => {
    // Create a new account object with defaults from the store
    const updatedAccount = { ...state.account };

    // Only update fields that are explicitly defined in the server data
    if (serverAccount.phone !== undefined) updatedAccount.phone = serverAccount.phone;
    if (serverAccount.bio !== undefined) updatedAccount.bio = serverAccount.bio;
    if (serverAccount.language !== undefined) updatedAccount.language = serverAccount.language;
    if (serverAccount.timezone !== undefined) updatedAccount.timezone = serverAccount.timezone;
    if (serverAccount.dateFormat !== undefined)
      updatedAccount.dateFormat = serverAccount.dateFormat;
    if (serverAccount.timeFormat !== undefined)
      updatedAccount.timeFormat = serverAccount.timeFormat;

    // Handle nested accessibility settings
    if (serverAccount.accessibility) {
      updatedAccount.accessibility = { ...updatedAccount.accessibility };

      const accessibility = serverAccount.accessibility;
      if (accessibility.theme !== undefined)
        updatedAccount.accessibility.theme = accessibility.theme;
      if (accessibility.highContrast !== undefined)
        updatedAccount.accessibility.highContrast = accessibility.highContrast;
      if (accessibility.reducedMotion !== undefined)
        updatedAccount.accessibility.reducedMotion = accessibility.reducedMotion;
      if (accessibility.largeText !== undefined)
        updatedAccount.accessibility.largeText = accessibility.largeText;
      if (accessibility.screenReader !== undefined)
        updatedAccount.accessibility.screenReader = accessibility.screenReader;

      // UI Preferences
      if (accessibility.sidebarCollapsed !== undefined)
        updatedAccount.accessibility.sidebarCollapsed = accessibility.sidebarCollapsed;
      if (accessibility.viewMode !== undefined)
        updatedAccount.accessibility.viewMode = accessibility.viewMode;
    }

    // Handle nested privacy settings
    if (serverAccount.privacy) {
      updatedAccount.privacy = { ...updatedAccount.privacy };

      const privacy = serverAccount.privacy;
      if (privacy.profileVisibility !== undefined)
        updatedAccount.privacy.profileVisibility = privacy.profileVisibility;
      if (privacy.activityVisibility !== undefined)
        updatedAccount.privacy.activityVisibility = privacy.activityVisibility;
      if (privacy.allowDataCollection !== undefined)
        updatedAccount.privacy.allowDataCollection = privacy.allowDataCollection;
      if (privacy.allowThirdPartySharing !== undefined)
        updatedAccount.privacy.allowThirdPartySharing = privacy.allowThirdPartySharing;
    }

    // Handle nested cookie preferences
    if (serverAccount.cookiePreferences) {
      updatedAccount.cookiePreferences = { ...updatedAccount.cookiePreferences };

      const cookiePrefs = serverAccount.cookiePreferences;
      if (cookiePrefs.functional !== undefined)
        updatedAccount.cookiePreferences.functional = cookiePrefs.functional;
      if (cookiePrefs.analytics !== undefined)
        updatedAccount.cookiePreferences.analytics = cookiePrefs.analytics;
      if (cookiePrefs.advertising !== undefined)
        updatedAccount.cookiePreferences.advertising = cookiePrefs.advertising;
    }

    // Also update UI state from accessibility settings
    const updatedState = { ...state };

    if (serverAccount.accessibility) {
      const accessibility = serverAccount.accessibility;

      // Get the collapsed state
      const isSidebarCollapsed =
        accessibility.sidebarCollapsed !== undefined
          ? accessibility.sidebarCollapsed
          : updatedState.ui.sidebarCollapsed;

      // Update UI state with the correct values
      updatedState.ui = {
        ...updatedState.ui,
        sidebarCollapsed: isSidebarCollapsed,
        viewMode:
          accessibility.viewMode !== undefined ? accessibility.viewMode : updatedState.ui.viewMode,
      };

      // Log the initialization for debugging
      console.log(`Sidebar initialized: collapsed=${isSidebarCollapsed}`);
    }

    return {
      ...updatedState,
      account: updatedAccount,
    };
  });
}
