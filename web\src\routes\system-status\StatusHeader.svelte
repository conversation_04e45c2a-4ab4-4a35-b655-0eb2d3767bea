<script lang="ts">
  // Props
  export let lastUpdated: Date;

  // Format date
  function formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  }
</script>

<div class="border-border flex items-center justify-between border-b p-6">
  <div class="flex flex-col">
    <h1 class="text-3xl font-bold">System Status</h1>
    <p class="text-muted-foreground">Current status of Hirli services</p>
  </div>
  <div class="flex items-center gap-2 self-end">
    <p class="text-muted-foreground text-sm">Last updated: {formatDate(lastUpdated)}</p>
  </div>
</div>
