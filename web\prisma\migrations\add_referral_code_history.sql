-- Migration to add referral code history tracking
-- This preserves old referral codes for analytics when users generate new ones

-- Create ReferralCodeHistory table
CREATE TABLE "web"."ReferralCodeHistory" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "referralCode" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deactivatedAt" TIMESTAMP(3),
    "reason" TEXT, -- 'user_generated', 'custom_set', 'admin_change', etc.
    "metadata" JSONB DEFAULT '{}',

    CONSTRAINT "ReferralCodeHistory_pkey" PRIMARY KEY ("id")
);

-- Add foreign key constraint
ALTER TABLE "web"."ReferralCodeHistory" ADD CONSTRAINT "ReferralCodeHistory_userId_fkey" 
    FOREIGN KEY ("userId") REFERENCES "web"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add indexes for performance
CREATE INDEX "ReferralCodeHistory_userId_idx" ON "web"."ReferralCodeHistory"("userId");
CREATE INDEX "ReferralCodeHistory_referralCode_idx" ON "web"."ReferralCodeHistory"("referralCode");
CREATE INDEX "ReferralCodeHistory_isActive_idx" ON "web"."ReferralCodeHistory"("isActive");
CREATE UNIQUE INDEX "ReferralCodeHistory_referralCode_unique" ON "web"."ReferralCodeHistory"("referralCode");

-- Migrate existing referral codes to history table
INSERT INTO "web"."ReferralCodeHistory" ("id", "userId", "referralCode", "isActive", "createdAt", "reason")
SELECT 
    gen_random_uuid(),
    "id",
    "referralCode",
    true,
    "created_at",
    'initial_migration'
FROM "web"."User" 
WHERE "referralCode" IS NOT NULL;

-- Add comment for documentation
COMMENT ON TABLE "web"."ReferralCodeHistory" IS 'Tracks all referral codes a user has had over time for analytics and historical tracking';
COMMENT ON COLUMN "web"."ReferralCodeHistory"."isActive" IS 'Whether this is the currently active referral code for the user';
COMMENT ON COLUMN "web"."ReferralCodeHistory"."reason" IS 'Why this code was created: user_generated, custom_set, admin_change, etc.';
