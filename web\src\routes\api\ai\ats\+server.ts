import { json } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';
import { env } from '$env/dynamic/private';

// Worker API URL
const WORKER_API_URL = env.WORKER_API_URL ?? 'http://localhost:3002';

export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { resumeId, jobId } = await request.json();

    if (!resumeId) {
      return json({ error: 'Resume ID is required' }, { status: 400 });
    }

    // Get the resume
    const resume = await prisma.document.findUnique({
      where: {
        id: resumeId,
        userId: locals.user.id,
      },
      include: {
        resume: true,
      },
    });

    if (!resume) {
      return json({ error: 'Resume not found' }, { status: 404 });
    }

    // Get job details if jobId is provided
    let jobDetails = null;
    if (jobId) {
      const job = await prisma.jobListing.findUnique({
        where: {
          id: jobId,
        },
        select: {
          title: true,
          company: true,
          description: true,
          requirements: true,
        },
      });

      if (job) {
        jobDetails = job;
      }
    }

    // Generate ATS analysis
    const analysis = await generateATSAnalysis(resume, jobDetails);

    // Save the analysis to the database
    const savedAnalysis = await prisma.atsAnalysis.create({
      data: {
        userId: locals.user.id,
        resumeId,
        jobId,
        overallScore: analysis.overallScore,
        keywordScore: analysis.keywordScore,
        formatScore: analysis.formatScore,
        contentScore: analysis.contentScore,
        readabilityScore: analysis.readabilityScore,
        keywordMatches: analysis.keywordMatches,
        missingKeywords: analysis.missingKeywords,
        formatIssues: analysis.formatIssues,
        contentSuggestions: analysis.contentSuggestions,
        readabilitySuggestions: analysis.readabilitySuggestions,
        jobSpecific: !!jobDetails,
      },
    });

    // Update feature usage
    await prisma.featureUsage.upsert({
      where: {
        userId_featureId_limitId: {
          userId: locals.user.id,
          featureId: 'ats_optimization',
          limitId: 'ats_scans_monthly',
        },
      },
      update: {
        usage: {
          increment: 1,
        },
      },
      create: {
        userId: locals.user.id,
        featureId: 'ats_optimization',
        limitId: 'ats_scans_monthly',
        usage: 1,
      },
    });

    return json({ analysis: savedAnalysis });
  } catch (error) {
    console.error('Error generating ATS analysis:', error);
    return json({ error: 'Failed to generate ATS analysis' }, { status: 500 });
  }
};

export const GET: RequestHandler = async ({ url, locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const resumeId = url.searchParams.get('resumeId');
    const jobId = url.searchParams.get('jobId');

    if (!resumeId) {
      return json({ error: 'Resume ID is required' }, { status: 400 });
    }

    // Get the latest ATS analysis for the resume
    const query: any = {
      where: {
        userId: locals.user.id,
        resumeId,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 1,
    };

    // Add jobId to query if provided
    if (jobId) {
      query.where.jobId = jobId;
    }

    const analysis = await prisma.atsAnalysis.findMany(query);

    if (analysis.length === 0) {
      return json({ error: 'No analysis found' }, { status: 404 });
    }

    return json({ analysis: analysis[0] });
  } catch (error) {
    console.error('Error fetching ATS analysis:', error);
    return json({ error: 'Failed to fetch ATS analysis' }, { status: 500 });
  }
};

// Helper function to generate ATS analysis
async function generateATSAnalysis(resume: any, jobDetails: any = null): Promise<any> {
  try {
    // Extract resume content
    const resumeContent = resume.resume ? JSON.stringify(resume.resume) : '';
    const resumeText = resume.content || '';

    // Prepare the request body
    const requestBody: any = {
      resumeText: resumeText,
    };

    // Add job details if available
    if (jobDetails) {
      requestBody.jobDescription = `
Job Title: ${jobDetails.title}
Company: ${jobDetails.company}
Description: ${jobDetails.description || ''}
Requirements: ${jobDetails.requirements || ''}
`;
    }

    // Call the worker API for AI analysis
    const response = await fetch(`${WORKER_API_URL}/api/ai/resume-analysis`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`Worker API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    // Return the analysis
    return {
      overallScore: data.analysis.overallScore ?? 0,
      keywordScore: data.analysis.keywordScore ?? 0,
      formatScore: data.analysis.formatScore ?? 0,
      contentScore: data.analysis.contentScore ?? 0,
      readabilityScore: data.analysis.readabilityScore ?? 0,
      keywordMatches: data.analysis.keywordMatches ?? [],
      missingKeywords: data.analysis.missingKeywords ?? [],
      formatIssues: data.analysis.formatIssues ?? [],
      contentSuggestions: data.analysis.contentSuggestions ?? [],
      readabilitySuggestions: data.analysis.readabilitySuggestions ?? [],
    };
  } catch (error) {
    console.error('Error in ATS analysis generation:', error);

    // Return default values if analysis fails
    return {
      overallScore: 70,
      keywordScore: 65,
      formatScore: 75,
      contentScore: 70,
      readabilityScore: 80,
      keywordMatches: ['resume', 'experience', 'skills'],
      missingKeywords: ['specific technical skills', 'certifications'],
      formatIssues: ['Consider using a more ATS-friendly format'],
      contentSuggestions: ['Add more quantifiable achievements'],
      readabilitySuggestions: ['Use more bullet points for better readability'],
    };
  }
}
