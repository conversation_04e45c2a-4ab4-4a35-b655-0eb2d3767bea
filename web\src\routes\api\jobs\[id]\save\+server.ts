import { json } from '@sveltejs/kit';
import { PrismaClient } from '@prisma/client';
import type { RequestHandler } from './$types';

const prisma = new PrismaClient();

export const POST: RequestHandler = async ({ params, request, locals }) => {
  // Get the user from the session
  const user = locals.user;

  if (!user) {
    return json({ error: 'Authentication required' }, { status: 401 });
  }

  try {
    const jobId = params.id;
    const { notes } = await request.json();

    // Get the job details
    const job = await prisma.job_listing.findUnique({
      where: { id: jobId },
    });

    if (!job) {
      return json({ error: 'Job not found' }, { status: 404 });
    }

    // Check if the job is already applied to
    const appliedJob = await prisma.application.findFirst({
      where: {
        url: job.url,
        userId: user.id,
      },
    });

    if (appliedJob) {
      return json(
        {
          success: false,
          message: 'Cannot save job that is already applied to',
          isApplied: true,
        },
        { status: 400 }
      );
    }

    // Check if the job is already saved
    const existingSavedJob = await prisma.savedJob.findUnique({
      where: {
        userId_jobId: {
          userId: user.id,
          jobId: jobId,
        },
      },
    });

    if (existingSavedJob) {
      // Update the existing saved job
      const updatedSavedJob = await prisma.savedJob.update({
        where: {
          id: existingSavedJob.id,
        },
        data: {
          notes: notes || existingSavedJob.notes,
          updatedAt: new Date(),
        },
      });

      return json({
        success: true,
        message: 'Job updated successfully',
        savedJob: updatedSavedJob,
      });
    } else {
      // Create a new saved job
      const savedJob = await prisma.savedJob.create({
        data: {
          userId: user.id,
          jobId: jobId,
          notes: notes || null,
          updatedAt: new Date(),
        },
      });

      return json({
        success: true,
        message: 'Job saved successfully',
        savedJob,
      });
    }
  } catch (error) {
    console.error('Error saving job:', error);
    return json({ error: 'Failed to save job' }, { status: 500 });
  }
};

export const DELETE: RequestHandler = async ({ params, locals }) => {
  // Get the user from the session
  const user = locals.user;

  if (!user) {
    return json({ error: 'Authentication required' }, { status: 401 });
  }

  try {
    const jobId = params.id;

    // Get the job details
    const job = await prisma.job_listing.findUnique({
      where: { id: jobId },
    });

    if (!job) {
      return json({ error: 'Job not found' }, { status: 404 });
    }

    // Check if the job is already applied to
    const appliedJob = await prisma.application.findFirst({
      where: {
        url: job.url,
        userId: user.id,
      },
    });

    if (appliedJob) {
      return json(
        {
          success: false,
          message: 'Cannot unsave job that is already applied to',
          isApplied: true,
        },
        { status: 400 }
      );
    }

    // Check if the job is saved
    const existingSavedJob = await prisma.savedJob.findUnique({
      where: {
        userId_jobId: {
          userId: user.id,
          jobId: jobId,
        },
      },
    });

    if (!existingSavedJob) {
      return json({ error: 'Job not found' }, { status: 404 });
    }

    // Delete the saved job
    await prisma.savedJob.delete({
      where: {
        id: existingSavedJob.id,
      },
    });

    return json({
      success: true,
      message: 'Job removed successfully',
    });
  } catch (error) {
    console.error('Error removing saved job:', error);
    return json({ error: 'Failed to remove saved job' }, { status: 500 });
  }
};
