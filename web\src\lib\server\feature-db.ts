/**
 * Feature Database Service
 * 
 * This service provides functions to interact with features in the database.
 * It replaces the static feature definitions with database queries.
 */

import { prisma } from './prisma';
import type { Feature, FeatureLimit } from '$lib/models/features/types';
import { FeatureAccessLevel } from '$lib/models/features/features';

/**
 * Get all features from the database
 * @returns A promise that resolves to an array of features
 */
export async function getAllFeatures(): Promise<Feature[]> {
  try {
    const features = await prisma.feature.findMany({
      include: {
        limits: true,
      },
      orderBy: {
        category: 'asc',
      },
    });
    
    return features.map(dbFeatureToFeature);
  } catch (error) {
    console.error('Error getting all features:', error);
    return [];
  }
}

/**
 * Get a feature by its ID
 * @param id The ID of the feature to get
 * @returns A promise that resolves to the feature or null if not found
 */
export async function getFeatureById(id: string): Promise<Feature | null> {
  try {
    const feature = await prisma.feature.findUnique({
      where: { id },
      include: {
        limits: true,
      },
    });
    
    if (!feature) {
      return null;
    }
    
    return dbFeatureToFeature(feature);
  } catch (error) {
    console.error(`Error getting feature by ID ${id}:`, error);
    return null;
  }
}

/**
 * Get features by category
 * @param category The category to filter by
 * @returns A promise that resolves to an array of features
 */
export async function getFeaturesByCategory(category: string): Promise<Feature[]> {
  try {
    const features = await prisma.feature.findMany({
      where: { category },
      include: {
        limits: true,
      },
      orderBy: {
        name: 'asc',
      },
    });
    
    return features.map(dbFeatureToFeature);
  } catch (error) {
    console.error(`Error getting features by category ${category}:`, error);
    return [];
  }
}

/**
 * Get a feature limit by its ID
 * @param id The ID of the limit to get
 * @returns A promise that resolves to the limit or null if not found
 */
export async function getFeatureLimitById(id: string): Promise<FeatureLimit | null> {
  try {
    const limit = await prisma.featureLimit.findUnique({
      where: { id },
    });
    
    if (!limit) {
      return null;
    }
    
    return dbLimitToLimit(limit);
  } catch (error) {
    console.error(`Error getting feature limit by ID ${id}:`, error);
    return null;
  }
}

/**
 * Get all limits for a feature
 * @param featureId The ID of the feature
 * @returns A promise that resolves to an array of limits
 */
export async function getLimitsForFeature(featureId: string): Promise<FeatureLimit[]> {
  try {
    const limits = await prisma.featureLimit.findMany({
      where: { featureId },
    });
    
    return limits.map(dbLimitToLimit);
  } catch (error) {
    console.error(`Error getting limits for feature ${featureId}:`, error);
    return [];
  }
}

/**
 * Convert a database feature to a Feature object
 * @param dbFeature The database feature
 * @returns A Feature object
 */
function dbFeatureToFeature(dbFeature: any): Feature {
  return {
    id: dbFeature.id,
    name: dbFeature.name,
    description: dbFeature.description,
    category: dbFeature.category,
    icon: dbFeature.icon || undefined,
    beta: dbFeature.beta || false,
    limits: dbFeature.limits ? dbFeature.limits.map(dbLimitToLimit) : [],
  };
}

/**
 * Convert a database limit to a FeatureLimit object
 * @param dbLimit The database limit
 * @returns A FeatureLimit object
 */
function dbLimitToLimit(dbLimit: any): FeatureLimit {
  return {
    id: dbLimit.id,
    name: dbLimit.name,
    description: dbLimit.description,
    defaultValue: dbLimit.defaultValue === 'unlimited' ? 'unlimited' : parseInt(dbLimit.defaultValue, 10),
    type: dbLimit.type,
    unit: dbLimit.unit || undefined,
    resetDay: dbLimit.resetDay || undefined,
  };
}
