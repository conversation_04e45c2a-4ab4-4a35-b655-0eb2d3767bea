<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Button from '$lib/components/ui/button/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import * as Table from '$lib/components/ui/table/index.js';
  import * as Popover from '$lib/components/ui/popover/index.js';
  import { RangeCalendar } from '$lib/components/ui/range-calendar';
  import { getLocalTimeZone } from '@internationalized/date';
  import type { DateRange } from 'bits-ui';
  import {
    Mail,
    MousePointerClick,
    AlertTriangle,
    UserX,
    ChevronLeft,
    ChevronRight,
    RefreshCw,
  } from 'lucide-svelte';

  // Props
  export let dateRange: { startDate: Date | undefined; endDate: Date | undefined };
  export let calendarDateRange: DateRange;
  export let templateFilter: string;
  export let eventType: string;
  export let templates: Array<{
    name: string;
    label: string;
    category?: string;
    description?: string;
  }> = [];
  export let eventTypeOptions: Array<{ value: string; label: string }> = [];
  export let emailEvents: Array<{
    id: string;
    email: string;
    templateName: string | null;
    type: string;
    timestamp: string;
    data?: any;
  }> = [];
  export let isLoading = false;
  export let currentPage = 1;
  export let totalPages = 1;
  export let totalEvents = 0;
  export let itemsPerPage = 10;

  // Functions
  export let handleCalendarDateChange: (event: CustomEvent) => void;
  export let loadEmailEvents: () => Promise<void>;
  export let goToPage: (page: number) => void;
  export let getEventTypeBadgeClass: (type: string) => string;
  export let formatDate: (dateString: string) => string;
</script>

<!-- Events Filters -->
<div class="mb-6 flex flex-wrap gap-4">
  <div>
    <label for="eventType" class="mb-1 block text-sm font-medium">Event Type</label>
    <Select.Root
      type="single"
      value={eventType}
      onValueChange={(value) => {
        eventType = value;
        loadEmailEvents();
      }}>
      <Select.Trigger class="w-[180px]">
        <Select.Value placeholder="Select event type" />
      </Select.Trigger>
      <Select.Content class="w-[180px]">
        <Select.Group>
          {#each eventTypeOptions as option}
            <Select.Item value={option.value}>
              <div class="flex items-center">
                {#if option.value === 'delivered'}
                  <Mail class="mr-2 h-4 w-4 text-green-500" />
                {:else if option.value === 'opened'}
                  <Mail class="mr-2 h-4 w-4 text-blue-500" />
                {:else if option.value === 'clicked'}
                  <MousePointerClick class="mr-2 h-4 w-4 text-purple-500" />
                {:else if option.value === 'bounced'}
                  <AlertTriangle class="mr-2 h-4 w-4 text-red-500" />
                {:else if option.value === 'complained'}
                  <AlertTriangle class="mr-2 h-4 w-4 text-orange-500" />
                {:else if option.value === 'unsubscribed'}
                  <UserX class="mr-2 h-4 w-4 text-gray-500" />
                {/if}
                {option.label}
              </div>
            </Select.Item>
          {/each}
        </Select.Group>
      </Select.Content>
    </Select.Root>
  </div>

  <div>
    <label for="templateFilter" class="mb-1 block text-sm font-medium">Template</label>
    <Select.Root
      type="single"
      value={templateFilter}
      onValueChange={(value) => {
        templateFilter = value;
        loadEmailEvents();
      }}>
      <Select.Trigger class="w-[250px]">
        <Select.Value placeholder="Select template" />
      </Select.Trigger>
      <Select.Content class="w-[250px]">
        <Select.Group>
          <Select.Item value="all">All Templates</Select.Item>
        </Select.Group>

        {#if templates.length > 1}
          {#each [...new Set(templates
                .filter((t) => t.name !== 'all')
                .map((t) => t.category || 'Other'))] as category}
            <Select.Separator />
            <div class="px-2 py-1.5 text-sm font-semibold">
              {category}
            </div>
            <Select.Group>
              {#each templates.filter((t) => t.name !== 'all' && (t.category || 'Other') === category) as template}
                <Select.Item value={template.name} title={template.description || ''}>
                  {template.label}
                </Select.Item>
              {/each}
            </Select.Group>
          {/each}
        {/if}
      </Select.Content>
    </Select.Root>
  </div>

  <div>
    <label for="timeRange2" class="mb-1 block text-sm font-medium">Time Range</label>
    <div>
      <Popover.Root>
        <Popover.Trigger>
          <Button.Root variant="outline" class="w-[250px] justify-start">
            <span class="mr-2">📅</span>
            {#if dateRange.startDate && dateRange.endDate}
              {new Date(dateRange.startDate).toLocaleDateString()} - {new Date(
                dateRange.endDate
              ).toLocaleDateString()}
            {:else}
              Select date range
            {/if}
          </Button.Root>
        </Popover.Trigger>
        <Popover.Content class="w-auto p-0" align="start">
          <RangeCalendar
            value={calendarDateRange}
            onValueChange={(newValue) => {
              if (newValue?.start && newValue?.end) {
                // Convert to JavaScript Date objects
                const startDate = newValue.start.toDate(getLocalTimeZone());
                const endDate = newValue.end.toDate(getLocalTimeZone());

                // Create a proper CustomEvent
                const customEvent = new CustomEvent('change', {
                  detail: { startDate, endDate, calendarValue: newValue },
                });

                // Call the handler with the proper event
                handleCalendarDateChange(customEvent);
              }
            }}
            numberOfMonths={2} />
        </Popover.Content>
      </Popover.Root>
    </div>
  </div>

  <div class="ml-auto flex gap-2">
    <div>
      <div class="mb-1 block text-sm font-medium opacity-0">Refresh</div>
      <Button.Root
        variant="outline"
        size="icon"
        onclick={() => loadEmailEvents()}
        disabled={isLoading}
        class="h-10 w-10">
        <RefreshCw class={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
        <span class="sr-only">Refresh</span>
      </Button.Root>
    </div>
  </div>
</div>

<Card.Root>
  <Card.Header class="flex flex-row items-center justify-between">
    <div>
      <Card.Title>Email Event Log</Card.Title>
      <Card.Description>Detailed log of email events</Card.Description>
    </div>
    <Button.Root
      variant="outline"
      size="sm"
      onclick={() => loadEmailEvents()}
      disabled={isLoading}
      class="h-8 w-8 p-0">
      <RefreshCw class={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
      <span class="sr-only">Refresh</span>
    </Button.Root>
  </Card.Header>

  <Card.Content>
    {#if isLoading}
      <div class="flex h-64 items-center justify-center">
        <div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent">
        </div>
      </div>
    {:else if emailEvents.length === 0}
      <div class="flex h-40 flex-col items-center justify-center gap-4">
        <p class="text-muted-foreground text-center">Click the button below to load email events</p>
        <Button.Root onclick={() => loadEmailEvents()}>Load Email Events</Button.Root>
      </div>
    {:else}
      <!-- Event table and pagination would go here but truncated for brevity -->
      <div class="space-y-4">
        <Table.Root>
          <Table.Header>
            <Table.Row>
              <Table.Head>Event</Table.Head>
              <Table.Head>Email</Table.Head>
              <Table.Head>Template</Table.Head>
              <Table.Head>Timestamp</Table.Head>
              <Table.Head>Details</Table.Head>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {#each emailEvents as event}
              <Table.Row>
                <Table.Cell>
                  <div class="flex items-center">
                    <span
                      class="rounded-full px-2 py-1 text-xs {getEventTypeBadgeClass(
                        event.type
                      )} mr-2">
                      {#if event.type === 'delivered'}
                        <Mail class="mr-1 inline-block h-3 w-3" />
                      {:else if event.type === 'opened'}
                        <Mail class="mr-1 inline-block h-3 w-3" />
                      {:else if event.type === 'clicked'}
                        <MousePointerClick class="mr-1 inline-block h-3 w-3" />
                      {:else if event.type === 'bounced' || event.type === 'complained'}
                        <AlertTriangle class="mr-1 inline-block h-3 w-3" />
                      {:else if event.type === 'unsubscribed'}
                        <UserX class="mr-1 inline-block h-3 w-3" />
                      {:else}
                        <Mail class="mr-1 inline-block h-3 w-3" />
                      {/if}
                      {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                    </span>
                  </div>
                </Table.Cell>
                <Table.Cell>{event.email}</Table.Cell>
                <Table.Cell>{event.templateName || '-'}</Table.Cell>
                <Table.Cell>{formatDate(event.timestamp)}</Table.Cell>
                <Table.Cell>
                  {#if event.data}
                    <Button.Root variant="ghost" size="sm" class="h-8 px-2">
                      View Details
                    </Button.Root>
                  {:else}
                    -
                  {/if}
                </Table.Cell>
              </Table.Row>
            {/each}
          </Table.Body>
        </Table.Root>

        <!-- Pagination -->
        {#if totalPages > 1}
          <div class="flex items-center justify-between">
            <div class="text-muted-foreground text-sm">
              Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(
                currentPage * itemsPerPage,
                totalEvents
              )} of {totalEvents} events
            </div>
            <div class="flex items-center space-x-2">
              <Button.Root
                variant="outline"
                size="sm"
                disabled={currentPage === 1}
                onclick={() => goToPage(currentPage - 1)}>
                <ChevronLeft class="h-4 w-4" />
                <span class="sr-only">Previous Page</span>
              </Button.Root>

              {#if totalPages <= 5}
                {#each Array(totalPages) as _, i}
                  <Button.Root
                    variant={currentPage === i + 1 ? 'default' : 'outline'}
                    size="sm"
                    onclick={() => goToPage(i + 1)}>
                    {i + 1}
                  </Button.Root>
                {/each}
              {:else}
                <!-- First page -->
                <Button.Root
                  variant={currentPage === 1 ? 'default' : 'outline'}
                  size="sm"
                  onclick={() => goToPage(1)}>
                  1
                </Button.Root>

                <!-- Ellipsis if needed -->
                {#if currentPage > 3}
                  <span class="text-muted-foreground">...</span>
                {/if}

                <!-- Pages around current -->
                {#each Array(3) as _, i}
                  {#if currentPage - 1 + i > 1 && currentPage - 1 + i < totalPages}
                    <Button.Root
                      variant={currentPage === currentPage - 1 + i ? 'default' : 'outline'}
                      size="sm"
                      onclick={() => goToPage(currentPage - 1 + i)}>
                      {currentPage - 1 + i}
                    </Button.Root>
                  {/if}
                {/each}

                <!-- Ellipsis if needed -->
                {#if currentPage < totalPages - 2}
                  <span class="text-muted-foreground">...</span>
                {/if}

                <!-- Last page -->
                <Button.Root
                  variant={currentPage === totalPages ? 'default' : 'outline'}
                  size="sm"
                  onclick={() => goToPage(totalPages)}>
                  {totalPages}
                </Button.Root>
              {/if}

              <Button.Root
                variant="outline"
                size="sm"
                disabled={currentPage === totalPages}
                onclick={() => goToPage(currentPage + 1)}>
                <ChevronRight class="h-4 w-4" />
                <span class="sr-only">Next Page</span>
              </Button.Root>
            </div>
          </div>
        {/if}
      </div>
    {/if}
  </Card.Content>
</Card.Root>
