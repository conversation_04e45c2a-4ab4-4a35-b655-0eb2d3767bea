import { fetchJobCollections } from "../lib/linkedin/fetchCategoryList";
import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";

const prisma = new PrismaClient();

export async function fetchAndStoreLinkedInCategories() {
  logger.info("🚀 Starting LinkedIn job collections fetch...");

  const collections = await fetchJobCollections();

  for (const collection of collections) {
    await prisma.jobCollections.upsert({
      where: { slug: collection.slug },
      update: { name: collection.name },
      create: {
        name: collection.name,
        slug: collection.slug,
        platform: "linkedin",
      },
    });
  }

  // 🔁 Clean up outdated collections
  const validSlugs = collections.map((c) => c.slug);
  await prisma.jobCollections.deleteMany({
    where: {
      slug: {
        notIn: validSlugs,
      },
    },
  });

  logger.info(`✅ Stored ${collections.length} job collections in DB`);
}

await fetchAndStoreLinkedInCategories();
