// @ts-nocheck
// src/routes/dashboard/settings/admin/maintenance/+page.server.ts
import { prisma } from '$lib/server/prisma';
import { redirect, fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import { maintenanceSchema } from '$lib/schemas/maintenance';
import type { PageServerLoad } from './$types';
import type { Actions } from './$types';

export const load = async ({ locals }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  // Check if user is an admin
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
    select: { isAdmin: true, role: true },
  });

  // Allow access if either isAdmin is true OR role is 'admin'
  if (!userData || (!userData.isAdmin && userData.role !== 'admin')) {
    throw redirect(302, '/dashboard/settings');
  }

  // Create form objects for create, edit, and delete operations
  const createForm = await superValidate(zod(maintenanceSchema));
  const editForm = await superValidate(zod(maintenanceSchema));
  const deleteForm = await superValidate(zod(z.object({ id: z.string() })));

  try {
    let maintenanceEvents = [];
    let upcomingEvents = [];
    let pastEvents = [];

    try {
      // Use raw SQL to avoid Prisma model issues
      try {
        // Get all maintenance events
        // Get all maintenance events using raw SQL
        const allEvents = await prisma.$queryRaw`
          SELECT * FROM "web"."MaintenanceEvent"
          ORDER BY "startTime" DESC
        `;
        maintenanceEvents = Array.isArray(allEvents) ? allEvents : [];

        // Get upcoming maintenance events
        const upcoming = await prisma.$queryRaw`
          SELECT * FROM "web"."MaintenanceEvent"
          WHERE "startTime" >= NOW()
          AND "status" IN ('scheduled', 'in-progress')
          ORDER BY "startTime" ASC
        `;
        upcomingEvents = Array.isArray(upcoming) ? upcoming : [];

        // Get past maintenance events
        const past = await prisma.$queryRaw`
          SELECT * FROM "web"."MaintenanceEvent"
          WHERE "endTime" < NOW()
          OR "status" IN ('completed', 'cancelled')
          ORDER BY "startTime" DESC
          LIMIT 10
        `;
        pastEvents = Array.isArray(past) ? past : [];
      } catch (sqlError) {
        console.warn('Error fetching maintenance events with raw SQL:', sqlError);
        // Keep the arrays empty if the table doesn't exist yet
      }
    } catch (dbError) {
      console.warn('MaintenanceEvent table may not exist yet:', dbError);
      // Return empty arrays if the table doesn't exist yet
    }

    return {
      maintenanceEvents,
      upcomingEvents,
      pastEvents,
      createForm,
      editForm,
      deleteForm,
    };
  } catch (error) {
    console.error('Error loading maintenance events:', error);
    return {
      maintenanceEvents: [],
      upcomingEvents: [],
      pastEvents: [],
      createForm,
      editForm,
      deleteForm,
    };
  }
};

export const actions = {
  // Create a new maintenance event
  create: async ({ request, locals, fetch }: import('./$types').RequestEvent) => {
    // Check if user is admin
    const user = locals.user;
    if (!user || !user.isAdmin) {
      return fail(401, { success: false, error: 'Unauthorized' });
    }

    // Validate form with SuperForms
    const form = await superValidate(request, zod(maintenanceSchema));

    if (!form.valid) {
      return fail(400, { form, success: false });
    }

    try {
      // Make API call to create maintenance event
      const response = await fetch('/api/maintenance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(form.data),
        credentials: 'include',
      });

      const result = await response.json();

      if (!response.ok) {
        return fail(response.status, {
          form,
          success: false,
          error: result.error || 'Failed to create maintenance event',
        });
      }

      // Result is already checked above

      // Return success with the form
      return {
        form,
        success: true,
      };
    } catch (error) {
      console.error('Error creating maintenance event:', error);
      return fail(500, {
        form,
        success: false,
        error: 'Failed to create maintenance event',
      });
    }
  },

  // Update a maintenance event
  update: async ({ request, locals, fetch }: import('./$types').RequestEvent) => {
    // Check if user is admin
    const user = locals.user;
    if (!user || !user.isAdmin) {
      return fail(401, { success: false, error: 'Unauthorized' });
    }

    // Validate form with SuperForms
    const form = await superValidate(request, zod(maintenanceSchema));

    if (!form.valid) {
      return fail(400, { form, success: false });
    }

    try {
      // Make API call to update maintenance event
      const response = await fetch('/api/maintenance', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(form.data),
        credentials: 'include',
      });

      const result = await response.json();

      if (!response.ok) {
        return fail(response.status, {
          form,
          success: false,
          error: result.error || 'Failed to update maintenance event',
        });
      }

      // Return success with the form
      return {
        form,
        success: true,
      };
    } catch (error) {
      console.error('Error updating maintenance event:', error);
      return fail(500, {
        form,
        success: false,
        error: 'Failed to update maintenance event',
      });
    }
  },

  // Delete a maintenance event
  delete: async ({ request, locals, fetch }: import('./$types').RequestEvent) => {
    // Check if user is admin
    const user = locals.user;
    if (!user || !user.isAdmin) {
      return fail(401, { success: false, error: 'Unauthorized' });
    }

    // Validate form with SuperForms
    const form = await superValidate(request, zod(z.object({ id: z.string() })));

    if (!form.valid) {
      return fail(400, { form, success: false });
    }

    try {
      // Make API call to delete maintenance event
      const response = await fetch('/api/maintenance', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: form.data.id }),
        credentials: 'include',
      });

      if (!response.ok) {
        const result = await response.json();
        return fail(response.status, {
          form,
          success: false,
          error: result.error || 'Failed to delete maintenance event',
        });
      }

      // Return success with the form
      return {
        form,
        success: true,
      };
    } catch (error) {
      console.error('Error deleting maintenance event:', error);
      return fail(500, {
        form,
        success: false,
        error: 'Failed to delete maintenance event',
      });
    }
  },
};
;null as any as Actions;