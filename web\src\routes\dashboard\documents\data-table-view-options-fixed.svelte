<script lang="ts">
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import { Button } from '$lib/components/ui/button';
  import { MixerHorizontal } from 'svelte-radix';

  // Destructure props directly without using $props
  export let tableModel: any;

  // Define which columns can be toggled in the view options
  const hidableCols = ['document', 'type', 'createdAt', 'updatedAt'];

  // Get the current visibility state from the table model if available
  let currentVisibility = {};

  // Try different ways to get column visibility from the table model
  if (tableModel?.getState && typeof tableModel.getState === 'function') {
    const state = tableModel.getState();
    currentVisibility = state?.columnVisibility || {};
  } else if (tableModel?.state && tableModel.state.columnVisibility) {
    // Direct access to state property
    currentVisibility = tableModel.state.columnVisibility;
  }

  // Initialize with default visibility (all visible) merged with current state
  let columnVisibility: Record<string, boolean> = {
    document: true,
    type: true,
    createdAt: true,
    updatedAt: true,
    ...currentVisibility,
  };

  function handleHide(id: string) {
    if (!tableModel) {
      console.error('tableModel is not available');
      return;
    }

    // Create a new visibility state object
    const newVisibility = { ...columnVisibility };

    // Toggle visibility: if false, make true; otherwise make false
    newVisibility[id] = newVisibility[id] === false ? true : false;

    // Update our local state
    columnVisibility = newVisibility;

    // Try to update the table model using the most appropriate method
    try {
      if (tableModel.setColumnVisibility && typeof tableModel.setColumnVisibility === 'function') {
        // Method 1: Use setColumnVisibility if available
        tableModel.setColumnVisibility(newVisibility);
      } else if (
        tableModel.onColumnVisibilityChange &&
        typeof tableModel.onColumnVisibilityChange === 'function'
      ) {
        // Method 2: Use onColumnVisibilityChange if available
        tableModel.onColumnVisibilityChange(newVisibility);
      } else if (tableModel.setState && typeof tableModel.setState === 'function') {
        // Method 3: Try to update the state directly
        tableModel.setState({ columnVisibility: newVisibility });
      } else {
        // Method 4: Dispatch a custom event as a fallback
        if (typeof document !== 'undefined') {
          document.dispatchEvent(
            new CustomEvent('columnVisibilityChange', {
              detail: { visibility: newVisibility },
            })
          );
        }
      }
    } catch (error) {
      console.error('Error updating column visibility:', error);
      
      // Fallback: dispatch a custom event if the other methods fail
      if (typeof document !== 'undefined') {
        document.dispatchEvent(
          new CustomEvent('columnVisibilityChange', {
            detail: { visibility: newVisibility },
          })
        );
      }
    }
  }
</script>

{#if tableModel}
  <div class="flex items-center gap-2">
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>
        <Button variant="outline" size="sm" class="ml-auto hidden h-8 lg:flex">
          <MixerHorizontal class="mr-2 h-4 w-4" />
          View
        </Button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content align="end">
        <DropdownMenu.Label>Toggle columns</DropdownMenu.Label>
        <DropdownMenu.Separator />
        {#if tableModel.getAllColumns && typeof tableModel.getAllColumns === 'function'}
          {#each tableModel.getAllColumns() || [] as col}
            {#if hidableCols.includes(col.id)}
              <DropdownMenu.Item onclick={() => handleHide(col.id)}>
                <div class="flex items-center">
                  <div
                    class="border-primary mr-2 flex h-4 w-4 items-center justify-center rounded border">
                    <!-- Show the checkbox as checked if the column is visible -->
                    <!-- A column is visible if it's not explicitly set to false -->
                    <!-- or if it's explicitly set to true -->
                    {#if columnVisibility[col.id] !== false}
                      <div class="bg-primary h-2 w-2 rounded-sm"></div>
                    {/if}
                  </div>
                  <span>
                    {col.id === 'document'
                      ? 'Document Name'
                      : col.id === 'type'
                        ? 'Type'
                        : col.id === 'createdAt'
                          ? 'Created'
                          : col.id === 'updatedAt'
                            ? 'Edited'
                            : col.id.charAt(0).toUpperCase() + col.id.slice(1)}
                  </span>
                </div>
              </DropdownMenu.Item>
            {/if}
          {/each}
        {:else}
          <DropdownMenu.Item>
            <span class="text-muted-foreground">No columns available</span>
          </DropdownMenu.Item>
        {/if}
      </DropdownMenu.Content>
    </DropdownMenu.Root>

    <!-- Debug button - only visible in development -->
    {#if typeof window !== 'undefined' && window.location.hostname === 'localhost'}
      <Button
        variant="ghost"
        size="sm"
        onclick={() => {
          // Reset all columns to visible
          const newVisibility = {};
          hidableCols.forEach((colId) => {
            newVisibility[colId] = true;
          });

          // Update local state
          columnVisibility = newVisibility;

          // Use the same update logic as handleHide
          try {
            if (
              tableModel.setColumnVisibility &&
              typeof tableModel.setColumnVisibility === 'function'
            ) {
              tableModel.setColumnVisibility(newVisibility);
            } else if (
              tableModel.onColumnVisibilityChange &&
              typeof tableModel.onColumnVisibilityChange === 'function'
            ) {
              tableModel.onColumnVisibilityChange(newVisibility);
            } else if (tableModel.setState && typeof tableModel.setState === 'function') {
              tableModel.setState({ columnVisibility: newVisibility });
            } else {
              // Fallback to custom event
              document.dispatchEvent(
                new CustomEvent('columnVisibilityChange', {
                  detail: { visibility: newVisibility },
                })
              );
            }
          } catch (error) {
            console.error('Error resetting column visibility:', error);
            // Fallback
            document.dispatchEvent(
              new CustomEvent('columnVisibilityChange', {
                detail: { visibility: newVisibility },
              })
            );
          }
        }}
        class="h-8">
        Reset
      </Button>
    {/if}
  </div>
{/if}
