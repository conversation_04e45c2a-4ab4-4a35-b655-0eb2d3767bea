<!--
  Feature Management Tab Content Component

  This component displays the feature management tab content for the plan management page.
  It allows administrators to view, add, and remove features from the database.
-->
<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Accordion from '$lib/components/ui/accordion/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import { Select } from '$lib/components/ui/select/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { Plus, Trash, Edit, Save, X } from 'lucide-svelte';
  import { FeatureCategory } from '$lib/models/features/features';
  import { toast } from 'svelte-sonner';
  import { onMount } from 'svelte';

  // State for features from database
  let features = [];
  let loading = true;
  let error = null;

  // State for adding a new feature
  let newFeature = {
    id: '',
    name: '',
    description: '',
    category: FeatureCategory.Core,
    icon: '',
    beta: false,
  };

  // State for editing a feature
  let editingFeature = null;
  let editFeatureData = { ...newFeature };

  // Load features from the database
  async function loadFeatures() {
    try {
      loading = true;
      error = null;

      const response = await fetch('/api/admin/features', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to load features: ${response.status}`);
      }

      features = await response.json();
    } catch (err) {
      console.error('Error loading features:', err);
      error = err.message;
    } finally {
      loading = false;
    }
  }

  // Add a new feature to the database
  async function addFeature() {
    try {
      if (!newFeature.id || !newFeature.name) {
        toast.error('Feature ID and name are required');
        return;
      }

      const response = await fetch('/api/admin/features', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'add_feature',
          feature: newFeature,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `Failed to add feature: ${response.status}`);
      }

      const result = await response.json();

      toast.success('Feature added', {
        description: `Feature ${newFeature.name} added successfully`,
        duration: 3000,
      });

      // Reset the new feature form
      newFeature = {
        id: '',
        name: '',
        description: '',
        category: FeatureCategory.Core,
        icon: '',
        beta: false,
      };

      // Reload features
      await loadFeatures();
    } catch (err) {
      console.error('Error adding feature:', err);
      toast.error('Failed to add feature', {
        description: err.message,
        duration: 5000,
      });
    }
  }

  // Remove a feature from the database
  async function removeFeature(featureId) {
    try {
      if (
        !confirm(
          `Are you sure you want to remove feature "${featureId}"? This will also remove it from all plans.`
        )
      ) {
        return;
      }

      const response = await fetch('/api/admin/features', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'remove_feature',
          featureId,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `Failed to remove feature: ${response.status}`);
      }

      const result = await response.json();

      toast.success('Feature removed', {
        description: `Feature ${featureId} removed successfully`,
        duration: 3000,
      });

      // Reload features
      await loadFeatures();
    } catch (err) {
      console.error('Error removing feature:', err);
      toast.error('Failed to remove feature', {
        description: err.message,
        duration: 5000,
      });
    }
  }

  // Start editing a feature
  function startEditFeature(feature) {
    editingFeature = feature.id;
    editFeatureData = { ...feature };
  }

  // Cancel editing a feature
  function cancelEditFeature() {
    editingFeature = null;
    editFeatureData = { ...newFeature };
  }

  // Save edited feature
  async function saveEditFeature() {
    try {
      if (!editFeatureData.id || !editFeatureData.name) {
        toast.error('Feature ID and name are required');
        return;
      }

      const response = await fetch('/api/admin/features', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'update_feature',
          feature: editFeatureData,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `Failed to update feature: ${response.status}`);
      }

      const result = await response.json();

      toast.success('Feature updated', {
        description: `Feature ${editFeatureData.name} updated successfully`,
        duration: 3000,
      });

      // Reset editing state
      editingFeature = null;
      editFeatureData = { ...newFeature };

      // Reload features
      await loadFeatures();
    } catch (err) {
      console.error('Error updating feature:', err);
      toast.error('Failed to update feature', {
        description: err.message,
        duration: 5000,
      });
    }
  }

  // Group features by category
  $: featuresByCategory = features.reduce((acc, feature) => {
    if (!acc[feature.category]) {
      acc[feature.category] = [];
    }
    acc[feature.category].push(feature);
    return acc;
  }, {});

  // Load features on component mount
  onMount(loadFeatures);
</script>

<div class="space-y-6">
  <Card.Root>
    <Card.Content>
      <div class="grid grid-cols-2 gap-4">
        <div class="space-y-2">
          <Label for="feature-id">Feature ID</Label>
          <Input id="feature-id" bind:value={newFeature.id} placeholder="e.g. custom_reports" />
        </div>
        <div class="space-y-2">
          <Label for="feature-name">Feature Name</Label>
          <Input id="feature-name" bind:value={newFeature.name} placeholder="e.g. Custom Reports" />
        </div>
        <div class="col-span-2 space-y-2">
          <Label for="feature-description">Description</Label>
          <Textarea
            id="feature-description"
            bind:value={newFeature.description}
            placeholder="Describe what this feature does" />
        </div>
        <div class="space-y-2">
          <Label for="feature-category">Category</Label>
          <select
            id="feature-category"
            class="w-full rounded border p-2"
            bind:value={newFeature.category}>
            {#each Object.values(FeatureCategory) as category}
              <option value={category}>{category}</option>
            {/each}
          </select>
        </div>
        <div class="space-y-2">
          <Label for="feature-icon">Icon (optional)</Label>
          <Input id="feature-icon" bind:value={newFeature.icon} placeholder="e.g. file-bar-chart" />
        </div>
        <div class="col-span-2 flex items-center space-y-2">
          <input type="checkbox" id="feature-beta" bind:checked={newFeature.beta} class="mr-2" />
          <Label for="feature-beta">Beta Feature</Label>
        </div>
      </div>
    </Card.Content>
    <Card.Footer class="flex justify-end">
      <Button on:click={addFeature}>
        <Plus class="mr-2 h-4 w-4" />
        Add Feature
      </Button>
    </Card.Footer>
  </Card.Root>

  {#if loading}
    <div class="flex h-64 items-center justify-center">
      <div class="text-center">
        <div
          class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]">
        </div>
        <p class="mt-4 text-lg">Loading features...</p>
      </div>
    </div>
  {:else if error}
    <div class="mb-4 rounded-lg bg-red-100 p-4 text-sm text-red-700">
      <p>Error loading features: {error}</p>
    </div>
  {:else}
    <div class="space-y-6">Manage Features</div>
    <div>View, edit, and remove features from the database.</div>

    <Accordion.Root class="w-full" type="multiple">
      {#each Object.entries(featuresByCategory) as [category, categoryFeatures]}
        <Accordion.Item value={category}>
          <Accordion.Trigger>{category} Features</Accordion.Trigger>
          <Accordion.Content>
            <div class="space-y-4">
              {#each categoryFeatures as feature}
                <div class="border-border rounded-md border p-4">
                  {#if editingFeature === feature.id}
                    <!-- Edit Feature Form -->
                    <div class="grid grid-cols-2 gap-4">
                      <div class="space-y-2">
                        <Label for="edit-feature-id">Feature ID</Label>
                        <Input id="edit-feature-id" bind:value={editFeatureData.id} disabled />
                      </div>
                      <div class="space-y-2">
                        <Label for="edit-feature-name">Feature Name</Label>
                        <Input id="edit-feature-name" bind:value={editFeatureData.name} />
                      </div>
                      <div class="col-span-2 space-y-2">
                        <Label for="edit-feature-description">Description</Label>
                        <Textarea
                          id="edit-feature-description"
                          bind:value={editFeatureData.description} />
                      </div>
                      <div class="space-y-2">
                        <Label for="edit-feature-category">Category</Label>
                        <select
                          id="edit-feature-category"
                          class="w-full rounded border p-2"
                          bind:value={editFeatureData.category}>
                          {#each Object.values(FeatureCategory) as category}
                            <option value={category}>{category}</option>
                          {/each}
                        </select>
                      </div>
                      <div class="space-y-2">
                        <Label for="edit-feature-icon">Icon (optional)</Label>
                        <Input id="edit-feature-icon" bind:value={editFeatureData.icon} />
                      </div>
                      <div class="col-span-2 flex items-center space-y-2">
                        <input
                          type="checkbox"
                          id="edit-feature-beta"
                          bind:checked={editFeatureData.beta}
                          class="mr-2" />
                        <Label for="edit-feature-beta">Beta Feature</Label>
                      </div>
                      <div class="col-span-2 flex justify-end space-x-2">
                        <Button variant="outline" on:click={cancelEditFeature}>
                          <X class="mr-2 h-4 w-4" />
                          Cancel
                        </Button>
                        <Button on:click={saveEditFeature}>
                          <Save class="mr-2 h-4 w-4" />
                          Save Changes
                        </Button>
                      </div>
                    </div>
                  {:else}
                    <!-- Feature Display -->
                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="font-medium">{feature.name}</h4>
                        <p class="text-muted-foreground text-sm">
                          ID: {feature.id}
                        </p>
                        <p class="text-muted-foreground text-sm">
                          {feature.description || 'No description'}
                        </p>
                      </div>
                      <div class="flex items-center gap-2">
                        {#if feature.beta}
                          <Badge variant="outline">Beta</Badge>
                        {/if}
                        <Button
                          variant="outline"
                          size="sm"
                          on:click={() => startEditFeature(feature)}>
                          <Edit class="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          on:click={() => removeFeature(feature.id)}>
                          <Trash class="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  {/if}
                </div>
              {/each}
            </div>
          </Accordion.Content>
        </Accordion.Item>
      {/each}
    </Accordion.Root>
  {/if}
</div>
