<script lang="ts">
  import * as Accordion from '$lib/components/ui/accordion/index.js';
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { History } from 'lucide-svelte';
  import MaintenanceAccordion from './MaintenanceAccordion.svelte';
  import type { Incident } from './types';

  // Props
  export let recentIncidents: Incident[] = [];
  export let olderIncidents: Incident[] = [];

  // State
  let isHistoryDialogOpen = false;
</script>

<div class="container mx-auto mb-8">
  <h2 class="mb-4 text-xl font-semibold">Recent Notices</h2>

  {#if recentIncidents.length > 0}
    <Accordion.Root type="multiple" class="w-full space-y-4">
      {#each recentIncidents as incident, i}
        <MaintenanceAccordion {incident} index={i} />
      {/each}
    </Accordion.Root>
  {:else}
    <div class="rounded-lg border p-6 text-center">
      <div class="flex items-center justify-center">
        <div class="mr-2 h-4 w-4 rounded-full bg-green-500"></div>
        <p class="text-muted-foreground">No notices reported for the past 7 days</p>
      </div>
    </div>
  {/if}

  <!-- Show Incident History Button -->
  {#if olderIncidents.length > 0}
    <button
      class="mt-6 w-full rounded-lg border p-3 text-center hover:bg-gray-50 dark:hover:bg-gray-900"
      on:click={() => (isHistoryDialogOpen = true)}>
      <div class="flex items-center justify-center">
        <History class="mr-2 h-4 w-4" />
        <span>View notice history</span>
      </div>
    </button>
  {/if}
</div>

<!-- Incident History Dialog -->
<Dialog.Root bind:open={isHistoryDialogOpen}>
  <Dialog.Content class="sm:max-w-[700px]">
    <Dialog.Header>
      <Dialog.Title>Notice History</Dialog.Title>
      <Dialog.Description>View past incidents and maintenance events</Dialog.Description>
    </Dialog.Header>

    <div class="max-h-[60vh] overflow-y-auto pr-2">
      <div class="space-y-6">
        <!-- Past Incidents Section -->
        {#if olderIncidents.length > 0}
          <div class="space-y-4">
            <h3 class="text-lg font-medium">Past Incidents</h3>
            {#each olderIncidents as incident, i}
              <MaintenanceAccordion {incident} index={i + 100} />
            {/each}
          </div>
        {/if}
      </div>
    </div>

    <Dialog.Footer>
      <button
        class="bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium"
        on:click={() => (isHistoryDialogOpen = false)}>
        Close
      </button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
