<script lang="ts">
  import { type ProjectSchema } from '$lib/validators/profile';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import * as Checkbox from '$lib/components/ui/checkbox/index.js';
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import * as AlertDialog from '$lib/components/ui/alert-dialog/index.js';
  import { Plus, Edit, Trash2, FolderKanban, Link } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';

  // Props
  const {
    data,
    onSave,
    disabled = false,
  } = $props<{
    data: ProjectSchema[];
    onSave: (data: ProjectSchema[]) => Promise<boolean>;
    disabled?: boolean;
  }>();

  // Initialize form data
  let formData = $state<ProjectSchema>({
    title: '',
    description: '',
    startDate: '',
    endDate: '',
    current: false,
    url: '',
    skills: [],
  });

  // Form state
  let submitting = $state(false);
  let errors = $state<Record<string, string>>({});

  // State
  let projects = $state<ProjectSchema[]>(data || []);
  let dialogOpen = $state(false);
  let deleteDialogOpen = $state(false);
  let editingIndex = $state<number | null>(null);
  let deletingIndex = $state<number | null>(null);
  let newSkill = $state('');

  // Open dialog for adding new project
  function addProject() {
    formData = {
      title: '',
      description: '',
      startDate: '',
      endDate: '',
      current: false,
      url: '',
      skills: [],
    };
    editingIndex = null;
    dialogOpen = true;
  }

  // Open dialog for editing project
  function editProject(index: number) {
    const project = projects[index];
    formData = { ...project };
    editingIndex = index;
    dialogOpen = true;
  }

  // Open dialog for deleting project
  function confirmDeleteProject(index: number) {
    deletingIndex = index;
    deleteDialogOpen = true;
  }

  // Delete project
  function deleteProject() {
    if (deletingIndex !== null) {
      projects = projects.filter((_, i) => i !== deletingIndex);
      saveProjects();
      deleteDialogOpen = false;
      deletingIndex = null;
    }
  }

  // Add skill to project
  function addSkill() {
    if (newSkill && !formData.skills?.includes(newSkill)) {
      formData.skills = [...(formData.skills || []), newSkill];
      newSkill = '';
    }
  }

  // Remove skill from project
  function removeSkill(skill: string) {
    formData.skills = formData.skills?.filter((s: string) => s !== skill) || [];
  }

  // Handle form submission
  async function handleSubmit() {
    // Reset errors
    errors = {};

    // Validate form
    let isValid = true;

    if (!formData.title) {
      errors.title = 'Title is required';
      isValid = false;
    }

    if (!isValid) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Set submitting state
    submitting = true;

    try {
      // Create new project object
      const newProject: ProjectSchema = {
        ...formData,
      };

      // Update or add project
      if (editingIndex !== null) {
        projects[editingIndex] = newProject;
      } else {
        projects = [...projects, newProject];
      }

      // Save projects
      await saveProjects();

      // Close dialog
      dialogOpen = false;
    } catch (error) {
      console.error('Error saving project:', error);
      toast.error('Failed to save project');
    } finally {
      submitting = false;
    }
  }

  // Save projects to server
  async function saveProjects() {
    const success = await onSave(projects);
    if (success) {
      toast.success('Projects updated successfully');
    }
    return success;
  }

  // Format date for display
  function formatDate(dateString: string | undefined): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
    });
  }

  // Format date range for display
  function formatDateRange(
    startDate: string | undefined,
    endDate: string | undefined,
    current: boolean
  ): string {
    if (!startDate) return '';

    const start = formatDate(startDate);
    const end = current ? 'Present' : formatDate(endDate);

    return start && end ? `${start} - ${end}` : start || end;
  }
</script>

<div class="rounded-lg border p-6">
  <div class="flex items-center justify-between">
    <h2 class="text-xl font-semibold">Projects & Outside Experience</h2>
    <Button variant="outline" size="sm" onclick={addProject} {disabled}>
      <Plus class="mr-2 h-4 w-4" />
      Add Project
    </Button>
  </div>

  <div class="mt-4">
    {#if projects.length > 0}
      <div class="space-y-4">
        {#each projects as project, index}
          <div class="flex items-start justify-between rounded-md border p-4">
            <div class="flex-1">
              <div class="flex items-center">
                <FolderKanban class="mr-2 h-5 w-5 text-blue-500" />
                <h3 class="font-medium">{project.title}</h3>
              </div>
              {#if project.startDate}
                <p class="text-muted-foreground text-sm">
                  {formatDateRange(project.startDate, project.endDate, project.current)}
                </p>
              {/if}
              {#if project.url}
                <a
                  href={project.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  class="text-primary flex items-center text-sm hover:underline">
                  <Link class="mr-1 h-3 w-3" />
                  Project Link
                </a>
              {/if}
              {#if project.description}
                <p class="mt-2 text-sm">{project.description}</p>
              {/if}
              {#if project.skills && project.skills.length > 0}
                <div class="mt-2 flex flex-wrap gap-1">
                  {#each project.skills as skill}
                    <span class="bg-primary/10 text-primary rounded-full px-2 py-0.5 text-xs">
                      {skill}
                    </span>
                  {/each}
                </div>
              {/if}
            </div>
            <div class="flex space-x-2">
              <Button variant="ghost" size="icon" onclick={() => editProject(index)} {disabled}>
                <Edit class="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onclick={() => confirmDeleteProject(index)}
                {disabled}>
                <Trash2 class="h-4 w-4" />
              </Button>
            </div>
          </div>
        {/each}
      </div>
    {:else}
      <div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8">
        <FolderKanban class="text-muted-foreground mb-2 h-10 w-10" />
        <p class="text-muted-foreground text-center">No projects added yet</p>
        <Button variant="outline" class="mt-4" onclick={addProject} {disabled}>
          <Plus class="mr-2 h-4 w-4" />
          Add Project
        </Button>
      </div>
    {/if}
  </div>
</div>

<!-- Add/Edit Project Dialog -->
<Dialog.Root bind:open={dialogOpen}>
  <Dialog.Content class="sm:max-w-[600px]">
    <Dialog.Header>
      <Dialog.Title>{editingIndex !== null ? 'Edit Project' : 'Add Project'}</Dialog.Title>
      <Dialog.Description>
        {editingIndex !== null
          ? 'Update your project details.'
          : 'Add a new project to your profile.'}
      </Dialog.Description>
    </Dialog.Header>

    <form method="POST" class="space-y-4">
      <div class="space-y-2">
        <Label for="title">Project Title *</Label>
        <Input id="title" bind:value={formData.title} />
        {#if errors.title}
          <p class="text-destructive text-sm">{errors.title}</p>
        {/if}
      </div>

      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div class="space-y-2">
          <Label for="startDate">Start Date</Label>
          <Input id="startDate" type="month" bind:value={formData.startDate} />
        </div>
        <div class="space-y-2">
          <div class="flex items-center space-x-2">
            <Checkbox.Root
              id="current"
              checked={formData.current}
              onCheckedChange={(checked) => (formData.current = checked)} />
            <Label for="current">This is an ongoing project</Label>
          </div>
        </div>
        <div class="space-y-2">
          <Label for="endDate">End Date</Label>
          <Input
            id="endDate"
            type="month"
            bind:value={formData.endDate}
            disabled={formData.current} />
        </div>
        <div class="space-y-2">
          <Label for="url">Project URL</Label>
          <Input id="url" bind:value={formData.url} placeholder="https://" />
          {#if errors.url}
            <p class="text-destructive text-sm">{errors.url}</p>
          {/if}
        </div>
      </div>

      <div class="space-y-2">
        <Label for="description">Description</Label>
        <Textarea
          id="description"
          bind:value={formData.description}
          rows={4}
          placeholder="Describe your project, its purpose, and your role..." />
      </div>

      <div class="space-y-2">
        <Label>Skills Used</Label>
        <div class="flex items-center space-x-2">
          <Input bind:value={newSkill} placeholder="Add a skill" />
          <!-- Handle keydown event in script -->
          <script>
            const handleKeydown = (e: KeyboardEvent) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                addSkill();
              }
            };

            document.addEventListener('keydown', handleKeydown);
          </script>
          <Button type="button" variant="outline" onclick={addSkill} {disabled}>Add</Button>
        </div>
        {#if formData.skills && formData.skills.length > 0}
          <div class="mt-2 flex flex-wrap gap-2">
            {#each formData.skills as skill}
              <span
                class="bg-primary/10 text-primary flex items-center rounded-full px-3 py-1 text-sm">
                {skill}
                <button
                  type="button"
                  class="hover:bg-primary/20 ml-1 rounded-full p-0.5"
                  onclick={() => removeSkill(skill)}
                  {disabled}>
                  <Trash2 class="h-3 w-3" />
                </button>
              </span>
            {/each}
          </div>
        {/if}
      </div>

      <Dialog.Footer>
        <Button variant="outline" type="button" onclick={() => (dialogOpen = false)}>Cancel</Button>
        <Button type="button" onclick={handleSubmit} disabled={submitting}>
          {#if submitting}
            Saving...
          {:else}
            Save
          {/if}
        </Button>
      </Dialog.Footer>
    </form>
  </Dialog.Content>
</Dialog.Root>

<!-- Delete Confirmation Dialog -->
<AlertDialog.Root bind:open={deleteDialogOpen}>
  <AlertDialog.Content>
    <AlertDialog.Header>
      <AlertDialog.Title>Delete Project</AlertDialog.Title>
      <AlertDialog.Description>
        Are you sure you want to delete this project? This action cannot be undone.
      </AlertDialog.Description>
    </AlertDialog.Header>
    <AlertDialog.Footer>
      <AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
      <AlertDialog.Action onclick={deleteProject}>Delete</AlertDialog.Action>
    </AlertDialog.Footer>
  </AlertDialog.Content>
</AlertDialog.Root>
