# Feature Limits Setup

This directory contains scripts to set up feature limits for all features in the system.

## Scripts Overview

1. **feature-limits.mjs**: Defines all feature limits and their mapping to features.
2. **seed-feature-limits.mjs**: Seeds the feature limits to the database.
3. **setup-feature-limits.mjs**: Combines all operations into a single command.

## How to Use

### Run the Script

To run the feature limits setup, use the npm script:

```bash
npm run setup-feature-limits
```

This will seed all feature limits to the database.

### Run Individual Scripts

If you need to run the scripts individually:

```bash
# Seed feature limits to the database
npm run seed-feature-limits
```

## Script Output

When run successfully, the script will output something like:

```
Starting feature limits setup...
Running script: A:\Projects\auto-apply\web\scripts\seed-feature-limits.mjs
Starting feature limits seeding...
Found 44 features in the database
Processing feature: resume_scanner with 1 limits
Updated feature limit: Resume Scans for feature resume_scanner
Processing feature: resume_builder with 2 limits
Updated feature limit: Resume Versions for feature resume_builder
Updated feature limit: Resume Templates for feature resume_builder
...
Feature limits seeding completed!
Features created: 0
Features updated: 0
Limits created: 0
Limits updated: 13
Errors: 0
```

## Customizing Feature Limits

To add or modify feature limits:

1. Edit `scripts/feature-limits.ts`:

   - Add new limits to the `FEATURE_LIMITS` object
   - Update the `FEATURE_TO_LIMITS_MAP` to associate limits with features

2. Run the setup script:
   ```bash
   npm run setup-feature-limits
   ```

## Feature Limits Structure

Each feature limit has the following structure:

```typescript
{
  id: string;              // Unique identifier for the limit
  name: string;            // Display name of the limit
  description: string;     // Description of the limit
  defaultValue: number;    // Default value for the limit
  type: LimitType;         // Type of limit (Monthly, Total, Concurrent, Unlimited)
  unit?: string;           // Unit of measurement (e.g., "scans", "profiles", "GB")
  resetDay?: number;       // Day of month when the limit resets (for monthly limits)
}
```

## Limit Types

- **Monthly**: Resets every month on the specified reset day
- **Total**: Doesn't reset, represents a total limit
- **Concurrent**: Limit on concurrent usage
- **Unlimited**: No limit

## Example

```typescript
// Define a limit
const myLimit = {
  id: 'my_feature_limit',
  name: 'My Feature Limit',
  description: 'Description of my feature limit',
  defaultValue: 10,
  type: LimitType.Monthly,
  unit: 'uses',
  resetDay: 1,
};

// Add it to FEATURE_LIMITS
FEATURE_LIMITS.my_feature_limit = myLimit;

// Associate it with a feature
FEATURE_TO_LIMITS_MAP.my_feature = ['my_feature_limit'];
```

Then run `npm run setup-feature-limits` to apply the changes.
