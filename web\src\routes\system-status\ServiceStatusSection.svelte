<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import ServiceStatusCard from '$components/system-status/ServiceStatusCard.svelte';

  // Status types
  type StatusType = 'operational' | 'degraded' | 'outage' | 'maintenance' | 'unknown';

  // Service status interface
  interface ServiceStatus {
    name: string;
    status: StatusType;
    description?: string;
    lastUpdated?: Date;
  }

  // Props
  export let services: ServiceStatus[] = [];
  export let serviceHistory: Record<string, any> = {};
  export let serviceHealth: Record<string, any> = {};
  export let metrics: {
    uptime: number;
    emailDeliveryRate: number;
    apiResponseTime: number;
    jobSuccessRate: number;
  };

  // Get service-specific metrics based on service name
  function getServiceMetrics(serviceName: string) {
    // Default metrics
    const defaultMetrics = {
      responseTime: 0,
      successRate: 0,
      requestCount: 0,
      errorRate: 0,
    };

    // Map service names to their corresponding health data
    const serviceMapping: Record<string, any> = {
      Matches: { status: 'operational', details: {} },
      Jobs: { status: 'operational', details: {} },
      Tracker: { status: 'operational', details: {} },
      Documents: { status: 'operational', details: {} },
      Automation: { status: 'operational', details: {} },
      System: { status: 'operational', details: {} },
      Website: { status: 'operational', details: {} },
    };

    // Update with real data if available
    if (serviceHealth.web) {
      serviceMapping.Website = serviceHealth.web;
    }

    if (serviceHealth.api) {
      // API affects Jobs and Matches
      serviceMapping.Jobs = serviceHealth.api;
      serviceMapping.Matches = serviceHealth.api;
    }

    if (serviceHealth.worker) {
      serviceMapping.Automation = serviceHealth.worker;
    }

    if (serviceHealth.database) {
      serviceMapping.System = serviceHealth.database;
      // Database affects Tracker and Documents
      serviceMapping.Tracker = serviceHealth.database;
      serviceMapping.Documents = serviceHealth.database;
    }

    // Get health data for this service
    const serviceData = serviceMapping[serviceName] || {};

    // Extract details from the service data
    const details = serviceData.details || {};

    // Return metrics with real data if available
    return {
      responseTime: details.responseTime || defaultMetrics.responseTime,
      successRate:
        details.successRate ||
        (serviceName === 'Jobs'
          ? metrics.jobSuccessRate
          : serviceName === 'System'
            ? serviceHealth.database?.status === 'operational'
              ? 100
              : 80
            : serviceName === 'Website'
              ? serviceHealth.web?.status === 'operational'
                ? 100
                : 80
              : defaultMetrics.successRate),
      requestCount: details.requestCount || defaultMetrics.requestCount,
      errorRate:
        details.errorRate ||
        (serviceName === 'Jobs'
          ? 100 - metrics.jobSuccessRate
          : serviceName === 'System'
            ? serviceHealth.database?.status === 'operational'
              ? 0
              : 20
            : serviceName === 'Website'
              ? serviceHealth.web?.status === 'operational'
                ? 0
                : 20
              : defaultMetrics.errorRate),
      // Add additional metrics if available
      ...(details.queueSize !== undefined ? { queueSize: details.queueSize } : {}),
      ...(details.processingCount !== undefined ? { processingCount: details.processingCount } : {}),
      ...(details.memoryUsage !== undefined ? { memoryUsage: details.memoryUsage } : {}),
      ...(details.dbSizeMB !== undefined ? { dbSizeMB: details.dbSizeMB } : {}),
      ...(details.activeConnections !== undefined ? { activeConnections: details.activeConnections } : {}),
      ...(details.connectedClients !== undefined ? { connectedClients: details.connectedClients } : {}),
    };
  }
</script>

<Card.Root>
  <Card.Header>
    <Card.Title>Service Status</Card.Title>
    <Card.Description>Current status of core application services</Card.Description>
  </Card.Header>
  <Card.Content>
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {#each services as service}
        <ServiceStatusCard
          name={service.name}
          status={service.status}
          description={service.description || ''}
          historyData={serviceHistory?.[service.name] || []}
          metrics={getServiceMetrics(service.name)} />
      {/each}
    </div>
  </Card.Content>
</Card.Root>
