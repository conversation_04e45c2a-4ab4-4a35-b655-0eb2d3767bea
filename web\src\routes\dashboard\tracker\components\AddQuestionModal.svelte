<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import { Slider } from '$lib/components/ui/slider/index.js';
  import { Loader2 } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import { superForm } from 'sveltekit-superforms/client';
  import { zodClient } from 'sveltekit-superforms/adapters';
  import { questionSchema, questionDefaultValues } from '$lib/validators/interview';
  import { cn } from '$lib/utils';

  // Props
  let {
    applicationId,
    interviewId,
    open = false,
    onClose,
    onSuccess,
  } = $props<{
    applicationId: string;
    interviewId: string;
    open?: boolean;
    onClose: () => void;
    onSuccess: () => void;
  }>();

  // Initialize form with Superforms
  const { form, errors, enhance, submitting } = superForm(questionDefaultValues, {
    validators: zodClient(questionSchema),
    dataType: 'json',
    onSubmit: async () => {
      try {
        const response = await fetch(
          `/api/applications/${applicationId}/interviews/${interviewId}/questions`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              question: $form.question,
              category: $form.category,
              difficulty: $form.difficulty ? parseInt($form.difficulty, 10) : null,
              userResponse: $form.userResponse || null,
              userConfidence: $form.userConfidence ? parseInt($form.userConfidence, 10) : null,
              notes: $form.notes || null,
            }),
          }
        );

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to add question');
        }

        toast.success('Question added successfully');
        onSuccess();
        return;
      } catch (error) {
        console.error('Error adding question:', error);
        toast.error(error.message || 'Failed to add question');
        return;
      }
    },
  });

  // Question category options
  const categoryOptions = [
    { value: 'Technical', label: 'Technical' },
    { value: 'Behavioral', label: 'Behavioral' },
    { value: 'Problem Solving', label: 'Problem Solving' },
    { value: 'System Design', label: 'System Design' },
    { value: 'Coding', label: 'Coding' },
    { value: 'Cultural Fit', label: 'Cultural Fit' },
    { value: 'Background', label: 'Background' },
    { value: 'Experience', label: 'Experience' },
    { value: 'Other', label: 'Other' },
  ];

  // Difficulty options
  const difficultyOptions = [
    { value: '1', label: 'Very Easy' },
    { value: '2', label: 'Easy' },
    { value: '3', label: 'Medium' },
    { value: '4', label: 'Hard' },
    { value: '5', label: 'Very Hard' },
  ];

  // Confidence options
  const confidenceOptions = [
    { value: '1', label: 'Not Confident' },
    { value: '2', label: 'Slightly Confident' },
    { value: '3', label: 'Moderately Confident' },
    { value: '4', label: 'Confident' },
    { value: '5', label: 'Very Confident' },
  ];

  // Form validation
  let isFormValid = $state(false);

  // Update form validity whenever form values change
  $effect(() => {
    isFormValid =
      $form.question &&
      $form.question.trim() !== '' &&
      $form.category &&
      $form.category.trim() !== '';
  });
</script>

<Dialog.Root {open}>
  <Dialog.Overlay />
  <Dialog.Content class="sm:max-w-[500px]">
    <Dialog.Header>
      <Dialog.Title>Add Interview Question</Dialog.Title>
      <Dialog.Description>Record a question asked during the interview.</Dialog.Description>
    </Dialog.Header>

    <form
      method="POST"
      use:enhance={{
        onSubmit: () => {
          if (!isFormValid) {
            toast.error('Please fill in all required fields');
            return false;
          }
          return true;
        },
      }}
      class="space-y-4">
      <p class="text-muted-foreground mb-2 text-sm">Fields marked with * are required</p>

      <div class="space-y-2">
        <Label for="question">Question*</Label>
        <Textarea bind:value={$form.question} />
        {#if $errors.question}
          <p class="text-destructive text-sm">{$errors.question}</p>
        {/if}
      </div>

      <div class="space-y-2">
        <Label for="category">Category*</Label>
        <Select.Root type="single" bind:value={$form.category}>
          <Select.Trigger class="h-10 w-full">
            <Select.Value placeholder="Select question category" />
          </Select.Trigger>
          <Select.Content class="z-50 max-h-60 w-full overflow-y-auto">
            {#each categoryOptions as option}
              <Select.Item value={option.value}>{option.label}</Select.Item>
            {/each}
          </Select.Content>
        </Select.Root>
        {#if $errors.category}
          <p class="text-destructive text-sm">{$errors.category}</p>
        {/if}
      </div>

      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <Label for="difficulty"
            >Difficulty: {$form.difficulty
              ? difficultyOptions[parseInt($form.difficulty) - 1]?.label
              : 'Not set'}</Label>
          <span class="text-muted-foreground text-sm">{$form.difficulty || '0'}/5</span>
        </div>
        <Slider
          value={$form.difficulty ? parseInt($form.difficulty) : 1}
          onValueChange={(value: number) => {
            $form.difficulty = value.toString();
          }}
          min={1}
          max={5}
          step={1}
          class="py-4" />
        <div class="text-muted-foreground mt-1 flex justify-between text-xs">
          <span>Very Easy</span>
          <span>Very Hard</span>
        </div>
      </div>

      <div class="space-y-2">
        <Label for="userResponse">Your Response</Label>
        <Textarea bind:value={$form.userResponse} />
      </div>

      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <Label for="userConfidence"
            >Confidence: {$form.userConfidence
              ? confidenceOptions[parseInt($form.userConfidence) - 1]?.label
              : 'Not set'}</Label>
          <span class="text-muted-foreground text-sm">{$form.userConfidence || '0'}/5</span>
        </div>
        <Slider
          value={$form.userConfidence ? parseInt($form.userConfidence) : 1}
          onValueChange={(value: number) => {
            $form.userConfidence = value.toString();
          }}
          min={1}
          max={5}
          step={1}
          class="py-4" />
        <div class="text-muted-foreground mt-1 flex justify-between text-xs">
          <span>Not Confident</span>
          <span>Very Confident</span>
        </div>
      </div>

      <div class="space-y-2">
        <Label for="notes">Notes</Label>
        <Textarea bind:value={$form.notes} />
      </div>

      <Dialog.Footer>
        <Button type="button" variant="outline" onclick={onClose}>Cancel</Button>
        <Button
          type="submit"
          disabled={$submitting || !isFormValid}
          class={!isFormValid ? 'cursor-not-allowed opacity-50' : ''}>
          {#if $submitting}
            <Loader2 class="mr-2 h-4 w-4 animate-spin" />
            Saving...
          {:else}
            Save Question
          {/if}
        </Button>
      </Dialog.Footer>
    </form>
  </Dialog.Content>
</Dialog.Root>
