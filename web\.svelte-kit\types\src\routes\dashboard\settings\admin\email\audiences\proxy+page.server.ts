// @ts-nocheck
/**
 * Email Audiences Page Server
 *
 * This module handles server-side logic for the email audiences page.
 */

import { Resend } from 'resend';
import { logger } from '$lib/server/logger';
import type { PageServerLoad } from './$types';

// Initialize Resend with API key if available
const resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;

export const load = async () => {
  try {
    // Get audiences from Resend
    let audiences = [];

    if (resend) {
      try {
        const response = await resend.audiences.list();

        if (response.error) {
          logger.error('Error fetching audiences:', response.error);
        } else {
          audiences = response.data || [];
          logger.info(`Successfully loaded ${audiences.length} audiences from Resend`);
        }
      } catch (error) {
        logger.error('Error fetching audiences:', error);
      }
    } else {
      logger.warn('Resend API key not available, using mock data');
      // Use mock data when API key is not available
      audiences = [
        { id: 'mock-1', name: 'All Users (Mock)', created_at: new Date().toISOString() },
        { id: 'mock-2', name: 'Active Users (Mock)', created_at: new Date().toISOString() },
      ];
    }

    return {
      audiences,
      apiStatus: {
        isAvailable: true,
        hasApiKey: !!process.env.RESEND_API_KEY,
      },
    };
  } catch (error) {
    logger.error('Error in audiences page load:', error);

    return {
      audiences: [],
      apiStatus: {
        isAvailable: false,
        hasApiKey: !!process.env.RESEND_API_KEY,
        error: error.message,
      },
    };
  }
};
;null as any as PageServerLoad;