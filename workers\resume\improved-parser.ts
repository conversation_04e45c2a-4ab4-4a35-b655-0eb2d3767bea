/**
 * Improved Resume Parser
 *
 * This module provides enhanced parsing functions to improve the accuracy of resume parsing.
 * It focuses on better extraction of:
 * 1. Contact information (phone, location)
 * 2. Education details
 * 3. Work experience (title, company, dates)
 * 4. Skills
 */

import { PathLike } from "fs";
import fs from "fs";
import path from "path";
import { PrismaClient } from "@prisma/client";

// Initialize Prisma client
const prisma = new PrismaClient();

// Improved regex patterns for better extraction
const improvedPatterns = {
  // Phone pattern with better support for various formats
  phone:
    /(?:\+\d{1,3}[-.\s]?)?\(?(?:\d{3})\)?[-.\s]?\d{3}[-.\s]?\d{4}|\+\d{10,12}/g,

  // Location pattern with better city/state/country detection
  location:
    /(?:[A-Z][a-zA-Z\s]+,\s*(?:[A-Z]{2}|[A-Z][a-zA-Z\s]+)(?:\s*\d{5})?)|(?:[A-Z][a-zA-Z\s]+,\s*[A-Z][a-zA-Z\s]+)/g,

  // Email pattern (unchanged but included for completeness)
  email: /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,

  // Date pattern with better support for various formats
  date: /(?:(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\.?[\s,-]+\d{4}\s*[-–—to]*\s*(?:Present|Current|Now|Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)?[a-z]*\.?[\s,-]*\d{0,4}|\d{4}\s*[-–—to]*\s*(?:\d{4}|Present|Current|Now)|\d{1,2}\/\d{4}\s*[-–—to]*\s*(?:\d{1,2}\/\d{4}|Present|Current|Now))/gi,

  // URL pattern with better support for various formats
  url: /(https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*)|(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*))/gi,

  // Job title pattern with common titles
  jobTitle:
    /(?:^|\s)(Software Engineer|Senior Software Engineer|Lead Software Engineer|Principal Software Engineer|Software Developer|Senior Software Developer|Lead Software Developer|Full Stack Developer|Frontend Developer|Backend Developer|Web Developer|Mobile Developer|iOS Developer|Android Developer|DevOps Engineer|Site Reliability Engineer|Data Engineer|Data Scientist|Machine Learning Engineer|AI Engineer|Product Manager|Project Manager|Program Manager|Engineering Manager|Technical Lead|Architect|Solutions Architect|System Architect|Technical Architect|Enterprise Architect|Cloud Architect|Network Engineer|Systems Engineer|QA Engineer|Test Engineer|Quality Assurance Engineer|Automation Engineer|Security Engineer|Information Security Engineer|Database Administrator|DBA|Business Analyst|Systems Analyst|Data Analyst|UX Designer|UI Designer|UX\/UI Designer|Graphic Designer|Web Designer|Product Designer|Technical Writer|Content Writer|Copywriter|Marketing Manager|Sales Manager|Account Manager|Customer Success Manager|Operations Manager|IT Manager|IT Director|CTO|CEO|COO|CFO|CIO|CISO|VP of Engineering|Director of Engineering|VP of Product|Director of Product|VP of Marketing|Director of Marketing|VP of Sales|Director of Sales|VP of Operations|Director of Operations|VP of IT|Director of IT|VP of Security|Director of Security|VP of Finance|Director of Finance|VP of HR|Director of HR|VP of Customer Success|Director of Customer Success|VP of Business Development|Director of Business Development|VP of Strategy|Director of Strategy|VP of Design|Director of Design|VP of Content|Director of Content|VP of Communications|Director of Communications|VP of Legal|Director of Legal|VP of Compliance|Director of Compliance|VP of Risk|Director of Risk|VP of Analytics|Director of Analytics|VP of Data|Director of Data|VP of Research|Director of Research|VP of Innovation|Director of Innovation|VP of Technology|Director of Technology|VP of Infrastructure|Director of Infrastructure|VP of Cloud|Director of Cloud|VP of Mobile|Director of Mobile|VP of Web|Director of Web|VP of E-commerce|Director of E-commerce|VP of Digital|Director of Digital|VP of Social Media|Director of Social Media|VP of Growth|Director of Growth|VP of Revenue|Director of Revenue|VP of Partnerships|Director of Partnerships|VP of Alliances|Director of Alliances|VP of Channel|Director of Channel|VP of Sales Operations|Director of Sales Operations|VP of Marketing Operations|Director of Marketing Operations|VP of Business Operations|Director of Business Operations|VP of People|Director of People|VP of Talent|Director of Talent|VP of Culture|Director of Culture|VP of Diversity|Director of Diversity|VP of Inclusion|Director of Inclusion|VP of Learning|Director of Learning|VP of Development|Director of Development|VP of Training|Director of Training|VP of Knowledge|Director of Knowledge|VP of Support|Director of Support|VP of Services|Director of Services|VP of Solutions|Director of Solutions|VP of Customer Experience|Director of Customer Experience|VP of User Experience|Director of User Experience|VP of Product Marketing|Director of Product Marketing|VP of Content Marketing|Director of Content Marketing|VP of Digital Marketing|Director of Digital Marketing|VP of Brand|Director of Brand|VP of Creative|Director of Creative|VP of Design|Director of Design|VP of UX|Director of UX|VP of UI|Director of UI|VP of Research|Director of Research|VP of Analytics|Director of Analytics|VP of Insights|Director of Insights|VP of Data Science|Director of Data Science|VP of Machine Learning|Director of Machine Learning|VP of AI|Director of AI|VP of Automation|Director of Automation|VP of DevOps|Director of DevOps|VP of SRE|Director of SRE|VP of Security|Director of Security|VP of Privacy|Director of Privacy|VP of Compliance|Director of Compliance|VP of Risk|Director of Risk|VP of Audit|Director of Audit|VP of Governance|Director of Governance|VP of Legal|Director of Legal|VP of Regulatory|Director of Regulatory|VP of Ethics|Director of Ethics|VP of Sustainability|Director of Sustainability|VP of Corporate Social Responsibility|Director of Corporate Social Responsibility|VP of Community|Director of Community|VP of Public Relations|Director of Public Relations|VP of Communications|Director of Communications|VP of Media|Director of Media|VP of Public Affairs|Director of Public Affairs|VP of Government Relations|Director of Government Relations|VP of Policy|Director of Policy|VP of Advocacy|Director of Advocacy|VP of Lobbying|Director of Lobbying|VP of Political Affairs|Director of Political Affairs|VP of Regulatory Affairs|Director of Regulatory Affairs|VP of International|Director of International|VP of Global|Director of Global|VP of Regional|Director of Regional|VP of Local|Director of Local|VP of National|Director of National|VP of Enterprise|Director of Enterprise|VP of Commercial|Director of Commercial|VP of Consumer|Director of Consumer|VP of Retail|Director of Retail|VP of Wholesale|Director of Wholesale|VP of Distribution|Director of Distribution|VP of Supply Chain|Director of Supply Chain|VP of Logistics|Director of Logistics|VP of Procurement|Director of Procurement|VP of Sourcing|Director of Sourcing|VP of Vendor Management|Director of Vendor Management|VP of Supplier Management|Director of Supplier Management|VP of Manufacturing|Director of Manufacturing|VP of Production|Director of Production|VP of Quality|Director of Quality|VP of Research and Development|Director of Research and Development|VP of Innovation|Director of Innovation|VP of Strategy|Director of Strategy|VP of Business Development|Director of Business Development|VP of Partnerships|Director of Partnerships|VP of Alliances|Director of Alliances|VP of Channel|Director of Channel|VP of Sales Operations|Director of Sales Operations|VP of Marketing Operations|Director of Marketing Operations|VP of Business Operations|Director of Business Operations|Junior Developer|Intern|Associate|Consultant)(?:\s|$)/i,

  // Company pattern with common company indicators
  company:
    /(?:^|\s)(Inc\.|LLC|Ltd\.|Corp\.|Corporation|Company|Co\.|Group|Agency|Associates|Partners|Technologies|Solutions|Systems|Services|Software|Consulting|International|Enterprises|Industries)(?:\s|$)/i,

  // Education institution pattern
  institution:
    /(?:^|\s)(University|College|School|Institute|Academy|Polytechnic)(?:\s|$)/i,

  // Degree pattern with common degrees
  degree:
    /(?:^|\s)(Bachelor|Master|PhD|Doctorate|Associate|B\.S\.|M\.S\.|B\.A\.|M\.A\.|B\.Eng\.|M\.Eng\.|Ph\.D\.|Diploma|Certificate|BS|MS|BA|MA|MBA|BBA|BSc|MSc|MEng|BEng)(?:\s|$)/i,

  // Skills pattern with common technical skills
  skills:
    /(?:^|\s)(JavaScript|TypeScript|Python|Java|C\+\+|C#|Ruby|PHP|Swift|Kotlin|Go|Rust|Scala|Perl|R|MATLAB|SQL|NoSQL|MongoDB|MySQL|PostgreSQL|Oracle|SQL Server|Redis|Cassandra|DynamoDB|Firebase|AWS|Amazon Web Services|Azure|Google Cloud|GCP|Heroku|Docker|Kubernetes|Jenkins|Travis CI|CircleCI|Git|GitHub|GitLab|Bitbucket|Jira|Confluence|Trello|Asana|Slack|Microsoft Teams|Zoom|Agile|Scrum|Kanban|Waterfall|DevOps|CI\/CD|TDD|BDD|REST|GraphQL|SOAP|API|JSON|XML|HTML|CSS|SASS|LESS|Bootstrap|Tailwind|Material UI|React|Angular|Vue|Svelte|Next.js|Nuxt.js|Node.js|Express|Django|Flask|Spring|Laravel|Ruby on Rails|ASP.NET|jQuery|Redux|MobX|RxJS|WebSockets|PWA|SPA|SSR|Microservices|Serverless|Blockchain|Ethereum|Smart Contracts|Solidity|Machine Learning|Deep Learning|Neural Networks|TensorFlow|PyTorch|Keras|scikit-learn|NLP|Computer Vision|Data Science|Big Data|Hadoop|Spark|Kafka|Airflow|ETL|Data Warehousing|Data Modeling|Data Visualization|Tableau|Power BI|D3.js|Excel|VBA|Cybersecurity|Penetration Testing|Ethical Hacking|Cryptography|Encryption|Firewall|VPN|IAM|OAuth|SAML|SSO|GDPR|HIPAA|SOC 2|ISO 27001|PCI DSS|Networking|TCP\/IP|HTTP|HTTPS|DNS|DHCP|Load Balancing|CDN|Caching|SEO|SEM|Digital Marketing|Content Marketing|Social Media Marketing|Email Marketing|CRM|Salesforce|HubSpot|Adobe Creative Suite|Photoshop|Illustrator|InDesign|Premiere Pro|After Effects|Figma|Sketch|InVision|Adobe XD|UI\/UX|User Research|Wireframing|Prototyping|Usability Testing|A\/B Testing|Responsive Design|Mobile Design|Accessibility|WCAG|Section 508|Technical Writing|Documentation|Public Speaking|Leadership|Team Management|Project Management|Product Management|Agile|Scrum|Kanban|Waterfall|JIRA|Confluence|Trello|Asana|Monday.com|ClickUp|Microsoft Project|Gantt Charts|Budgeting|Forecasting|Financial Analysis|Strategic Planning|Business Development|Sales|Marketing|Customer Service|Operations|Supply Chain|Logistics|Procurement|Human Resources|Recruiting|Talent Acquisition|Performance Management|Training|Development|Coaching|Mentoring|Conflict Resolution|Negotiation|Problem Solving|Critical Thinking|Analytical Thinking|Creative Thinking|Decision Making|Time Management|Prioritization|Organization|Communication|Presentation|Writing|Editing|Research|Analysis|Synthesis|Evaluation|Innovation|Creativity|Adaptability|Flexibility|Resilience|Stress Management|Emotional Intelligence|Empathy|Collaboration|Teamwork|Cross-functional Collaboration|Remote Work|Virtual Collaboration|Cultural Awareness|Diversity|Inclusion|Equity|Belonging|Social Responsibility|Ethics|Integrity|Accountability|Reliability|Attention to Detail|Quality Assurance|Continuous Improvement|Lean|Six Sigma|Kaizen|5S|Root Cause Analysis|Process Improvement|Workflow Optimization|Automation|RPA|Business Process Management|Change Management|Organizational Development|Strategic Thinking|Systems Thinking|Design Thinking|Customer-centric|User-centric|Product-centric|Data-driven|Results-oriented|Goal-oriented|Action-oriented|Solution-oriented|Problem-oriented|Detail-oriented|Big Picture Thinking|Entrepreneurship|Intrapreneurship|Innovation|Disruption|Transformation|Digital Transformation|Business Transformation|Organizational Transformation|Cultural Transformation|Leadership Development|Executive Presence|Influence|Persuasion|Motivation|Inspiration|Vision|Mission|Values|Purpose|Strategy|Execution|Implementation|Adoption|Scaling|Growth|Expansion|Globalization|Localization|Internationalization|Market Research|Competitive Analysis|SWOT Analysis|PESTEL Analysis|Porter's Five Forces|Business Model Canvas|Value Proposition Canvas|Customer Journey Mapping|Persona Development|Stakeholder Management|Risk Management|Issue Management|Crisis Management|Disaster Recovery|Business Continuity|Compliance|Governance|Audit|Quality Management|Performance Management|KPIs|OKRs|Metrics|Analytics|Reporting|Dashboards|Data Visualization|Insights|Recommendations|Decision Support|Business Intelligence|Competitive Intelligence|Market Intelligence|Customer Intelligence|Artificial Intelligence|Machine Learning|Deep Learning|Neural Networks|Natural Language Processing|Computer Vision|Robotics|Automation|Internet of Things|IoT|Edge Computing|Cloud Computing|Fog Computing|Grid Computing|High Performance Computing|Quantum Computing|Blockchain|Distributed Ledger|Cryptocurrency|Bitcoin|Ethereum|Smart Contracts|NFTs|Web3|Metaverse|Virtual Reality|Augmented Reality|Mixed Reality|Extended Reality|3D Printing|Additive Manufacturing|Nanotechnology|Biotechnology|Genetic Engineering|CRISPR|Synthetic Biology|Renewable Energy|Solar Energy|Wind Energy|Hydroelectric Energy|Geothermal Energy|Biomass Energy|Nuclear Energy|Energy Storage|Battery Technology|Fuel Cells|Hydrogen|Electric Vehicles|Autonomous Vehicles|Drones|Robotics|Space Technology|Satellite Technology|Telecommunications|5G|6G|Fiber Optics|Wireless Communication|Mobile Technology|Wearable Technology|Smart Home|Smart City|Smart Grid|Smart Factory|Industry 4.0|Manufacturing 4.0|Agriculture 4.0|Healthcare 4.0|Education 4.0|Retail 4.0|Finance 4.0|Government 4.0|Transportation 4.0|Logistics 4.0|Supply Chain 4.0|Energy 4.0|Construction 4.0|Real Estate 4.0|Hospitality 4.0|Tourism 4.0|Entertainment 4.0|Media 4.0|Sports 4.0|Fashion 4.0|Food 4.0|Beverage 4.0|Pharmaceutical 4.0|Chemical 4.0|Materials 4.0|Mining 4.0|Oil 4.0|Gas 4.0|Utilities 4.0|Waste Management 4.0|Recycling 4.0|Circular Economy|Sustainability|Climate Change|Carbon Footprint|Carbon Neutrality|Carbon Offsetting|Carbon Capture|Carbon Storage|Carbon Trading|Carbon Tax|Carbon Credits|Carbon Accounting|Carbon Disclosure|Carbon Reporting|Carbon Management|Carbon Strategy|Carbon Policy|Carbon Regulation|Carbon Compliance|Carbon Governance|Carbon Leadership|Carbon Innovation|Carbon Technology|Carbon Finance|Carbon Investment|Carbon Risk|Carbon Opportunity|Carbon Future|Carbon Present|Carbon Past|Carbon Now|Carbon Tomorrow|Carbon Yesterday|Carbon Today|Carbon Always|Carbon Never|Carbon Sometimes|Carbon Often|Carbon Rarely|Carbon Frequently|Carbon Occasionally|Carbon Regularly|Carbon Periodically|Carbon Sporadically|Carbon Intermittently|Carbon Continuously|Carbon Constantly|Carbon Consistently|Carbon Persistently|Carbon Steadily|Carbon Gradually|Carbon Rapidly|Carbon Quickly|Carbon Slowly|Carbon Swiftly|Carbon Promptly|Carbon Immediately|Carbon Eventually|Carbon Finally|Carbon Ultimately|Carbon Eventually|Carbon Finally|Carbon Ultimately)(?:\s|$)/i,
};

/**
 * Improved resume parser that enhances the accuracy of extracted information
 */
export class ImprovedResumeParser {
  private patterns = improvedPatterns;

  /**
   * Parse a resume file
   * @param filePath Path to the resume file
   * @returns Parsed resume data
   */
  async parseResume(filePath: string) {
    try {
      console.log(`Parsing resume file: ${filePath}`);

      // Read the file content
      const fileContent = await fs.promises.readFile(filePath, "utf8");
      const fileType = path.extname(filePath).substring(1).toLowerCase();

      // Process the content
      const result = await this.processContent(fileContent, filePath);

      // Add metadata
      result.metadata = {
        parserVersion: "2.0.0",
        parsedAt: new Date().toISOString(),
        duration: 0, // Will be updated later
        fileName: path.basename(filePath),
        fileType,
      };

      return result;
    } catch (error) {
      console.error("Error parsing resume:", error);
      throw error;
    }
  }

  /**
   * Clean PDF artifacts from text
   * @param content The content to clean
   * @returns Cleaned content
   */
  private cleanPdfArtifacts(content: string): string {
    if (!content) return "";

    // Check if this is a PDF file (contains PDF header)
    const isPdf = content.includes("%PDF-");

    if (!isPdf) return content;

    console.log("Cleaning PDF artifacts from content");

    // Remove PDF header and binary markers
    let cleaned = content.replace(/%PDF-[\d.]+/g, "");
    cleaned = cleaned.replace(
      /stream|endstream|obj|endobj|xref|trailer|startxref/g,
      ""
    );

    // Remove non-printable characters
    cleaned = cleaned.replace(/[^\x20-\x7E\n\r\t]/g, "");

    // Remove excessive whitespace
    cleaned = cleaned.replace(/\s+/g, " ");

    // Remove lines that are likely PDF artifacts
    const lines = cleaned.split("\n").filter((line) => {
      const trimmed = line.trim();
      if (trimmed.length < 3) return false;
      if (/^[0-9\s]+$/.test(trimmed)) return false; // Just numbers
      if (/^\s*\//.test(trimmed)) return false; // Starts with /
      return true;
    });

    // Join the lines back together
    cleaned = lines.join("\n");

    return cleaned;
  }

  /**
   * Process the content of a resume
   * @param content The content of the resume
   * @param filePath Path to the resume file (for reference)
   * @returns Processed resume data
   */
  async processContent(content: string, filePath: PathLike | fs.FileHandle) {
    // Import improved extractors
    const {
      improveProfileExtraction,
      improveEducationExtraction,
      improveWorkExperienceExtraction,
      improveSkillsExtraction,
      improveProjectsExtraction,
      improveCertificationsExtraction,
      improvePublicationsExtraction,
      improveAchievementsExtraction,
      improvePatentsExtraction,
      improveLanguagesExtraction,
    } = await import("./improved-extractors.js");

    // Clean PDF artifacts if present
    content = this.cleanPdfArtifacts(content);

    // Pre-process content to handle page breaks
    content = this.handlePageBreaks(content);

    // Split into lines and clean
    const lines = content.split("\n").map((line) => line.trim());

    // Identify sections
    const sections = this.identifySections(lines);

    // Extract basic information with improved accuracy
    let basicInfo = this.extractBasicInfo(sections.profile || [], content);

    // Improve profile extraction
    basicInfo = improveProfileExtraction(
      sections.profile || [],
      content,
      basicInfo
    );

    // Extract education with improved accuracy using helper
    const education = improveEducationExtraction(
      sections.education || [],
      content,
      this.extractEducation.bind(this)
    );

    // Extract work experience with improved accuracy using helper
    const experience = improveWorkExperienceExtraction(
      sections.experience || [],
      content,
      this.extractWorkExperience.bind(this)
    );

    // Extract skills with improved accuracy using helper
    const skills = improveSkillsExtraction(
      sections.skills || [],
      content,
      this.extractSkills.bind(this)
    );

    // Extract publications with improved accuracy
    const publications = improvePublicationsExtraction(
      sections.publications || [],
      content,
      this.extractPublications.bind(this)
    );

    // Extract projects with improved accuracy using helper
    const projects = improveProjectsExtraction(
      sections.projects || [],
      content,
      this.extractProjects.bind(this)
    );

    // Extract certifications with improved accuracy
    const certifications = improveCertificationsExtraction(
      sections.certifications || [],
      content,
      this.extractCertifications.bind(this)
    );

    // Extract achievements with improved accuracy
    const achievements = improveAchievementsExtraction(
      sections.achievements || [],
      content,
      this.extractAchievements.bind(this)
    );

    // Extract patents with improved accuracy
    const patents = improvePatentsExtraction(
      sections.patents || [],
      content,
      this.extractPatents.bind(this)
    );

    // Extract additional sections with improved accuracy
    const languages = improveLanguagesExtraction(
      sections.languages || [],
      content,
      this.extractLanguages.bind(this)
    );
    const volunteer = this.extractVolunteer(sections.volunteer || []);
    const interests = this.extractInterests(sections.interests || []);
    const references = this.extractReferences(sections.references || []);

    // Create the result object
    const result = {
      profile: {
        name: basicInfo.name,
        email: basicInfo.email,
        phone: basicInfo.phone,
        location: basicInfo.location,
        summary: basicInfo.summary,
        url: basicInfo.url,
        links: basicInfo.links || [],
      },
      education,
      workExperiences: experience,
      skills,
      publications,
      projects,
      certifications,
      languages,
      patents,
      achievements,
      volunteer,
      interests,
      references,
      rawText: content,
      sectionMap: sections,
      confidenceScores: this.calculateConfidenceScores(
        basicInfo,
        education,
        experience,
        skills,
        publications,
        projects,
        certifications,
        languages,
        patents,
        achievements,
        volunteer,
        interests,
        references
      ),
    };

    return result;
  }

  /**
   * Handle page breaks in the content
   * @param content The content to process
   * @returns Processed content
   */
  private handlePageBreaks(content: string): string {
    // Replace page break indicators
    return content
      .replace(/\f/g, "\n") // Form feed
      .replace(/\r\n/g, "\n") // Windows line endings
      .replace(/\r/g, "\n") // Mac line endings
      .replace(/\n{3,}/g, "\n\n"); // Multiple blank lines
  }

  /**
   * Identify sections in the resume
   */
  private identifySections(lines: string[]): any {
    // First, identify all section headers in the resume
    const sectionHeaders = this.identifySectionHeaders(lines);

    // Log the identified section headers for debugging
    console.log(
      "Identified section headers:",
      sectionHeaders.map((h) => `${h.name} at line ${h.index}`).join(", ")
    );

    // For PDFs, we need a more aggressive approach to find sections
    // Check if this is likely a PDF by looking for PDF artifacts
    const isPdf = lines.some(
      (line) =>
        line.includes("%PDF") ||
        line.match(/^(stream|endstream|obj|endobj|xref|trailer|startxref)/)
    );

    if (isPdf) {
      console.log("PDF detected, using aggressive section identification");

      // For PDFs, look for common section headers with more flexible matching
      const sections = {
        profile: lines.slice(0, 20), // First 20 lines likely contain profile info
        education: [],
        experience: [],
        skills: [],
        publications: [],
        projects: [],
        certifications: [],
        languages: [],
        patents: [],
        achievements: [],
        volunteer: [],
        interests: [],
        references: [],
      };

      // Look for education section with more flexible matching
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].toLowerCase();
        if (
          line.includes("education") ||
          line.includes("academic") ||
          line.includes("university") ||
          line.includes("college") ||
          line.includes("degree") ||
          line.includes("school")
        ) {
          // Found education section, extract the next 10 lines
          sections.education = lines.slice(i, i + 10);
          console.log(`Found education section at line ${i}`);
          break;
        }
      }

      // Look for experience section with more flexible matching
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].toLowerCase();
        if (
          line.includes("experience") ||
          line.includes("employment") ||
          line.includes("work") ||
          line.includes("career") ||
          line.includes("professional")
        ) {
          // Found experience section, extract the next 30 lines
          sections.experience = lines.slice(i, i + 30);
          console.log(`Found experience section at line ${i}`);
          break;
        }
      }

      // Look for skills section with more flexible matching
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].toLowerCase();
        if (
          line.includes("skills") ||
          line.includes("technologies") ||
          line.includes("technical") ||
          line.includes("proficiencies") ||
          line.includes("competencies")
        ) {
          // Found skills section, extract the next 15 lines
          sections.skills = lines.slice(i, i + 15);
          console.log(`Found skills section at line ${i}`);
          break;
        }
      }

      // Look for languages section with more flexible matching
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].toLowerCase();
        if (
          line.includes("languages") ||
          line.includes("language proficiency") ||
          (line.includes("language") && line.includes("proficiency"))
        ) {
          // Found languages section, extract the next 5 lines
          sections.languages = lines.slice(i, i + 5);
          console.log(`Found languages section at line ${i}`);
          break;
        }
      }

      return sections;
    }

    // For non-PDFs, use the standard approach
    return {
      profile: lines.slice(0, sectionHeaders[0]?.index || 10),
      education: this.findEducationSection(lines),
      experience: this.findExperienceSection(lines),
      skills: this.findSkillsSection(lines),
      publications: this.findPublicationsSection(lines),
      projects: this.findProjectsSection(lines),
      certifications: this.findCertificationsSection(lines),
      languages: this.findLanguagesSection(lines),
      patents: this.findPatentsSection(lines),
      achievements: this.findAchievementsSection(lines),
      volunteer: this.findVolunteerSection(lines),
      interests: this.findInterestsSection(lines),
      references: this.findReferencesSection(lines),
    };
  }

  /**
   * Identify all section headers in the resume
   * This helps with more accurate section detection
   */
  private identifySectionHeaders(
    lines: string[]
  ): Array<{ name: string; index: number }> {
    const headers = [];
    const commonHeaders = [
      "summary",
      "profile",
      "about",
      "objective",
      "education",
      "academic background",
      "academic history",
      "educational background",
      "experience",
      "work experience",
      "employment history",
      "professional experience",
      "work history",
      "skills",
      "technical skills",
      "core competencies",
      "competencies",
      "expertise",
      "projects",
      "project experience",
      "personal projects",
      "professional projects",
      "certifications",
      "certificates",
      "credentials",
      "professional certifications",
      "languages",
      "language proficiency",
      "language skills",
      "patents",
      "patent applications",
      "intellectual property",
      "publications",
      "published works",
      "research publications",
      "papers",
      "achievements",
      "awards",
      "honors",
      "recognitions",
      "accomplishments",
      "volunteer",
      "volunteering",
      "volunteer experience",
      "community service",
      "interests",
      "hobbies",
      "activities",
      "personal interests",
      "references",
      "professional references",
      "recommendations",
    ];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim().toLowerCase();

      // Check if this line is a section header
      if (
        line &&
        // All caps or title case, short line
        ((line === line.toUpperCase() && line.length < 30) ||
          // Common section header
          commonHeaders.some(
            (header) =>
              line === header ||
              line === header + ":" ||
              line.startsWith(header + " ") ||
              line.endsWith(" " + header)
          ))
      ) {
        // This is likely a section header
        headers.push({
          name: line.replace(":", "").trim(),
          index: i,
        });
      }
    }

    return headers;
  }

  /**
   * Find the education section in the resume
   */
  private findEducationSection(lines: string[]): string[] {
    // Look for education section headers
    const educationHeaders = [
      "education",
      "academic background",
      "academic history",
      "academic experience",
      "educational background",
      "educational history",
      "educational experience",
      "academic credentials",
      "academic qualifications",
      "academic achievements",
      "degrees",
      "qualifications",
      "academic training",
    ];

    // First try to find the section using the standard method
    const educationSection = this.findSectionByHeaders(lines, educationHeaders);

    // If we found a section, return it
    if (educationSection.length > 0) {
      return educationSection;
    }

    // If we didn't find a section, try a more aggressive approach
    // Look for lines that might indicate education
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].toLowerCase().trim();

      // Check for common education keywords
      if (
        (line.includes("university") ||
          line.includes("college") ||
          line.includes("school") ||
          line.includes("institute") ||
          line.includes("academy")) &&
        (line.includes("bachelor") ||
          line.includes("master") ||
          line.includes("ph.d") ||
          line.includes("phd") ||
          line.includes("doctorate") ||
          line.includes("b.s.") ||
          line.includes("m.s.") ||
          line.includes("b.a.") ||
          line.includes("m.a.") ||
          line.includes("mba") ||
          line.includes("degree"))
      ) {
        // This is likely the start of an education section
        // Find the end of the section
        let endIndex = i;
        for (let j = i + 1; j < lines.length; j++) {
          const nextLine = lines[j].toLowerCase().trim();

          // Check if this line might be the start of a new section
          if (
            nextLine === "experience" ||
            nextLine === "work experience" ||
            nextLine === "employment" ||
            nextLine === "skills" ||
            nextLine === "projects" ||
            nextLine === "publications" ||
            nextLine === "certifications" ||
            nextLine === "languages" ||
            nextLine === "patents" ||
            nextLine === "achievements" ||
            nextLine === "volunteer" ||
            nextLine === "interests" ||
            nextLine === "references"
          ) {
            endIndex = j - 1;
            break;
          }

          // If we reach the end of the file, use that as the end index
          if (j === lines.length - 1) {
            endIndex = j;
          }
        }

        // Return the education section
        return lines.slice(i, endIndex + 1);
      }
    }

    return [];
  }

  /**
   * Find the experience section in the resume
   */
  private findExperienceSection(lines: string[]): string[] {
    // Look for experience section headers
    const experienceHeaders = [
      "experience",
      "work experience",
      "professional experience",
      "employment history",
      "work history",
      "career history",
      "professional history",
      "employment experience",
      "professional background",
      "career experience",
      "relevant experience",
      "work background",
      "professional summary",
      "career summary",
    ];

    // First try to find the section using the standard method
    const experienceSection = this.findSectionByHeaders(
      lines,
      experienceHeaders
    );

    // If we found a section, return it
    if (experienceSection.length > 0) {
      return experienceSection;
    }

    // If we didn't find a section, try a more aggressive approach
    // Look for lines that might indicate work experience
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].toLowerCase().trim();

      // Check for job titles followed by company names
      if (
        (line.includes("senior") ||
          line.includes("junior") ||
          line.includes("lead") ||
          line.includes("principal") ||
          line.includes("engineer") ||
          line.includes("developer") ||
          line.includes("manager") ||
          line.includes("director") ||
          line.includes("analyst") ||
          line.includes("consultant") ||
          line.includes("specialist") ||
          line.includes("architect")) &&
        i + 1 < lines.length &&
        (lines[i + 1].includes(",") ||
          lines[i + 1].includes("Inc") ||
          lines[i + 1].includes("LLC") ||
          lines[i + 1].includes("Ltd") ||
          lines[i + 1].includes("Corp") ||
          lines[i + 1].includes("Company") ||
          lines[i + 1].includes("Technologies") ||
          lines[i + 1].includes("Solutions"))
      ) {
        // This is likely the start of a work experience section
        // Find the end of the section
        let endIndex = i;
        for (let j = i + 1; j < lines.length; j++) {
          const nextLine = lines[j].toLowerCase().trim();

          // Check if this line might be the start of a new section
          if (
            nextLine === "education" ||
            nextLine === "skills" ||
            nextLine === "projects" ||
            nextLine === "publications" ||
            nextLine === "certifications" ||
            nextLine === "languages" ||
            nextLine === "patents" ||
            nextLine === "achievements" ||
            nextLine === "volunteer" ||
            nextLine === "interests" ||
            nextLine === "references"
          ) {
            endIndex = j - 1;
            break;
          }

          // If we reach the end of the file, use that as the end index
          if (j === lines.length - 1) {
            endIndex = j;
          }
        }

        // Return the work experience section
        return lines.slice(i, endIndex + 1);
      }

      // Also check for lines with dates that might indicate work experience
      if (
        (line.match(/\b(19|20)\d{2}\b/) ||
          line.includes("present") ||
          line.includes("current")) &&
        (line.includes("-") || line.includes("–") || line.includes("to")) &&
        !(
          line.includes("education") ||
          line.includes("university") ||
          line.includes("college") ||
          line.includes("school") ||
          line.includes("degree")
        )
      ) {
        // This is likely a date range in a work experience section
        // Look backward to find the start of the section
        let startIndex = i;
        for (let j = i - 1; j >= 0; j--) {
          const prevLine = lines[j].toLowerCase().trim();

          // Check if this line might be the start of the work experience section
          if (
            prevLine === "experience" ||
            prevLine === "work experience" ||
            prevLine === "professional experience" ||
            prevLine === "employment history" ||
            prevLine === "work history" ||
            prevLine === "career history"
          ) {
            startIndex = j;
            break;
          }

          // If we reach the start of the file or another section, stop
          if (
            j === 0 ||
            prevLine === "education" ||
            prevLine === "skills" ||
            prevLine === "projects" ||
            prevLine === "publications" ||
            prevLine === "certifications" ||
            prevLine === "languages" ||
            prevLine === "patents" ||
            prevLine === "achievements" ||
            prevLine === "volunteer" ||
            prevLine === "interests" ||
            prevLine === "references"
          ) {
            startIndex = j + 1;
            break;
          }
        }

        // Find the end of the section
        let endIndex = i;
        for (let j = i + 1; j < lines.length; j++) {
          const nextLine = lines[j].toLowerCase().trim();

          // Check if this line might be the start of a new section
          if (
            nextLine === "education" ||
            nextLine === "skills" ||
            nextLine === "projects" ||
            nextLine === "publications" ||
            nextLine === "certifications" ||
            nextLine === "languages" ||
            nextLine === "patents" ||
            nextLine === "achievements" ||
            nextLine === "volunteer" ||
            nextLine === "interests" ||
            nextLine === "references"
          ) {
            endIndex = j - 1;
            break;
          }

          // If we reach the end of the file, use that as the end index
          if (j === lines.length - 1) {
            endIndex = j;
          }
        }

        // Return the work experience section
        return lines.slice(startIndex, endIndex + 1);
      }
    }

    return [];
  }

  /**
   * Find the skills section in the resume
   */
  private findSkillsSection(lines: string[]): string[] {
    // Look for skills section headers
    const skillsHeaders = [
      "skills",
      "technical skills",
      "core skills",
      "key skills",
      "professional skills",
      "competencies",
      "areas of expertise",
      "areas of experience",
      "areas of knowledge",
      "skill set",
      "skillset",
      "expertise",
      "proficiencies",
      "qualifications",
      "technical proficiencies",
      "technical qualifications",
      "technical expertise",
      "technologies",
      "tools",
      "languages",
      "frameworks",
      "platforms",
      "software",
      "hardware",
      "systems",
      "applications",
      "programming languages",
    ];

    return this.findSectionByHeaders(lines, skillsHeaders);
  }

  /**
   * Find the publications section in the resume
   */
  private findPublicationsSection(lines: string[]): string[] {
    // Look for publications section headers
    const publicationsHeaders = [
      "publications",
      "published works",
      "published papers",
      "published articles",
      "research publications",
      "academic publications",
      "journal publications",
      "conference publications",
      "papers",
      "articles",
      "research papers",
      "research articles",
      "scholarly works",
      "scholarly publications",
      "books",
      "book chapters",
    ];

    // First try to find the section using the standard method
    const publicationsSection = this.findSectionByHeaders(
      lines,
      publicationsHeaders
    );

    // If we found a section, return it
    if (publicationsSection.length > 0) {
      return publicationsSection;
    }

    // If we didn't find a section, try a more aggressive approach
    // Look for lines that might indicate publications
    let startIndex = -1;
    let endIndex = -1;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].toLowerCase().trim();

      // Check if this line contains a publication indicator
      if (
        line === "publications" ||
        line.includes("peer-reviewed") ||
        line.includes("journal articles") ||
        line.includes("conference proceedings") ||
        line.includes("workshop papers") ||
        line.includes("preprints")
      ) {
        startIndex = i;
        break;
      }
    }

    // If we found a start index, look for the end of the section
    if (startIndex >= 0) {
      for (let i = startIndex + 1; i < lines.length; i++) {
        const line = lines[i].toLowerCase().trim();

        // Check if this line might be the start of a new section
        if (
          line === "presentations" ||
          line === "presentations & talks" ||
          line === "research projects" ||
          line === "grants" ||
          line === "grants & funding" ||
          line === "awards" ||
          line === "awards & honors" ||
          line === "professional service" ||
          line === "teaching" ||
          line === "teaching experience" ||
          line === "languages" ||
          line === "volunteer" ||
          line === "community involvement" ||
          line === "volunteer & community involvement" ||
          line === "patents" ||
          line.includes("patent")
        ) {
          endIndex = i - 1;
          break;
        }
      }

      // If we didn't find an end index, use the end of the file
      if (endIndex < 0) {
        endIndex = lines.length - 1;
      }

      // Return the section
      return lines.slice(startIndex, endIndex + 1);
    }

    // If we still didn't find a section, return an empty array
    return [];
  }

  /**
   * Find the projects section in the resume
   */
  private findProjectsSection(lines: string[]): string[] {
    // Look for projects section headers
    const projectsHeaders = [
      "projects",
      "project experience",
      "personal projects",
      "professional projects",
      "academic projects",
      "research projects",
      "side projects",
      "portfolio",
      "portfolio projects",
      "project portfolio",
      "key projects",
      "notable projects",
      "selected projects",
      "featured projects",
      "project highlights",
      "project work",
    ];

    return this.findSectionByHeaders(lines, projectsHeaders);
  }

  /**
   * Find the certifications section in the resume
   */
  private findCertificationsSection(lines: string[]): string[] {
    // Look for certifications section headers
    const certificationsHeaders = [
      "certifications",
      "certificates",
      "professional certifications",
      "technical certifications",
      "industry certifications",
      "credentials",
      "professional credentials",
      "licenses",
      "professional licenses",
      "accreditations",
      "professional development",
      "training",
      "professional training",
      "courses",
      "completed courses",
      "relevant courses",
    ];

    return this.findSectionByHeaders(lines, certificationsHeaders);
  }

  /**
   * Find the languages section in the resume
   */
  private findLanguagesSection(lines: string[]): string[] {
    // Look for languages section headers
    const languagesHeaders = [
      "languages",
      "language proficiency",
      "language skills",
      "foreign languages",
      "spoken languages",
      "language competencies",
      "linguistic skills",
      "multilingual skills",
      "language abilities",
      "language fluency",
    ];

    // First try to find the section using the standard method
    const languagesSection = this.findSectionByHeaders(lines, languagesHeaders);

    // If we found a section, return it
    if (languagesSection.length > 0) {
      return languagesSection;
    }

    // If we didn't find a section, try a more aggressive approach
    // Look for lines that might indicate languages
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim().toUpperCase();

      // Check if this line is exactly "LANGUAGES"
      if (line === "LANGUAGES") {
        // This is likely the start of a languages section
        // Find the end of the section
        let endIndex = i;
        for (let j = i + 1; j < lines.length; j++) {
          const nextLine = lines[j].trim().toUpperCase();

          // Check if this line might be the start of a new section
          if (
            nextLine === "EDUCATION" ||
            nextLine === "EXPERIENCE" ||
            nextLine === "SKILLS" ||
            nextLine === "PROJECTS" ||
            nextLine === "PUBLICATIONS" ||
            nextLine === "CERTIFICATIONS" ||
            nextLine === "PATENTS" ||
            nextLine === "ACHIEVEMENTS" ||
            nextLine === "VOLUNTEER" ||
            nextLine === "INTERESTS" ||
            nextLine === "REFERENCES"
          ) {
            endIndex = j - 1;
            break;
          }

          // If we reach the end of the file, use that as the end index
          if (j === lines.length - 1) {
            endIndex = j;
          }
        }

        // Return the languages section
        return lines.slice(i, endIndex + 1);
      }
    }

    // If we still didn't find a section, look for language patterns in the content
    const humanLanguages = [
      "english",
      "spanish",
      "french",
      "german",
      "italian",
      "chinese",
      "japanese",
      "russian",
      "arabic",
      "portuguese",
      "hindi",
      "bengali",
      "mandarin",
      "cantonese",
    ];

    // Look for lines that contain human languages
    const languageLines = [];
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim().toLowerCase();

      // Check if this line contains a human language
      if (humanLanguages.some((lang) => line.includes(lang))) {
        // Check if this line also contains a proficiency level
        if (
          line.includes("native") ||
          line.includes("fluent") ||
          line.includes("proficient") ||
          line.includes("advanced") ||
          line.includes("intermediate") ||
          line.includes("beginner") ||
          line.includes("basic") ||
          line.includes("elementary") ||
          line.includes("working") ||
          line.includes("professional") ||
          line.includes("conversational")
        ) {
          languageLines.push(lines[i]);
        }
      }
    }

    if (languageLines.length > 0) {
      return languageLines;
    }

    return [];
  }

  /**
   * Find the patents section in the resume
   */
  private findPatentsSection(lines: string[]): string[] {
    // Look for patents section headers
    const patentsHeaders = [
      "patents",
      "patent applications",
      "patent filings",
      "patent portfolio",
      "intellectual property",
      "ip portfolio",
      "inventions",
      "registered patents",
      "granted patents",
      "pending patents",
    ];

    // First try to find the section using the standard method
    const patentsSection = this.findSectionByHeaders(lines, patentsHeaders);

    // If we found a section, return it
    if (patentsSection.length > 0) {
      return patentsSection;
    }

    // If we didn't find a section, try a more aggressive approach
    // Look for lines that might indicate patents
    let startIndex = -1;
    let endIndex = -1;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].toLowerCase().trim();

      // Check if this line contains a patent indicator
      if (
        line === "patents" ||
        line.includes("patent no") ||
        line.includes("patent application") ||
        line.includes("u.s. patent")
      ) {
        startIndex = i;
        break;
      }
    }

    // If we found a start index, look for the end of the section
    if (startIndex >= 0) {
      for (let i = startIndex + 1; i < lines.length; i++) {
        const line = lines[i].toLowerCase().trim();

        // Check if this line might be the start of a new section
        if (
          line === "publications" ||
          line === "presentations" ||
          line === "research projects" ||
          line === "grants" ||
          line === "awards" ||
          line === "professional service" ||
          line === "teaching" ||
          line === "languages" ||
          line === "volunteer" ||
          line === "skills" ||
          line === "education" ||
          line === "experience" ||
          line === "work experience"
        ) {
          endIndex = i - 1;
          break;
        }
      }

      // If we didn't find an end index, use the end of the file
      if (endIndex < 0) {
        endIndex = lines.length - 1;
      }

      // Return the section
      return lines.slice(startIndex, endIndex + 1);
    }

    // If we still didn't find a section, return an empty array
    return [];
  }

  /**
   * Find the achievements section in the resume
   */
  private findAchievementsSection(lines: string[]): string[] {
    // Look for achievements section headers
    const achievementsHeaders = [
      "achievements",
      "accomplishments",
      "awards",
      "honors",
      "recognitions",
      "accolades",
      "distinctions",
      "achievements & awards",
      "awards & honors",
      "honors & awards",
      "achievements & recognitions",
      "recognitions & awards",
      "notable achievements",
      "key achievements",
      "significant achievements",
      "major accomplishments",
      "professional achievements",
      "academic achievements",
      "personal achievements",
    ];

    return this.findSectionByHeaders(lines, achievementsHeaders);
  }

  /**
   * Find the volunteer section in the resume
   */
  private findVolunteerSection(lines: string[]): string[] {
    // Look for volunteer section headers
    const volunteerHeaders = [
      "volunteer",
      "volunteering",
      "volunteer experience",
      "volunteer work",
      "community service",
      "community involvement",
      "community engagement",
      "community outreach",
      "civic engagement",
      "social responsibility",
      "pro bono work",
      "philanthropy",
      "charitable work",
      "non-profit experience",
      "volunteer & community involvement",
      "community & volunteer work",
    ];

    return this.findSectionByHeaders(lines, volunteerHeaders);
  }

  /**
   * Find the interests section in the resume
   */
  private findInterestsSection(lines: string[]): string[] {
    // Look for interests section headers
    const interestsHeaders = [
      "interests",
      "personal interests",
      "hobbies",
      "hobbies & interests",
      "interests & activities",
      "activities",
      "personal activities",
      "extracurricular activities",
      "extracurricular",
      "pastimes",
      "leisure activities",
      "recreational activities",
      "personal pursuits",
    ];

    return this.findSectionByHeaders(lines, interestsHeaders);
  }

  /**
   * Find the references section in the resume
   */
  private findReferencesSection(lines: string[]): string[] {
    // Look for references section headers
    const referencesHeaders = [
      "references",
      "professional references",
      "personal references",
      "character references",
      "academic references",
      "reference list",
      "references available",
      "references upon request",
      "recommendations",
      "professional recommendations",
      "endorsements",
      "professional endorsements",
    ];

    return this.findSectionByHeaders(lines, referencesHeaders);
  }

  /**
   * Find a section by its headers
   */
  private findSectionByHeaders(lines: string[], headers: string[]): string[] {
    const sectionLines: string[] = [];
    let inSection = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].toLowerCase();

      // Check if this line contains a section header
      const isHeader = headers.some(
        (header) =>
          line.includes(header) ||
          line === header ||
          line.startsWith(header + ":") ||
          line.startsWith(header + " ")
      );

      if (isHeader) {
        inSection = true;
        // Include the header line in the section
        sectionLines.push(lines[i]);
        continue;
      }

      // Check if we've reached the next section
      if (inSection) {
        // Check if this line might be the start of another section
        const isPotentialNewSection =
          line.length < 30 &&
          (line === line.toUpperCase() ||
            line.endsWith(":") ||
            /^[A-Z][a-z]+(\s+[A-Z][a-z]+)*$/.test(line));

        if (isPotentialNewSection && sectionLines.length > 0) {
          break;
        }

        sectionLines.push(lines[i]);
      }
    }

    return sectionLines;
  }

  /**
   * Extract basic information from the profile section with improved accuracy
   * @param profileLines Lines from the profile section
   * @param fullText The full text of the resume
   * @returns Basic information
   */
  private extractBasicInfo(profileLines: string[], fullText: string) {
    const result = {
      name: null as string | null,
      email: null as string | null,
      phone: null as string | null,
      location: null as string | null,
      summary: null as string | null,
      url: null as string | null,
    };

    // Extract name - look for a name pattern in the first few lines
    if (profileLines.length > 0) {
      // Try to find a name in the first 3 lines
      for (let i = 0; i < Math.min(3, profileLines.length); i++) {
        const line = profileLines[i].trim();
        // Names are typically short (1-3 words) and don't contain special characters
        if (
          line &&
          line.split(" ").length <= 4 &&
          !line.includes("@") &&
          !line.includes("http") &&
          !line.includes("%PDF") && // Not a PDF header
          !/^\d+/.test(line) && // Doesn't start with a number
          line.length > 3 &&
          line.length < 40 &&
          !line.match(/^[A-Z][a-z]+,\s*[A-Z]{2}/) // Not a location
        ) {
          result.name = line;
          break;
        }
      }

      // If no name found, use the first non-empty line
      if (!result.name) {
        for (const line of profileLines) {
          if (
            line.trim() &&
            !line.includes("@") &&
            !line.includes("http") &&
            !line.includes("%PDF") && // Not a PDF header
            !line.match(/^\d{3}[-.\s]?\d{3}[-.\s]?\d{4}$/) &&
            !line.match(/^[A-Z][a-z]+,\s*[A-Z]{2}/) && // Not a location
            !line.match(/^(stream|endstream|obj|endobj|xref|trailer|startxref)/) // Not PDF artifacts
          ) {
            result.name = line.trim();
            break;
          }
        }
      }

      // If we still have a PDF header in the name, set it to null
      if (
        result.name &&
        (result.name.includes("%PDF") ||
          result.name.match(
            /^(stream|endstream|obj|endobj|xref|trailer|startxref)/
          ))
      ) {
        result.name = null;
      }
    }

    // Extract email - look for email pattern in the entire text
    const emailMatches = fullText.match(this.patterns.email);
    if (emailMatches && emailMatches.length > 0) {
      result.email = emailMatches[0];
    }

    // Extract phone - look for phone pattern in the entire text
    // Improved phone extraction with better pattern matching
    const phoneMatches = fullText.match(this.patterns.phone);
    if (phoneMatches && phoneMatches.length > 0) {
      // Clean up the phone number
      let phone = phoneMatches[0].replace(/[^+0-9]/g, "");

      // Format the phone number
      if (phone.length === 10) {
        // US format: (XXX) XXX-XXXX
        phone = `(${phone.substring(0, 3)}) ${phone.substring(3, 6)}-${phone.substring(6)}`;
      } else if (phone.startsWith("+")) {
        // International format: keep as is
      } else if (phone.length === 11 && phone.startsWith("1")) {
        // US with country code: +1 (XXX) XXX-XXXX
        phone = `+1 (${phone.substring(1, 4)}) ${phone.substring(4, 7)}-${phone.substring(7)}`;
      }

      result.phone = phone;
    }

    // Extract URL - first check for LinkedIn or GitHub URLs
    const urlMatches = fullText.match(this.patterns.url);
    if (urlMatches && urlMatches.length > 0) {
      // Filter out email addresses which can be matched as URLs
      // Also filter out PDF artifacts
      const urls = urlMatches.filter(
        (url) =>
          !url.includes("@") &&
          !url.includes("%PDF") &&
          !url.match(/^(stream|endstream|obj|endobj|xref|trailer|startxref)/)
      );

      if (urls.length > 0) {
        // Prefer GitHub or LinkedIn URLs
        const githubUrl = urls.find((url) => url.includes("github.com"));
        const linkedinUrl = urls.find((url) => url.includes("linkedin.com"));
        result.url = githubUrl ?? linkedinUrl ?? urls[0];

        // Add https:// if missing
        if (result.url && !result.url.startsWith("http")) {
          result.url = "https://" + result.url;
        }

        // If URL still contains PDF artifacts, set it to null
        if (
          result.url &&
          (result.url.includes("%PDF") ||
            result.url.match(
              /^(stream|endstream|obj|endobj|xref|trailer|startxref)/
            ))
        ) {
          result.url = null;
        }
      }
    }

    // Extract location - improved location extraction
    // First try to find a line that looks like an address
    const addressPattern =
      /([A-Z][a-zA-Z\s]+,\s*[A-Z]{2}\s*\d{5}|[A-Z][a-zA-Z\s]+,\s*[A-Z][a-zA-Z\s]+)/g;
    const addressMatches = fullText.match(addressPattern);

    if (addressMatches && addressMatches.length > 0) {
      result.location = addressMatches[0];
    } else {
      // Fall back to the location pattern
      const locationMatches = fullText.match(this.patterns.location);
      if (locationMatches && locationMatches.length > 0) {
        // Filter out false positives that might be skills or other non-location text
        const validLocations = locationMatches.filter((loc) => {
          // Exclude locations that are likely to be skills or technologies
          const invalidTerms = [
            "mysql",
            "oracle",
            "sql",
            "java",
            "python",
            "react",
            "angular",
            "vue",
            "node",
            "https",
          ];
          return !invalidTerms.some((term) => loc.toLowerCase().includes(term));
        });

        if (validLocations.length > 0) {
          result.location = validLocations[0];
        } else {
          // If we still didn't find a location, look for a line that might contain a location
          for (const line of profileLines) {
            if (
              line.includes(",") &&
              !line.includes("@") &&
              !line.includes("http") &&
              !line.includes("www") &&
              line.length < 50
            ) {
              // Make sure it's not a name with a title
              if (
                !line.toLowerCase().includes("senior") &&
                !line.toLowerCase().includes("junior") &&
                !line.toLowerCase().includes("lead") &&
                !line.toLowerCase().includes("principal") &&
                !line.toLowerCase().includes("engineer") &&
                !line.toLowerCase().includes("developer") &&
                !line.toLowerCase().includes("manager") &&
                !line.toLowerCase().includes("director") &&
                !line.toLowerCase().includes("analyst") &&
                !line.toLowerCase().includes("consultant") &&
                !line.toLowerCase().includes("specialist") &&
                !line.toLowerCase().includes("architect")
              ) {
                result.location = line.trim();
                break;
              }
            }
          }
        }
      }
    }

    // Clean up location - remove any URLs or email addresses
    if (result.location) {
      result.location = result.location
        .replace(/https?:\/\/[^\s]+/g, "")
        .trim();
      result.location = result.location
        .replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, "")
        .trim();

      // If location still contains "https", it's probably not a valid location
      if (result.location.includes("https")) {
        result.location = null;
      }
    }

    // Extract summary - look for a paragraph that appears to be a summary
    const summaryLabels = [
      "summary",
      "professional summary",
      "career objective",
      "objective",
      "profile",
    ];
    let summaryFound = false;

    for (let i = 0; i < profileLines.length; i++) {
      const line = profileLines[i].toLowerCase();

      // Check if this line contains a summary label
      if (summaryLabels.some((label) => line.includes(label))) {
        // The summary is likely to be the next few lines
        const summaryLines = [];
        for (let j = i + 1; j < Math.min(i + 6, profileLines.length); j++) {
          if (profileLines[j].trim()) {
            summaryLines.push(profileLines[j].trim());
          }
        }

        if (summaryLines.length > 0) {
          result.summary = summaryLines.join(" ");
          summaryFound = true;
          break;
        }
      }
    }

    // If no summary found, use the longest paragraph in the profile section
    if (!summaryFound && profileLines.length > 3) {
      let longestParagraph = "";
      let currentParagraph = "";

      for (const line of profileLines) {
        if (line.trim()) {
          currentParagraph += " " + line.trim();
        } else if (currentParagraph) {
          // End of paragraph
          if (currentParagraph.length > longestParagraph.length) {
            longestParagraph = currentParagraph;
          }
          currentParagraph = "";
        }
      }

      // Check the last paragraph
      if (currentParagraph.length > longestParagraph.length) {
        longestParagraph = currentParagraph;
      }

      if (longestParagraph && longestParagraph.length > 50) {
        result.summary = longestParagraph.trim();
      } else if (profileLines.length > 3) {
        // Fallback to joining all lines after the first few
        const usedLines = new Set<string>();
        if (result.name) usedLines.add(result.name);
        if (result.email) usedLines.add(result.email);
        if (result.phone) usedLines.add(result.phone);
        if (result.location) usedLines.add(result.location);
        if (result.url) usedLines.add(result.url);

        const summaryLines = [];
        for (const line of profileLines.slice(3)) {
          if (
            line.trim() &&
            !usedLines.has(line.trim()) &&
            !line.includes("@") &&
            !line.includes("http") &&
            !line.includes("www") &&
            !line.match(/^\d{3}[-.\s]?\d{3}[-.\s]?\d{4}$/)
          ) {
            summaryLines.push(line.trim());

            // Only use the first 3 lines for the summary
            if (summaryLines.length >= 3) {
              break;
            }
          }
        }

        if (summaryLines.length > 0) {
          result.summary = summaryLines.join(" ");
        }
      }
    }

    // If summary is too long (contains the entire resume), truncate it
    if (result.summary && result.summary.length > 500) {
      result.summary = result.summary.substring(0, 497) + "...";
    }

    return result;
  }

  /**
   * Extract education information with improved accuracy
   * @param educationLines Lines from the education section
   * @returns Education entries
   */
  private extractEducation(educationLines: string[]) {
    const education = [];
    let current: any = {};
    let inEducationEntry = false;

    // Process each line
    for (let i = 0; i < educationLines.length; i++) {
      const line = educationLines[i].trim();
      if (!line) continue;

      // Check for institution name
      const institutionMatch = line.match(this.patterns.institution);
      if (institutionMatch) {
        // If we already have a current education entry, save it
        if (current.school) {
          education.push(current);
        }

        // Start a new education entry
        current = { school: this.extractFullSchoolName(line) };
        inEducationEntry = true;

        // Check if this line also contains degree information
        const degreeMatch = line.match(this.patterns.degree);
        if (degreeMatch) {
          current.degree = this.extractFullDegree(line);
        }

        // Check if this line also contains date information
        const dateMatch = line.match(this.patterns.date);
        if (dateMatch) {
          current.date = dateMatch[0];
        }
      }
      // Check for degree information
      else if (inEducationEntry && !current.degree) {
        const degreeMatch = line.match(this.patterns.degree);
        if (degreeMatch) {
          current.degree = this.extractFullDegree(line);
        }
      }
      // Check for date information
      else if (inEducationEntry && !current.date) {
        const dateMatch = line.match(this.patterns.date);
        if (dateMatch) {
          current.date = dateMatch[0];
        }
      }
      // Check for GPA information
      else if (
        inEducationEntry &&
        !current.gpa &&
        line.toLowerCase().includes("gpa")
      ) {
        const gpaMatch = line.match(
          /gpa\s*(?:of|:)?\s*([0-9](?:\.[0-9]+)?)\s*(?:\/|\sout\sof\s)\s*([0-9](?:\.[0-9]+)?)/i
        );
        if (gpaMatch) {
          current.gpa = `${gpaMatch[1]}/${gpaMatch[2]}`;
        } else {
          const simpleGpaMatch = line.match(
            /gpa\s*(?:of|:)?\s*([0-9](?:\.[0-9]+)?)/i
          );
          if (simpleGpaMatch) {
            current.gpa = simpleGpaMatch[1];
          }
        }
      }
      // Check for bullet points or additional information
      else if (inEducationEntry && line.match(/^[\s]*[•\-\*][\s]*/)) {
        // This is a bullet point - add to descriptions
        current.descriptions = current.descriptions || [];
        current.descriptions.push(
          line.replace(/^[\s]*[•\-\*][\s]*/, "").trim()
        );
      }
      // If we're in an education entry but the line doesn't match any specific pattern,
      // it might be additional information about the education
      else if (inEducationEntry) {
        if (!current.additionalInfo) {
          current.additionalInfo = line;
        } else {
          current.additionalInfo += " " + line;
        }
      }
    }

    // Add the last education entry
    if (current.school) {
      education.push(current);
    }

    return education;
  }

  /**
   * Extract the full school name from a line
   * @param line The line to extract from
   * @returns The full school name
   */
  private extractFullSchoolName(line: string): string {
    // Check for patterns like "University of X" or "X University"
    const universityMatch = line.match(
      /([A-Z][a-zA-Z\s]+University|University\s+of\s+[A-Z][a-zA-Z\s]+|[A-Z][a-zA-Z\s]+\s+College|College\s+of\s+[A-Z][a-zA-Z\s]+|[A-Z][a-zA-Z\s]+\s+Institute|Institute\s+of\s+[A-Z][a-zA-Z\s]+)/i
    );
    if (universityMatch) {
      return universityMatch[0];
    }

    // Check for full school name with parentheses
    const parenthesesMatch = line.match(/([A-Z][a-zA-Z\s]+)\s*\(/i);
    if (parenthesesMatch) {
      return parenthesesMatch[1].trim();
    }

    // Check for full school name before a year or date
    const beforeDateMatch = line.match(
      /([A-Z][a-zA-Z\s,]+)\s+(?:20\d{2}|19\d{2}|\d{1,2}\/\d{1,2}\/\d{2,4})/i
    );
    if (beforeDateMatch) {
      return beforeDateMatch[1].trim();
    }

    // If we couldn't extract the full name, return the line
    return line;
  }

  /**
   * Extract the full degree from a line
   * @param line The line to extract from
   * @returns The full degree
   */
  private extractFullDegree(line: string): string {
    // Check for full degree pattern like "Bachelor of Science in Computer Science"
    const fullDegreeMatch = line.match(
      /(?:Bachelor|Master|PhD|Doctorate|Associate)\s+(?:of|in)\s+[A-Z][a-zA-Z\s]+(?:\s+in\s+[A-Z][a-zA-Z\s]+)?/i
    );
    if (fullDegreeMatch) {
      return fullDegreeMatch[0];
    }

    // Check for "Bachelor's Degree in X" pattern
    const bachelorsDegreeMatch = line.match(
      /(?:Bachelor's|Master's|Doctoral)\s+Degree\s+in\s+[A-Z][a-zA-Z\s]+/i
    );
    if (bachelorsDegreeMatch) {
      return bachelorsDegreeMatch[0];
    }

    // Check for abbreviated degree patterns
    const degreeMatch = line.match(this.patterns.degree);
    if (degreeMatch) {
      // Try to extract the full degree (e.g., "B.S. in Computer Science")
      const degreeStart = line.indexOf(degreeMatch[0]);
      if (degreeStart >= 0) {
        // Look for the end of the degree (usually ends with a comma, period, or the end of the line)
        const degreeEnd = line.indexOf(",", degreeStart);
        if (degreeEnd > degreeStart) {
          return line.substring(degreeStart, degreeEnd).trim();
        }

        // If no comma, try to find a period
        const periodEnd = line.indexOf(
          ".",
          degreeStart + degreeMatch[0].length
        );
        if (periodEnd > degreeStart) {
          return line.substring(degreeStart, periodEnd + 1).trim();
        }

        // If no punctuation, return the rest of the line
        return line.substring(degreeStart).trim();
      }

      return degreeMatch[0];
    }

    return line;
  }

  /**
   * Extract work experience information with improved accuracy
   * @param experienceLines Lines from the experience section
   * @returns Work experience entries
   */
  private extractWorkExperience(experienceLines: string[]) {
    const experiences = [];
    let current: any = {};
    let inExperienceEntry = false;

    // Process each line
    for (let i = 0; i < experienceLines.length; i++) {
      const line = experienceLines[i].trim();
      if (!line) continue;

      // Check for job title
      const titleMatch = line.match(this.patterns.jobTitle);
      if (titleMatch) {
        // If we already have a current experience entry, save it
        if (current.title) {
          experiences.push(current);
        }

        // Start a new experience entry
        current = { title: this.extractFullJobTitle(line) };
        inExperienceEntry = true;

        // Check if this line also contains company information
        if (line.toLowerCase().includes(" at ")) {
          const parts = line.split(/\\s+at\\s+/i);
          if (parts.length >= 2) {
            current.company = parts[1].trim();
          }
        }

        // Check if this line also contains date information
        const dateMatch = line.match(this.patterns.date);
        if (dateMatch) {
          current.date = dateMatch[0];
        }
      }
      // Check for company information
      else if (inExperienceEntry && !current.company) {
        const companyMatch = line.match(this.patterns.company);
        if (companyMatch) {
          current.company = this.extractFullCompanyName(line);

          // Check if this line also contains location information
          const locationMatch = line.match(this.patterns.location);
          if (locationMatch) {
            current.location = locationMatch[0];
          }
        }
      }
      // Check for date information
      else if (inExperienceEntry && !current.date) {
        const dateMatch = line.match(this.patterns.date);
        if (dateMatch) {
          current.date = dateMatch[0];
        }
      }
      // Check for location information
      else if (inExperienceEntry && !current.location) {
        const locationMatch = line.match(this.patterns.location);
        if (locationMatch) {
          current.location = locationMatch[0];
        }
      }
      // Check for bullet points or responsibilities
      else if (inExperienceEntry && line.match(/^[\s]*[•\-\*][\s]*/)) {
        // This is a bullet point - add to descriptions
        current.descriptions = current.descriptions || [];
        current.descriptions.push(
          line.replace(/^[\s]*[•\-\*][\s]*/, "").trim()
        );
      }
      // If we're in an experience entry but the line doesn't match any specific pattern,
      // it might be additional information about the experience
      else if (inExperienceEntry) {
        // Check if this might be the start of a new entry
        const nextLineIsTitle =
          i < experienceLines.length - 1 &&
          experienceLines[i + 1].match(this.patterns.jobTitle);

        if (nextLineIsTitle) {
          // This line probably belongs to the current entry
          if (!current.additionalInfo) {
            current.additionalInfo = line;
          } else {
            current.additionalInfo += " " + line;
          }
        } else {
          // This might be a continuation of the current entry
          if (!current.descriptions) {
            current.descriptions = [line];
          } else {
            current.descriptions.push(line);
          }
        }
      }
    }

    // Add the last experience entry
    if (current.title) {
      experiences.push(current);
    }

    return experiences;
  }

  /**
   * Extract the full job title from a line
   * @param line The line to extract from
   * @returns The full job title
   */
  private extractFullJobTitle(line: string): string {
    // Check for job title pattern
    const titleMatch = line.match(this.patterns.jobTitle);
    if (titleMatch) {
      // Check if the title is followed by "at" or a company name
      const atIndex = line.toLowerCase().indexOf(" at ");
      if (atIndex > 0) {
        return line.substring(0, atIndex).trim();
      }

      // Check if the title is followed by a date
      const dateMatch = line.match(this.patterns.date);
      if (dateMatch) {
        const dateIndex = line.indexOf(dateMatch[0]);
        if (dateIndex > 0) {
          return line.substring(0, dateIndex).trim();
        }
      }

      // If we couldn't extract a clean title, return the match
      return titleMatch[0];
    }

    return line;
  }

  /**
   * Extract the full company name from a line
   * @param line The line to extract from
   * @returns The full company name
   */
  private extractFullCompanyName(line: string): string {
    // Check for company pattern
    const companyMatch = line.match(this.patterns.company);
    if (companyMatch) {
      // Check if the company is followed by a location
      const locationMatch = line.match(this.patterns.location);
      if (locationMatch) {
        const locationIndex = line.indexOf(locationMatch[0]);
        if (locationIndex > 0) {
          return line.substring(0, locationIndex).trim().replace(/,$/, "");
        }
      }

      // Check if the company is followed by a date
      const dateMatch = line.match(this.patterns.date);
      if (dateMatch) {
        const dateIndex = line.indexOf(dateMatch[0]);
        if (dateIndex > 0) {
          return line.substring(0, dateIndex).trim();
        }
      }

      // If the line contains a comma, it might separate company and location
      if (line.includes(",")) {
        return line.split(",")[0].trim();
      }

      // If we couldn't extract a clean company name, return the line
      return line;
    }

    return line;
  }

  /**
   * Extract skills with improved accuracy
   * @param skillsLines Lines from the skills section
   * @param fullText The full text of the resume
   * @returns Skills
   */
  private extractSkills(skillsLines: string[], fullText: string) {
    const skills: string[] = [];

    // Process each line in the skills section
    for (const line of skillsLines) {
      // Skip empty lines
      if (!line.trim()) continue;

      // Check if this is a bullet point
      if (line.match(/^[\s]*[•\-\*][\s]*/)) {
        // This is a bullet point - extract the skill
        const skill = line.replace(/^[\s]*[•\-\*][\s]*/, "").trim();

        // Add the skill if it's not already in the list
        if (skill && !skills.includes(skill)) {
          skills.push(skill);
        }
      }
      // Check if this line contains multiple skills separated by commas
      else if (line.includes(",")) {
        const skillList = line.split(",").map((s) => s.trim());

        // Add each skill if it's not already in the list
        for (const skill of skillList) {
          if (skill && !skills.includes(skill)) {
            skills.push(skill);
          }
        }
      }
      // Check if this line contains multiple skills separated by bullets or pipes
      else if (line.includes("•") || line.includes("|")) {
        const separator = line.includes("•") ? "•" : "|";
        const skillList = line.split(separator).map((s) => s.trim());

        // Add each skill if it's not already in the list
        for (const skill of skillList) {
          if (skill && !skills.includes(skill)) {
            skills.push(skill);
          }
        }
      }
      // If none of the above, treat the whole line as a skill
      else {
        // Add the skill if it's not already in the list
        if (line && !skills.includes(line)) {
          skills.push(line);
        }
      }
    }

    // If we couldn't extract any skills from the skills section,
    // try to extract skills from the full text using the skills pattern
    if (skills.length === 0) {
      const skillMatches = fullText.match(this.patterns.skills);
      if (skillMatches) {
        // Add each skill if it's not already in the list
        for (const skill of skillMatches) {
          const cleanSkill = skill.trim();
          if (cleanSkill && !skills.includes(cleanSkill)) {
            skills.push(cleanSkill);
          }
        }
      }
    }

    return skills;
  }

  /**
   * Extract projects from the resume
   * @param projectsLines Lines from the projects section
   * @returns Projects
   */
  private extractProjects(projectsLines: string[]) {
    const projects = [];
    let current: any = {};
    let inProjectEntry = false;

    // Process each line in the projects section
    for (const line of projectsLines) {
      if (!line.trim()) continue;

      // Check if this is a bullet point
      if (line.match(/^[\s]*[•\-\*][\s]*/)) {
        // This is a bullet point - add to descriptions
        if (inProjectEntry) {
          current.descriptions = current.descriptions || [];
          current.descriptions.push(
            line.replace(/^[\s]*[•\-\*][\s]*/, "").trim()
          );
        } else {
          // This might be a new project
          if (current.title) {
            projects.push(current);
          }
          current = {
            title: line.replace(/^[\s]*[•\-\*][\s]*/, "").trim(),
            descriptions: [],
          };
          inProjectEntry = true;
        }
      } else if (line.includes(":")) {
        // This might be a project title or detail
        const [key, value] = line.split(":", 2).map((part) => part.trim());

        if (value && key.length < 30) {
          // This is likely a key-value pair
          if (inProjectEntry) {
            // Add to current project
            if (
              key.toLowerCase().includes("date") ||
              key.toLowerCase().includes("year") ||
              key.toLowerCase().includes("duration")
            ) {
              current.date = value;
            } else if (
              key.toLowerCase().includes("tech") ||
              key.toLowerCase().includes("tool") ||
              key.toLowerCase().includes("skill")
            ) {
              current.technologies = value;
            } else if (
              key.toLowerCase().includes("link") ||
              key.toLowerCase().includes("url") ||
              key.toLowerCase().includes("github")
            ) {
              current.url = value;
            } else if (
              key.toLowerCase().includes("role") ||
              key.toLowerCase().includes("position")
            ) {
              current.role = value;
            } else {
              // Generic key-value pair
              current[key.toLowerCase()] = value;
            }
          } else {
            // Start a new project
            if (current.title) {
              projects.push(current);
            }
            current = { title: key };

            if (value) {
              if (key.toLowerCase().includes("project")) {
                current.title = value;
              } else {
                current.description = value;
              }
            }

            inProjectEntry = true;
          }
        } else {
          // This is likely a project title
          if (current.title) {
            projects.push(current);
          }
          current = { title: line.trim() };
          inProjectEntry = true;
        }
      } else if (line.match(/\b(19|20)\d{2}\b/) && !current.date) {
        // This line contains a year, likely a date
        current.date = line.trim();
      } else if (inProjectEntry) {
        // This is likely a description or continuation
        if (!current.description) {
          current.description = line.trim();
        } else {
          current.description += " " + line.trim();
        }
      } else {
        // This might be a new project title
        if (current.title) {
          projects.push(current);
        }
        current = { title: line.trim() };
        inProjectEntry = true;
      }
    }

    // Add the last project
    if (current.title) {
      projects.push(current);
    }

    return projects;
  }

  /**
   * Extract certifications from the resume
   * @param certificationsLines Lines from the certifications section
   * @returns Certifications
   */
  private extractCertifications(certificationsLines: string[]) {
    const certifications = [];
    let current: any = {};
    let inCertificationEntry = false;

    // Process each line in the certifications section
    for (const line of certificationsLines) {
      if (!line.trim()) continue;

      // Check if this is a bullet point
      if (line.match(/^[\s]*[•\-\*][\s]*/)) {
        // This is a bullet point - likely a new certification
        if (current.name) {
          certifications.push(current);
        }
        current = {
          name: line.replace(/^[\s]*[•\-\*][\s]*/, "").trim(),
        };
        inCertificationEntry = true;
      } else if (line.includes(":")) {
        // This might be a certification detail
        const [key, value] = line.split(":", 2).map((part) => part.trim());

        if (value && key.length < 30) {
          // This is likely a key-value pair
          if (inCertificationEntry) {
            // Add to current certification
            if (
              key.toLowerCase().includes("date") ||
              key.toLowerCase().includes("year") ||
              key.toLowerCase().includes("issued")
            ) {
              current.date = value;
            } else if (
              key.toLowerCase().includes("issuer") ||
              key.toLowerCase().includes("authority") ||
              key.toLowerCase().includes("organization")
            ) {
              current.issuer = value;
            } else if (
              key.toLowerCase().includes("id") ||
              key.toLowerCase().includes("credential") ||
              key.toLowerCase().includes("number")
            ) {
              current.id = value;
            } else if (
              key.toLowerCase().includes("expiration") ||
              key.toLowerCase().includes("expires") ||
              key.toLowerCase().includes("valid until")
            ) {
              current.expirationDate = value;
            } else {
              // Generic key-value pair
              current[key.toLowerCase()] = value;
            }
          } else {
            // Start a new certification
            if (current.name) {
              certifications.push(current);
            }
            current = { name: key };

            if (value) {
              if (
                key.toLowerCase().includes("certification") ||
                key.toLowerCase().includes("certificate")
              ) {
                current.name = value;
              } else {
                current.description = value;
              }
            }

            inCertificationEntry = true;
          }
        } else {
          // This is likely a certification name
          if (current.name) {
            certifications.push(current);
          }
          current = { name: line.trim() };
          inCertificationEntry = true;
        }
      } else if (line.match(/\b(19|20)\d{2}\b/) && !current.date) {
        // This line contains a year, likely a date
        current.date = line.trim();
      } else if (inCertificationEntry) {
        // This is likely a description or continuation
        if (!current.description) {
          current.description = line.trim();
        } else {
          current.description += " " + line.trim();
        }
      } else {
        // This might be a new certification name
        if (current.name) {
          certifications.push(current);
        }
        current = { name: line.trim() };
        inCertificationEntry = true;
      }
    }

    // Add the last certification
    if (current.name) {
      certifications.push(current);
    }

    return certifications;
  }

  /**
   * Extract languages from the resume
   * @param languagesLines Lines from the languages section
   * @returns Languages
   */
  private extractLanguages(languagesLines: string[]) {
    const languages = [];

    // List of common human languages
    const humanLanguages = [
      "english",
      "spanish",
      "french",
      "german",
      "italian",
      "portuguese",
      "dutch",
      "swedish",
      "norwegian",
      "danish",
      "finnish",
      "icelandic",
      "russian",
      "polish",
      "czech",
      "slovak",
      "hungarian",
      "romanian",
      "bulgarian",
      "greek",
      "turkish",
      "arabic",
      "hebrew",
      "persian",
      "hindi",
      "urdu",
      "bengali",
      "punjabi",
      "gujarati",
      "marathi",
      "tamil",
      "telugu",
      "kannada",
      "malayalam",
      "thai",
      "lao",
      "vietnamese",
      "khmer",
      "burmese",
      "malay",
      "indonesian",
      "tagalog",
      "filipino",
      "japanese",
      "korean",
      "chinese",
      "mandarin",
      "cantonese",
      "swahili",
      "zulu",
      "xhosa",
      "afrikaans",
      "amharic",
      "somali",
      "yoruba",
      "igbo",
      "hausa",
    ];

    // List of technology-related terms to filter out
    const techTerms = [
      "programming",
      "framework",
      "library",
      "tool",
      "database",
      "cloud",
      "devops",
      "javascript",
      "typescript",
      "python",
      "java",
      "c++",
      "c#",
      "ruby",
      "php",
      "swift",
      "kotlin",
      "go",
      "rust",
      "scala",
      "perl",
      "r",
      "matlab",
      "sql",
      "nosql",
      "mongodb",
      "mysql",
      "postgresql",
      "oracle",
      "redis",
      "cassandra",
      "dynamodb",
      "firebase",
      "aws",
      "azure",
      "gcp",
      "docker",
      "kubernetes",
      "jenkins",
      "git",
      "github",
      "gitlab",
      "bitbucket",
      "jira",
      "confluence",
      "react",
      "angular",
      "vue",
      "node",
      "express",
      "django",
      "flask",
      "spring",
      "laravel",
      "rails",
      "asp.net",
    ];

    // Process each line in the languages section
    for (const line of languagesLines) {
      if (!line.trim()) continue;

      // Skip the section header (e.g., "LANGUAGES")
      if (line.trim().toUpperCase() === "LANGUAGES") continue;

      // Skip lines that are likely about programming languages or technologies
      const lineLC = line.toLowerCase();
      if (techTerms.some((term) => lineLC.includes(term))) {
        continue;
      }

      // Check if this is a bullet point
      if (line.match(/^[\s]*[•\-\*][\s]*/)) {
        // This is a bullet point - extract the language
        const languageLine = line.replace(/^[\s]*[•\-\*][\s]*/, "").trim();

        // Check if there's a proficiency level indicated
        if (languageLine.includes(":")) {
          const [language, proficiency] = languageLine
            .split(":", 2)
            .map((part) => part.trim());

          // Only add if it's likely a human language
          if (isLikelyHumanLanguage(language, humanLanguages)) {
            languages.push({ language, proficiency });
          }
        } else if (languageLine.includes("-")) {
          const [language, proficiency] = languageLine
            .split("-", 2)
            .map((part) => part.trim());

          // Only add if it's likely a human language
          if (isLikelyHumanLanguage(language, humanLanguages)) {
            languages.push({ language, proficiency });
          }
        } else if (languageLine.includes("(")) {
          const language = languageLine.split("(")[0].trim();
          const proficiency = languageLine.match(/\((.*?)\)/)?.[1] || "";

          // Only add if it's likely a human language
          if (isLikelyHumanLanguage(language, humanLanguages)) {
            languages.push({ language, proficiency });
          }
        } else {
          // Just a language without proficiency
          if (isLikelyHumanLanguage(languageLine, humanLanguages)) {
            languages.push({ language: languageLine });
          }
        }
      } else if (line.includes(":")) {
        // This might be a language with proficiency
        const [language, proficiency] = line
          .split(":", 2)
          .map((part) => part.trim());

        // Only add if it's likely a human language
        if (isLikelyHumanLanguage(language, humanLanguages)) {
          languages.push({ language, proficiency });
        }
      } else if (line.includes("-")) {
        // This might be a language with proficiency
        const [language, proficiency] = line
          .split("-", 2)
          .map((part) => part.trim());

        // Only add if it's likely a human language
        if (isLikelyHumanLanguage(language, humanLanguages)) {
          languages.push({ language, proficiency });
        }
      } else if (line.includes("(")) {
        // This might be a language with proficiency in parentheses
        const language = line.split("(")[0].trim();
        const proficiency = line.match(/\((.*?)\)/)?.[1] || "";

        // Only add if it's likely a human language
        if (isLikelyHumanLanguage(language, humanLanguages)) {
          languages.push({ language, proficiency });
        }
      } else {
        // Just a language without proficiency
        if (isLikelyHumanLanguage(line.trim(), humanLanguages)) {
          languages.push({ language: line.trim() });
        }
      }
    }

    // Helper function to check if a language is likely a human language
    function isLikelyHumanLanguage(
      language: string,
      humanLanguages: string[]
    ): boolean {
      const languageLC = language.toLowerCase();

      // Check if it's in our list of human languages
      if (humanLanguages.some((lang) => languageLC.includes(lang))) {
        return true;
      }

      // Check if it contains tech-related terms
      if (techTerms.some((term) => languageLC.includes(term))) {
        return false;
      }

      // If the language name is capitalized and short, it's likely a human language
      if (language.length < 20 && language[0] === language[0].toUpperCase()) {
        return true;
      }

      return false;
    }

    return languages;
  }

  /**
   * Extract patents from the resume
   * @param patentsLines Lines from the patents section
   * @returns Patents
   */
  private extractPatents(patentsLines: string[]) {
    const patents = [];
    let current: any = {};
    let inPatentEntry = false;

    // Process each line in the patents section
    for (const line of patentsLines) {
      if (!line.trim()) continue;

      // Check if this is a bullet point
      if (line.match(/^[\s]*[•\-\*][\s]*/)) {
        // This is a bullet point - likely a new patent
        if (current.title) {
          patents.push(current);
        }

        const cleanLine = line.replace(/^[\s]*[•\-\*][\s]*/, "").trim();
        current = { title: cleanLine };
        inPatentEntry = true;

        // Check if this line contains a patent number
        if (
          cleanLine.includes("Patent No") ||
          cleanLine.includes("patent no")
        ) {
          const patentNoMatch =
            cleanLine.match(/Patent No\.?\s*([0-9,]+)/i) ||
            cleanLine.match(/patent no\.?\s*([0-9,]+)/i);
          if (patentNoMatch) {
            current.patentNumber = patentNoMatch[1].replace(/,/g, "");
          }
        }

        // Check if this line contains a year
        const yearMatch = cleanLine.match(/\b(19|20)\d{2}\b/);
        if (yearMatch) {
          current.year = yearMatch[0];
        }

        // Check if this line contains authors
        if (cleanLine.includes(",") && !cleanLine.includes("et al")) {
          const commaIndex = cleanLine.indexOf(",");
          if (commaIndex > 0 && commaIndex < 30) {
            current.inventors = cleanLine.substring(0, commaIndex).trim();
          }
        }
      } else if (line.includes(":")) {
        // This might be a patent detail
        const [key, value] = line.split(":", 2).map((part) => part.trim());

        if (value && key.length < 30) {
          // This is likely a key-value pair
          if (inPatentEntry) {
            // Add to current patent
            if (
              key.toLowerCase().includes("date") ||
              key.toLowerCase().includes("year") ||
              key.toLowerCase().includes("filed")
            ) {
              current.date = value;
            } else if (
              key.toLowerCase().includes("inventor") ||
              key.toLowerCase().includes("author")
            ) {
              current.inventors = value;
            } else if (
              key.toLowerCase().includes("patent") ||
              key.toLowerCase().includes("number")
            ) {
              current.patentNumber = value;
            } else if (key.toLowerCase().includes("status")) {
              current.status = value;
            } else {
              // Generic key-value pair
              current[key.toLowerCase()] = value;
            }
          } else {
            // Start a new patent
            if (current.title) {
              patents.push(current);
            }
            current = { title: key };

            if (value) {
              if (
                key.toLowerCase().includes("patent") ||
                key.toLowerCase().includes("invention")
              ) {
                current.title = value;
              } else {
                current.description = value;
              }
            }

            inPatentEntry = true;
          }
        }
      } else if (line.match(/\b(19|20)\d{2}\b/) && !current.year) {
        // This line contains a year
        current.year = line.match(/\b(19|20)\d{2}\b/)[0];
      } else if (line.includes("Patent No") || line.includes("patent no")) {
        // This line contains a patent number
        const patentNoMatch =
          line.match(/Patent No\.?\s*([0-9,]+)/i) ||
          line.match(/patent no\.?\s*([0-9,]+)/i);
        if (patentNoMatch) {
          current.patentNumber = patentNoMatch[1].replace(/,/g, "");
        }
      } else if (inPatentEntry) {
        // This is likely a description or continuation
        if (!current.description) {
          current.description = line.trim();
        } else {
          current.description += " " + line.trim();
        }
      } else {
        // This might be a new patent title
        if (current.title) {
          patents.push(current);
        }
        current = { title: line.trim() };
        inPatentEntry = true;
      }
    }

    // Add the last patent
    if (current.title) {
      patents.push(current);
    }

    return patents;
  }

  /**
   * Extract achievements from the resume
   * @param achievementsLines Lines from the achievements section
   * @returns Achievements
   */
  private extractAchievements(achievementsLines: string[]) {
    const achievements = [];

    // Process each line in the achievements section
    for (const line of achievementsLines) {
      if (!line.trim()) continue;

      // Check if this is a bullet point
      if (line.match(/^[\s]*[•\-\*][\s]*/)) {
        // This is a bullet point - extract the achievement
        const achievement = line.replace(/^[\s]*[•\-\*][\s]*/, "").trim();

        // Check if there's a year indicated
        const yearMatch = achievement.match(/\b(19|20)\d{2}\b/);
        if (yearMatch) {
          achievements.push({
            description: achievement,
            year: yearMatch[0],
          });
        } else {
          achievements.push({ description: achievement });
        }
      } else {
        // This is a regular line - extract the achievement

        // Check if there's a year indicated
        const yearMatch = line.match(/\b(19|20)\d{2}\b/);
        if (yearMatch) {
          achievements.push({
            description: line.trim(),
            year: yearMatch[0],
          });
        } else {
          achievements.push({ description: line.trim() });
        }
      }
    }

    return achievements;
  }

  /**
   * Extract volunteer experience from the resume
   * @param volunteerLines Lines from the volunteer section
   * @returns Volunteer experiences
   */
  private extractVolunteer(volunteerLines: string[]) {
    const volunteer = [];
    let current: any = {};
    let inVolunteerEntry = false;

    // Process each line in the volunteer section
    for (const line of volunteerLines) {
      if (!line.trim()) continue;

      // Check if this is a bullet point
      if (line.match(/^[\s]*[•\-\*][\s]*/)) {
        // This is a bullet point
        if (inVolunteerEntry) {
          // Add to descriptions
          current.descriptions = current.descriptions || [];
          current.descriptions.push(
            line.replace(/^[\s]*[•\-\*][\s]*/, "").trim()
          );
        } else {
          // This might be a new volunteer entry
          if (current.organization) {
            volunteer.push(current);
          }
          current = {
            role: line.replace(/^[\s]*[•\-\*][\s]*/, "").trim(),
            descriptions: [],
          };
          inVolunteerEntry = true;
        }
      } else if (line.includes(",")) {
        // This might be an organization and location
        const parts = line.split(",").map((part) => part.trim());

        if (parts.length >= 2) {
          // If we already have a current volunteer entry, save it
          if (current.organization) {
            volunteer.push(current);
          }

          // Start a new volunteer entry
          current = {
            organization: parts[0],
            location: parts.slice(1).join(", "),
          };
          inVolunteerEntry = true;
        }
      } else if (line.match(/\b(19|20)\d{2}\b/) && !current.date) {
        // This line contains a year, likely a date
        current.date = line.trim();
      } else if (inVolunteerEntry) {
        // This is likely a description or continuation
        if (!current.role) {
          current.role = line.trim();
        } else if (!current.description) {
          current.description = line.trim();
        } else {
          current.description += " " + line.trim();
        }
      } else {
        // This might be a new volunteer organization
        if (current.organization) {
          volunteer.push(current);
        }
        current = { organization: line.trim() };
        inVolunteerEntry = true;
      }
    }

    // Add the last volunteer entry
    if (current.organization || current.role) {
      volunteer.push(current);
    }

    return volunteer;
  }

  /**
   * Extract interests from the resume
   * @param interestsLines Lines from the interests section
   * @returns Interests
   */
  private extractInterests(interestsLines: string[]) {
    const interests = [];

    // Process each line in the interests section
    for (const line of interestsLines) {
      if (!line.trim()) continue;

      // Check if this is a bullet point
      if (line.match(/^[\s]*[•\-\*][\s]*/)) {
        // This is a bullet point - extract the interest
        interests.push(line.replace(/^[\s]*[•\-\*][\s]*/, "").trim());
      } else if (line.includes(",")) {
        // This might be a list of interests
        const interestsList = line.split(",").map((part) => part.trim());
        interests.push(...interestsList);
      } else if (line.includes("•")) {
        // This might be a list of interests with bullet points
        const interestsList = line
          .split("•")
          .map((part) => part.trim())
          .filter(Boolean);
        interests.push(...interestsList);
      } else {
        // This is a regular line - extract the interest
        interests.push(line.trim());
      }
    }

    return interests;
  }

  /**
   * Extract references from the resume
   * @param referencesLines Lines from the references section
   * @returns References
   */
  private extractReferences(referencesLines: string[]) {
    const references = [];
    let current: any = {};
    let inReferenceEntry = false;

    // Process each line in the references section
    for (const line of referencesLines) {
      if (!line.trim()) continue;

      // Check if this is a bullet point
      if (line.match(/^[\s]*[•\-\*][\s]*/)) {
        // This is a bullet point - likely a new reference
        if (current.name) {
          references.push(current);
        }
        current = {
          name: line.replace(/^[\s]*[•\-\*][\s]*/, "").trim(),
        };
        inReferenceEntry = true;
      } else if (line.includes(":")) {
        // This might be a reference detail
        const [key, value] = line.split(":", 2).map((part) => part.trim());

        if (value && key.length < 30) {
          // This is likely a key-value pair
          if (inReferenceEntry) {
            // Add to current reference
            if (key.toLowerCase().includes("email")) {
              current.email = value;
            } else if (key.toLowerCase().includes("phone")) {
              current.phone = value;
            } else if (
              key.toLowerCase().includes("title") ||
              key.toLowerCase().includes("position")
            ) {
              current.title = value;
            } else if (
              key.toLowerCase().includes("company") ||
              key.toLowerCase().includes("organization")
            ) {
              current.company = value;
            } else if (
              key.toLowerCase().includes("relation") ||
              key.toLowerCase().includes("relationship")
            ) {
              current.relationship = value;
            } else {
              // Generic key-value pair
              current[key.toLowerCase()] = value;
            }
          } else {
            // Start a new reference
            if (current.name) {
              references.push(current);
            }
            current = { name: key };

            if (value) {
              if (
                key.toLowerCase().includes("reference") ||
                key.toLowerCase().includes("name")
              ) {
                current.name = value;
              } else {
                current[key.toLowerCase()] = value;
              }
            }

            inReferenceEntry = true;
          }
        }
      } else if (line.includes("@") && line.includes(".")) {
        // This line contains an email
        current.email = line.trim();
      } else if (
        line.match(/\(\d{3}\)\s*\d{3}-\d{4}/) ||
        line.match(/\d{3}-\d{3}-\d{4}/)
      ) {
        // This line contains a phone number
        current.phone = line.trim();
      } else if (inReferenceEntry) {
        // This is likely a title, company, or continuation
        if (!current.title) {
          current.title = line.trim();
        } else if (!current.company) {
          current.company = line.trim();
        } else if (!current.description) {
          current.description = line.trim();
        } else {
          current.description += " " + line.trim();
        }
      } else {
        // This might be a new reference name
        if (current.name) {
          references.push(current);
        }
        current = { name: line.trim() };
        inReferenceEntry = true;
      }
    }

    // Add the last reference
    if (current.name) {
      references.push(current);
    }

    return references;
  }

  /**
   * Extract publications from the resume
   * @param publicationsLines Lines from the publications section
   * @param fullText The full text of the resume
   * @returns Publications
   */
  private extractPublications(publicationsLines: string[], fullText: string) {
    const publications = [];
    let current: any = {};
    let inPublicationEntry = false;

    // Check if this is actually a publications section
    // If it contains many technical skills or programming languages, it's likely not publications
    const techTerms = [
      "python",
      "javascript",
      "java",
      "c++",
      "c#",
      "ruby",
      "php",
      "swift",
      "kotlin",
      "go",
      "rust",
      "scala",
      "perl",
      "r",
      "matlab",
      "sql",
      "nosql",
      "mongodb",
      "mysql",
      "postgresql",
      "oracle",
      "redis",
      "cassandra",
      "dynamodb",
      "firebase",
      "aws",
      "azure",
      "gcp",
      "docker",
      "kubernetes",
      "jenkins",
      "git",
      "github",
      "gitlab",
      "jira",
      "confluence",
      "agile",
      "scrum",
      "kanban",
      "devops",
      "ci/cd",
      "rest",
      "graphql",
      "api",
      "json",
      "xml",
      "html",
      "css",
      "react",
      "angular",
      "vue",
      "node.js",
      "express",
      "django",
      "flask",
      "spring",
      "laravel",
      "machine learning",
      "deep learning",
      "neural networks",
      "tensorflow",
      "pytorch",
      "keras",
    ];

    const sectionText = publicationsLines.join(" ").toLowerCase();
    let techTermCount = 0;

    for (const term of techTerms) {
      if (sectionText.includes(term)) {
        techTermCount++;
      }
    }

    // If more than 10 tech terms are found, this is likely a skills section, not publications
    if (techTermCount > 10) {
      return [];
    }

    // Check for patents section
    let patentLines: string[] = [];
    let inPatentsSection = false;

    for (let i = 0; i < publicationsLines.length; i++) {
      const line = publicationsLines[i].trim().toLowerCase();

      if (line === "patents" || line.includes("patent no")) {
        inPatentsSection = true;
        patentLines = publicationsLines.slice(i);
        break;
      }
    }

    // Process patents if found
    if (inPatentsSection && patentLines.length > 0) {
      for (const line of patentLines) {
        if (!line.trim()) continue;

        // Skip the "Patents" header
        if (line.trim().toLowerCase() === "patents") continue;

        // Check if this line contains patent information
        if (line.includes("Patent No") || line.includes("patent no")) {
          const cleanLine = line.replace(/^[\s]*[•\-\*][\s]*/, "").trim();

          // Create a new publication entry for the patent
          const patent = {
            title: cleanLine,
            type: "Patent",
          };

          // Check if this line contains a year
          const yearMatch = line.match(/\b(19|20)\d{2}\b/);
          if (yearMatch) {
            patent.year = yearMatch[0];
          }

          // Check if this line contains authors
          if (cleanLine.includes(",") && !cleanLine.includes("et al")) {
            const commaIndex = cleanLine.indexOf(",");
            if (commaIndex > 0 && commaIndex < 30) {
              patent.authors = cleanLine.substring(0, commaIndex).trim();
            }
          }

          publications.push(patent);
        }
      }
    }

    // Process each line in the publications section
    for (const line of publicationsLines) {
      if (!line.trim()) continue;

      // Check if this line might be the start of a new publication entry
      // Publications often start with a title that might be in quotes or italics
      // Clean the line first to remove bullet points
      const cleanLine = line.replace(/^[\s]*[•\-\*][\s]*/, "").trim();

      const isNewPublication =
        cleanLine.includes('"') ||
        cleanLine.includes('"') ||
        cleanLine.includes('"') ||
        cleanLine.includes("'") ||
        cleanLine.includes("*") ||
        cleanLine.includes("_") ||
        /^\d{4}/.test(cleanLine) || // Starts with a year
        /^[A-Z]/.test(cleanLine); // Starts with a capital letter

      if (isNewPublication) {
        // If we already have a current publication entry, save it
        if (current.title) {
          publications.push(current);
        }

        // Start a new publication entry
        current = { title: cleanLine };
        inPublicationEntry = true;

        // Check if this line contains a year
        const yearMatch = line.match(/\b(19|20)\d{2}\b/);
        if (yearMatch) {
          current.year = yearMatch[0];
        }

        // Check if this line contains authors
        if (cleanLine.includes(",") && !cleanLine.includes("et al")) {
          // This might be a list of authors
          const commaIndex = cleanLine.indexOf(",");
          if (commaIndex > 0 && commaIndex < 30) {
            // The comma is near the beginning, likely separating authors
            current.authors = cleanLine.substring(0, commaIndex).trim();
          }
        } else if (cleanLine.includes("et al")) {
          // This likely includes "et al" for multiple authors
          const etAlIndex = cleanLine.indexOf("et al");
          if (etAlIndex > 0) {
            current.authors = cleanLine.substring(0, etAlIndex + 5).trim();
          }
        }
      }
      // If we're in a publication entry, add details
      else if (inPublicationEntry) {
        // Check if this line contains a journal or conference name
        if (
          line.includes("Journal") ||
          line.includes("Conference") ||
          line.includes("Proceedings") ||
          line.includes("Symposium") ||
          line.includes("Workshop") ||
          line.includes("Publication") ||
          line.includes("Patent")
        ) {
          current.venue = line.trim();

          // If this is a patent, mark it as such
          if (line.includes("Patent")) {
            current.type = "Patent";
          }
        }
        // Check if this line contains a year
        else if (!current.year && line.match(/\b(19|20)\d{2}\b/)) {
          current.year = line.match(/\b(19|20)\d{2}\b/)[0];
        }
        // Check if this line contains a DOI
        else if (line.includes("DOI") || line.includes("doi")) {
          current.doi = line.trim();
        }
        // Check if this line contains a URL
        else if (line.match(/https?:\/\/[^\s]+/)) {
          current.url = line.match(/https?:\/\/[^\s]+/)[0];
        }
        // Otherwise, this might be additional information
        else {
          if (!current.description) {
            current.description = line.trim();
          } else {
            current.description += " " + line.trim();
          }
        }
      }
    }

    // Add the last publication entry
    if (current.title) {
      publications.push(current);
    }

    return publications;
  }

  /**
   * Calculate confidence scores for the parsed resume
   * @param basicInfo Basic information
   * @param education Education information
   * @param experience Work experience information
   * @param skills Skills information
   * @param publications Publications information
   * @returns Confidence scores
   */
  private calculateConfidenceScores(
    basicInfo: any,
    education: any[],
    experience: any[],
    skills: string[],
    publications: any[] = [],
    projects: any[] = [],
    certifications: any[] = [],
    languages: any[] = [],
    patents: any[] = [],
    achievements: any[] = [],
    volunteer: any[] = [],
    interests: any[] = [],
    references: any[] = []
  ) {
    const scores: any = {
      profile: {
        name: basicInfo.name ? 1 : 0,
        email: basicInfo.email ? 1 : 0,
        phone: basicInfo.phone ? 1 : 0,
        location: basicInfo.location ? 1 : 0,
        summary: basicInfo.summary ? 1 : 0,
        url: basicInfo.url ? 1 : 0,
      },
      education: education.length > 0 ? 1 : 0,
      experience: experience.length > 0 ? 1 : 0,
      skills: skills.length > 0 ? 1 : 0,
      publications: publications.length > 0 ? 1 : 0,
    };

    // Calculate profile completeness
    const profileFields = Object.keys(scores.profile).length;
    const profileScore =
      Object.values(scores.profile).reduce(
        (sum: any, score: any) => sum + score,
        0
      ) / profileFields;
    scores.profile.completeness = profileScore;

    // Calculate scores for new sections
    scores.projects = projects && projects.length > 0 ? 1 : 0;
    scores.certifications = certifications && certifications.length > 0 ? 1 : 0;
    scores.languages = languages && languages.length > 0 ? 1 : 0;
    scores.patents = patents && patents.length > 0 ? 1 : 0;
    scores.achievements = achievements && achievements.length > 0 ? 1 : 0;
    scores.volunteer = volunteer && volunteer.length > 0 ? 1 : 0;
    scores.interests = interests && interests.length > 0 ? 1 : 0;
    scores.references = references && references.length > 0 ? 1 : 0;

    // Calculate overall score
    const weights = {
      profile: 0.2,
      education: 0.15,
      experience: 0.2,
      skills: 0.15,
      publications: 0.05,
      projects: 0.05,
      certifications: 0.05,
      languages: 0.05,
      patents: 0.025,
      achievements: 0.025,
      volunteer: 0.025,
      interests: 0.025,
      references: 0.025,
    };

    const overallScore =
      scores.profile.completeness * weights.profile +
      scores.education * weights.education +
      scores.experience * weights.experience +
      scores.skills * weights.skills +
      scores.publications * weights.publications +
      scores.projects * weights.projects +
      scores.certifications * weights.certifications +
      scores.languages * weights.languages +
      scores.patents * weights.patents +
      scores.achievements * weights.achievements +
      scores.volunteer * weights.volunteer +
      scores.interests * weights.interests +
      scores.references * weights.references;

    scores.overall = overallScore;
    scores.overallPercentage = Math.round(overallScore * 100);

    return scores;
  }
}

// Export the improved parser
export const improvedParser = new ImprovedResumeParser();

// Export a function to parse a resume using the improved parser
export async function parseResumeImproved(filePath: string) {
  return improvedParser.parseResume(filePath);
}
