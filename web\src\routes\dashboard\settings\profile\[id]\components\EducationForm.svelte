<script lang="ts">
  import { type EducationSchema } from '$lib/validators/profile';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import * as Checkbox from '$lib/components/ui/checkbox/index.js';
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import * as AlertDialog from '$lib/components/ui/alert-dialog/index.js';
  import { Plus, Edit, Trash2, GraduationCap } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';

  // Props
  const {
    data,
    onSave,
    disabled = false,
  } = $props<{
    data: EducationSchema[];
    onSave: (data: EducationSchema[]) => Promise<boolean>;
    disabled?: boolean;
  }>();

  // Initialize form data
  let formData = $state<EducationSchema>({
    institution: '',
    degree: '',
    fieldOfStudy: '',
    startDate: '',
    endDate: '',
    current: false,
    gpa: '',
    description: '',
  });

  // Form state
  let submitting = $state(false);
  let errors = $state<Record<string, string>>({});

  // State
  let educations = $state<EducationSchema[]>(data || []);
  let dialogOpen = $state(false);
  let deleteDialogOpen = $state(false);
  let editingIndex = $state<number | null>(null);
  let deletingIndex = $state<number | null>(null);

  // Open dialog for adding new education
  function addEducation() {
    formData = {
      institution: '',
      degree: '',
      fieldOfStudy: '',
      startDate: '',
      endDate: '',
      current: false,
      gpa: '',
      description: '',
    };
    editingIndex = null;
    dialogOpen = true;
  }

  // Open dialog for editing education
  function editEducation(index: number) {
    const education = educations[index];
    formData = { ...education };
    editingIndex = index;
    dialogOpen = true;
  }

  // Open dialog for deleting education
  function confirmDeleteEducation(index: number) {
    deletingIndex = index;
    deleteDialogOpen = true;
  }

  // Delete education
  function deleteEducation() {
    if (deletingIndex !== null) {
      educations = educations.filter((_, i) => i !== deletingIndex);
      saveEducations();
      deleteDialogOpen = false;
      deletingIndex = null;
    }
  }

  // Handle form submission
  async function handleSubmit() {
    // Reset errors
    errors = {};

    // Validate form
    let isValid = true;

    if (!formData.institution) {
      errors.institution = 'Institution is required';
      isValid = false;
    }

    if (!formData.startDate) {
      errors.startDate = 'Start date is required';
      isValid = false;
    }

    if (!isValid) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Set submitting state
    submitting = true;

    try {
      // Create new education object
      const newEducation: EducationSchema = {
        ...formData,
      };

      // Update or add education
      if (editingIndex !== null) {
        educations[editingIndex] = newEducation;
      } else {
        educations = [...educations, newEducation];
      }

      // Save educations
      await saveEducations();

      // Close dialog
      dialogOpen = false;
    } catch (error) {
      console.error('Error saving education:', error);
      toast.error('Failed to save education');
    } finally {
      submitting = false;
    }
  }

  // Save educations to server
  async function saveEducations() {
    const success = await onSave(educations);
    if (success) {
      toast.success('Education updated successfully');
    }
    return success;
  }

  // Format date for display
  function formatDate(dateString: string | undefined): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
    });
  }

  // Format date range for display
  function formatDateRange(
    startDate: string | undefined,
    endDate: string | undefined,
    current: boolean
  ): string {
    const start = formatDate(startDate);
    const end = current ? 'Present' : formatDate(endDate);

    return `${start} - ${end}`;
  }
</script>

<div class="rounded-lg border p-6">
  <div class="flex items-center justify-between">
    <h2 class="text-xl font-semibold">Education</h2>
    <Button variant="outline" size="sm" onclick={addEducation} {disabled}>
      <Plus class="mr-2 h-4 w-4" />
      Add Education
    </Button>
  </div>

  <div class="mt-4">
    {#if educations.length > 0}
      <div class="space-y-4">
        {#each educations as education, index}
          <div class="flex items-start justify-between rounded-md border p-4">
            <div class="flex-1">
              <div class="flex items-center">
                <GraduationCap class="mr-2 h-5 w-5 text-blue-500" />
                <h3 class="font-medium">{education.institution}</h3>
              </div>
              {#if education.degree || education.fieldOfStudy}
                <p class="text-muted-foreground">
                  {education.degree}{education.degree && education.fieldOfStudy ? ', ' : ''}
                  {education.fieldOfStudy}
                </p>
              {/if}
              <p class="text-muted-foreground text-sm">
                {formatDateRange(education.startDate, education.endDate, education.current)}
              </p>
              {#if education.gpa}
                <p class="text-muted-foreground text-sm">GPA: {education.gpa}</p>
              {/if}
              {#if education.description}
                <p class="mt-2 text-sm">{education.description}</p>
              {/if}
            </div>
            <div class="flex space-x-2">
              <Button variant="ghost" size="icon" onclick={() => editEducation(index)} {disabled}>
                <Edit class="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onclick={() => confirmDeleteEducation(index)}
                {disabled}>
                <Trash2 class="h-4 w-4" />
              </Button>
            </div>
          </div>
        {/each}
      </div>
    {:else}
      <div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8">
        <GraduationCap class="text-muted-foreground mb-2 h-10 w-10" />
        <p class="text-muted-foreground text-center">No education added yet</p>
        <Button variant="outline" class="mt-4" onclick={addEducation} {disabled}>
          <Plus class="mr-2 h-4 w-4" />
          Add Education
        </Button>
      </div>
    {/if}
  </div>
</div>

<!-- Add/Edit Education Dialog -->
<Dialog.Root bind:open={dialogOpen}>
  <Dialog.Content class="sm:max-w-[600px]">
    <Dialog.Header>
      <Dialog.Title>{editingIndex !== null ? 'Edit Education' : 'Add Education'}</Dialog.Title>
      <Dialog.Description>
        {editingIndex !== null
          ? 'Update your education details.'
          : 'Add a new education to your profile.'}
      </Dialog.Description>
    </Dialog.Header>

    <form method="POST" class="space-y-4">
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div class="space-y-2 sm:col-span-2">
          <Label for="institution">Institution *</Label>
          <Input id="institution" bind:value={formData.institution} />
          {#if errors.institution}
            <p class="text-destructive text-sm">{errors.institution}</p>
          {/if}
        </div>
        <div class="space-y-2">
          <Label for="degree">Degree</Label>
          <Input id="degree" bind:value={formData.degree} />
        </div>
        <div class="space-y-2">
          <Label for="fieldOfStudy">Field of Study</Label>
          <Input id="fieldOfStudy" bind:value={formData.fieldOfStudy} />
        </div>
        <div class="space-y-2">
          <Label for="startDate">Start Date *</Label>
          <Input id="startDate" type="month" bind:value={formData.startDate} />
          {#if errors.startDate}
            <p class="text-destructive text-sm">{errors.startDate}</p>
          {/if}
        </div>
        <div class="space-y-2">
          <div class="flex items-center space-x-2">
            <Checkbox.Root
              id="current"
              checked={formData.current}
              onCheckedChange={(checked) => (formData.current = checked)} />
            <Label for="current">I am currently studying here</Label>
          </div>
        </div>
        <div class="space-y-2">
          <Label for="endDate">End Date</Label>
          <Input
            id="endDate"
            type="month"
            bind:value={formData.endDate}
            disabled={formData.current} />
        </div>
        <div class="space-y-2">
          <Label for="gpa">GPA</Label>
          <Input id="gpa" bind:value={formData.gpa} />
        </div>
      </div>

      <div class="space-y-2">
        <Label for="description">Description</Label>
        <Textarea
          id="description"
          bind:value={formData.description}
          rows={4}
          placeholder="Describe your studies, achievements, or activities..." />
      </div>

      <Dialog.Footer>
        <Button variant="outline" type="button" onclick={() => (dialogOpen = false)}>Cancel</Button>
        <Button type="button" onclick={handleSubmit} disabled={submitting}>
          {#if submitting}
            Saving...
          {:else}
            Save
          {/if}
        </Button>
      </Dialog.Footer>
    </form>
  </Dialog.Content>
</Dialog.Root>

<!-- Delete Confirmation Dialog -->
<AlertDialog.Root bind:open={deleteDialogOpen}>
  <AlertDialog.Content>
    <AlertDialog.Header>
      <AlertDialog.Title>Delete Education</AlertDialog.Title>
      <AlertDialog.Description>
        Are you sure you want to delete this education? This action cannot be undone.
      </AlertDialog.Description>
    </AlertDialog.Header>
    <AlertDialog.Footer>
      <AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
      <AlertDialog.Action onclick={deleteEducation}>Delete</AlertDialog.Action>
    </AlertDialog.Footer>
  </AlertDialog.Content>
</AlertDialog.Root>
