<script lang="ts">
  import { type LanguageSchema } from '$lib/validators/profile';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Edit, Plus, Languages } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import LanguagesModal from './LanguagesModal.svelte';

  // Props
  const {
    data,
    onSave,
    disabled = false,
  } = $props<{
    data: LanguageSchema[];
    onSave: (data: LanguageSchema[]) => Promise<boolean>;
    disabled?: boolean;
  }>();

  // Modal state
  let modalOpen = $state(false);

  // Proficiency options for display
  const proficiencyOptions = [
    { value: 'beginner', label: 'Beginner' },
    { value: 'intermediate', label: 'Intermediate' },
    { value: 'advanced', label: 'Advanced' },
    { value: 'native', label: 'Native' },
  ];

  // Format proficiency for display
  function formatProficiency(proficiency: string): string {
    const option = proficiencyOptions.find((opt) => opt.value === proficiency);
    return option ? option.label : 'Not specified';
  }

  // Open edit modal
  function openEditModal() {
    modalOpen = true;
  }

  // Handle modal save
  async function handleSave(languages: LanguageSchema[]): Promise<boolean> {
    try {
      const success = await onSave(languages);
      return success;
    } catch (error) {
      console.error('Error saving languages:', error);
      toast.error('Failed to save languages');
      return false;
    }
  }
</script>

<div class="rounded-lg border p-6">
  <div class="flex items-center justify-between">
    <h2 class="text-xl font-semibold">Languages</h2>
    <Button variant="outline" size="sm" onclick={openEditModal} {disabled}>
      <Plus class="mr-2 h-4 w-4" />
      Add Language
    </Button>
  </div>

  <div class="mt-4">
    {#if data && data.length > 0}
      <div class="space-y-4">
        {#each data as language}
          <div class="flex items-start justify-between rounded-md border p-4">
            <div class="flex-1">
              <div class="flex items-center">
                <Languages class="mr-2 h-5 w-5 text-blue-500" />
                <h3 class="font-medium">{language.language}</h3>
              </div>
              <p class="text-muted-foreground text-sm">
                {formatProficiency(language.proficiency)}
              </p>
            </div>
            <Button variant="ghost" size="icon" onclick={openEditModal} {disabled}>
              <Edit class="h-4 w-4" />
            </Button>
          </div>
        {/each}
      </div>
    {:else}
      <div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8">
        <Languages class="text-muted-foreground mb-2 h-10 w-10" />
        <p class="text-muted-foreground text-center">No languages added yet</p>
        <Button variant="outline" class="mt-4" onclick={openEditModal} {disabled}>
          <Plus class="mr-2 h-4 w-4" />
          Add Language
        </Button>
      </div>
    {/if}
  </div>
</div>

<!-- Languages Modal -->
<LanguagesModal
  open={modalOpen}
  languages={data || []}
  onClose={() => (modalOpen = false)}
  onSave={handleSave}
  {disabled} />
