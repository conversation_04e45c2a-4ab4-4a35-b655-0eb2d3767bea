<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { X } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import type { ProfileData } from '$lib/types/profile';

  // Props
  const { profileData, onSave, onCancel } = $props<{
    profileData: ProfileData;
    onSave: (data: Partial<ProfileData>) => Promise<boolean>;
    onCancel: () => void;
  }>();

  // Form data
  let minSalary = $state<number | undefined>(profileData.jobPreferences?.minSalary);
  let desiredSalary = $state<number | undefined>(profileData.jobPreferences?.desiredSalary);
  let salaryType = $state(profileData.jobPreferences?.salaryType || '');
  let benefitsDesired = $state<string[]>(profileData.jobPreferences?.benefitsDesired || []);

  // New benefit input
  let newBenefit = $state('');

  // Salary type options
  const salaryTypeOptions = [
    { value: 'annual', label: 'Annual' },
    { value: 'hourly', label: 'Hourly' },
    { value: 'contract', label: 'Contract' },
  ];

  // Common benefits
  const commonBenefits = [
    'Health Insurance',
    'Dental Insurance',
    'Vision Insurance',
    '401(k)',
    'Remote Work',
    'Flexible Hours',
    'Paid Time Off',
    'Professional Development',
  ];

  // Add benefit from dropdown
  function addBenefitFromDropdown(benefit: string) {
    if (benefit && !benefitsDesired.includes(benefit)) {
      benefitsDesired = [...benefitsDesired, benefit];
    }
  }

  // Add custom benefit
  function addCustomBenefit() {
    if (newBenefit && !benefitsDesired.includes(newBenefit)) {
      benefitsDesired = [...benefitsDesired, newBenefit];
      newBenefit = '';
    }
  }

  // Remove benefit
  function removeBenefit(benefit: string) {
    benefitsDesired = benefitsDesired.filter((b) => b !== benefit);
  }

  // Handle form submission
  async function handleSubmit() {
    try {
      // Validate form
      if (minSalary && isNaN(minSalary)) {
        toast.error('Minimum salary must be a number');
        return;
      }

      if (desiredSalary && isNaN(desiredSalary)) {
        toast.error('Desired salary must be a number');
        return;
      }

      // Prepare data
      const updatedData: Partial<ProfileData> = {
        jobPreferences: {
          ...profileData.jobPreferences,
          minSalary,
          desiredSalary,
          salaryType,
          benefitsDesired,
        },
      };

      // Save data
      const success = await onSave(updatedData);
      if (success) {
        toast.success('Salary expectations updated successfully');
      }
    } catch (error) {
      console.error('Error saving salary expectations:', error);
      toast.error('Failed to save salary expectations');
    }
  }
</script>

<form on:submit|preventDefault={handleSubmit} class="space-y-6">
  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
    <div class="space-y-2">
      <Label for="minSalary">Minimum Salary</Label>
      <div class="relative">
        <span class="absolute inset-y-0 left-0 flex items-center pl-3">$</span>
        <Input
          id="minSalary"
          type="number"
          bind:value={minSalary}
          placeholder="0"
          class="pl-7" />
      </div>
    </div>
    <div class="space-y-2">
      <Label for="desiredSalary">Desired Salary</Label>
      <div class="relative">
        <span class="absolute inset-y-0 left-0 flex items-center pl-3">$</span>
        <Input
          id="desiredSalary"
          type="number"
          bind:value={desiredSalary}
          placeholder="0"
          class="pl-7" />
      </div>
    </div>
  </div>

  <div class="space-y-2">
    <Label for="salaryType">Salary Type</Label>
    <Select.Root value={salaryType} onValueChange={(value) => (salaryType = value)}>
      <Select.Trigger id="salaryType" class="w-full">
        <Select.Value placeholder="Select salary type" />
      </Select.Trigger>
      <Select.Content>
        <Select.Group>
          {#each salaryTypeOptions as option}
            <Select.Item value={option.value}>{option.label}</Select.Item>
          {/each}
        </Select.Group>
      </Select.Content>
    </Select.Root>
  </div>

  <div class="space-y-2">
    <Label>Benefits Desired</Label>
    <div class="grid grid-cols-2 gap-2">
      {#each commonBenefits as benefit}
        <Button
          type="button"
          variant={benefitsDesired.includes(benefit) ? 'default' : 'outline'}
          size="sm"
          class="justify-start"
          on:click={() => {
            if (benefitsDesired.includes(benefit)) {
              removeBenefit(benefit);
            } else {
              addBenefitFromDropdown(benefit);
            }
          }}>
          {benefit}
        </Button>
      {/each}
    </div>

    <div class="mt-4 flex items-center space-x-2">
      <Input
        bind:value={newBenefit}
        placeholder="Add custom benefit"
        on:keydown={(e) => {
          if (e.key === 'Enter') {
            e.preventDefault();
            addCustomBenefit();
          }
        }} />
      <Button type="button" variant="outline" on:click={addCustomBenefit}>Add</Button>
    </div>

    {#if benefitsDesired.length > 0}
      <div class="mt-2 flex flex-wrap gap-2">
        {#each benefitsDesired as benefit}
          <Badge variant="secondary" class="flex items-center gap-1">
            {benefit}
            <button
              type="button"
              class="ml-1 rounded-full p-0.5 hover:bg-primary/20"
              on:click={() => removeBenefit(benefit)}>
              <X class="h-3 w-3" />
            </button>
          </Badge>
        {/each}
      </div>
    {/if}
  </div>

  <div class="flex justify-end space-x-2 pt-4">
    <Button variant="outline" type="button" on:click={onCancel}>Cancel</Button>
    <Button type="submit">Save Changes</Button>
  </div>
</form>
