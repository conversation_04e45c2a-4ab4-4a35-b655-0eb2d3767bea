# Use a more capable base image for Ollama
FROM node:20-slim

# Install required packages
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Ollama
RUN curl -fsSL https://ollama.com/install.sh | sh

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install all dependencies (including dev dependencies for build)
RUN npm ci

# Copy source code
COPY src/ ./src/

# Build the application and remove dev dependencies for smaller image
RUN npm run build && \
    npm ci --only=production

# Expose the API port and Ollama port
EXPOSE 3100 11434

# Add health check - run every 10 minutes with longer start period to allow model download
# Use the Ollama-specific health check endpoint to ensure the model is loaded
HEALTHCHECK --interval=10m --timeout=30s --start-period=10m --retries=3 \
  CMD ["wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3100/api/health/ollama"]

# Copy the startup script
COPY start.sh /start.sh
RUN chmod +x /start.sh

# Start the application with Ollama
CMD ["/start.sh"]
