<script lang="ts">
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import { User, FileText, Briefcase } from 'lucide-svelte';
  import type { CompleteProfileSchema } from '$lib/validators/profile';
  import type { ProfileData } from '$lib/types/profile';

  // Import form components
  import ProfileHeaderForm from './ProfileHeaderForm.svelte';
  import ProfileVisibilityForm from './ProfileVisibilityForm.svelte';
  import PersonalInfoForm from './PersonalInfoForm.svelte';
  import ResumeForm from './ResumeForm.svelte';
  import WorkExperienceForm from './WorkExperienceForm.svelte';
  import EducationForm from './EducationForm.svelte';
  import ProjectsForm from './ProjectsForm.svelte';
  import PortfolioLinksForm from './PortfolioLinksForm.svelte';
  import SkillsForm from './SkillsForm.svelte';
  import LanguagesForm from './LanguagesForm.svelte';
  import JobPreferencesForm from './JobPreferencesForm.svelte';
  import EmploymentInfoForm from './EmploymentInfoForm.svelte';

  // Props
  const {
    completeProfile,
    profileData,
    saveHandlers,
    isResumeBeingParsed = false,
    onResumeParsingChange,
  } = $props<{
    completeProfile: CompleteProfileSchema;
    profileData: ProfileData;
    isResumeBeingParsed?: boolean;
    onResumeParsingChange?: (isParsing: boolean) => void;
    saveHandlers: {
      saveProfileHeader: (data: any) => Promise<boolean>;
      saveProfileVisibility: (data: any) => Promise<boolean>;
      savePersonalInfo: (data: any) => Promise<boolean>;
      saveResume: (data: any) => Promise<boolean>;
      saveWorkExperiences: (data: any) => Promise<boolean>;
      saveEducations: (data: any) => Promise<boolean>;
      saveProjects: (data: any) => Promise<boolean>;
      savePortfolioLinks: (data: any) => Promise<boolean>;
      saveSkills: (data: any) => Promise<boolean>;
      saveLanguages: (data: any) => Promise<boolean>;
      saveJobPreferences: (data: any) => Promise<boolean>;
      saveEmploymentInfo: (data: any) => Promise<boolean>;
    };
  }>();

  // Active tab state
  let activeTab = $state('profile');
</script>

<div class="flex flex-col space-y-6">
  <!-- Profile Header - Always visible at the top -->

  <!-- Two-column layout for tabs and content -->
  <div class="grid grid-cols-1 gap-6 md:grid-cols-4">
    <!-- Left column - Tabs -->
    <div class="flex flex-col gap-4 md:col-span-1">
      <ProfileHeaderForm data={completeProfile.header} onSave={saveHandlers.saveProfileHeader} />
      <Tabs.Root
        value={activeTab}
        onValueChange={(value) => (activeTab = value)}
        class="w-full"
        orientation="horizontal">
        <Tabs.List class="flex flex-col space-y-1 rounded-md border p-2">
          <Tabs.Trigger
            value="profile"
            class="data-[state=active]:bg-muted flex items-center justify-start gap-2 rounded-md px-3 py-2 text-sm font-medium">
            <User class="h-4 w-4" />
            <span>Profile</span>
          </Tabs.Trigger>
          <Tabs.Trigger
            value="personal"
            class="data-[state=active]:bg-muted flex items-center justify-start gap-2 rounded-md px-3 py-2 text-sm font-medium">
            <FileText class="h-4 w-4" />
            <span>Personal Info</span>
          </Tabs.Trigger>
          <Tabs.Trigger
            value="job"
            class="data-[state=active]:bg-muted flex items-center justify-start gap-2 rounded-md px-3 py-2 text-sm font-medium">
            <Briefcase class="h-4 w-4" />
            <span>Job Preferences</span>
          </Tabs.Trigger>
        </Tabs.List>
      </Tabs.Root>

      <ProfileVisibilityForm
        data={completeProfile.visibility}
        onSave={saveHandlers.saveProfileVisibility} />
    </div>

    <!-- Right column - Tab content -->
    <div class="space-y-6 md:col-span-3">
      {#if activeTab === 'profile'}
        <ResumeForm
          data={completeProfile.resume}
          onSave={saveHandlers.saveResume}
          disabled={isResumeBeingParsed}
          onParsingStatusChange={(isParsing) => {
            if (onResumeParsingChange) {
              onResumeParsingChange(isParsing);
            }
          }} />
        <WorkExperienceForm
          data={completeProfile.workExperiences}
          onSave={saveHandlers.saveWorkExperiences}
          disabled={isResumeBeingParsed} />
        <EducationForm
          data={completeProfile.educations}
          onSave={saveHandlers.saveEducations}
          disabled={isResumeBeingParsed} />
        <ProjectsForm
          data={completeProfile.projects}
          onSave={saveHandlers.saveProjects}
          disabled={isResumeBeingParsed} />
        <PortfolioLinksForm
          data={completeProfile.portfolioLinks}
          onSave={saveHandlers.savePortfolioLinks}
          disabled={isResumeBeingParsed} />
        <SkillsForm
          data={completeProfile.skills}
          onSave={saveHandlers.saveSkills}
          disabled={isResumeBeingParsed} />
        <LanguagesForm
          data={completeProfile.languages}
          onSave={saveHandlers.saveLanguages}
          disabled={isResumeBeingParsed} />
      {:else if activeTab === 'personal'}
        <PersonalInfoForm
          data={completeProfile.personalInfo}
          onSave={saveHandlers.savePersonalInfo}
          disabled={isResumeBeingParsed} />
        <EmploymentInfoForm
          data={profileData.employmentInfo || {}}
          onSave={saveHandlers.saveEmploymentInfo}
          disabled={isResumeBeingParsed} />
      {:else if activeTab === 'job'}
        <JobPreferencesForm
          data={profileData.jobPreferences || {}}
          onSave={saveHandlers.saveJobPreferences}
          disabled={isResumeBeingParsed} />
      {/if}
    </div>
  </div>
</div>
