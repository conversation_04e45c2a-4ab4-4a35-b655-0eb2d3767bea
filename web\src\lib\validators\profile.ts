import { z } from 'zod';

// Profile Header Schema
export const profileHeaderSchema = z.object({
  profileName: z.string().min(1, { message: 'Profile name is required' }),
  fullName: z.string().min(1, { message: 'Full name is required' }),
  jobTitle: z.string().optional(),
  jobSearchStatus: z.enum(['actively_looking', 'open_to_opportunities', 'not_looking']),
});

export type ProfileHeaderSchema = z.infer<typeof profileHeaderSchema>;

// Profile Visibility Schema
export const profileVisibilitySchema = z.object({
  showToRecruiters: z.boolean().default(false),
  getDiscovered: z.boolean().default(true),
  hideFromCurrentEmployer: z.boolean().default(false),
});

export type ProfileVisibilitySchema = z.infer<typeof profileVisibilitySchema>;

// Resume Schema
export const resumeSchema = z.object({
  resumeId: z.string().optional(),
  fileName: z.string().optional(),
  uploadedAt: z.string().optional(),
  isDefault: z.boolean().default(false),
  parseIntoProfile: z.boolean().default(true),
});

export type ResumeSchema = z.infer<typeof resumeSchema>;

// Work Experience Schema
export const workExperienceSchema = z.object({
  id: z.string().optional(),
  company: z.string().min(1, { message: 'Company name is required' }),
  title: z.string().min(1, { message: 'Job title is required' }),
  location: z.string().optional(),
  startDate: z.string().min(1, { message: 'Start date is required' }),
  endDate: z.string().optional(),
  current: z.boolean().default(false),
  description: z.string().optional(),
});

export type WorkExperienceSchema = z.infer<typeof workExperienceSchema>;

export const workExperiencesSchema = z.array(workExperienceSchema);

// Education Schema
export const educationSchema = z.object({
  id: z.string().optional(),
  institution: z.string().min(1, { message: 'Institution name is required' }),
  degree: z.string().optional(),
  fieldOfStudy: z.string().optional(),
  startDate: z.string().min(1, { message: 'Start date is required' }),
  endDate: z.string().optional(),
  current: z.boolean().default(false),
  gpa: z.string().optional(),
  description: z.string().optional(),
});

export type EducationSchema = z.infer<typeof educationSchema>;

export const educationsSchema = z.array(educationSchema);

// Project Schema
export const projectSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, { message: 'Project title is required' }),
  description: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  current: z.boolean().default(false),
  url: z.string().url().optional().or(z.literal('')),
  skills: z.array(z.string()).optional(),
});

export type ProjectSchema = z.infer<typeof projectSchema>;

export const projectsSchema = z.array(projectSchema);

// Portfolio Links Schema
export const portfolioLinksSchema = z.object({
  linkedinUrl: z.string().url().optional().or(z.literal('')),
  githubUrl: z.string().url().optional().or(z.literal('')),
  portfolioUrl: z.string().url().optional().or(z.literal('')),
  otherUrl: z.string().url().optional().or(z.literal('')),
});

export type PortfolioLinksSchema = z.infer<typeof portfolioLinksSchema>;

// Skills Schema
export const skillsSchema = z.object({
  skills: z.array(z.string()).min(1, { message: 'At least one skill is required' }),
});

export type SkillsSchema = z.infer<typeof skillsSchema>;

// Language Schema
export const languageSchema = z.object({
  id: z.string().optional(),
  language: z.string().min(1, { message: 'Language name is required' }),
  proficiency: z.enum(['beginner', 'intermediate', 'advanced', 'native']),
});

export type LanguageSchema = z.infer<typeof languageSchema>;

export const languagesSchema = z.array(languageSchema);

// Personal Info Schema
export const personalInfoSchema = z.object({
  email: z.string().email({ message: 'Invalid email format' }).optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
  country: z.string().default('USA'),
});

export type PersonalInfoSchema = z.infer<typeof personalInfoSchema>;

// Complete Profile Schema
export const completeProfileSchema = z.object({
  header: profileHeaderSchema,
  visibility: profileVisibilitySchema,
  personalInfo: personalInfoSchema.optional(),
  resume: resumeSchema.optional(),
  workExperiences: workExperiencesSchema.default([]),
  educations: educationsSchema.default([]),
  projects: projectsSchema.default([]),
  portfolioLinks: portfolioLinksSchema.optional(),
  skills: skillsSchema.optional(),
  languages: languagesSchema.default([]),
});

export type CompleteProfileSchema = z.infer<typeof completeProfileSchema>;
