<script lang="ts">
  import { toast } from 'svelte-sonner';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Edit, Download, Upload } from 'lucide-svelte';
  import ResumeModal from './ResumeModal.svelte';

  // Define ResumeSchema type
  interface ResumeSchema {
    resumeId?: string;
    fileName?: string;
    uploadedAt?: string;
    isDefault?: boolean;
    parseIntoProfile?: boolean;
  }

  // Props
  const {
    data = null,
    onSave,
    disabled = false,
    onParsingStatusChange,
  } = $props<{
    data: ResumeSchema | null;
    onSave: (data: ResumeSchema) => Promise<boolean>;
    disabled?: boolean;
    onParsingStatusChange?: (isParsing: boolean) => void;
  }>();

  // State
  let modalOpen = $state(false);
  let isParsing = $state(false);

  // Initialize WebSocket listener for resume parsing status
  import {
    registerParsingListener,
    unregisterParsingListener,
  } from '$lib/websocket/resume-parsing-handler';
  import { onDestroy } from 'svelte';

  // Open edit modal
  function openEditModal() {
    modalOpen = true;
  }

  // Download resume
  async function downloadResume() {
    if (!data?.resumeId) return;

    try {
      // Get the resume document
      const response = await fetch(`/api/resume/${data.resumeId}/download`);
      if (!response.ok) {
        throw new Error('Failed to download resume');
      }

      // Get the blob
      const blob = await response.blob();

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = data.fileName || 'resume.pdf';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading resume:', error);
      toast.error('Failed to download resume');
    }
  }

  // Handle modal save
  async function handleSave(updatedData: ResumeSchema): Promise<boolean> {
    try {
      const success = await onSave(updatedData);

      // If we're parsing a resume, update the UI state and notify the parent component
      if (updatedData.parseIntoProfile) {
        console.log('Resume parsing requested, updating UI state');
        isParsing = true;
        if (onParsingStatusChange) {
          onParsingStatusChange(true);
        }
      }

      return success;
    } catch (error) {
      console.error('Error saving resume:', error);
      toast.error('Failed to save resume');
      return false;
    }
  }

  // Format date for display
  function formatDate(dateString: string | undefined): string {
    if (!dateString) return 'Not available';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  // Setup WebSocket listener for resume parsing status
  $effect(() => {
    if (data?.resumeId) {
      // Register listener for this resume
      registerParsingListener(
        data.resumeId,
        (resumeId, parsedData) => {
          console.log(`Resume parsing completed for ${resumeId}:`, parsedData);
          isParsing = false;
          if (onParsingStatusChange) {
            onParsingStatusChange(false);
          }

          // Show success toast notification
          toast.success('Resume parsing completed successfully', {
            description: 'Your resume has been parsed and the data has been added to your profile.',
            duration: 5000,
          });
        },
        (resumeId, error) => {
          console.error(`Resume parsing failed for ${resumeId}:`, error);
          isParsing = false;
          if (onParsingStatusChange) {
            onParsingStatusChange(false);
          }

          // Show error toast notification
          toast.error(`Resume parsing failed`, {
            description: error ? `Error: ${error}` : 'Please try again later.',
            duration: 5000,
          });
        }
      );

      // Check if resume is currently being parsed - do this immediately and force update UI
      checkParsingStatus(data.resumeId).then((isCurrentlyParsing) => {
        console.log(`Resume ${data.resumeId} parsing status check: ${isCurrentlyParsing}`);
        isParsing = isCurrentlyParsing;
        if (onParsingStatusChange) {
          onParsingStatusChange(isCurrentlyParsing);
        }
      });

      // Listen for the custom resume-parsing-completed event
      const handleParsingCompleted = (event: any) => {
        if (event.detail?.resumeId === data.resumeId) {
          console.log(`Received resume-parsing-completed event for ${data.resumeId}`);
          isParsing = false;
          if (onParsingStatusChange) {
            onParsingStatusChange(false);
          }
        }
      };

      // Listen for the custom resume-parsing-status event (for in-progress updates)
      const handleParsingStatus = (event: any) => {
        if (event.detail?.resumeId === data.resumeId && event.detail?.isParsing) {
          console.log(
            `Received resume-parsing-status event for ${data.resumeId} with isParsing=true`
          );
          isParsing = true;
          if (onParsingStatusChange) {
            onParsingStatusChange(true);
          }
        }
      };

      window.addEventListener('resume-parsing-completed', handleParsingCompleted);
      window.addEventListener('resume-parsing-status', handleParsingStatus);

      // Clean up event listeners when component is destroyed
      return () => {
        window.removeEventListener('resume-parsing-completed', handleParsingCompleted);
        window.removeEventListener('resume-parsing-status', handleParsingStatus);
      };
    }
  });

  // Cleanup listener on component destroy
  onDestroy(() => {
    if (data?.resumeId) {
      unregisterParsingListener(data.resumeId);
    }
  });

  // Check if resume is currently being parsed
  async function checkParsingStatus(resumeId: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/resume/${resumeId}/parsing-status`);
      if (!response.ok) {
        throw new Error('Failed to check parsing status');
      }

      const statusData = await response.json();

      // Only update parsing status if it's actually parsing
      // This prevents unnecessary state updates for resumes that aren't being parsed
      if (statusData.isParsing) {
        isParsing = true;
        if (onParsingStatusChange) {
          onParsingStatusChange(true);
        }
        return true;
      } else {
        // If not parsing, don't need to check again
        isParsing = false;
        if (onParsingStatusChange) {
          onParsingStatusChange(false);
        }
        return false;
      }
    } catch (error) {
      console.error('Error checking parsing status:', error);
      return false;
    }
  }
</script>

<div class="border-border space-y-4 rounded-md border p-6">
  <div class="flex items-center justify-between">
    <div class="flex items-center gap-2">
      <h3 class="text-lg font-medium">Resume</h3>
      {#if isParsing}
        <div
          class="flex items-center rounded-md bg-amber-50 px-2 py-1 text-xs text-amber-600 dark:bg-amber-900/20 dark:text-amber-200">
          <span class="mr-1 animate-pulse">●</span>
          Parsing...
        </div>
      {/if}
    </div>
    <div class="flex space-x-2">
      {#if data?.resumeId}
        <Button
          variant="outline"
          size="sm"
          onclick={downloadResume}
          disabled={disabled || isParsing}>
          <Download class="mr-2 h-4 w-4" />
          Download
        </Button>
      {/if}
      <Button variant="ghost" size="sm" onclick={openEditModal} disabled={disabled || isParsing}>
        <Edit class="mr-2 h-4 w-4" />
        {data?.resumeId ? 'Replace' : 'Upload'}
      </Button>
    </div>
  </div>

  {#if data?.resumeId}
    <div class="grid grid-cols-1 gap-4 rounded-md border p-4 md:grid-cols-2">
      <div>
        <p class="text-sm font-medium">File Name</p>
        <p class="text-sm">{data.fileName || 'Not available'}</p>
      </div>
      <div>
        <p class="text-sm font-medium">Uploaded</p>
        <p class="text-sm">{formatDate(data.uploadedAt)}</p>
      </div>
    </div>
  {:else}
    <div class="flex flex-col items-center justify-center rounded-md border p-8">
      <p class="text-muted-foreground mb-4">No resume uploaded yet</p>
      <Button variant="outline" onclick={openEditModal} {disabled}>
        <Upload class="mr-2 h-4 w-4" />
        Upload Resume
      </Button>
    </div>
  {/if}
</div>

<ResumeModal
  open={modalOpen}
  {data}
  onClose={() => (modalOpen = false)}
  onSave={handleSave}
  {disabled} />
