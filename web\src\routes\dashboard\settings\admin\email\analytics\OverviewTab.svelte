<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Button from '$lib/components/ui/button/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import * as Table from '$lib/components/ui/table/index.js';
  import * as Popover from '$lib/components/ui/popover/index.js';
  import { RangeCalendar } from '$lib/components/ui/range-calendar';
  import { getLocalTimeZone } from '@internationalized/date';
  import type { DateRange } from 'bits-ui';
  import { Mail, MousePointerClick, AlertTriangle, Download, RefreshCw } from 'lucide-svelte';

  // Props
  export let dateRange: { startDate: Date | undefined; endDate: Date | undefined };
  export let calendarDateRange: DateRange;
  export let templateFilter: string;
  export let templates: Array<{
    name: string;
    label: string;
    category?: string;
    description?: string;
  }> = [];
  export let emailStats: {
    total: number;
    delivered: number;
    opened: number;
    clicked: number;
    bounced: number;
    complained?: number;
    unsubscribed?: number;
  } = { total: 0, delivered: 0, opened: 0, clicked: 0, bounced: 0 };
  export let chartData: Array<any> = [];
  export let topEmails: Array<any> = [];
  export let isLoading = false;
  export let isExporting = false;

  // Functions
  export let handleCalendarDateChange: (event: CustomEvent) => void;
  export let handleFilterChange: () => void;
  export let loadEmailStats: () => Promise<void>;
  export let exportData: () => Promise<void>;
</script>

<!-- Overview Filters -->
<div class="mb-6 flex flex-wrap gap-4">
  <div>
    <label for="timeRange" class="mb-1 block text-sm font-medium">Time Range</label>
    <div>
      <Popover.Root>
        <Popover.Trigger>
          <Button.Root variant="outline" class="w-[250px] justify-start">
            <span class="mr-2">📅</span>
            {#if dateRange.startDate && dateRange.endDate}
              {new Date(dateRange.startDate).toLocaleDateString()} - {new Date(
                dateRange.endDate
              ).toLocaleDateString()}
            {:else}
              Select date range
            {/if}
          </Button.Root>
        </Popover.Trigger>
        <Popover.Content class="w-auto p-0" align="start">
          <RangeCalendar
            value={calendarDateRange}
            onValueChange={(newValue) => {
              if (newValue?.start && newValue?.end) {
                // Convert to JavaScript Date objects
                const startDate = newValue.start.toDate(getLocalTimeZone());
                const endDate = newValue.end.toDate(getLocalTimeZone());

                // Create a proper CustomEvent
                const customEvent = new CustomEvent('change', {
                  detail: { startDate, endDate, calendarValue: newValue },
                });

                // Call the handler with the proper event
                handleCalendarDateChange(customEvent);
              }
            }}
            numberOfMonths={2} />
        </Popover.Content>
      </Popover.Root>
    </div>
  </div>

  <div>
    <label for="templateFilter" class="mb-1 block text-sm font-medium">Template</label>
    <Select.Root
      type="single"
      value={templateFilter}
      onValueChange={(value) => {
        templateFilter = value;
        handleFilterChange();
      }}>
      <Select.Trigger class="w-[250px]">
        <Select.Value placeholder="Select template" />
      </Select.Trigger>
      <Select.Content class="w-[250px]">
        <Select.Group>
          <Select.Item value="all">All Templates</Select.Item>
        </Select.Group>

        {#if templates.length > 1}
          {#each [...new Set(templates
                .filter((t) => t.name !== 'all')
                .map((t) => t.category || 'Other'))] as category}
            <Select.Separator />
            <div class="px-2 py-1.5 text-sm font-semibold">
              {category}
            </div>
            <Select.Group>
              {#each templates.filter((t) => t.name !== 'all' && (t.category || 'Other') === category) as template}
                <Select.Item value={template.name} title={template.description || ''}>
                  {template.label}
                </Select.Item>
              {/each}
            </Select.Group>
          {/each}
        {/if}
      </Select.Content>
    </Select.Root>
  </div>

  <div class="ml-auto flex gap-2">
    <div>
      <div class="mb-1 block text-sm font-medium opacity-0">Refresh</div>
      <Button.Root
        variant="outline"
        size="icon"
        onclick={() => loadEmailStats()}
        disabled={isLoading}
        class="h-10 w-10">
        <RefreshCw class={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
        <span class="sr-only">Refresh</span>
      </Button.Root>
    </div>

    <div>
      <div class="mb-1 block text-sm font-medium opacity-0">Export</div>
      <Button.Root variant="outline" disabled={isExporting} onclick={() => exportData()}>
        {#if isExporting}
          <div
            class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent">
          </div>
        {:else}
          <Download class="mr-2 h-4 w-4" />
        {/if}
        Export Data
      </Button.Root>
    </div>
  </div>
</div>

{#if !emailStats.total && !isLoading}
  <div class="flex h-64 flex-col items-center justify-center gap-4">
    <p class="text-muted-foreground text-center">
      Click the button below to load email analytics data
    </p>
    <Button.Root onclick={() => loadEmailStats()}>Load Analytics Data</Button.Root>
  </div>
{:else if isLoading}
  <div class="flex h-64 items-center justify-center">
    <div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent">
    </div>
  </div>
{:else}
  <!-- Stats Cards -->
  <div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
    <Card.Root>
      <Card.Content class="p-6">
        <div class="flex items-start justify-between">
          <div>
            <p class="text-muted-foreground text-sm font-medium">Total Emails</p>
            <h3 class="mt-1 text-2xl font-bold">{emailStats.total.toLocaleString()}</h3>
          </div>
          <div class="bg-primary/10 rounded-full p-2">
            <Mail class="text-primary h-5 w-5" />
          </div>
        </div>
        <div class="text-muted-foreground mt-4 text-sm">
          <span class="font-medium text-green-600"
            >{Math.round((emailStats.delivered / emailStats.total) * 100) || 0}%</span> delivery rate
        </div>
      </Card.Content>
    </Card.Root>

    <Card.Root>
      <Card.Content class="p-6">
        <div class="flex items-start justify-between">
          <div>
            <p class="text-muted-foreground text-sm font-medium">Open Rate</p>
            <h3 class="mt-1 text-2xl font-bold">
              {Math.round((emailStats.opened / emailStats.delivered) * 100) || 0}%
            </h3>
          </div>
          <div class="rounded-full bg-blue-100 p-2">
            <Mail class="h-5 w-5 text-blue-600" />
          </div>
        </div>
        <div class="text-muted-foreground mt-4 text-sm">
          <span class="font-medium">{emailStats.opened.toLocaleString()}</span> emails opened
        </div>
      </Card.Content>
    </Card.Root>

    <Card.Root>
      <Card.Content class="p-6">
        <div class="flex items-start justify-between">
          <div>
            <p class="text-muted-foreground text-sm font-medium">Click Rate</p>
            <h3 class="mt-1 text-2xl font-bold">
              {Math.round((emailStats.clicked / emailStats.opened) * 100) || 0}%
            </h3>
          </div>
          <div class="rounded-full bg-purple-100 p-2">
            <MousePointerClick class="h-5 w-5 text-purple-600" />
          </div>
        </div>
        <div class="text-muted-foreground mt-4 text-sm">
          <span class="font-medium">{emailStats.clicked.toLocaleString()}</span> emails clicked
        </div>
      </Card.Content>
    </Card.Root>

    <Card.Root>
      <Card.Content class="p-6">
        <div class="flex items-start justify-between">
          <div>
            <p class="text-muted-foreground text-sm font-medium">Bounce Rate</p>
            <h3 class="mt-1 text-2xl font-bold">
              {Math.round((emailStats.bounced / emailStats.total) * 100) || 0}%
            </h3>
          </div>
          <div class="rounded-full bg-red-100 p-2">
            <AlertTriangle class="h-5 w-5 text-red-600" />
          </div>
        </div>
        <div class="text-muted-foreground mt-4 text-sm">
          <span class="font-medium">{emailStats.bounced.toLocaleString()}</span> emails bounced
        </div>
      </Card.Content>
    </Card.Root>
  </div>

  <!-- Charts -->
  <div class="mb-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
    <Card.Root>
      <Card.Header>
        <Card.Title>Email Activity Over Time</Card.Title>
        <Card.Description>Email events over the selected time period</Card.Description>
      </Card.Header>

      <Card.Content>
        {#if chartData.length === 0}
          <div class="text-muted-foreground flex h-64 items-center justify-center">
            <p>No data available for the selected time period</p>
          </div>
        {:else}
          <div class="text-muted-foreground flex h-64 items-center justify-center">
            <Mail class="h-8 w-8 opacity-50" />
            <p class="ml-2">Chart visualization would appear here</p>
          </div>
        {/if}
      </Card.Content>
    </Card.Root>

    <Card.Root>
      <Card.Header>
        <Card.Title>Event Distribution</Card.Title>
        <Card.Description>Distribution of email events by type</Card.Description>
      </Card.Header>

      <Card.Content>
        {#if emailStats.total === 0}
          <div class="text-muted-foreground flex h-64 items-center justify-center">
            <p>No data available for the selected time period</p>
          </div>
        {:else}
          <div class="text-muted-foreground flex h-64 items-center justify-center">
            <Mail class="h-8 w-8 opacity-50" />
            <p class="ml-2">Chart visualization would appear here</p>
          </div>
        {/if}
      </Card.Content>
    </Card.Root>
  </div>

  <!-- Top Performing Emails -->
  <Card.Root>
    <Card.Header>
      <Card.Title>Top Performing Emails</Card.Title>
      <Card.Description>Emails with the highest open and click rates</Card.Description>
    </Card.Header>

    <Card.Content>
      {#if topEmails.length === 0}
        <div class="text-muted-foreground flex h-40 items-center justify-center">
          <p>No data available for the selected time period</p>
        </div>
      {:else}
        <Table.Root>
          <Table.Header>
            <Table.Row>
              <Table.Head>Template</Table.Head>
              <Table.Head>Subject</Table.Head>
              <Table.Head>Sent</Table.Head>
              <Table.Head>Open Rate</Table.Head>
              <Table.Head>Click Rate</Table.Head>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {#each topEmails as email}
              <Table.Row>
                <Table.Cell>{email.template}</Table.Cell>
                <Table.Cell>{email.subject}</Table.Cell>
                <Table.Cell>{email.sent.toLocaleString()}</Table.Cell>
                <Table.Cell>
                  <div class="flex items-center">
                    <div class="mr-2 h-2 w-16 rounded-full bg-gray-200">
                      <div class="h-2 rounded-full bg-blue-600" style="width: {email.openRate}%">
                      </div>
                    </div>
                    <span>{email.openRate}%</span>
                  </div>
                </Table.Cell>
                <Table.Cell>
                  <div class="flex items-center">
                    <div class="mr-2 h-2 w-16 rounded-full bg-gray-200">
                      <div class="h-2 rounded-full bg-purple-600" style="width: {email.clickRate}%">
                      </div>
                    </div>
                    <span>{email.clickRate}%</span>
                  </div>
                </Table.Cell>
              </Table.Row>
            {/each}
          </Table.Body>
        </Table.Root>
      {/if}
    </Card.Content>
  </Card.Root>
{/if}
