// @ts-nocheck
// File: src/routes/dashboard/resume/[id]/optimize/+page.server.ts
import type { PageServerLoad } from './$types.js';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth.js';
import { redirect } from '@sveltejs/kit';

// Using the shared Prisma client from $lib/server/prisma

export const load = async ({ params, cookies, locals }: Parameters<PageServerLoad>[0]) => {
  const token = cookies.get('auth_token');
  const user = token && verifySessionToken(token);

  if (user) locals.user = user;
  if (!locals.user) throw redirect(302, '/auth/sign-in');

  const resume = await prisma.resume.findFirst({
    where: {
      id: params.id,
      profile: {
        OR: [{ userId: user.id }, { team: { members: { some: { userId: user.id } } } }],
      },
    },
    include: { profile: true },
  });

  if (!resume) throw redirect(302, '/dashboard/resume');

  const optimization = await prisma.resumeOptimizationResult.findUnique({
    where: { resumeId: resume.id },
  });

  return { resume, optimization };
};
