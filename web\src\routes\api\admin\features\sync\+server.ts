import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';
import type { RequestHandler } from './$types';

/**
 * API endpoint to sync features across all plans
 * This ensures that all plans have all features assigned to them
 */
export const POST: RequestHandler = async ({ cookies, request }) => {
  const token = cookies.get('auth_token');

  if (!token) {
    return new Response('Unauthorized', { status: 401 });
  }

  const userData = await verifySessionToken(token);
  if (!userData?.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Check if user is an admin
  const user = await prisma.user.findUnique({
    where: { id: userData.id },
    select: { isAdmin: true, role: true },
  });

  // Allow access if either isAdmin is true OR role is 'admin'
  if (!user || (!user.isAdmin && user.role !== 'admin')) {
    return new Response('Unauthorized - Admin access required', { status: 401 });
  }

  try {
    // Get all features
    const features = await prisma.feature.findMany();

    // Get all plans
    const plans = await prisma.plan.findMany({
      include: {
        features: true
      }
    });

    // Track changes for reporting
    const changes = {
      total: 0,
      byPlan: {}
    };

    // For each plan, ensure it has all features
    for (const plan of plans) {
      // Initialize changes counter for this plan
      changes.byPlan[plan.id] = 0;

      // Get existing feature IDs for this plan
      const existingFeatureIds = plan.features.map(pf => pf.featureId);

      // Find features that are not assigned to this plan
      const missingFeatures = features.filter(feature =>
        !existingFeatureIds.includes(feature.id)
      );

      if (missingFeatures.length > 0) {
        console.log(`Adding ${missingFeatures.length} missing features to plan ${plan.name}`);

        // Determine default access level based on plan
        let defaultAccessLevel = 'included';

        // For free plan, set most features to not_included by default
        if (plan.id === 'free' || plan.name.toLowerCase() === 'free') {
          defaultAccessLevel = 'not_included';

          // For free plan, we'll set specific categories to be included or limited
          const includedCategories = ['core'];
          const limitedCategories = ['resume', 'job_search', 'applications'];

          // Specific features that should be included or limited for the free plan
          const includedFeatures = ['application_tracker', 'application_submit'];
          const limitedFeatures = ['cover_letter_generator', 'application_tracking', 'resume_scanner', 'resume_builder'];

          // Create feature assignments with appropriate access levels
          const featureAssignments = missingFeatures.map(feature => {
            let accessLevel = defaultAccessLevel;

            // First check specific features
            if (includedFeatures.includes(feature.id)) {
              accessLevel = 'included';
            } else if (limitedFeatures.includes(feature.id)) {
              accessLevel = 'limited';
            }
            // Then check categories if not already set
            else if (includedCategories.includes(feature.category)) {
              accessLevel = 'included';
            } else if (limitedCategories.includes(feature.category)) {
              accessLevel = 'limited';
            }

            return {
              planId: plan.id,
              featureId: feature.id,
              accessLevel
            };
          });

          // Add features to plan
          await prisma.planFeature.createMany({
            data: featureAssignments
          });

          changes.total += featureAssignments.length;
          changes.byPlan[plan.id] = featureAssignments.length;
        } else {
          // For paid plans, include all features by default
          // Create feature assignments
          const featureAssignments = missingFeatures.map(feature => ({
            planId: plan.id,
            featureId: feature.id,
            accessLevel: defaultAccessLevel
          }));

          // Add features to plan
          await prisma.planFeature.createMany({
            data: featureAssignments
          });

          changes.total += featureAssignments.length;
          changes.byPlan[plan.id] = featureAssignments.length;
        }
      }
    }

    return json({
      success: true,
      message: `Synced features across all plans. Added ${changes.total} missing feature assignments.`,
      changes
    });
  } catch (error) {
    console.error('Error syncing features:', error);
    return json({
      success: false,
      error: 'Failed to sync features',
      details: error.message
    }, { status: 500 });
  }
};
