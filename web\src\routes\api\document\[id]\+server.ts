import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';

/**
 * GET handler to fetch a document by ID
 */
export const GET: RequestHandler = async ({ params, locals }) => {
  const user = locals.user;
  if (!user) return new Response('Unauthorized', { status: 401 });

  const { id } = params;
  if (!id) {
    return json({ error: 'Document ID is required' }, { status: 400 });
  }

  try {
    // Get the document
    const document = await prisma.document.findUnique({
      where: { id },
    });

    if (!document) {
      return json({ error: 'Document not found' }, { status: 404 });
    }

    // Check if the user has access to this document
    if (document.userId !== user.id) {
      return json({ error: 'Unauthorized access to document' }, { status: 403 });
    }

    return json(document);
  } catch (error) {
    console.error('Error fetching document:', error);
    return json(
      {
        error: 'Failed to fetch document',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
};

/**
 * PATCH handler to update a document
 */
export const PATCH: RequestHandler = async ({ params, request, locals }) => {
  const user = locals.user;
  if (!user) return new Response('Unauthorized', { status: 401 });

  const { id } = params;
  if (!id) {
    return json({ error: 'Document ID is required' }, { status: 400 });
  }

  try {
    // Get the document
    const document = await prisma.document.findUnique({
      where: { id },
    });

    if (!document) {
      return json({ error: 'Document not found' }, { status: 404 });
    }

    // Check if the user has access to this document
    if (document.userId !== user.id) {
      return json({ error: 'Unauthorized access to document' }, { status: 403 });
    }

    // Get the request body
    const data = await request.json();

    // Validate the data
    const allowedFields = ['label', 'profileId', 'isDefault'];
    const updateData = {};

    // Only allow updating specific fields
    for (const field of allowedFields) {
      if (field in data) {
        updateData[field] = data[field];
      }
    }

    // If profileId is provided, check if the profile exists and belongs to the user
    if ('profileId' in data) {
      const profile = await prisma.profile.findUnique({
        where: {
          id: data.profileId,
          userId: user.id,
        },
      });

      if (!profile) {
        return json({ error: 'Profile not found or unauthorized' }, { status: 404 });
      }
    }

    // Update the document
    const updatedDocument = await prisma.document.update({
      where: { id },
      data: updateData,
    });

    return json({
      success: true,
      document: updatedDocument,
    });
  } catch (error) {
    console.error('Error updating document:', error);
    return json(
      {
        error: 'Failed to update document',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
};

/**
 * DELETE handler to delete a document
 */
export const DELETE: RequestHandler = async ({ params, locals }) => {
  const user = locals.user;
  if (!user) return new Response('Unauthorized', { status: 401 });

  const { id } = params;
  if (!id) {
    return json({ error: 'Document ID is required' }, { status: 400 });
  }

  try {
    // Get the document
    const document = await prisma.document.findUnique({
      where: { id },
    });

    if (!document) {
      return json({ error: 'Document not found' }, { status: 404 });
    }

    // Check if the user has access to this document
    if (document.userId !== user.id) {
      return json({ error: 'Unauthorized access to document' }, { status: 403 });
    }

    // Delete the document
    await prisma.document.delete({
      where: { id },
    });

    return json({
      success: true,
      message: 'Document deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting document:', error);
    return json(
      {
        error: 'Failed to delete document',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
};
