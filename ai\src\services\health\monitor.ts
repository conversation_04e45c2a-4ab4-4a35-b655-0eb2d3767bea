import os from "os";
import { logger } from "../../utils/logger.js";
import { config } from "../../config.js";
import { updateCircuitBreakerState } from "./circuit-breaker.js";
import { recordMetrics } from "./metrics.js";
import { getContainerMetrics } from "../../utils/containerMetrics.js";

export interface SystemMetrics {
  cpuUsage: number;
  memoryUsage: number;
  uptime: number;
  timestamp: number;
}

let metrics: SystemMetrics = {
  cpuUsage: 0,
  memoryUsage: 0,
  uptime: 0,
  timestamp: Date.now(),
};

export async function collectMetrics(): Promise<SystemMetrics> {
  try {
    // Get container metrics
    let cpuUsage = 0;
    let memoryUsage = 0;

    try {
      const containerMetrics = await getContainerMetrics();

      if (containerMetrics) {
        // We're in a containerized environment and could read cgroups data
        cpuUsage = containerMetrics.cpuUsagePercent;
        memoryUsage = containerMetrics.memoryUsagePercent;

        logger.debug(
          `📊 Using container metrics - CPU: ${cpuUsage.toFixed(
            1
          )}%, Memory: ${memoryUsage.toFixed(1)}%`
        );
      } else {
        // Not in a container, use default values
        logger.warn(
          `⚠️ Not running in a container, using default values for metrics`
        );
      }
    } catch (metricsError) {
      // If container metrics fail, log error and use default values
      logger.error(`❌ Error getting container metrics: ${metricsError}`);
    }

    // Ensure we don't have NaN values
    if (isNaN(cpuUsage)) {
      logger.warn(`⚠️ CPU usage is NaN, using fallback value of 0`);
      cpuUsage = 0;
    }

    if (isNaN(memoryUsage)) {
      logger.warn(`⚠️ Memory usage is NaN, using fallback value of 0`);
      memoryUsage = 0;
    }

    metrics = {
      cpuUsage,
      memoryUsage,
      uptime: os.uptime(),
      timestamp: Date.now(),
    };

    // Update circuit breaker state
    updateCircuitBreakerState(metrics);

    // Log metrics in the same format as other services
    logger.info(
      `📊 System metrics - CPU: ${cpuUsage.toFixed(
        1
      )}%, Memory: ${memoryUsage.toFixed(1)}%`
    );

    return metrics;
  } catch (error) {
    logger.error("Failed to collect system metrics:", error);
    return metrics;
  }
}

export function getLatestMetrics(): SystemMetrics {
  return metrics;
}

export function setupHealthMonitor() {
  // Collect metrics immediately
  collectMetrics();

  // Set up periodic metrics collection
  setInterval(async () => {
    await collectMetrics();
  }, config.health.checkInterval);

  // Set up periodic metrics recording
  setInterval(async () => {
    await recordMetrics(metrics);
  }, config.health.metricsInterval);

  logger.info("✅ Health monitoring started");
}
