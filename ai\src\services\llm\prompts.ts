export const RESUME_ANALYSIS_PROMPT = `
You are an expert ATS (Applicant Tracking System) analyzer. Analyze this resume for ATS compatibility.

{{#if jobDescription}}
Job Description: {{jobDescription}}
{{else}}
Analyze for general ATS compatibility
{{/if}}

Resume:
{{resumeText}}

Provide a JSON response with the following structure:
{
  "overallScore": number between 0-100,
  "keywordScore": number between 0-100,
  "formatScore": number between 0-100,
  "contentScore": number between 0-100,
  "readabilityScore": number between 0-100,
  "keywordMatches": [list of keywords found in resume],
  "missingKeywords": [list of important keywords missing from resume],
  "formatIssues": [list of formatting issues],
  "contentSuggestions": [list of content improvement suggestions],
  "readabilitySuggestions": [list of readability improvement suggestions]
}
`;

export const KEYWORD_EXTRACTION_PROMPT = `
Extract the most important professional keywords from this text that would be relevant for an ATS system.
Focus on skills, technologies, job titles, and qualifications.
Return only a comma-separated list of keywords, with no additional text.

Text:
{{text}}
`;

export const INDUSTRY_KEYWORDS_PROMPT = `
Analyze this resume text and identify the industry or field it belongs to.
Then, provide a list of 10-15 important keywords that are commonly expected by ATS systems for this industry.

Resume text:
{{resumeText}}

Output format:
Industry: [identified industry]
Keywords: [comma-separated list of keywords]
`;

export function compilePrompt(
  template: string,
  data: Record<string, any>
): string {
  return template.replace(/\{\{([^}]+)\}\}/g, (match, key, offset) => {
    const trimmedKey = key.trim();

    // Handle conditional blocks
    if (trimmedKey.startsWith("#if ")) {
      const condition = trimmedKey.substring(4);
      // @ts-ignore
      const endIfIndex = template.indexOf("{{/if}}", offset);
      if (endIfIndex === -1) return ""; // Safety check
      const blockContent = template.substring(
        match.length + offset,
        endIfIndex
      );

      return data[condition] ? compilePrompt(blockContent, data) : "";
    }

    // Handle regular variables
    return data[trimmedKey] !== undefined ? data[trimmedKey] : "";
  });
}
