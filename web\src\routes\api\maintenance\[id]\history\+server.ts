// src/routes/api/maintenance/[id]/history/+server.ts
import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { logger } from '$lib/server/logger';
import type { RequestHandler } from './$types';

// Get maintenance event history
export const GET: RequestHandler = async ({ params, locals }) => {
  try {
    // Check if user is admin
    const user = locals.user;
    if (!user || !user.isAdmin) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get event ID from params
    const { id } = params;
    if (!id) {
      return json({ error: 'Missing event ID' }, { status: 400 });
    }

    // Check if event exists
    const event = await prisma.maintenanceEvent.findUnique({
      where: { id },
    });

    if (!event) {
      return json({ error: 'Maintenance event not found' }, { status: 404 });
    }

    // Get history for this event
    const history = await prisma.maintenanceEventHistory.findMany({
      where: { eventId: id },
      orderBy: { createdAt: 'desc' },
    });

    return json(history);
  } catch (error) {
    logger.error('Error fetching maintenance event history:', error);
    return json({ error: 'Failed to fetch maintenance event history' }, { status: 500 });
  }
};
