<!-- src/components/help/HelpSearch.svelte -->
<script lang="ts">
  import { goto } from '$app/navigation';
  import { SearchInput } from '$lib/components/ui/search-input';
  import { searchHelpArticles } from '$lib/services/help-service';
  import { debounce } from '$lib/utils';
  import * as Popover from '$lib/components/ui/popover';
  import * as ScrollArea from '$lib/components/ui/scroll-area';
  import { ArrowRight } from 'lucide-svelte';
  import { onMount } from 'svelte';

  // Props
  let { className = '', searchQuery = $bindable('') } = $props<{
    className?: string;
    searchQuery?: string;
  }>();

  // State
  let searchResults = $state<any[]>([]);
  let isLoading = $state(false);
  let isOpen = $state(false);
  let totalResults = $state(0);

  // Debounced search function
  const debouncedSearch = debounce(async (query: string) => {
    if (!query || query.length < 2) {
      searchResults = [];
      isLoading = false;
      return;
    }

    isLoading = true;
    try {
      const response = await searchHelpArticles(query);
      searchResults = response.articles.slice(0, 5); // Show top 5 results in dropdown
      totalResults = response.pagination.total;
    } catch (error) {
      console.error('Search error:', error);
      searchResults = [];
      totalResults = 0;
    } finally {
      isLoading = false;
    }
  }, 300);

  // Watch for search query changes
  $effect(() => {
    if (searchQuery) {
      isOpen = true;
      debouncedSearch(searchQuery);
    } else {
      searchResults = [];
      isOpen = false;
    }
  });

  // Handle search submission
  function handleSearch() {
    if (searchQuery.trim()) {
      goto(`/help/search?q=${encodeURIComponent(searchQuery.trim())}`);
      isOpen = false;
    }
  }

  // We don't need the handleKeyDown function anymore since we removed the on:keydown event

  // Close popover when clicking outside
  onMount(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.help-search-container')) {
        isOpen = false;
      }
    };

    document.addEventListener('click', handleClickOutside);

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  });
</script>

<div class="help-search-container {className}">
  <Popover.Root bind:open={isOpen}>
    <Popover.Trigger>
      <div class="w-full">
        <SearchInput
          bind:value={searchQuery}
          placeholder="Search help articles..."
          onSearch={handleSearch}
          className="w-full" />
      </div>
    </Popover.Trigger>
    <Popover.Content
      class="w-[var(--radix-popover-trigger-width)] p-0"
      align="start"
      sideOffset={5}>
      {#if isLoading}
        <div class="flex items-center justify-center p-4">
          <div class="border-muted border-t-primary h-5 w-5 animate-spin rounded-full border-2">
          </div>
          <span class="text-muted-foreground ml-2 text-sm">Searching...</span>
        </div>
      {:else if searchResults.length > 0}
        <ScrollArea.Root class="h-[300px]">
          <div class="p-2">
            <div class="space-y-1">
              {#each searchResults as result}
                <a
                  href="/help/{result.slug}"
                  class="hover:bg-accent flex items-center justify-between rounded-md p-2">
                  <div>
                    <div class="font-medium">{result.title}</div>
                    {#if result.excerpt}
                      <div class="text-muted-foreground line-clamp-1 text-sm">
                        {result.excerpt}
                      </div>
                    {/if}
                  </div>
                  <ArrowRight class="text-muted-foreground h-4 w-4" />
                </a>
              {/each}
            </div>
            {#if totalResults > 5}
              <div class="mt-2 border-t pt-2">
                <a
                  href="/help/search?q={encodeURIComponent(searchQuery)}"
                  class="text-primary hover:bg-accent flex items-center justify-between rounded-md p-2 text-sm">
                  <span>See all {totalResults} results</span>
                  <ArrowRight class="h-4 w-4" />
                </a>
              </div>
            {/if}
          </div>
        </ScrollArea.Root>
      {:else if searchQuery.length >= 2}
        <div class="text-muted-foreground p-4 text-center text-sm">
          No results found for "{searchQuery}"
        </div>
      {:else}
        <div class="text-muted-foreground p-4 text-center text-sm">
          Type at least 2 characters to search
        </div>
      {/if}
    </Popover.Content>
  </Popover.Root>
</div>
