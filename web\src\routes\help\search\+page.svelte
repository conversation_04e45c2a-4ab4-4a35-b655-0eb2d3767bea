<!-- src/routes/help/search/+page.svelte -->
<script lang="ts">
  import SEO from '$components/shared/SEO.svelte';
  import HelpSearch from '$components/help/HelpSearch.svelte';
  import HelpSidebar from '$components/help/HelpSidebar.svelte';
  import HelpArticleCard from '$components/help/HelpArticleCard.svelte';
  import { ArrowLeft, Search } from 'lucide-svelte';

  // Get data from server
  export let data;
</script>

<SEO
  title="Search Results | Help Center"
  description="Search results for '{data.query}' in the Help Center."
  keywords="help center, search, {data.query}, support, guides, tutorials" />

<div class="container mx-auto px-4 py-12">
  <div class="grid gap-8 lg:grid-cols-4">
    <!-- Sidebar -->
    <div class="lg:col-span-1">
      <HelpSidebar categories={data.categories} />
    </div>

    <!-- Main Content -->
    <div class="lg:col-span-3">
      <div class="mb-6">
        <a href="/help" class="text-primary inline-flex items-center text-sm hover:underline">
          <ArrowLeft class="mr-1 h-4 w-4" />
          Back to Help Center
        </a>
      </div>

      <div class="mb-8 flex items-center gap-3">
        <div class="bg-primary/10 text-primary rounded-full p-3">
          <Search class="h-6 w-6" />
        </div>
        <h1 class="text-3xl font-bold">Search Results</h1>
      </div>

      <!-- Search Bar -->
      <div class="mb-8">
        <HelpSearch className="w-full" />
      </div>

      <!-- Search Results -->
      {#if data.query}
        <div class="mb-6">
          <p class="text-muted-foreground">
            {data.resultCount} result{data.resultCount !== 1 ? 's' : ''} for "{data.query}"
          </p>
        </div>

        {#if data.searchResults.length > 0}
          <div class="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
            {#each data.searchResults as article}
              <HelpArticleCard {article} />
            {/each}
          </div>
        {:else}
          <div class="bg-muted rounded-lg border p-8 text-center">
            <p class="text-muted-foreground mb-2">No results found for "{data.query}".</p>
            <p>Try different keywords or browse categories.</p>
          </div>
        {/if}
      {:else}
        <div class="bg-muted rounded-lg border p-8 text-center">
          <p class="text-muted-foreground mb-2">Enter a search term to find help articles.</p>
          <p>Or browse categories in the sidebar.</p>
        </div>
      {/if}
    </div>
  </div>
</div>
