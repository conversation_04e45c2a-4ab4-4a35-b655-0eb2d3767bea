// scripts/seedStatesAndCities.ts
// Run with:  npx ts-node scripts/seedStatesAndCities.ts

import axios from "axios";
import { PrismaClient } from "@prisma/client";
import fs from "fs/promises";
import path from "path";

const prisma = new PrismaClient();
const DATA_URL =
  "https://gist.githubusercontent.com/knvaughn/f301aa1a662d8f3ed9b3747d6c20cdbc/raw/9b1675e83f0fa060b4d08ab3e130d7d4207308db/US-States-and-Cities.json";
const STATES_FILE = path.resolve("utils/json/states.json"); // <-- local lookup

type StateRow = { name: string; abbreviation: string };
const statesData = JSON.parse(
  await fs.readFile(STATES_FILE, "utf-8")
) as StateRow[];
const stateCodeMap: Record<string, string> = Object.fromEntries(
  statesData.map(({ name, abbreviation }) => [
    name.trim(),
    abbreviation.trim().toUpperCase(),
  ])
);

async function main() {
  console.time("⏱  Seeding");

  /* 2‑A  ensure United States row exists */
  const us = await prisma.country.upsert({
    where: { isoCode: "US" },
    create: { name: "United States", isoCode: "US" },
    update: {},
  });

  /* 2‑B  fetch state → cities map */
  const { data } = await axios.get<Record<string, string[]>>(DATA_URL, {
    responseType: "json",
  });

  let totalCities = 0;
  let newStates = 0;

  /* 2‑C  iterate over states */
  for (const [stateName, cities] of Object.entries(data)) {
    const code = stateCodeMap[stateName] ?? null; // may be null if not found

    const dbState = await prisma.state.upsert({
      where: {
        countryId_name: { countryId: us.id, name: stateName },
      },
      update: { code }, // update code if missing
      create: { name: stateName, code, countryId: us.id },
    });

    if (dbState.createdAt.getTime() === Date.now()) {
      newStates++;
    }

    /* unique city list, bulk insert */
    const uniqueCities = Array.from(
      new Set(cities.map((c) => c.trim()).filter(Boolean))
    );

    await prisma.city.createMany({
      data: uniqueCities.map((city) => ({ name: city, stateId: dbState.id })),
      skipDuplicates: true,
    });

    totalCities += uniqueCities.length;
    console.log(
      `🌎  ${stateName} (${code ?? "∅"}): +${uniqueCities.length} cities`
    );
  }

  console.timeEnd("⏱  Seeding");
  console.log(
    `✅  Inserted/updated ${
      Object.keys(data).length
    } states (${newStates} new) and ${totalCities} cities`
  );
}

main()
  .catch((err) => {
    console.error("❌  Seeding failed", err);
    process.exit(1);
  })
  .finally(() => prisma.$disconnect());
