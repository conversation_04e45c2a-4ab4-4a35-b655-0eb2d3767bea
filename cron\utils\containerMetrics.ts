// cron/utils/containerMetrics.ts
// Utility for reading container metrics from cgroups in containerized environments
// Specifically targeting Render's container metrics paths

import fs from "fs";
import os from "os";
import { logger } from "./logger";

/**
 * Container metrics interface
 */
export interface ContainerMetrics {
  memoryUsagePercent: number;
  cpuUsagePercent: number;
  isContainerized: boolean;
}

// Render-specific cgroup paths (updated for newer cgroups v2 paths)
const RENDER_CGROUP_PATHS = {
  memory: "/sys/fs/cgroup/memory.stat",
  cpu: "/sys/fs/cgroup/cpu.stat",
  io: "/sys/fs/cgroup/io.stat",
  cpu_max: "/sys/fs/cgroup/cpu.max", // For quota and period
};

// Alternative paths to try if the primary paths don't exist
const ALTERNATIVE_CGROUP_PATHS = {
  memory: "/sys/fs/cgroup/memory/memory.stat",
  cpu: "/sys/fs/cgroup/cpu/cpu.stat",
  io: "/sys/fs/cgroup/blkio/blkio.stat",
};

// Unified cgroups v2 paths
const UNIFIED_CGROUP_PATHS = {
  memory: "/sys/fs/cgroup/memory.current",
  memoryMax: "/sys/fs/cgroup/memory.max",
  cpu: "/sys/fs/cgroup/cpu.stat",
  io: "/sys/fs/cgroup/io.stat",
};

// No longer needed with the new approach

/**
 * Check if running in a containerized environment
 * Checks multiple possible cgroup paths to handle different container runtimes
 */
export function isContainerized(): boolean {
  try {
    // Check for Render's cgroup paths
    if (
      fs.existsSync(RENDER_CGROUP_PATHS.memory) ||
      fs.existsSync(RENDER_CGROUP_PATHS.cpu)
    ) {
      logger.debug(
        "Detected containerized environment using primary cgroup paths"
      );
      return true;
    }

    // Check alternative cgroup paths
    if (
      fs.existsSync(ALTERNATIVE_CGROUP_PATHS.memory) ||
      fs.existsSync(ALTERNATIVE_CGROUP_PATHS.cpu)
    ) {
      logger.debug(
        "Detected containerized environment using alternative cgroup paths"
      );
      return true;
    }

    // Check unified cgroup v2 paths
    if (
      fs.existsSync(UNIFIED_CGROUP_PATHS.memory) ||
      fs.existsSync(UNIFIED_CGROUP_PATHS.cpu)
    ) {
      logger.debug(
        "Detected containerized environment using unified cgroup v2 paths"
      );
      return true;
    }

    // Check for .dockerenv file (common in Docker)
    if (fs.existsSync("/.dockerenv")) {
      logger.debug("Detected Docker container via .dockerenv file");
      return true;
    }

    // Check for cgroups
    if (fs.existsSync("/proc/self/cgroup")) {
      const cgroupContent = fs.readFileSync("/proc/self/cgroup", "utf8");
      if (
        cgroupContent.includes("docker") ||
        cgroupContent.includes("kubepods") ||
        cgroupContent.includes("render")
      ) {
        logger.debug(
          "Detected containerized environment via /proc/self/cgroup"
        );
        return true;
      }
    }

    // Check for container-specific environment variables
    if (
      process.env.KUBERNETES_SERVICE_HOST ||
      process.env.RENDER ||
      process.env.DOCKER_CONTAINER
    ) {
      logger.debug(
        "Detected containerized environment via environment variables"
      );
      return true;
    }

    return false;
  } catch (error) {
    logger.debug(`Error checking if containerized: ${error}`);
    return false;
  }
}

/**
 * Parse a cgroup stat file and extract values
 */
function parseCgroupStat(filePath: string): Map<string, number> {
  const result = new Map<string, number>();

  try {
    if (!fs.existsSync(filePath)) {
      return result;
    }

    const content = fs.readFileSync(filePath, "utf8");
    const lines = content.split("\n");

    for (const line of lines) {
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 2) {
        const key = parts[0];
        const value = parseInt(parts[1], 10);
        if (!isNaN(value)) {
          result.set(key, value);
        }
      }
    }
  } catch (error) {
    logger.debug(`Error parsing ${filePath}: ${error}`);
  }

  return result;
}

/**
 * Get memory usage percentage from cgroups
 * Directly reads memory.current and memory.max files to calculate usage percentage
 */
function getMemoryUsagePercent(): number | null {
  try {
    // Direct implementation of the shell script approach
    const currentPath = "/sys/fs/cgroup/memory.current";
    const maxPath = "/sys/fs/cgroup/memory.max";

    // Check if both files exist
    if (fs.existsSync(currentPath) && fs.existsSync(maxPath)) {
      logger.debug("Found memory.current and memory.max files");

      // Read current memory usage
      const current = Number(fs.readFileSync(currentPath, "utf8").trim());

      // Read memory limit
      const maxContent = fs.readFileSync(maxPath, "utf8").trim();

      // Check if max is set to "max" (no limit)
      if (maxContent === "max") {
        logger.debug("No memory limit set (memory.max = max)");

        // If no limit is set, use total system memory as fallback
        const totalMem = os.totalmem();
        const usagePercent = (current / totalMem) * 100;
        logger.debug(
          `Memory usage: ${usagePercent.toFixed(2)}% (using system total memory as fallback)`
        );
        return usagePercent;
      } else {
        // Calculate percentage using memory.current and memory.max
        const max = Number(maxContent);

        // Validate max to avoid division by zero or NaN
        if (isNaN(max) || max <= 0) {
          logger.debug(
            `Invalid max memory value: ${maxContent}, using system total memory as fallback`
          );
          const totalMem = os.totalmem();
          const usagePercent = (current / totalMem) * 100;
          return usagePercent;
        }

        const usagePercent = (current / max) * 100;
        logger.debug(
          `Memory usage: ${current} / ${max} = ${usagePercent.toFixed(2)}%`
        );
        return usagePercent;
      }
    }

    // If direct approach fails, try alternative paths
    const alternativeCurrentPaths = [
      UNIFIED_CGROUP_PATHS.memory,
      "/sys/fs/cgroup/memory/memory.usage_in_bytes",
      "/sys/fs/cgroup/memory.usage_in_bytes",
    ];

    const alternativeMaxPaths = [
      UNIFIED_CGROUP_PATHS.memoryMax,
      "/sys/fs/cgroup/memory/memory.limit_in_bytes",
      "/sys/fs/cgroup/memory.limit_in_bytes",
    ];

    // Try each combination of paths
    for (let i = 0; i < alternativeCurrentPaths.length; i++) {
      const currPath = alternativeCurrentPaths[i];
      const limitPath = alternativeMaxPaths[i];

      if (fs.existsSync(currPath) && fs.existsSync(limitPath)) {
        logger.debug(
          `Found alternative memory paths: ${currPath} and ${limitPath}`
        );

        // Read current memory usage
        const current = Number(fs.readFileSync(currPath, "utf8").trim());

        // Read memory limit
        const maxContent = fs.readFileSync(limitPath, "utf8").trim();

        // Check if max is set to "max" (no limit)
        if (maxContent === "max") {
          logger.debug("No memory limit set (memory.max = max)");

          // If no limit is set, use total system memory as fallback
          const totalMem = os.totalmem();
          const usagePercent = (current / totalMem) * 100;
          logger.debug(
            `Memory usage: ${usagePercent.toFixed(2)}% (using system total memory as fallback)`
          );
          return usagePercent;
        } else {
          // Calculate percentage using current and max
          const max = Number(maxContent);

          // Validate max to avoid division by zero or NaN
          if (isNaN(max) || max <= 0) {
            logger.debug(
              `Invalid max memory value: ${maxContent}, using system total memory as fallback`
            );
            const totalMem = os.totalmem();
            const usagePercent = (current / totalMem) * 100;
            return usagePercent;
          }

          const usagePercent = (current / max) * 100;
          logger.debug(
            `Memory usage: ${current} / ${max} = ${usagePercent.toFixed(2)}%`
          );
          return usagePercent;
        }
      }
    }

    // If we still couldn't get memory usage, try traditional cgroups format
    const memStatPath = RENDER_CGROUP_PATHS.memory;
    if (fs.existsSync(memStatPath)) {
      logger.debug(`Falling back to memory stat file: ${memStatPath}`);
      const memStats = parseCgroupStat(memStatPath);

      // Extract memory usage and limit
      const memoryUsage =
        memStats.get("anon") ??
        memStats.get("total_inactive_anon") ??
        memStats.get("rss") ??
        memStats.get("active_anon") ??
        0;

      const memoryLimit =
        memStats.get("hierarchical_memory_limit") ??
        memStats.get("limit_in_bytes") ??
        0;

      // If we have both values and they're reasonable
      if (
        memoryUsage > 0 &&
        memoryLimit > 0 &&
        memoryLimit < Number.MAX_SAFE_INTEGER
      ) {
        const usagePercent = (memoryUsage / memoryLimit) * 100;
        logger.debug(`Memory usage from stat: ${usagePercent.toFixed(2)}%`);
        return usagePercent;
      }
    }

    // If all else fails, use OS memory metrics as fallback
    try {
      const totalMem = os.totalmem();
      const freeMem = os.freemem();
      const usedMem = totalMem - freeMem;
      const usagePercent = (usedMem / totalMem) * 100;
      logger.debug(`Memory usage from OS: ${usagePercent.toFixed(2)}%`);
      return usagePercent;
    } catch (osError) {
      logger.debug(`Error getting OS memory metrics: ${osError}`);
    }

    // If all else fails, return null
    logger.debug("Could not determine memory usage from any source");
    return null;
  } catch (error) {
    logger.debug(`Error getting container memory usage: ${error}`);
    return null;
  }
}

/**
 * Get CPU usage percentage from cgroups
 * Uses vendor-recommended approach for Render's cgroups v2 environment
 * Returns a Promise that resolves to the CPU usage percentage
 */
function getCpuUsagePercent(): Promise<number | null> {
  return new Promise((resolve) => {
    try {
      // Check if the CPU stat file exists
      const cpuStatPath = RENDER_CGROUP_PATHS.cpu;
      if (!fs.existsSync(cpuStatPath)) {
        logger.debug(
          `CPU stat file not found at ${cpuStatPath}, falling back to alternative methods`
        );
        resolve(fallbackCpuUsage());
        return;
      }

      // Get current CPU usage from usage_usec in cpu.stat
      const u1 = getCpuUsageUsec();
      if (u1 === null) {
        logger.debug("Could not get valid usage_usec value, using fallback");
        resolve(fallbackCpuUsage());
        return;
      }

      // Wait 1 second for accurate measurement (as recommended by vendor)
      setTimeout(() => {
        try {
          // Get second measurement after 1 second
          const u2 = getCpuUsageUsec();
          if (u2 === null) {
            logger.debug(
              "Could not get valid second usage_usec value, using fallback"
            );
            resolve(fallbackCpuUsage());
            return;
          }

          // Calculate difference in microseconds
          const dt = u2 - u1;

          // Get number of CPU cores
          const cpuCount = os.cpus().length;

          // Calculate per-core CPU usage (dt/1000000*100)
          const perCoreCpuPercent = (dt / 1000000) * 100;

          // Calculate all-cores percentage (divided by number of cores)
          const allCoresCpuPercent = perCoreCpuPercent / cpuCount;

          logger.debug(
            `CPU usage: per-core: ${perCoreCpuPercent.toFixed(2)}%, all-cores: ${allCoresCpuPercent.toFixed(2)}%`
          );

          // Return the per-core percentage as recommended by vendor
          resolve(Math.min(perCoreCpuPercent, 100));
        } catch (error) {
          logger.debug(`Error in second CPU measurement: ${error}`);
          resolve(fallbackCpuUsage());
        }
      }, 1000);
    } catch (error) {
      logger.debug(`Error getting container CPU usage: ${error}`);
      resolve(fallbackCpuUsage());
    }
  });
}

/**
 * Helper function to get the usage_usec value from cpu.stat
 */
function getCpuUsageUsec(): number | null {
  try {
    const cpuStatPath = RENDER_CGROUP_PATHS.cpu;
    if (!fs.existsSync(cpuStatPath)) {
      return null;
    }

    const content = fs.readFileSync(cpuStatPath, "utf8");
    const regex = /usage_usec\s+(\d+)/;
    const match = regex.exec(content);

    return match?.[1] ? parseInt(match[1], 10) : null;
  } catch (error) {
    logger.debug(`Error reading usage_usec: ${error}`);
    return null;
  }
}

/**
 * Fallback method to get CPU usage when cgroups data is unavailable
 * Uses OS load average as a proxy for CPU usage
 * Returns a Promise to match the signature of getCpuUsagePercent
 */
function fallbackCpuUsage(): Promise<number | null> {
  return new Promise((resolve) => {
    try {
      // Get load average and CPU count
      const loadAvg = os.loadavg()[0]; // 1-minute load average
      const cpuCount = os.cpus().length;

      // Calculate load per CPU and convert to percentage
      const loadPerCpu = loadAvg / cpuCount;
      const cpuPercent = Math.min(loadPerCpu * 100, 100); // Cap at 100%

      logger.debug(
        `Using fallback CPU metrics: ${cpuPercent.toFixed(2)}% (load avg: ${loadAvg}, cpus: ${cpuCount})`
      );

      resolve(cpuPercent);
    } catch (error) {
      logger.debug(`Error getting fallback CPU metrics: ${error}`);
      resolve(null);
    }
  });
}

/**
 * Get container metrics
 * Returns null if not in a containerized environment
 * Always returns valid metrics with safe defaults if in a container
 * Now returns a Promise to handle async CPU calculations
 */
export async function getContainerMetrics(): Promise<ContainerMetrics | null> {
  try {
    // Check if we're in a containerized environment
    const containerized = isContainerized();

    if (!containerized) {
      logger.debug("Not in a containerized environment, returning null");
      return null;
    }

    // Get memory metrics
    let memoryUsagePercent = getMemoryUsagePercent();

    // If we couldn't get memory metrics, use OS fallback
    if (memoryUsagePercent === null) {
      try {
        const totalMem = os.totalmem();
        const freeMem = os.freemem();
        const usedMem = totalMem - freeMem;
        memoryUsagePercent = (usedMem / totalMem) * 100;
        logger.debug(
          `Using OS fallback for memory: ${memoryUsagePercent.toFixed(2)}%`
        );
      } catch (osError) {
        logger.debug(`Error getting OS memory metrics: ${osError}`);
        memoryUsagePercent = 0; // Safe default
      }
    }

    // Get CPU metrics (now async)
    let cpuUsagePercent: number;
    try {
      // Await the CPU usage calculation
      const cpuResult = await getCpuUsagePercent();

      if (cpuResult === null) {
        // If we couldn't get CPU metrics, use OS fallback
        const fallbackResult = await fallbackCpuUsage();
        cpuUsagePercent = fallbackResult ?? 0;
        logger.debug(`Using fallback for CPU: ${cpuUsagePercent.toFixed(2)}%`);
      } else {
        cpuUsagePercent = cpuResult;
      }
    } catch (cpuError) {
      logger.debug(`Error getting CPU metrics: ${cpuError}`);
      const fallbackResult = await fallbackCpuUsage();
      cpuUsagePercent = fallbackResult ?? 0;
    }

    // Log the metrics we're returning
    logger.debug(
      `Container metrics - Memory: ${memoryUsagePercent.toFixed(2)}%, CPU: ${cpuUsagePercent.toFixed(2)}%`
    );

    // Ensure we don't have NaN values
    if (isNaN(memoryUsagePercent)) {
      logger.warn(`Memory usage is NaN, using fallback value of 0`);
      memoryUsagePercent = 0;
    }

    if (isNaN(cpuUsagePercent)) {
      logger.warn(`CPU usage is NaN, using fallback value of 0`);
      cpuUsagePercent = 0;
    }

    // Always return valid metrics with safe defaults
    return {
      memoryUsagePercent: memoryUsagePercent ?? 0,
      cpuUsagePercent: cpuUsagePercent,
      isContainerized: true,
    };
  } catch (error) {
    // Even if everything fails, return safe defaults
    logger.error(`Error getting container metrics: ${error}`);
    return {
      memoryUsagePercent: 0,
      cpuUsagePercent: 0,
      isContainerized: true,
    };
  }
}
