// @ts-nocheck
import { redirect, fail } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { getUserFromToken } from '$lib/server/auth.js';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import type { PageServerLoad, Actions } from './$types.js';

// Define the schema with Zod for validation
const profileSchema = z.object({
  name: z.string().min(1, 'Profile name is required'),
  jobType: z.string().min(1, 'Job type is required'),
  industry: z.string().optional(),
  resumeId: z.string().optional(),
});

export const load = async ({ locals }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user || !user.email) {
    throw redirect(302, '/auth/sign-in');
  }

  // Get user data
  const userData = await prisma.user.findUnique({
    where: { email: user.email },
  });

  if (!userData) {
    throw redirect(302, '/auth/sign-in');
  }

  locals.user = userData;

  // Get user's profiles
  const profiles = await prisma.profile.findMany({
    where: { userId: userData.id },
    orderBy: { updatedAt: 'desc' },
    include: {
      defaultDocument: true,
      data: true,
    },
  });

  // Get user's documents with resumes for the form
  const documents = await prisma.document.findMany({
    where: {
      userId: userData.id,
      resume: { isNot: null },
    },
    include: {
      resume: true,
    },
    orderBy: { updatedAt: 'desc' },
  });

  // Format documents for the form
  const formattedDocuments = documents.map((doc) => ({
    id: doc.id,
    label: doc.label || doc.fileName,
    resume: doc.resume,
  }));

  // Create an empty form for new profile creation
  const form = await superValidate(
    {
      name: '',
      jobType: '',
      industry: '',
      resumeId: '',
    },
    zod(profileSchema)
  );

  return {
    user: userData,
    profiles,
    documents: formattedDocuments,
    form,
  };
};

export const actions = {
  default: async ({ request, cookies, locals }: import('./$types').RequestEvent) => {
    const tokenData = getUserFromToken(cookies);

    if (!tokenData || !(await tokenData).email) {
      throw redirect(302, '/auth/sign-in');
    }

    // Get user data
    const userData = await prisma.user.findUnique({
      where: { email: (await tokenData).email },
    });

    if (!userData) {
      throw redirect(302, '/auth/sign-in');
    }

    const form = await superValidate(request, zod(profileSchema));

    if (!form.valid) {
      return fail(400, { form });
    }

    try {
      // Create a new profile
      const newProfile = await prisma.profile.create({
        data: {
          name: form.data.name,
          data: {
            create: {
              data: JSON.stringify({
                jobType: form.data.jobType,
                industry: form.data.industry || null,
              }),
            },
          },
          defaultDocumentId: form.data.resumeId ? form.data.resumeId : null,
          user: {
            connect: { id: userData.id },
          },
        },
      });

      // Return with success message and the new profile
      return { form, success: true, profile: newProfile };
    } catch (error) {
      console.error('Error updating profile:', error);
      return fail(500, { form, error: 'Failed to update profile' });
    }
  },
};
;null as any as Actions;