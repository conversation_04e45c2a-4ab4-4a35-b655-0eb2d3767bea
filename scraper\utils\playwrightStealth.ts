// scraper/utils/playwrightStealth.ts
import { chromium, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>onte<PERSON><PERSON>, Page } from "playwright";
import { logger } from "./logger.js";

/**
 * Launch a stealth browser that's harder to detect as automated
 */
export async function launchStealthBrowser(
  headless: boolean = false,
  slowMo: number = 50,
  proxyServer?: string
): Promise<Browser> {
  try {
    // Force headless mode in production or if SCRAPER_HEADLESS is set to true
    const forceHeadless =
      process.env.SCRAPER_HEADLESS === "true" ||
      process.env.NODE_ENV === "production";
    const finalHeadless = forceHeadless ? true : headless;
    const finalSlowMo = finalHeadless ? Math.min(slowMo, 30) : slowMo; // Reduce slowMo in headless mode

    logger.info(
      `🥷 Launching stealth browser with anti-detection features (headless: ${finalHeadless})...`
    );

    // Launch with minimal arguments to appear as a real user
    const browser = await chromium.launch({
      headless: finalHeadless,
      slowMo: finalSlowMo,
      proxy: proxyServer ? { server: proxyServer } : undefined,
      args: [
        // These are the only arguments a typical Chrome user would have
        "--disable-blink-features=AutomationControlled",
        "--no-sandbox",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
      ],
    });

    logger.info("✅ Stealth browser launched successfully");
    return browser;
  } catch (error) {
    logger.error(`❌ Failed to launch stealth browser: ${error}`);
    throw error;
  }
}

/**
 * Create a new stealth browser context with advanced fingerprinting
 */
export async function createStealthContext(
  browser: Browser,
  options: {
    viewport?: { width: number; height: number };
    userAgent?: string;
    locale?: string;
    timezoneId?: string;
    deviceScaleFactor?: number;
    httpCredentials?: { username: string; password: string };
  } = {}
): Promise<BrowserContext> {
  try {
    logger.info("🥷 Creating stealth browser context...");

    // Create a new context with the provided options
    const context = await browser.newContext({
      viewport: options.viewport || { width: 1920, height: 1080 },
      // Use a completely standard user agent that matches real Chrome users
      userAgent:
        options.userAgent ||
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      locale: options.locale || "en-US",
      timezoneId: options.timezoneId || "America/New_York",
      deviceScaleFactor: options.deviceScaleFactor || 1,
      httpCredentials: options.httpCredentials,
      // Additional options to make the browser harder to detect
      javaScriptEnabled: true,
      ignoreHTTPSErrors: true,
      permissions: ["geolocation"],
      colorScheme: "light",
      isMobile: false,
      hasTouch: false,
    });

    // Apply additional stealth features at the context level
    await context.addInitScript(() => {
      // Override the navigator properties
      const overrideNavigator = () => {
        // Make navigator.webdriver not detectable
        Object.defineProperty(navigator, "webdriver", {
          get: () => false,
          configurable: true,
        });

        // Override navigator.plugins
        Object.defineProperty(navigator, "plugins", {
          get: () => [
            {
              name: "Chrome PDF Plugin",
              filename: "internal-pdf-viewer",
              description: "Portable Document Format",
            },
            {
              name: "Chrome PDF Viewer",
              filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
              description: "",
            },
            {
              name: "Native Client",
              filename: "internal-nacl-plugin",
              description: "",
            },
          ],
        });

        // Override navigator.languages
        Object.defineProperty(navigator, "languages", {
          get: () => ["en-US", "en"],
        });

        // Override navigator.permissions
        const originalQuery = navigator.permissions.query;
        navigator.permissions.query = (parameters: any) =>
          Promise.resolve({
            state: "granted",
            addEventListener: () => {},
            removeEventListener: () => {},
            dispatchEvent: () => true,
          } as PermissionStatus);
      };

      // Override WebGL fingerprinting
      const overrideWebGL = () => {
        const getParameterProxyHandler = {
          apply: function (target: any, thisArg: any, argumentsList: any) {
            const parameter = argumentsList[0];
            // Return consistent values for WebGL parameters
            if (parameter === 37445) {
              return "Intel Inc."; // UNMASKED_VENDOR_WEBGL
            }
            if (parameter === 37446) {
              return "Intel(R) Iris(R) Xe Graphics"; // UNMASKED_RENDERER_WEBGL
            }
            return target.apply(thisArg, argumentsList);
          },
        };

        // Apply the proxy to WebGL getParameter
        if (window.WebGLRenderingContext) {
          const prototype = WebGLRenderingContext.prototype;
          const getParameter = prototype.getParameter;
          prototype.getParameter = new Proxy(
            getParameter,
            getParameterProxyHandler
          );
        }
      };

      // Override canvas fingerprinting
      const overrideCanvas = () => {
        // Add some noise to canvas fingerprinting
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;

        // Add subtle noise to canvas data
        HTMLCanvasElement.prototype.toDataURL = function (_type: string) {
          // Only add noise if it's likely being used for fingerprinting
          if (this.width === 16 && this.height === 16) {
            const context = this.getContext("2d");
            if (context) {
              // Add subtle noise that won't be visually noticeable
              const imageData = context.getImageData(
                0,
                0,
                this.width,
                this.height
              );
              const data = imageData.data;

              for (let i = 0; i < data.length; i += 4) {
                // Only modify alpha channel slightly
                data[i + 3] = data[i + 3] > 0 ? data[i + 3] - 1 : data[i + 3];
              }

              context.putImageData(imageData, 0, 0);
            }
          }

          return originalToDataURL.apply(this, arguments as any);
        };
      };

      // Apply all the overrides
      overrideNavigator();
      overrideWebGL();
      overrideCanvas();

      // Hide automation flags
      delete (window as any).cdc_adoQpoasnfa76pfcZLmcfl_Array;
      delete (window as any).cdc_adoQpoasnfa76pfcZLmcfl_Promise;
      delete (window as any).cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
    });

    logger.info("✅ Stealth browser context created successfully");
    return context;
  } catch (error) {
    logger.error(`❌ Failed to create stealth browser context: ${error}`);
    throw error;
  }
}

/**
 * Apply all stealth and fingerprinting techniques to a page
 */
export async function applyStealthFingerprinting(
  context: BrowserContext,
  page: Page,
  options: {
    viewport?: { width: number; height: number };
    userAgent?: string;
  } = {}
): Promise<void> {
  try {
    logger.info("🥷 Applying stealth fingerprinting to page...");

    // Additional page-level stealth techniques
    await page.addInitScript(() => {
      // Hide that we're using automation
      Object.defineProperty(navigator, "webdriver", { get: () => false });

      // Override navigator properties with consistent values
      Object.defineProperty(navigator, "hardwareConcurrency", {
        get: () => 8,
      });
      Object.defineProperty(navigator, "deviceMemory", {
        get: () => 8,
      });

      // Fake user interaction
      const originalFunction = HTMLElement.prototype.appendChild;
      HTMLElement.prototype.appendChild = function <T extends Node>(
        this: HTMLElement,
        node: T
      ): T {
        const element = originalFunction.apply(this, [node]) as T;
        // Simulate user interaction for specific elements
        if (
          element instanceof HTMLElement &&
          (element.tagName === "IFRAME" ||
            element.tagName === "IMG" ||
            element.tagName === "SCRIPT")
        ) {
          setTimeout(
            () => {
              element.dispatchEvent(new Event("mouseover", { bubbles: true }));
              element.dispatchEvent(new Event("mousedown", { bubbles: true }));
              element.dispatchEvent(new Event("mouseup", { bubbles: true }));
            },
            Math.floor(Math.random() * 500) + 100
          );
        }
        return element;
      };
    });

    // Set viewport if provided
    if (options.viewport) {
      await page.setViewportSize(options.viewport);
    }

    logger.info("✅ Stealth fingerprinting applied successfully");
  } catch (error) {
    logger.error(`❌ Failed to apply stealth fingerprinting: ${error}`);
    throw error;
  }
}
