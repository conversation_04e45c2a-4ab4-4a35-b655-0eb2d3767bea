import { logger } from '../../utils/logger.js';
import { generateCompletion } from '../llm/index.js';
import { compilePrompt, RESUME_ANALYSIS_PROMPT } from '../llm/prompts.js';
import { extractKeywords } from './keywords.js';
import { analyzeFormat } from './formatting.js';
import { analyzeReadability } from './readability.js';

export interface ResumeAnalysisResult {
  overallScore: number;
  keywordScore: number;
  formatScore: number;
  contentScore: number;
  readabilityScore: number;
  keywordMatches: string[];
  missingKeywords: string[];
  formatIssues: string[];
  contentSuggestions: string[];
  readabilitySuggestions: string[];
}

export async function analyzeResume(
  resumeText: string,
  jobDescription?: string
): Promise<ResumeAnalysisResult> {
  logger.info('Analyzing resume');
  
  try {
    // First, try to use the LLM for a comprehensive analysis
    const prompt = compilePrompt(RESUME_ANALYSIS_PROMPT, {
      resumeText,
      jobDescription,
    });
    
    const completion = await generateCompletion(prompt, {
      temperature: 0.2,
    });
    
    // Try to parse the JSON response
    try {
      // Extract JSON from the response
      const jsonMatch = completion.text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const result = JSON.parse(jsonMatch[0]);
        logger.info('Resume analysis completed successfully');
        return result;
      }
    } catch (parseError) {
      logger.warn('Failed to parse LLM response as JSON, falling back to hybrid analysis');
    }
    
    // If LLM analysis fails, fall back to hybrid approach
    return await hybridAnalysis(resumeText, jobDescription);
  } catch (error) {
    logger.error('Error in resume analysis:', error);
    // Fall back to hybrid analysis
    return await hybridAnalysis(resumeText, jobDescription);
  }
}

async function hybridAnalysis(
  resumeText: string,
  jobDescription?: string
): Promise<ResumeAnalysisResult> {
  logger.info('Performing hybrid resume analysis');
  
  // Extract keywords
  const keywordAnalysis = await extractKeywords(resumeText, jobDescription);
  
  // Analyze format
  const formatAnalysis = analyzeFormat(resumeText);
  
  // Analyze readability
  const readabilityAnalysis = analyzeReadability(resumeText);
  
  // Calculate overall score (weighted average)
  const overallScore = 
    keywordAnalysis.score * 0.4 + 
    formatAnalysis.score * 0.3 + 
    readabilityAnalysis.score * 0.3;
  
  return {
    overallScore: Math.round(overallScore),
    keywordScore: keywordAnalysis.score,
    formatScore: formatAnalysis.score,
    contentScore: formatAnalysis.score, // Use format score as a proxy for content
    readabilityScore: readabilityAnalysis.score,
    keywordMatches: keywordAnalysis.matches,
    missingKeywords: keywordAnalysis.missing,
    formatIssues: formatAnalysis.issues,
    contentSuggestions: [
      'Add quantifiable achievements to demonstrate impact',
      'Ensure each experience entry starts with a strong action verb',
      'Tailor your summary to highlight relevant experience',
    ],
    readabilitySuggestions: readabilityAnalysis.suggestions,
  };
}
