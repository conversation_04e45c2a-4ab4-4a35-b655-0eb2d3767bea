/**
 * WebSocket Singleton
 *
 * This module provides a minimal WebSocket client with no pinging functionality.
 */

import { browser } from '$app/environment';
import { writable, get } from 'svelte/store';

// Connection status type
export type ConnectionStatus = 'connected' | 'disconnected' | 'connecting' | 'error';

// Types for WebSocket messages
export type WebSocketMessage = {
  type: string;
  data?: any;
  message?: string;
  timestamp?: string;
  [key: string]: any;
};

// Create stores
export const status = writable<ConnectionStatus>('disconnected');
export const lastError = writable<string | null>(null);
export const messages = writable<WebSocketMessage[]>([]);

// Track processed message IDs to avoid duplicates
const processedMessageIds = new Set<string>();

// How long to consider a message as "recent" (in milliseconds)
const MESSAGE_DEDUPLICATION_WINDOW = 10000; // 10 seconds

// WebSocket instance - true singleton pattern
let socket: WebSocket | null = null;

// Flag to track if initialization has been done
let isInitialized = false;

// Default export for easier importing
const websocketSingleton = {
  initialize,
  disconnect,
  getStatus,
  status,
  messages,
  send,
};

export default websocketSingleton;

/**
 * Initialize the WebSocket connection
 * This should be called only once at the application level
 */
export function initialize(): void {
  if (!browser) return; // Only run in browser

  // If already initialized, don't initialize again
  if (isInitialized) {
    console.log('WebSocket already initialized, skipping initialization');
    return;
  }

  isInitialized = true;
  console.log('Initializing WebSocket singleton with improved deduplication...');

  // Clear message cache
  processedMessageIds.clear();

  // Connect to the WebSocket server
  connect();

  // Set up cleanup on page unload
  window.addEventListener('beforeunload', () => {
    disconnect();
  });
}

/**
 * Connect to the WebSocket server
 * This is private and should only be called by initialize()
 */
function connect(): void {
  // If already connected or connecting, don't try to connect again
  if (
    socket &&
    (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING)
  ) {
    console.log('WebSocket already connected or connecting, skipping connection attempt');
    return;
  }

  status.set('connecting');
  console.log('Connecting to WebSocket server...');

  // Determine WebSocket URL based on environment
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

  // For development, connect to port 3000, for production use the same host
  const isDev =
    window.location.port === '5173' ||
    window.location.port === '5174' ||
    window.location.port === '5175';
  const wsHost = isDev ? 'localhost:3000' : window.location.host;

  // Always use the /ws endpoint
  const wsUrl = `${protocol}//${wsHost}/ws?t=${Date.now()}`;

  console.log(`Connecting to WebSocket at ${wsUrl}`);

  try {
    // Create WebSocket connection
    socket = new WebSocket(wsUrl);

    // Connection opened
    socket.addEventListener('open', () => {
      status.set('connected');
      console.log('WebSocket connection established successfully');
    });

    // Listen for messages
    socket.addEventListener('message', (event) => {
      try {
        const data = JSON.parse(event.data) as WebSocketMessage;

        // Only process non-echo, non-ping messages
        if (data.type !== 'echo' && data.type !== 'ping') {
          // Ensure the message has a timestamp
          if (!data.timestamp) {
            data.timestamp = new Date().toISOString();
          }

          // Generate a unique message ID for deduplication
          const messageId =
            `${data.type}:${data.timestamp}:${JSON.stringify(data.data ?? {})}`.substring(0, 100);

          // Skip if we've already processed this exact message
          // But always process resume_parsing_completed and notification messages to ensure they're not missed
          if (
            processedMessageIds.has(messageId) &&
            data.type !== 'resume_parsing_completed' &&
            data.type !== 'notification'
          ) {
            console.log(
              `Skipping duplicate WebSocket message: ${data.type} (${messageId.substring(0, 20)}...)`
            );
            return;
          }

          // For resume_parsing_completed messages, log even if duplicate
          if (
            processedMessageIds.has(messageId) &&
            (data.type === 'resume_parsing_completed' || data.type === 'notification')
          ) {
            console.log(
              `Processing ${data.type} message even though it's a duplicate: ${messageId.substring(0, 20)}...`
            );
          }

          // Mark this message as processed
          processedMessageIds.add(messageId);

          // Clean up old message IDs after a while to prevent memory leaks
          setTimeout(() => {
            processedMessageIds.delete(messageId);
          }, MESSAGE_DEDUPLICATION_WINDOW);

          // Special handling for resume parsing messages
          if (data.type === 'resume_parsing_status' || data.type === 'resume_parsing_completed') {
            console.log(`Received resume parsing WebSocket message: ${data.type}`, data);

            // For resume_parsing_completed messages, ensure we dispatch a notification
            if (data.type === 'resume_parsing_completed') {
              console.log('Resume parsing completed, dispatching notification event');

              // Dispatch a custom event specifically for resume parsing completion
              if (typeof window !== 'undefined') {
                const resumeEvent = new CustomEvent('resume-parsing-completed', {
                  detail: {
                    resumeId: data.data?.resumeId,
                    profileId: data.data?.profileId,
                    userId: data.data?.userId,
                    message: 'Resume parsing completed successfully',
                    timestamp: data.timestamp || new Date().toISOString(),
                  },
                });
                window.dispatchEvent(resumeEvent);
                console.log('Dispatched resume-parsing-completed event');
              }
            }
            // For resume_parsing_status messages with pending/processing status
            else if (
              data.type === 'resume_parsing_status' &&
              (data.data?.status === 'pending' || data.data?.status === 'processing')
            ) {
              console.log('Resume parsing in progress, dispatching status event');

              // Dispatch a custom event for resume parsing status updates
              if (typeof window !== 'undefined') {
                const resumeEvent = new CustomEvent('resume-parsing-status', {
                  detail: {
                    resumeId: data.data?.resumeId,
                    profileId: data.data?.profileId,
                    userId: data.data?.userId,
                    isParsing: true,
                    status: data.data?.status,
                    message: 'Resume parsing in progress',
                    timestamp: data.timestamp || new Date().toISOString(),
                  },
                });
                window.dispatchEvent(resumeEvent);
                console.log('Dispatched resume-parsing-status event with isParsing=true');
              }
            }
          }

          // Add message to store
          messages.update((msgs) => {
            const updated = [data, ...msgs];
            // Keep only the most recent 100 messages
            if (updated.length > 100) {
              return updated.slice(0, 100);
            }
            return updated;
          });

          console.log(`Received WebSocket message: ${data.type}`);

          // Dispatch a custom event for this message type
          if (typeof window !== 'undefined') {
            const event = new CustomEvent('websocket-message', {
              detail: { type: data.type, data: data },
            });
            window.dispatchEvent(event);
          }
        }
        // Don't log echo or ping messages to avoid spam
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    });

    // Connection closed
    socket.addEventListener('close', () => {
      status.set('disconnected');
      socket = null;
      console.log('WebSocket connection closed');

      // Attempt to reconnect after a delay
      setTimeout(() => {
        if (!socket) {
          connect();
        }
      }, 3000);
    });

    // Connection error
    socket.addEventListener('error', () => {
      status.set('error');
      lastError.set('WebSocket connection error');
      console.log('WebSocket connection error occurred');
    });
  } catch (error) {
    status.set('error');
    lastError.set('Failed to create WebSocket connection');
    console.log(`Failed to create WebSocket connection: ${error}`);
  }
}

/**
 * Disconnect from the WebSocket server
 */
export function disconnect(): void {
  if (!socket) return;

  console.log('Disconnecting from WebSocket server...');

  // Close WebSocket if connected
  if (socket) {
    socket.close(1000, 'Client disconnected');
    socket = null;
    console.log('Closed WebSocket connection');
  }

  status.set('disconnected');

  // Clear message cache on disconnect
  processedMessageIds.clear();

  console.log('WebSocket disconnected and message cache cleared');
}

/**
 * Get the current WebSocket connection status
 */
export function getStatus(): ConnectionStatus {
  return get(status);
}

/**
 * Send a message to the WebSocket server
 */
export function send(data: any): boolean {
  if (!socket || socket.readyState !== WebSocket.OPEN) {
    return false;
  }

  try {
    socket.send(JSON.stringify(data));
    return true;
  } catch (error) {
    console.error('Error sending WebSocket message:', error);
    return false;
  }
}
