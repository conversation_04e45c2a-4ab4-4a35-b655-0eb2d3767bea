<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Plus, Trash } from 'lucide-svelte';
  import { FeatureCategory, LimitType } from '$lib/models/features/features';
  import { toast } from 'svelte-sonner';
  import { defaultFeature, defaultFeatureLimit } from '$lib/schemas/feature';

  // State for adding a new feature
  let newFeature = { ...defaultFeature };

  // State for adding a new limit
  let newLimit = { ...defaultFeatureLimit };

  // Add a limit to the feature
  function addLimit() {
    if (!newLimit.id || !newLimit.name) {
      toast.error('Limit ID and name are required');
      return;
    }

    // Add the new limit to the feature
    newFeature.limits = [...newFeature.limits, { ...newLimit }];

    // Reset the new limit form
    newLimit = {
      id: '',
      name: '',
      description: '',
      defaultValue: 10,
      type: LimitType.Monthly,
      unit: '',
      resetDay: 1,
    };
  }

  // Remove a limit from the feature
  function removeLimit(limitId: string) {
    newFeature.limits = newFeature.limits.filter((limit) => limit.id !== limitId);
  }

  // Add a new feature to the database
  async function addFeature(event: SubmitEvent) {
    event.preventDefault();

    try {
      if (!newFeature.id || !newFeature.name) {
        toast.error('Feature ID and name are required');
        return;
      }

      const response = await fetch('/api/admin/features', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'add_feature',
          feature: newFeature,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `Failed to add feature: ${response.status}`);
      }

      const result = await response.json();
      console.log('Feature added response:', result);

      toast.success('Feature added', {
        description: `Feature ${newFeature.name} added successfully`,
        duration: 3000,
      });

      // Reset the new feature form
      newFeature = { ...defaultFeature };

      // Dispatch an event to notify parent component to refresh features
      window.dispatchEvent(new CustomEvent('featureAdded'));
    } catch (err) {
      console.error('Error adding feature:', err);
      toast.error('Failed to add feature', {
        description: err.message,
        duration: 5000,
      });
    }
  }
</script>

<Card.Root>
  <Card.Content>
    <form on:submit={addFeature}>
      <div class="grid grid-cols-2 gap-4">
        <div class="space-y-2">
          <Label for="feature-id">Feature ID</Label>
          <input
            id="feature-id"
            bind:value={newFeature.id}
            placeholder="e.g. custom_reports"
            class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
          <p class="text-muted-foreground text-xs">A unique identifier for the feature</p>
        </div>
        <div class="space-y-2">
          <Label for="feature-name">Feature Name</Label>
          <input
            id="feature-name"
            bind:value={newFeature.name}
            placeholder="e.g. Custom Reports"
            class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
          <p class="text-muted-foreground text-xs">Display name for the feature</p>
        </div>
        <div class="col-span-2 space-y-2">
          <Label for="feature-description">Description</Label>
          <textarea
            id="feature-description"
            bind:value={newFeature.description}
            placeholder="Describe what this feature does"
            class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[60px] w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"
          ></textarea>
          <p class="text-muted-foreground text-xs">Detailed explanation of the feature</p>
        </div>
        <div class="space-y-2">
          <Label for="feature-category">Category</Label>
          <select
            id="feature-category"
            bind:value={newFeature.category}
            class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500">
            {#each Object.values(FeatureCategory) as category}
              <option value={category}>{category}</option>
            {/each}
          </select>
          <p class="text-muted-foreground text-xs">Group this feature belongs to</p>
        </div>
        <div class="space-y-2">
          <Label for="feature-icon">Icon (optional)</Label>
          <input
            id="feature-icon"
            bind:value={newFeature.icon}
            placeholder="e.g. file-bar-chart"
            class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
          <p class="text-muted-foreground text-xs">Lucide icon name for the feature</p>
        </div>
        <div class="col-span-2 flex items-center space-y-2">
          <input type="checkbox" id="feature-beta" bind:checked={newFeature.beta} class="mr-2" />
          <Label for="feature-beta">Beta Feature</Label>
        </div>

        <!-- Feature Limits Section -->
        <div class="col-span-2 mt-4 border-t pt-4">
          <h4 class="mb-2 text-base font-semibold">Feature Limits</h4>

          <!-- Existing Limits -->
          {#if newFeature.limits && newFeature.limits.length > 0}
            <div class="mb-4 space-y-2">
              {#each newFeature.limits as limit}
                <div class="flex items-center justify-between rounded-md border p-2">
                  <div>
                    <div class="font-medium">{limit.name}</div>
                    <div class="text-muted-foreground text-xs">
                      ID: {limit.id} | Default: {limit.defaultValue}
                      {limit.unit || ''}
                    </div>
                    <div class="text-muted-foreground text-xs">
                      {limit.description}
                    </div>
                  </div>
                  <button
                    class="text-destructive hover:bg-destructive/10 hover:text-destructive rounded-full p-1"
                    on:click|preventDefault={() => removeLimit(limit.id)}>
                    <Trash class="h-4 w-4" />
                  </button>
                </div>
              {/each}
            </div>
          {:else}
            <div class="text-muted-foreground mb-4 rounded-md border border-dashed p-4 text-center">
              No limits defined for this feature.
            </div>
          {/if}

          <!-- Add New Limit Form -->
          <div class="rounded-md border p-3">
            <h5 class="mb-2 text-sm font-semibold">Add New Limit</h5>
            <div class="grid grid-cols-2 gap-2">
              <div>
                <Label for="new-limit-id" class="text-xs">Limit ID</Label>
                <input
                  id="new-limit-id"
                  bind:value={newLimit.id}
                  placeholder="e.g. monthly_usage"
                  class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
              </div>
              <div>
                <Label for="new-limit-name" class="text-xs">Name</Label>
                <input
                  id="new-limit-name"
                  bind:value={newLimit.name}
                  placeholder="e.g. Monthly Usage"
                  class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
              </div>
              <div class="col-span-2">
                <Label for="new-limit-description" class="text-xs">Description</Label>
                <input
                  id="new-limit-description"
                  bind:value={newLimit.description}
                  placeholder="e.g. Maximum usage per month"
                  class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
              </div>
              <div>
                <Label for="new-limit-default" class="text-xs">Default Value</Label>
                <input
                  id="new-limit-default"
                  type="number"
                  bind:value={newLimit.defaultValue}
                  class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
              </div>
              <div>
                <Label for="new-limit-unit" class="text-xs">Unit (optional)</Label>
                <input
                  id="new-limit-unit"
                  bind:value={newLimit.unit}
                  placeholder="e.g. uses, items, GB"
                  class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
              </div>
              <div>
                <Label for="new-limit-type" class="text-xs">Limit Type</Label>
                <select
                  id="new-limit-type"
                  bind:value={newLimit.type}
                  class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500">
                  <option value={LimitType.Monthly}>Monthly</option>
                  <option value={LimitType.Total}>Total</option>
                  <option value={LimitType.Concurrent}>Concurrent</option>
                  <option value={LimitType.Unlimited}>Unlimited</option>
                </select>
              </div>
              <div>
                <Label for="new-limit-reset" class="text-xs">Reset Day (Monthly only)</Label>
                <input
                  id="new-limit-reset"
                  type="number"
                  bind:value={newLimit.resetDay}
                  disabled={newLimit.type !== LimitType.Monthly}
                  class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
              </div>
              <div class="col-span-2 mt-2">
                <button
                  type="button"
                  class="border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-8 items-center justify-center rounded-md border px-3 py-1 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"
                  on:click|preventDefault={() => addLimit()}>
                  <Plus class="mr-1.5 h-4 w-4" />
                  Add Limit
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-6 flex justify-end">
        <button
          type="submit"
          class="bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:ring-ring inline-flex h-9 items-center justify-center rounded-md px-4 py-2 text-sm font-medium shadow transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50">
          <Plus class="mr-2 h-4 w-4" />
          Add Feature
        </button>
      </div>
    </form>
  </Card.Content>
</Card.Root>
