<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Separator } from '$lib/components/ui/separator/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import {
    Calendar,
    Clock,
    Plus,
    MessageSquare,
    CheckCircle,
    XCircle,
    AlertCircle,
    Users,
    Edit,
    ArrowRight,
  } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import EditFieldModal from './EditFieldModal.svelte';

  export let open = false;
  export let interview: any;
  export let onClose = () => {};
  export let onAddQuestion = (interviewId: string) => {};

  // State for editing
  let showEditModal = false;
  let currentEditField = '';
  let currentEditTitle = '';
  let currentEditValue = '';

  // Format date
  function formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  // Define badge variant type
  type BadgeVariant = 'success' | 'destructive' | 'outline' | 'secondary' | 'default' | 'warning';

  // Get outcome badge
  function getOutcomeBadge(outcome: string): { variant: BadgeVariant; icon: any } {
    if (!outcome) return { variant: 'outline', icon: null };

    switch (outcome.toLowerCase()) {
      case 'passed':
      case 'offer':
      case 'accepted':
        return { variant: 'success', icon: CheckCircle };
      case 'failed':
      case 'rejected':
        return { variant: 'destructive', icon: XCircle };
      case 'pending':
      case 'scheduled':
      case 'in progress':
        return { variant: 'warning', icon: AlertCircle };
      default:
        return { variant: 'outline', icon: null };
    }
  }

  // Open edit modal for notes
  function openNotesModal() {
    currentEditField = 'notes';
    currentEditTitle = 'Interview Notes';
    currentEditValue = interview.notes || '';
    showEditModal = true;
  }

  // Open edit modal for next action
  function openNextActionModal() {
    currentEditField = 'nextAction';
    currentEditTitle = 'Next Action';
    currentEditValue = interview.nextAction || '';
    showEditModal = true;
  }

  // Handle save from modal
  async function handleSave(value: string): Promise<void> {
    try {
      // Make an API call to save the field
      const response = await fetch(
        `/api/applications/${interview.applicationId}/interviews/${interview.id}`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            [currentEditField]: value,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to update ${currentEditField}: ${response.statusText}`);
      }

      // Update the local state
      interview[currentEditField] = value;

      // Show success message
      toast.success(`${currentEditTitle} updated successfully`);
    } catch (err) {
      console.error(`Error updating ${currentEditField}:`, err);
      toast.error(`Failed to update ${currentEditField}`);
    }
  }
</script>

<Dialog.Root bind:open onOpenChange={onClose}>
  <Dialog.Portal>
    <Dialog.Overlay
      class="bg-background/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 backdrop-blur-sm" />
    <Dialog.Content
      class="bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid w-full max-w-3xl translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 sm:rounded-lg md:w-full">
      <Dialog.Header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <Dialog.Title class="text-xl font-semibold">{interview.stageName}</Dialog.Title>
            {#if interview.outcome}
              {@const badge = getOutcomeBadge(interview.outcome)}
              <Badge variant={badge.variant} class="flex items-center gap-1.5 px-2.5 py-1">
                {#if badge.icon}
                  <badge.icon class="h-3.5 w-3.5" />
                {/if}
                <span>{interview.outcome}</span>
              </Badge>
            {/if}
          </div>
          <div class="text-muted-foreground flex items-center gap-4 text-sm">
            <div class="flex items-center gap-1.5">
              <Calendar class="h-4 w-4" />
              <span>{formatDate(interview.stageDate)}</span>
            </div>
            {#if interview.duration}
              <div class="flex items-center gap-1.5">
                <Clock class="h-4 w-4" />
                <span>{interview.duration} min</span>
              </div>
            {/if}
          </div>
        </div>
      </Dialog.Header>

      <div class="grid gap-6 py-4">
        <!-- Interview Details Section -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          {#if interview.interviewers}
            <div class="bg-muted/30 flex items-start gap-3 rounded-md p-4 shadow-sm">
              <Users class="text-primary/70 mt-0.5 h-5 w-5" />
              <div class="flex-1 overflow-hidden">
                <p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">
                  Interviewers
                </p>
                <p class="mt-1.5 text-sm">{interview.interviewers}</p>
              </div>
            </div>
          {/if}

          {#if interview.feedback}
            <div class="bg-muted/30 flex items-start gap-3 rounded-md p-4 shadow-sm">
              <MessageSquare class="text-primary/70 mt-0.5 h-5 w-5" />
              <div class="flex-1 overflow-hidden">
                <p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">
                  Feedback
                </p>
                <p class="mt-1.5 text-sm">{interview.feedback}</p>
              </div>
            </div>
          {/if}
        </div>

        <!-- Next Action Section -->
        <div class="bg-muted/30 flex items-start gap-3 rounded-md p-4 shadow-sm">
          <ArrowRight class="text-primary/70 mt-0.5 h-5 w-5" />
          <div class="flex-1 overflow-hidden">
            <div class="flex items-center justify-between">
              <p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">
                Next Action
              </p>
              <Button variant="ghost" size="sm" class="h-7 w-7 p-0" onclick={openNextActionModal}>
                <Edit class="h-3.5 w-3.5" />
                <span class="sr-only">Edit Next Action</span>
              </Button>
            </div>
            <p class="mt-1.5 text-sm">{interview.nextAction || 'No next action set.'}</p>
          </div>
        </div>

        <!-- Notes Section -->
        <div class="bg-muted/30 flex items-start gap-3 rounded-md p-4 shadow-sm">
          <MessageSquare class="text-primary/70 mt-0.5 h-5 w-5" />
          <div class="flex-1 overflow-hidden">
            <div class="flex items-center justify-between">
              <p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Notes</p>
              <Button variant="ghost" size="sm" class="h-7 w-7 p-0" onclick={openNotesModal}>
                <Edit class="h-3.5 w-3.5" />
                <span class="sr-only">Edit Notes</span>
              </Button>
            </div>
            <p class="mt-1.5 text-sm">{interview.notes || 'No notes added yet.'}</p>
          </div>
        </div>

        <Separator class="my-2" />

        <!-- Questions Section -->
        <div>
          <div class="flex items-center justify-between">
            <h5 class="text-sm font-medium">Questions</h5>
            <Button
              variant="outline"
              size="sm"
              onclick={() => onAddQuestion(interview.id)}
              class="flex items-center gap-1.5 shadow-sm">
              <Plus class="h-3.5 w-3.5" />
              <span>Add Question</span>
            </Button>
          </div>

          {#if !interview.questions?.length}
            <div class="bg-muted/30 mt-4 rounded-md p-4 text-center shadow-sm">
              <p class="text-muted-foreground text-sm">No questions recorded yet.</p>
            </div>
          {:else}
            <div class="mt-4 space-y-4">
              {#each interview.questions as question}
                <div
                  class="hover:border-primary/30 rounded-lg border p-4 shadow-sm transition-colors">
                  <!-- Question Header -->
                  <div class="flex items-start">
                    <div class="flex-1 overflow-hidden">
                      <h6 class="text-sm font-medium">{question.question}</h6>
                      <div class="mt-2 flex flex-wrap items-center gap-2">
                        <Badge variant="outline" class="px-2 py-0.5">
                          {question.category}
                        </Badge>
                        {#if question.difficulty}
                          <Badge variant="secondary" class="px-2 py-0.5">
                            Difficulty: {question.difficulty}/5
                          </Badge>
                        {/if}
                      </div>
                    </div>
                  </div>

                  <!-- User Response -->
                  {#if question.userResponse}
                    <div class="bg-muted/20 mt-4 rounded-md p-4">
                      <p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">
                        Your Response
                      </p>
                      <p class="mt-1.5 text-sm">
                        {question.userResponse}
                      </p>

                      {#if question.userConfidence}
                        <div class="mt-3 flex items-center gap-2">
                          <span class="text-muted-foreground text-xs font-medium">Confidence:</span>
                          <div class="flex gap-0.5">
                            {#each Array(5) as _, i}
                              <div
                                class={`h-2 w-5 rounded-sm ${i < question.userConfidence ? 'bg-primary' : 'bg-muted'}`}>
                              </div>
                            {/each}
                          </div>
                        </div>
                      {/if}
                    </div>
                  {/if}

                  <!-- Question Notes -->
                  {#if question.notes}
                    <div class="mt-3 p-3">
                      <p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">
                        Notes
                      </p>
                      <p class="mt-1.5 text-sm">{question.notes}</p>
                    </div>
                  {/if}
                </div>
              {/each}
            </div>
          {/if}
        </div>
      </div>

      <Dialog.Footer class="flex items-center justify-end">
        <Button variant="outline" onclick={onClose}>Close</Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>

<!-- Edit Field Modal -->
<EditFieldModal
  bind:open={showEditModal}
  title={currentEditTitle}
  fieldValue={currentEditValue}
  fieldType={currentEditField}
  applicationId={interview?.applicationId}
  onSave={handleSave} />
