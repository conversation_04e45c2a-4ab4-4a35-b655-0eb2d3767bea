<!-- This file is the same as dashboard/settings/email-broadcast/+page.svelte -->
<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import * as Button from '$lib/components/ui/button/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import * as Input from '$lib/components/ui/input/index.js';
  import * as Textarea from '$lib/components/ui/textarea/index.js';
  import * as Table from '$lib/components/ui/table/index.js';
  import { toast } from 'svelte-sonner';
  import { onMount } from 'svelte';
  import { Calendar, Clock, CheckCircle, XCircle, AlertCircle, AlertTriangle } from 'lucide-svelte';

  // Type definitions
  type Template = {
    name: string;
    label: string;
  };

  type Audience = {
    id: string;
    name: string;
  };

  type Broadcast = {
    id: string;
    subject: string;
    templateName: string;
    audienceId: string;
    status: 'draft' | 'scheduled' | 'sent' | 'cancelled';
    scheduledAt: string | null;
    createdAt: string;
  };

  // State
  let marketingTemplates = $state<Template[]>([]);
  let audiences = $state<Audience[]>([]);
  let broadcasts = $state<Broadcast[]>([]);
  let selectedTemplate = $state<Template | null>(null);
  let selectedAudience = $state<Audience | null>(null);
  let subject = $state('');
  let title = $state('');
  let content = $state('');
  let imageUrl = $state('');
  let ctaText = $state('');
  let ctaUrl = $state('');
  let scheduledAt = $state('');
  let isSubmitting = $state(false);
  let previewHtml = $state('');
  let isPreviewLoading = $state(false);
  let isLoading = $state(true);

  // API status
  let apiStatus = $state({
    isAvailable: false,
    hasApiKey: false,
    error: null as string | null,
  });

  // API base URL
  const API_BASE_URL = '/api/email';

  // Load data on mount
  onMount(async () => {
    await loadData();
  });

  // Load data
  async function loadData() {
    isLoading = true;

    try {
      // Load marketing templates
      marketingTemplates = [
        { name: 'newsletter', label: 'Newsletter' },
        { name: 'announcement', label: 'Announcement' },
        { name: 'product-update', label: 'Product Update' },
        { name: 'feature-announcement', label: 'Feature Announcement' },
        { name: 'promotional', label: 'Promotional' },
      ];

      // Load audiences
      try {
        const audiencesResponse = await fetch(`${API_BASE_URL}/audiences`);

        if (audiencesResponse.ok) {
          const data = await audiencesResponse.json();
          audiences = data;
          apiStatus.isAvailable = true;
          apiStatus.hasApiKey = true;
        } else {
          const error = await audiencesResponse.json();

          // Check if the error is due to missing API key
          if (error.error && error.error.includes('API key')) {
            apiStatus.hasApiKey = false;
            console.warn(
              'Resend API key not configured. Please set the RESEND_API_KEY environment variable.'
            );
          } else {
            console.error('Error loading audiences:', error);
          }
        }
      } catch (audiencesError) {
        console.error('Error loading audiences:', audiencesError);
        apiStatus.error = audiencesError.message;
        apiStatus.isAvailable = false;
      }

      // Load broadcasts
      try {
        const broadcastsResponse = await fetch(`${API_BASE_URL}/broadcasts`);

        if (broadcastsResponse.ok) {
          const data = await broadcastsResponse.json();
          broadcasts = data;
        } else {
          const error = await broadcastsResponse.json();

          // Check if the error is due to missing API key
          if (error.error && error.error.includes('API key')) {
            // Already handled by the audiences API
            console.warn('Resend API key not configured for broadcasts');
          } else {
            console.error('Error loading broadcasts:', error);
          }
        }
      } catch (broadcastsError) {
        console.error('Error loading broadcasts:', broadcastsError);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    } finally {
      isLoading = false;
    }
  }

  // No need to redeclare these variables as they're already defined above

  // Format date
  function formatDate(date: string | null | undefined): string {
    if (!date) return '-';
    return new Date(date).toLocaleString();
  }

  // Get status badge class
  function getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'sent':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Get status icon
  function getStatusIcon(status: string) {
    switch (status) {
      case 'draft':
        return Clock;
      case 'scheduled':
        return Calendar;
      case 'sent':
        return CheckCircle;
      case 'cancelled':
        return XCircle;
      default:
        return AlertCircle;
    }
  }

  // Create broadcast
  async function createBroadcast() {
    if (!selectedTemplate) {
      toast.error('Please select a template');
      return;
    }

    if (!selectedAudience) {
      toast.error('Please select an audience');
      return;
    }

    if (!subject) {
      toast.error('Subject is required');
      return;
    }

    if (!title) {
      toast.error('Title is required');
      return;
    }

    if (!content) {
      toast.error('Content is required');
      return;
    }

    isSubmitting = true;

    try {
      const response = await fetch(`${API_BASE_URL}/broadcasts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          templateName: selectedTemplate.name,
          audienceId: selectedAudience.id,
          subject,
          title,
          content,
          imageUrl,
          ctaText,
          ctaUrl,
          scheduledAt,
        }),
      });

      if (response.ok) {
        toast.success('Broadcast created successfully');

        // Reset form
        selectedTemplate = null;
        selectedAudience = null;
        subject = '';
        title = '';
        content = '';
        imageUrl = '';
        ctaText = '';
        ctaUrl = '';
        scheduledAt = '';
        previewHtml = '';

        // Reload broadcasts
        await loadData();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create broadcast');
      }
    } catch (error) {
      console.error('Error creating broadcast:', error);
      toast.error('Failed to create broadcast');
    } finally {
      isSubmitting = false;
    }
  }

  // Cancel broadcast
  async function cancelBroadcast(broadcastId: string) {
    if (!confirm('Are you sure you want to cancel this broadcast?')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/broadcasts/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          broadcastId,
        }),
      });

      if (response.ok) {
        toast.success('Broadcast cancelled successfully');

        // Update the broadcast status locally
        broadcasts = broadcasts.map((b) =>
          b.id === broadcastId ? { ...b, status: 'cancelled' } : b
        );
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to cancel broadcast');
      }
    } catch (error) {
      console.error('Error cancelling broadcast:', error);
      toast.error('Failed to cancel broadcast');
    }
  }

  // Load preview
  async function loadPreview() {
    if (!selectedTemplate) {
      toast.error('Please select a template');
      return;
    }

    isPreviewLoading = true;

    try {
      // Generate HTML for preview using the same structure that would be sent via Resend
      const previewData = {
        title: title || 'Newsletter Title',
        content: content || '<p>Newsletter content goes here.</p>',
        imageUrl: imageUrl || '',
        ctaText: ctaText || '',
        ctaUrl: ctaUrl || '',
        firstName: 'Preview',
        unsubscribeUrl: '#',
        currentYear: new Date().getFullYear(),
      };

      // Create HTML for preview
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${subject || 'Email Preview'}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; padding: 20px 0; }
            .content { padding: 20px 0; }
            .footer { text-align: center; padding: 20px 0; font-size: 12px; color: #666; border-top: 1px solid #eee; }
            .button { display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; }
            img { max-width: 100%; height: auto; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${previewData.title}</h1>
          </div>
          <div class="content">
            ${previewData.content}
            ${previewData.imageUrl ? `<img src="${previewData.imageUrl}" alt="Newsletter Image" />` : ''}
            ${previewData.ctaText && previewData.ctaUrl ? `<p style="text-align: center; margin-top: 20px;"><a href="${previewData.ctaUrl}" class="button">${previewData.ctaText}</a></p>` : ''}
          </div>
          <div class="footer">
            <p>This is a preview of your ${selectedTemplate.label} email.</p>
            <p>© ${previewData.currentYear} Your Company. All rights reserved.</p>
            <p><a href="${previewData.unsubscribeUrl}">Unsubscribe</a></p>
          </div>
        </body>
        </html>
      `;

      previewHtml = html;
      toast.success('Preview generated');
    } catch (error) {
      console.error('Error generating preview:', error);
      toast.error('Failed to generate preview');
    } finally {
      isPreviewLoading = false;
    }
  }
</script>

{#if !apiStatus.hasApiKey}
  <div class="mb-4 rounded-md border border-amber-200 bg-amber-50 p-4 text-amber-800">
    <div class="flex items-center">
      <AlertTriangle class="mr-2 h-5 w-5" />
      <h3 class="text-sm font-medium">Resend API Key Not Configured</h3>
    </div>
    <div class="mt-2 text-sm">
      <p>
        The Resend API key is not configured. You need to set the RESEND_API_KEY environment
        variable to use audience and broadcast features.
      </p>
    </div>
  </div>
{/if}

<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
  <!-- Broadcast Form -->
  <Card.Root>
    <Card.Header>
      <Card.Title>Create New Broadcast</Card.Title>
      <Card.Description>Send a broadcast email to a selected audience</Card.Description>
    </Card.Header>

    <Card.Content>
      <form
        onsubmit={(e) => {
          e.preventDefault();
          createBroadcast();
        }}>
        <div class="space-y-4">
          <!-- Template Selection -->
          <div>
            <label for="templateName" class="mb-1 block text-sm font-medium">Template</label>
            <Select.Root
              selected={selectedTemplate
                ? { value: selectedTemplate.name, label: selectedTemplate.label }
                : null}
              onSelectedChange={(selected) => {
                if (selected) {
                  const template = marketingTemplates.find((t) => t.name === selected.value);
                  if (template) {
                    selectedTemplate = template;
                  }
                } else {
                  selectedTemplate = null;
                }
              }}>
              <Select.Trigger class="w-full">
                <Select.Value placeholder="Select template" />
              </Select.Trigger>
              <Select.Content>
                {#each marketingTemplates as template}
                  <Select.Item value={{ value: template.name, label: template.label }}
                    >{template.label}</Select.Item>
                {/each}
              </Select.Content>
            </Select.Root>
            <input type="hidden" name="templateName" value={selectedTemplate?.name || ''} />
          </div>

          <!-- Audience Selection -->
          <div>
            <label for="audienceId" class="mb-1 block text-sm font-medium">Audience</label>
            <Select.Root
              selected={selectedAudience
                ? { value: selectedAudience.id, label: selectedAudience.name }
                : null}
              onSelectedChange={(selected) => {
                if (selected) {
                  const audience = audiences.find((a) => a.id === selected.value);
                  if (audience) {
                    selectedAudience = audience;
                  }
                } else {
                  selectedAudience = null;
                }
              }}>
              <Select.Trigger class="w-full">
                <Select.Value placeholder="Select audience" />
              </Select.Trigger>
              <Select.Content>
                {#each audiences as audience}
                  <Select.Item value={{ value: audience.id, label: audience.name }}
                    >{audience.name}</Select.Item>
                {/each}
              </Select.Content>
            </Select.Root>
            <input type="hidden" name="audienceId" value={selectedAudience?.id || ''} />

            {#if audiences.length === 0}
              <p class="text-muted-foreground mt-1 text-xs">
                No audiences found. Create an audience in Resend first.
              </p>
            {/if}
          </div>

          <!-- Subject -->
          <div>
            <label for="subject" class="mb-1 block text-sm font-medium">Subject</label>
            <Input.Root
              id="subject"
              name="subject"
              placeholder="Email subject line"
              bind:value={subject} />
          </div>

          <!-- Title -->
          <div>
            <label for="title" class="mb-1 block text-sm font-medium">Title</label>
            <Input.Root id="title" name="title" placeholder="Email title" bind:value={title} />
          </div>

          <!-- Content -->
          <div>
            <label for="content" class="mb-1 block text-sm font-medium">Content</label>
            <Textarea.Root
              id="content"
              name="content"
              placeholder="Email content (HTML supported)"
              rows={6}
              bind:value={content} />
          </div>

          <!-- Image URL -->
          <div>
            <label for="imageUrl" class="mb-1 block text-sm font-medium"
              >Image URL (Optional)</label>
            <Input.Root
              id="imageUrl"
              name="imageUrl"
              placeholder="https://example.com/image.jpg"
              bind:value={imageUrl} />
          </div>

          <!-- CTA -->
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label for="ctaText" class="mb-1 block text-sm font-medium"
                >CTA Text (Optional)</label>
              <Input.Root
                id="ctaText"
                name="ctaText"
                placeholder="Call to action text"
                bind:value={ctaText} />
            </div>

            <div>
              <label for="ctaUrl" class="mb-1 block text-sm font-medium">CTA URL (Optional)</label>
              <Input.Root
                id="ctaUrl"
                name="ctaUrl"
                placeholder="https://example.com/action"
                bind:value={ctaUrl} />
            </div>
          </div>

          <!-- Schedule -->
          <div>
            <label for="scheduledAt" class="mb-1 block text-sm font-medium"
              >Schedule (Optional)</label>
            <Input.Root
              id="scheduledAt"
              name="scheduledAt"
              type="datetime-local"
              bind:value={scheduledAt} />
            <p class="text-muted-foreground mt-1 text-xs">Leave empty to send immediately</p>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-between pt-4">
            <Button.Root
              type="button"
              variant="outline"
              onclick={loadPreview}
              disabled={isPreviewLoading || !selectedTemplate}>
              {#if isPreviewLoading}
                <div
                  class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent">
                </div>
              {/if}
              Preview
            </Button.Root>

            <Button.Root
              type="submit"
              disabled={isSubmitting ||
                !selectedTemplate ||
                !selectedAudience ||
                !subject ||
                !title ||
                !content}>
              {#if isSubmitting}
                <div
                  class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent">
                </div>
              {/if}
              {scheduledAt ? 'Schedule' : 'Send'} Broadcast
            </Button.Root>
          </div>

          <!-- Form errors will be handled by the toast notifications -->
        </div>
      </form>
    </Card.Content>
  </Card.Root>

  <!-- Preview -->
  <Card.Root>
    <Card.Header>
      <Card.Title>Preview</Card.Title>
      <Card.Description>Preview how your email will look</Card.Description>
    </Card.Header>

    <Card.Content>
      {#if previewHtml}
        <div class="overflow-hidden rounded-md border">
          <iframe
            title="Email Preview"
            srcdoc={previewHtml}
            class="h-[600px] w-full border-0"
            sandbox="allow-same-origin">
          </iframe>
        </div>
      {:else if isPreviewLoading}
        <div class="flex h-[600px] items-center justify-center">
          <div
            class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent">
          </div>
        </div>
      {:else}
        <div class="text-muted-foreground flex h-[600px] flex-col items-center justify-center">
          <p>Select a template and click Preview to see how your email will look</p>
          <Button.Root
            variant="outline"
            class="mt-4"
            onclick={loadPreview}
            disabled={!selectedTemplate}>
            Preview
          </Button.Root>
        </div>
      {/if}
    </Card.Content>
  </Card.Root>
</div>

<!-- Broadcast History -->
<Card.Root class="mt-6">
  <Card.Header>
    <Card.Title>Broadcast History</Card.Title>
    <Card.Description>View and manage your email broadcasts</Card.Description>
  </Card.Header>

  <Card.Content>
    {#if isLoading}
      <div class="flex h-40 items-center justify-center">
        <div class="h-6 w-6 animate-spin rounded-full border-2 border-current border-t-transparent">
        </div>
      </div>
    {:else if broadcasts.length > 0}
      <Table.Root>
        <Table.Header>
          <Table.Row>
            <Table.Head>Subject</Table.Head>
            <Table.Head>Template</Table.Head>
            <Table.Head>Audience</Table.Head>
            <Table.Head>Status</Table.Head>
            <Table.Head>Scheduled</Table.Head>
            <Table.Head>Created</Table.Head>
            <Table.Head>Actions</Table.Head>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {#each broadcasts as broadcast}
            <Table.Row>
              <Table.Cell>{broadcast.subject}</Table.Cell>
              <Table.Cell>
                {marketingTemplates.find((t: Template) => t.name === broadcast.templateName)
                  ?.label || broadcast.templateName}
              </Table.Cell>
              <Table.Cell>
                {audiences.find((a: Audience) => a.id === broadcast.audienceId)?.name ||
                  broadcast.audienceId}
              </Table.Cell>
              <Table.Cell>
                <div class="flex items-center">
                  <span
                    class="rounded-full px-2 py-1 text-xs {getStatusBadgeClass(broadcast.status)}">
                    {#if getStatusIcon(broadcast.status) === Clock}
                      <Clock class="mr-1 inline-block h-3 w-3" />
                    {:else if getStatusIcon(broadcast.status) === Calendar}
                      <Calendar class="mr-1 inline-block h-3 w-3" />
                    {:else if getStatusIcon(broadcast.status) === CheckCircle}
                      <CheckCircle class="mr-1 inline-block h-3 w-3" />
                    {:else if getStatusIcon(broadcast.status) === XCircle}
                      <XCircle class="mr-1 inline-block h-3 w-3" />
                    {:else}
                      <AlertCircle class="mr-1 inline-block h-3 w-3" />
                    {/if}
                    {broadcast.status.charAt(0).toUpperCase() + broadcast.status.slice(1)}
                  </span>
                </div>
              </Table.Cell>
              <Table.Cell>{formatDate(broadcast.scheduledAt)}</Table.Cell>
              <Table.Cell>{formatDate(broadcast.createdAt)}</Table.Cell>
              <Table.Cell>
                {#if broadcast.status === 'scheduled'}
                  <Button.Root
                    variant="outline"
                    size="sm"
                    class="text-red-500 hover:text-red-700"
                    onclick={() => cancelBroadcast(broadcast.id)}>
                    Cancel
                  </Button.Root>
                {/if}
              </Table.Cell>
            </Table.Row>
          {/each}
        </Table.Body>
      </Table.Root>
    {:else}
      <div class="text-muted-foreground flex h-40 items-center justify-center">
        <p>No broadcasts found</p>
      </div>
    {/if}
  </Card.Content>
</Card.Root>
