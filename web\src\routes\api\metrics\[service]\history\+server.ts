// src/routes/api/metrics/[service]/history/+server.ts
import { json } from '@sveltejs/kit';
import { logger } from '$lib/server/logger';
import type { RequestHandler } from './$types';

// Mock data generator for 30-day history
function generateMockHistoryData(service: string, days: number = 30) {
  const data = [];
  const now = new Date();
  
  // Base metrics for each service
  const baseMetrics: Record<string, any> = {
    'resume-builder': {
      successRate: 98,
      responseTime: 2500,
      dailyUsage: 120
    },
    'resume-scanner': {
      successRate: 96,
      responseTime: 1800,
      dailyUsage: 85
    },
    'job-search': {
      successRate: 99,
      responseTime: 900,
      dailyUsage: 350
    },
    'application-system': {
      successRate: 97,
      responseTime: 3200,
      dailyUsage: 180
    },
    'account-services': {
      successRate: 99.5,
      responseTime: 300,
      dailyUsage: 850
    }
  };
  
  // Get base metrics for the requested service
  const metrics = baseMetrics[service] || {
    successRate: 99,
    responseTime: 500,
    dailyUsage: 100
  };
  
  // Generate data for each day with slight variations
  for (let i = 0; i < days; i++) {
    const date = new Date(now);
    date.setDate(date.getDate() - (days - i - 1));
    
    // Add some random variation
    const variation = (Math.random() * 0.1) - 0.05; // -5% to +5%
    const statusVariation = Math.random() > 0.9 ? 0.85 : 1; // 10% chance of a dip
    
    data.push({
      date: date.toISOString().split('T')[0],
      successRate: Math.min(100, Math.max(80, metrics.successRate * (1 + variation * 0.5) * statusVariation)),
      responseTime: Math.max(100, metrics.responseTime * (1 + variation)),
      dailyUsage: Math.round(metrics.dailyUsage * (1 + variation * 2)),
      status: statusVariation < 1 ? 'degraded' : 'operational'
    });
  }
  
  return data;
}

export const GET: RequestHandler = async ({ params, url }) => {
  try {
    const service = params.service;
    const days = parseInt(url.searchParams.get('days') || '30');
    
    // Limit days to a reasonable range
    const limitedDays = Math.min(90, Math.max(7, days));
    
    // In a real implementation, you would fetch this data from a database
    // For now, we'll generate mock data
    const historyData = generateMockHistoryData(service, limitedDays);
    
    return json({
      service,
      days: limitedDays,
      data: historyData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error fetching metrics history for ${params.service}:`, error);
    return json(
      {
        error: `Failed to fetch metrics history for ${params.service}`,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
};
