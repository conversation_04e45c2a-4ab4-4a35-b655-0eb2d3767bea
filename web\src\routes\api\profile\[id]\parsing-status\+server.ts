import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { json } from '@sveltejs/kit';
import { getProfileParsingStatus } from '$lib/utils/profile-parsing';
import { prisma } from '$lib/server/prisma';

export const GET: RequestHandler = async ({ params, locals }) => {
  const user = locals.user;
  if (!user) return new Response('Unauthorized', { status: 401 });

  const profileId = params.id;

  // Check if the user has access to this profile
  const profile = await prisma.profile.findUnique({
    where: { id: profileId },
    include: { team: { include: { members: true } } },
  });

  if (!profile) {
    return json({ error: 'Profile not found' }, { status: 404 });
  }

  const isOwner = profile.userId === user.id;
  const isTeamMember = profile.team?.members.some((m) => m.userId === user.id);

  if (!isOwner && !isTeamMember) {
    return json({ error: 'Unauthorized access to profile' }, { status: 403 });
  }

  // Get the profile parsing status
  const parsingStatus = await getProfileParsingStatus(profileId);

  return json(parsingStatus);
};
