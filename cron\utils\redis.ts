// cron/utils/redis.ts

import Redis, { RedisOptions } from "ioredis";
import { logger } from "./logger.js";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Determine which Redis URL to use
const REDIS_HOST = process.env.REDIS_HOST || "localhost";
const REDIS_PORT = parseInt(process.env.REDIS_PORT || "6379", 10);
const REDIS_PORT_VALIDATED = isNaN(REDIS_PORT) ? 6379 : REDIS_PORT;
const REDIS_PASSWORD = process.env.REDIS_PASSWORD || null;

// Log all environment variables for debugging
logger.info(`🔄 Environment variables for Redis:`);
logger.info(`🔄 REDIS_URL: ${process.env.REDIS_URL || "undefined"}`);
logger.info(
  `🔄 REDIS_URL_INTERNAL: ${process.env.REDIS_URL_INTERNAL || "undefined"}`
);
logger.info(
  `🔄 REDIS_URL_EXTERNAL: ${process.env.REDIS_URL_EXTERNAL || "undefined"}`
);
logger.info(`🔄 REDIS_HOST: ${process.env.REDIS_HOST || "undefined"}`);
logger.info(`🔄 REDIS_PORT: ${process.env.REDIS_PORT || "undefined"}`);
logger.info(`🔄 NODE_ENV: ${process.env.NODE_ENV || "undefined"}`);
logger.info(
  `🔄 REDIS_PASSWORD: ${process.env.REDIS_PASSWORD ? "defined" : "undefined"}`
);

// Default to local development URL
let REDIS_URL = `redis://${REDIS_HOST}:${REDIS_PORT_VALIDATED}`;

// In production, use the provided URLs if available
if (process.env.NODE_ENV === "production") {
  // Check for direct REDIS_URL first
  if (process.env.REDIS_URL) {
    REDIS_URL = process.env.REDIS_URL;
    logger.info(`🔄 Using REDIS_URL from environment`);
  }
  // Then check for internal/external URLs
  else if (process.env.REDIS_URL_INTERNAL) {
    REDIS_URL = process.env.REDIS_URL_INTERNAL;
    logger.info(`🔄 Using REDIS_URL_INTERNAL from environment`);
  } else if (process.env.REDIS_URL_EXTERNAL) {
    REDIS_URL = process.env.REDIS_URL_EXTERNAL;
    logger.info(`🔄 Using REDIS_URL_EXTERNAL from environment`);
  }
  // If REDIS_HOST is provided but not a full URL, construct it
  else if (process.env.REDIS_HOST && process.env.REDIS_HOST !== "localhost") {
    REDIS_URL = `redis://${REDIS_HOST}:${REDIS_PORT_VALIDATED}`;
    logger.info(`🔄 Constructed Redis URL from REDIS_HOST and REDIS_PORT`);
  }
  // Log the final decision
  logger.info(`🔄 Using production Redis configuration`);
} else {
  logger.info(`🔄 Using local Redis: ${REDIS_URL}`);
}

// Check if URL is an internal Render.com URL (starts with red- but doesn't have auth credentials)
const isInternalRenderUrl =
  REDIS_URL.includes("red-") && !REDIS_URL.includes("@");

// Initialize Redis client with appropriate options
const redisOptions: RedisOptions = {
  retryStrategy: (times: number) => {
    logger.info(`🔄 Redis retry attempt ${times}`);
    return Math.min(times * 100, 10000);
  },
  tls:
    REDIS_URL.startsWith("rediss://") ||
    (!isInternalRenderUrl && REDIS_URL.includes("@"))
      ? { rejectUnauthorized: false }
      : undefined,
  password: isInternalRenderUrl
    ? undefined
    : REDIS_URL.includes("@")
      ? undefined
      : REDIS_PASSWORD || undefined, // Convert null to undefined to satisfy TypeScript
  connectTimeout: 20000,
  maxRetriesPerRequest: 5,
  enableReadyCheck: true,
  enableOfflineQueue: true,
  showFriendlyErrorStack: process.env.NODE_ENV !== "production",
};

// Log the Redis options (without sensitive data)
logger.info(`🔄 Redis connection options:`, {
  ...redisOptions,
  password: redisOptions.password ? "****" : undefined,
});

// Create Redis client instance with fallback to localhost if URL is invalid
let finalRedisUrl = REDIS_URL;

// Validate the URL
if (
  finalRedisUrl.includes("${") ||
  finalRedisUrl.includes("NaN") ||
  finalRedisUrl.includes(":undefined")
) {
  logger.warn(`⚠️ Invalid Redis URL detected, falling back to localhost:6379`);
  finalRedisUrl = "redis://localhost:6379";
}

logger.info(`🔄 Final Redis URL: ${finalRedisUrl.replace(/(:.*@)/, ":****@")}`);

// Create and export the Redis client
export const redis = new Redis(finalRedisUrl, redisOptions);

// Set up event handlers
redis.on("connect", async () => {
  logger.info(`✅ Redis connected successfully`);
});

redis.on("error", (error) => {
  logger.error(`❌ Redis error: ${error}`);
});

redis.on("reconnecting", () => {
  logger.info(`🔄 Redis reconnecting...`);
});

redis.on("close", () => {
  logger.warn(`⚠️ Redis connection closed`);
});

// Helper function to create prefixed keys
export function createPrefixer(namespace: string) {
  return (key: string) => `${namespace}${key}`;
}

// Export a function to get a Redis client
export async function getRedisClient(): Promise<Redis> {
  try {
    // Check if Redis is connected
    await redis.ping();
    return redis;
  } catch (error) {
    logger.error(`❌ Failed to connect to Redis: ${error}`);
    throw new Error(`Failed to connect to Redis: ${error}`);
  }
}
