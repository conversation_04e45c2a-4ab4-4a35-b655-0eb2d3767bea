/**
 * Setup Feature Limits Script
 * 
 * This script runs the feature limits seeding process.
 */

import { spawn } from 'child_process';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function runScript(scriptPath: string): Promise<void> {
  return new Promise((resolve, reject) => {
    console.log(`Running script: ${scriptPath}`);
    
    const process = spawn('node', [scriptPath], {
      stdio: 'inherit',
      shell: true,
    });
    
    process.on('close', (code) => {
      if (code === 0) {
        console.log(`Script ${scriptPath} completed successfully`);
        resolve();
      } else {
        console.error(`Script ${scriptPath} failed with code ${code}`);
        reject(new Error(`Script ${scriptPath} failed with code ${code}`));
      }
    });
    
    process.on('error', (err) => {
      console.error(`Failed to start script ${scriptPath}:`, err);
      reject(err);
    });
  });
}

async function main(): Promise<void> {
  console.log('Starting feature limits setup...');
  
  try {
    // Seed the feature limits to the database
    await runScript(path.join(__dirname, 'seed-feature-limits.ts'));
    
    console.log('\nFeature limits setup completed successfully!');
    
  } catch (error) {
    console.error('Error setting up feature limits:', error);
    process.exit(1);
  }
}

// Run the script
main();
