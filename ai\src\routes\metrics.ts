import express from 'express';
import { getMetricsHistory } from '../services/health/metrics.js';

const router = express.Router();

// Get metrics history
router.get('/', (req, res) => {
  const metrics = getMetricsHistory();
  
  // Format metrics for display
  const formattedMetrics = metrics.map(metric => ({
    timestamp: new Date(metric.timestamp).toISOString(),
    cpuUsage: metric.cpuUsage.toFixed(1),
    memoryUsage: metric.memoryUsage.toFixed(1),
    uptime: metric.uptime,
  }));
  
  res.json({
    success: true,
    metrics: formattedMetrics,
  });
});

// Get metrics for the last N minutes
router.get('/last/:minutes', (req, res) => {
  const minutes = parseInt(req.params.minutes, 10) || 60;
  const metrics = getMetricsHistory();
  
  // Filter metrics for the requested time period
  const cutoffTime = Date.now() - (minutes * 60 * 1000);
  const filteredMetrics = metrics.filter(metric => metric.timestamp >= cutoffTime);
  
  // Format metrics for display
  const formattedMetrics = filteredMetrics.map(metric => ({
    timestamp: new Date(metric.timestamp).toISOString(),
    cpuUsage: metric.cpuUsage.toFixed(1),
    memoryUsage: metric.memoryUsage.toFixed(1),
    uptime: metric.uptime,
  }));
  
  res.json({
    success: true,
    timeRange: `Last ${minutes} minutes`,
    metrics: formattedMetrics,
  });
});

export default router;
