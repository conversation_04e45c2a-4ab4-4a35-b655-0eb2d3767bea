<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { Edit } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import type { ProfileHeaderSchema } from '$lib/validators/profile';
  import ProfileHeaderModal from './ProfileHeaderModal.svelte';

  // Props
  const { data, onSave } = $props<{
    data: ProfileHeaderSchema;
    onSave: (data: ProfileHeaderSchema) => Promise<boolean>;
  }>();

  // Modal state
  let modalOpen = $state(false);

  // Open edit modal
  function openEditModal() {
    modalOpen = true;
  }

  // Handle modal save
  async function handleSave(updatedData: ProfileHeaderSchema): Promise<boolean> {
    try {
      const success = await onSave(updatedData);
      return success;
    } catch (error) {
      console.error('Error saving profile header:', error);
      toast.error('Failed to save profile header');
      return false;
    }
  }

  // Job search status options
  const jobSearchStatusOptions = [
    { value: 'actively_looking', label: 'Actively Looking' },
    { value: 'open_to_opportunities', label: 'Open to Opportunities' },
    { value: 'not_looking', label: 'Not Looking' },
  ];

  // Format job search status for display
  function formatJobSearchStatus(status: string): string {
    const option = jobSearchStatusOptions.find((opt) => opt.value === status);
    return option ? option.label : 'Not specified';
  }
</script>

<div class="rounded-lg border p-6">
  <div class="flex items-center justify-between">
    <h2 class="text-xl font-semibold">Profile</h2>
    <Button variant="ghost" size="sm" onclick={openEditModal}>
      <Edit class="mr-2 h-4 w-4" />
      Edit
    </Button>
  </div>

  <div class="mt-4 space-y-4">
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
      <div>
        <h3 class="text-muted-foreground text-sm font-medium">Profile Name</h3>
        <p>{data.profileName || 'Not specified'}</p>
      </div>
      <div>
        <h3 class="text-muted-foreground text-sm font-medium">Full Name</h3>
        <p>{data.fullName || 'Not specified'}</p>
      </div>
      <div>
        <h3 class="text-muted-foreground text-sm font-medium">Job Title</h3>
        <p>{data.jobTitle || 'Not specified'}</p>
      </div>
      <div>
        <h3 class="text-muted-foreground text-sm font-medium">Job Search Status</h3>
        <div class="flex items-center">
          <span
            class={`mr-2 h-2 w-2 rounded-full ${
              data.jobSearchStatus === 'actively_looking'
                ? 'bg-green-500'
                : data.jobSearchStatus === 'open_to_opportunities'
                  ? 'bg-yellow-500'
                  : 'bg-gray-500'
            }`}></span>
          <p>{formatJobSearchStatus(data.jobSearchStatus)}</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Profile Header Modal -->
<ProfileHeaderModal
  open={modalOpen}
  {data}
  onClose={() => (modalOpen = false)}
  onSave={handleSave} />
