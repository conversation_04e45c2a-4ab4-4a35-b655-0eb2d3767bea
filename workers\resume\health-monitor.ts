// File: workers/resume/health-monitor.ts
import os from "os";
import { redis } from "../redis.js";
import { getSystemResourceInfo } from "../utils/system-resources.js";

// Constants for health monitoring
const HEALTH_CHECK_INTERVAL = 1800000; // 30 minutes
const MEMORY_THRESHOLD_WARNING = 85; // 85% memory usage triggers warning
const MEMORY_THRESHOLD_CRITICAL = 95; // 95% memory usage triggers critical alert
const CPU_THRESHOLD_WARNING = 80; // 80% CPU usage triggers warning
const CPU_THRESHOLD_CRITICAL = 90; // 90% CPU usage triggers critical alert

// Health status enum
enum HealthStatus {
  HEALTHY = "healthy",
  WARNING = "warning",
  CRITICAL = "critical",
  UNKNOWN = "unknown",
}

// Health metrics interface
interface HealthMetrics {
  status: HealthStatus;
  lastCheck: Date | null;
  memory: {
    total: number;
    used: number;
    free: number;
    percentUsed: number;
  };
  cpu: {
    usage: number;
    cores: number;
  };
  uptime: number;
  jobsProcessed: number;
  jobsSucceeded: number;
  jobsFailed: number;
  lastError: string | null;
}

// Store for health metrics
const healthMetrics: HealthMetrics = {
  status: HealthStatus.UNKNOWN,
  lastCheck: null,
  memory: {
    total: 0,
    used: 0,
    free: 0,
    percentUsed: 0,
  },
  cpu: {
    usage: 0,
    cores: os.cpus().length,
  },
  uptime: 0,
  jobsProcessed: 0,
  jobsSucceeded: 0,
  jobsFailed: 0,
  lastError: null,
};

/**
 * Initialize the health monitoring system
 */
export async function initHealthMonitor(): Promise<boolean> {
  console.log("[Health Monitor] 🚀 Initializing health monitoring system...");

  try {
    // Perform initial health check
    await checkHealth();

    // Set up interval for regular health checks
    setInterval(checkHealth, HEALTH_CHECK_INTERVAL);

    // Set up event listeners for process metrics
    setupEventListeners();

    console.log("[Health Monitor] ✅ Health monitoring system initialized");

    return true;
  } catch (error) {
    console.error(
      "[Health Monitor] ❌ Failed to initialize health monitoring:",
      error
    );
    return false;
  }
}

/**
 * Check the health of the resume worker
 * This function runs every 30 minutes to monitor system resources
 * Uses container metrics exclusively for standardized resource monitoring
 */
async function checkHealth(): Promise<HealthMetrics> {
  try {
    console.log("[Health Monitor] 🔍 Checking system health...");

    // Get system resource information using our standardized utility
    const resourceInfo = await getSystemResourceInfo();

    // Extract memory metrics
    const totalMem = resourceInfo.memory.totalBytes;
    const freeMem = resourceInfo.memory.freeBytes;
    const usedMem = resourceInfo.memory.usedBytes;
    const percentUsed = Math.round(resourceInfo.memory.usagePercentRaw);

    // Extract CPU metrics
    const cpuUsage = resourceInfo.cpu.usagePercentRaw;

    // Update health metrics
    healthMetrics.lastCheck = new Date();
    healthMetrics.memory = {
      total: totalMem,
      used: usedMem,
      free: freeMem,
      percentUsed,
    };
    healthMetrics.cpu.usage = cpuUsage;
    healthMetrics.uptime = process.uptime();

    // Determine health status
    let status = HealthStatus.HEALTHY;

    if (
      percentUsed >= MEMORY_THRESHOLD_CRITICAL ||
      cpuUsage >= CPU_THRESHOLD_CRITICAL
    ) {
      status = HealthStatus.CRITICAL;
    } else if (
      percentUsed >= MEMORY_THRESHOLD_WARNING ||
      cpuUsage >= CPU_THRESHOLD_WARNING
    ) {
      status = HealthStatus.WARNING;
    }

    healthMetrics.status = status;

    // Publish health metrics to Redis
    await publishHealthMetrics();

    // Log health status with emojis
    let statusEmoji = "❓";
    if (status === HealthStatus.HEALTHY) {
      statusEmoji = "✅";
    } else if (status === HealthStatus.WARNING) {
      statusEmoji = "⚠️";
    } else if (status === HealthStatus.CRITICAL) {
      statusEmoji = "🔴";
    }

    console.log(
      `[Health Monitor] ${statusEmoji} Status: ${status.toUpperCase()}`
    );
    console.log(`[Health Monitor] 📊 Memory: ${percentUsed}% used`);
    console.log(`[Health Monitor] 📊 CPU: ${cpuUsage}% used`);

    return healthMetrics;
  } catch (error: any) {
    console.error("[Health Monitor] ❌ Error checking health:", error);
    healthMetrics.status = HealthStatus.UNKNOWN;
    healthMetrics.lastError = error.message ?? "Unknown error";

    return healthMetrics;
  }
}

/**
 * Get CPU usage as a percentage
 * This function is no longer needed as we use the system-resources utility
 * Kept for backward compatibility
 */
async function getCpuUsage(): Promise<number> {
  try {
    // Get system resource information using our standardized utility
    const resourceInfo = await getSystemResourceInfo();

    // Return CPU usage percentage
    return resourceInfo.cpu.usagePercentRaw;
  } catch (error) {
    console.error(`[Health Monitor] ❌ Error in getCpuUsage: ${error}`);

    // Return a safe default
    return 0;
  }
}

/**
 * Publish health metrics to Redis
 */
async function publishHealthMetrics(): Promise<void> {
  try {
    // Check if Redis is connected by attempting a ping
    let isRedisConnected = false;

    try {
      const pong = await redis.ping();
      if (pong === "PONG") {
        isRedisConnected = true;
      } else {
        console.log(
          "[Health Monitor] Redis ping returned unexpected response, skipping health metrics publication"
        );
      }
    } catch (pingError: any) {
      // Log the error with details
      console.log(
        `[Health Monitor] Redis ping failed: ${pingError?.message ?? "Unknown error"}, skipping health metrics publication`
      );

      // Update the last error in health metrics
      healthMetrics.lastError = pingError?.message ?? "Redis connection error";
    }

    if (!isRedisConnected) {
      return;
    }

    await redis.publish(
      "resume-worker:health",
      JSON.stringify({
        type: "health_update",
        data: healthMetrics,
        timestamp: new Date().toISOString(),
      })
    );

    console.log("[Health Monitor] ✅ Health metrics published to Redis");
  } catch (error) {
    console.error(
      "[Health Monitor] ❌ Error publishing health metrics:",
      error
    );
  }
}

/**
 * Set up event listeners for tracking job metrics
 */
function setupEventListeners(): void {
  // These would be custom events emitted by the job processing system
  process.on("job:processed", () => {
    healthMetrics.jobsProcessed++;
  });

  process.on("job:succeeded", () => {
    healthMetrics.jobsSucceeded++;
  });

  process.on("job:failed", (error: Error | string | null) => {
    healthMetrics.jobsFailed++;

    // Handle different error types
    let errorMessage = "Unknown error";
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === "string") {
      errorMessage = error;
    }

    healthMetrics.lastError = errorMessage;
  });
}

/**
 * Get current health metrics
 */
export function getHealthMetrics(): HealthMetrics {
  return { ...healthMetrics };
}
