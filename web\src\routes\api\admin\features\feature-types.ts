/**
 * Feature Types
 *
 * This file defines the different types of features that can be seeded into the database.
 * Each type has a name, description, and endpoint for seeding.
 */

export interface FeatureType {
  id: string;
  name: string;
  description: string;
  endpoint: string;
  icon: string;
}

export const FEATURE_TYPES: FeatureType[] = [
  {
    id: 'service',
    name: 'Service Features',
    description:
      'Core service features including Auto-Apply, Co-Pilot, Job Tracker, Resume Builder, etc.',
    endpoint: '/api/admin/features/seed-service',
    icon: 'briefcase',
  },
  {
    id: 'analysis',
    name: 'Analysis Features',
    description:
      'Analytics and data analysis features for job market insights and career planning.',
    endpoint: '/api/admin/features/seed-analysis',
    icon: 'chart-bar',
  },
  {
    id: 'all',
    name: 'All Features',
    description: 'Seed all feature types at once.',
    endpoint: '/api/admin/features/seed-all',
    icon: 'layers',
  },
];
