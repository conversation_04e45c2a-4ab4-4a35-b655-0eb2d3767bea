<!-- This file is the same as dashboard/settings/email-audiences/+page.svelte -->
<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Button from '$lib/components/ui/button/index.js';
  import * as Input from '$lib/components/ui/input/index.js';
  import * as Table from '$lib/components/ui/table/index.js';
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { toast } from 'svelte-sonner';
  import { onMount } from 'svelte';
  import { Users, UserPlus, Trash, Edit, RefreshCw, Plus, AlertTriangle } from 'lucide-svelte';

  // Type definitions
  type Audience = {
    id: string;
    name: string;
    created_at: string;
  };

  type Contact = {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
    created_at: string;
  };

  // State
  let audiences = $state<Audience[]>([]);
  let selectedAudience = $state<Audience | null>(null);
  let contacts = $state<Contact[]>([]);
  let isLoadingAudiences = $state(true);
  let isLoadingContacts = $state(false);
  let newAudienceName = $state('');
  let isCreatingAudience = $state(false);
  let isAudienceDialogOpen = $state(false);
  let isContactDialogOpen = $state(false);
  let newContactEmail = $state('');
  let newContactFirstName = $state('');
  let newContactLastName = $state('');
  let isCreatingContact = $state(false);
  let editingContact = $state<Contact | null>(null);
  let isImportDialogOpen = $state(false);
  let importFile = $state<File | null>(null);
  let isImporting = $state(false);

  // API status
  let apiStatus = $state({
    isAvailable: false,
    hasApiKey: false,
    error: null as string | null,
  });

  // API base URL
  const API_BASE_URL = '/api/email';

  // Load audiences on mount
  onMount(async () => {
    await loadAudiences();
  });

  // Load audiences
  async function loadAudiences() {
    isLoadingAudiences = true;

    try {
      const response = await fetch(`${API_BASE_URL}/audiences`);

      if (response.ok) {
        const data = await response.json();

        // The API returns an array of audiences
        audiences = data;
        apiStatus.isAvailable = true;
        apiStatus.hasApiKey = true;
      } else {
        const error = await response.json();

        // Check if the error is due to missing API key
        if (error.error && error.error.includes('API key')) {
          apiStatus.hasApiKey = false;
          toast.error(
            'Resend API key not configured. Please set the RESEND_API_KEY environment variable.'
          );
        } else {
          toast.error(error.error || 'Failed to load audiences');
        }
      }
    } catch (error) {
      console.error('Error loading audiences:', error);
      apiStatus.error = error.message;
      apiStatus.isAvailable = false;

      toast.error('Failed to connect to email API server.');
    } finally {
      isLoadingAudiences = false;
    }
  }

  // Load contacts for an audience
  async function loadContacts(audienceId: string) {
    if (!audienceId) return;

    isLoadingContacts = true;
    selectedAudience = audiences.find((a) => a.id === audienceId);

    try {
      const response = await fetch(`/api/email/audiences/contacts?audienceId=${audienceId}`);

      if (response.ok) {
        const data = await response.json();

        // Check if the response is an array
        if (Array.isArray(data)) {
          contacts = data;
        } else {
          console.error('Unexpected response format:', data);
          toast.error('Unexpected response format from server');
          contacts = [];
        }
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to load contacts');
      }
    } catch (error) {
      console.error('Error loading contacts:', error);
      toast.error('Failed to load contacts');
    } finally {
      isLoadingContacts = false;
    }
  }

  // Create audience
  async function createAudience() {
    if (!newAudienceName) {
      toast.error('Audience name is required');
      return;
    }

    isCreatingAudience = true;

    try {
      const response = await fetch(`${API_BASE_URL}/audiences`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newAudienceName,
        }),
      });

      if (response.ok) {
        const audience = await response.json();
        audiences = [...audiences, audience];
        newAudienceName = '';
        isAudienceDialogOpen = false;
        toast.success('Audience created successfully');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create audience');
      }
    } catch (error) {
      console.error('Error creating audience:', error);
      toast.error('Failed to create audience');
    } finally {
      isCreatingAudience = false;
    }
  }

  // Delete audience
  async function deleteAudience(audienceId: string) {
    if (!confirm('Are you sure you want to delete this audience? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/email/audiences?id=${audienceId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        audiences = audiences.filter((a) => a.id !== audienceId);

        if (selectedAudience?.id === audienceId) {
          selectedAudience = null;
          contacts = [];
        }

        toast.success('Audience deleted successfully');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to delete audience');
      }
    } catch (error) {
      console.error('Error deleting audience:', error);
      toast.error('Failed to delete audience');
    }
  }

  // Create contact
  async function createContact() {
    if (!selectedAudience) {
      toast.error('No audience selected');
      return;
    }

    if (!newContactEmail) {
      toast.error('Email is required');
      return;
    }

    isCreatingContact = true;

    try {
      const response = await fetch('/api/email/audiences/contacts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          audienceId: selectedAudience.id,
          email: newContactEmail,
          firstName: newContactFirstName,
          lastName: newContactLastName,
        }),
      });

      if (response.ok) {
        const contact = await response.json();
        contacts = [...contacts, contact];
        newContactEmail = '';
        newContactFirstName = '';
        newContactLastName = '';
        isContactDialogOpen = false;
        toast.success('Contact added successfully');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to add contact');
      }
    } catch (error) {
      console.error('Error adding contact:', error);
      toast.error('Failed to add contact');
    } finally {
      isCreatingContact = false;
    }
  }

  // Update contact
  async function updateContact() {
    if (!selectedAudience || !editingContact) {
      toast.error('No audience or contact selected');
      return;
    }

    if (!newContactEmail) {
      toast.error('Email is required');
      return;
    }

    isCreatingContact = true;

    try {
      const response = await fetch('/api/email/audiences/contacts', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          audienceId: selectedAudience.id,
          id: editingContact.id,
          email: newContactEmail,
          firstName: newContactFirstName,
          lastName: newContactLastName,
        }),
      });

      if (response.ok) {
        const updatedContact = await response.json();
        contacts = contacts.map((c) => (c.id === updatedContact.id ? updatedContact : c));
        newContactEmail = '';
        newContactFirstName = '';
        newContactLastName = '';
        editingContact = null;
        isContactDialogOpen = false;
        toast.success('Contact updated successfully');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to update contact');
      }
    } catch (error) {
      console.error('Error updating contact:', error);
      toast.error('Failed to update contact');
    } finally {
      isCreatingContact = false;
    }
  }

  // Delete contact
  async function deleteContact(contactId: string) {
    if (!selectedAudience) {
      toast.error('No audience selected');
      return;
    }

    if (!confirm('Are you sure you want to delete this contact?')) {
      return;
    }

    try {
      const response = await fetch(
        `/api/email/audiences/contacts?audienceId=${selectedAudience.id}&id=${contactId}`,
        {
          method: 'DELETE',
        }
      );

      if (response.ok) {
        contacts = contacts.filter((c) => c.id !== contactId);
        toast.success('Contact deleted successfully');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to delete contact');
      }
    } catch (error) {
      console.error('Error deleting contact:', error);
      toast.error('Failed to delete contact');
    }
  }

  // Open contact dialog for editing
  function openEditContactDialog(contact: Contact) {
    editingContact = contact;
    newContactEmail = contact.email;
    newContactFirstName = contact.first_name || '';
    newContactLastName = contact.last_name || '';
    isContactDialogOpen = true;
  }

  // Open contact dialog for creating
  function openCreateContactDialog() {
    editingContact = null;
    newContactEmail = '';
    newContactFirstName = '';
    newContactLastName = '';
    isContactDialogOpen = true;
  }

  // Handle file selection for import
  function handleFileSelect(event: Event) {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files.length > 0) {
      importFile = target.files[0];
    }
  }

  // Import contacts from CSV
  async function importContacts() {
    if (!selectedAudience) {
      toast.error('No audience selected');
      return;
    }

    if (!importFile) {
      toast.error('Please select a CSV file');
      return;
    }

    isImporting = true;

    try {
      const formData = new FormData();
      formData.append('file', importFile);
      formData.append('audienceId', selectedAudience.id);

      const response = await fetch('/api/email/audiences/contacts/import', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        await loadContacts(selectedAudience.id);
        isImportDialogOpen = false;
        importFile = null;
        toast.success(`Imported ${result.imported} contacts successfully`);
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to import contacts');
      }
    } catch (error) {
      console.error('Error importing contacts:', error);
      toast.error('Failed to import contacts');
    } finally {
      isImporting = false;
    }
  }
</script>

{#if !apiStatus.hasApiKey}
  <div class="mb-4 rounded-md border border-amber-200 bg-amber-50 p-4 text-amber-800">
    <div class="flex items-center">
      <AlertTriangle class="mr-2 h-5 w-5" />
      <h3 class="text-sm font-medium">Resend API Key Not Configured</h3>
    </div>
    <div class="mt-2 text-sm">
      <p>
        The Resend API key is not configured. You need to set the RESEND_API_KEY environment
        variable to use audience and broadcast features.
      </p>
    </div>
  </div>
{/if}

<div class="grid grid-cols-1 gap-6 md:grid-cols-3">
  <!-- Audiences List -->
  <div class="md:col-span-1">
    <Card.Root>
      <Card.Header>
        <div class="flex items-center justify-between">
          <div>
            <Card.Title>Audiences</Card.Title>
            <Card.Description>Manage your email audiences</Card.Description>
          </div>
          <Button.Root variant="outline" size="sm" onclick={() => (isAudienceDialogOpen = true)}>
            <Plus class="mr-2 h-4 w-4" />
            New
          </Button.Root>
        </div>
      </Card.Header>

      <Card.Content>
        {#if isLoadingAudiences}
          <div class="flex h-40 items-center justify-center">
            <div
              class="h-6 w-6 animate-spin rounded-full border-2 border-current border-t-transparent">
            </div>
          </div>
        {:else if audiences.length === 0}
          <div class="text-muted-foreground flex h-40 flex-col items-center justify-center">
            <p>No audiences found</p>
            <Button.Root
              variant="outline"
              class="mt-4"
              onclick={() => (isAudienceDialogOpen = true)}>
              Create Audience
            </Button.Root>
          </div>
        {:else}
          <div class="max-h-[500px] divide-y overflow-y-auto rounded-md border">
            {#each audiences as audience}
              <div class="flex items-center justify-between p-3">
                <button
                  class="hover:text-primary flex-1 text-left transition-colors {selectedAudience?.id ===
                  audience.id
                    ? 'text-primary font-medium'
                    : ''}"
                  onclick={() => loadContacts(audience.id)}>
                  <div class="flex items-center">
                    <Users class="text-muted-foreground mr-2 h-4 w-4" />
                    {audience.name}
                  </div>
                </button>
                <Button.Root
                  variant="ghost"
                  size="sm"
                  class="text-red-500 hover:text-red-700"
                  onclick={() => deleteAudience(audience.id)}>
                  <Trash class="h-4 w-4" />
                </Button.Root>
              </div>
            {/each}
          </div>
        {/if}
      </Card.Content>
    </Card.Root>
  </div>

  <!-- Contacts List -->
  <div class="md:col-span-2">
    <Card.Root>
      <Card.Header>
        <div class="flex items-center justify-between">
          <div>
            <Card.Title>
              {selectedAudience ? `Contacts in ${selectedAudience.name}` : 'Contacts'}
            </Card.Title>
            <Card.Description>
              {selectedAudience
                ? `Manage contacts in ${selectedAudience.name}`
                : 'Select an audience to view contacts'}
            </Card.Description>
          </div>
          {#if selectedAudience}
            <div class="flex space-x-2">
              <Button.Root variant="outline" size="sm" onclick={() => (isImportDialogOpen = true)}>
                <RefreshCw class="mr-2 h-4 w-4" />
                Import
              </Button.Root>
              <Button.Root size="sm" onclick={openCreateContactDialog}>
                <UserPlus class="mr-2 h-4 w-4" />
                Add Contact
              </Button.Root>
            </div>
          {/if}
        </div>
      </Card.Header>

      <Card.Content>
        {#if !selectedAudience}
          <div class="text-muted-foreground flex h-40 items-center justify-center">
            <p>Select an audience to view contacts</p>
          </div>
        {:else if isLoadingContacts}
          <div class="flex h-40 items-center justify-center">
            <div
              class="h-6 w-6 animate-spin rounded-full border-2 border-current border-t-transparent">
            </div>
          </div>
        {:else if contacts.length === 0}
          <div class="text-muted-foreground flex h-40 flex-col items-center justify-center">
            <p>No contacts found in this audience</p>
            <Button.Root variant="outline" class="mt-4" onclick={openCreateContactDialog}>
              Add Contact
            </Button.Root>
          </div>
        {:else}
          <Table.Root>
            <Table.Header>
              <Table.Row>
                <Table.Head>Email</Table.Head>
                <Table.Head>First Name</Table.Head>
                <Table.Head>Last Name</Table.Head>
                <Table.Head>Created</Table.Head>
                <Table.Head>Actions</Table.Head>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {#each contacts as contact}
                <Table.Row>
                  <Table.Cell>{contact.email}</Table.Cell>
                  <Table.Cell>{contact.first_name || '-'}</Table.Cell>
                  <Table.Cell>{contact.last_name || '-'}</Table.Cell>
                  <Table.Cell>
                    {new Date(contact.created_at).toLocaleDateString()}
                  </Table.Cell>
                  <Table.Cell>
                    <div class="flex space-x-2">
                      <Button.Root
                        variant="ghost"
                        size="sm"
                        onclick={() => openEditContactDialog(contact)}>
                        <Edit class="h-4 w-4" />
                      </Button.Root>
                      <Button.Root
                        variant="ghost"
                        size="sm"
                        class="text-red-500 hover:text-red-700"
                        onclick={() => deleteContact(contact.id)}>
                        <Trash class="h-4 w-4" />
                      </Button.Root>
                    </div>
                  </Table.Cell>
                </Table.Row>
              {/each}
            </Table.Body>
          </Table.Root>
        {/if}
      </Card.Content>
    </Card.Root>
  </div>
</div>

<!-- New Audience Dialog -->
<Dialog.Root open={isAudienceDialogOpen} onOpenChange={(open) => (isAudienceDialogOpen = open)}>
  <Dialog.Content>
    <Dialog.Header>
      <Dialog.Title>Create New Audience</Dialog.Title>
      <Dialog.Description>Create a new audience for your email campaigns</Dialog.Description>
    </Dialog.Header>

    <div class="space-y-4 py-4">
      <div class="space-y-2">
        <label for="audienceName" class="text-sm font-medium">Audience Name</label>
        <Input.Root
          id="audienceName"
          placeholder="Enter audience name"
          bind:value={newAudienceName} />
      </div>
    </div>

    <Dialog.Footer>
      <Button.Root
        variant="outline"
        onclick={() => (isAudienceDialogOpen = false)}
        disabled={isCreatingAudience}>
        Cancel
      </Button.Root>
      <Button.Root onclick={createAudience} disabled={isCreatingAudience || !newAudienceName}>
        {#if isCreatingAudience}
          <div
            class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent">
          </div>
        {/if}
        Create Audience
      </Button.Root>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<!-- Contact Dialog -->
<Dialog.Root open={isContactDialogOpen} onOpenChange={(open) => (isContactDialogOpen = open)}>
  <Dialog.Content>
    <Dialog.Header>
      <Dialog.Title>{editingContact ? 'Edit Contact' : 'Add Contact'}</Dialog.Title>
      <Dialog.Description>
        {editingContact ? 'Edit contact information' : 'Add a new contact to the audience'}
      </Dialog.Description>
    </Dialog.Header>

    <div class="space-y-4 py-4">
      <div class="space-y-2">
        <label for="contactEmail" class="text-sm font-medium">Email *</label>
        <Input.Root
          id="contactEmail"
          type="email"
          placeholder="Enter email address"
          bind:value={newContactEmail} />
      </div>

      <div class="space-y-2">
        <label for="contactFirstName" class="text-sm font-medium">First Name</label>
        <Input.Root
          id="contactFirstName"
          placeholder="Enter first name"
          bind:value={newContactFirstName} />
      </div>

      <div class="space-y-2">
        <label for="contactLastName" class="text-sm font-medium">Last Name</label>
        <Input.Root
          id="contactLastName"
          placeholder="Enter last name"
          bind:value={newContactLastName} />
      </div>
    </div>

    <Dialog.Footer>
      <Button.Root
        variant="outline"
        onclick={() => (isContactDialogOpen = false)}
        disabled={isCreatingContact}>
        Cancel
      </Button.Root>
      <Button.Root
        onclick={editingContact ? updateContact : createContact}
        disabled={isCreatingContact || !newContactEmail}>
        {#if isCreatingContact}
          <div
            class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent">
          </div>
        {/if}
        {editingContact ? 'Update Contact' : 'Add Contact'}
      </Button.Root>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<!-- Import Dialog -->
<Dialog.Root open={isImportDialogOpen} onOpenChange={(open) => (isImportDialogOpen = open)}>
  <Dialog.Content>
    <Dialog.Header>
      <Dialog.Title>Import Contacts</Dialog.Title>
      <Dialog.Description>Import contacts from a CSV file</Dialog.Description>
    </Dialog.Header>

    <div class="space-y-4 py-4">
      <div class="space-y-2">
        <label for="importFile" class="text-sm font-medium">CSV File</label>
        <Input.Root id="importFile" type="file" accept=".csv" on:change={handleFileSelect} />
        <p class="text-muted-foreground mt-1 text-xs">
          CSV file should have columns: email, first_name, last_name
        </p>
      </div>
    </div>

    <Dialog.Footer>
      <Button.Root
        variant="outline"
        onclick={() => (isImportDialogOpen = false)}
        disabled={isImporting}>
        Cancel
      </Button.Root>
      <Button.Root onclick={importContacts} disabled={isImporting || !importFile}>
        {#if isImporting}
          <div
            class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent">
          </div>
        {/if}
        Import Contacts
      </Button.Root>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
