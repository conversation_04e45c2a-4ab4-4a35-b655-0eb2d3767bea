import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { prisma } from '$lib/server/prisma';
import { OpenAI } from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

export const GET: RequestHandler = async ({ params, locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = params;

  try {
    // Get the interview coaching session
    const session = await prisma.interviewCoachingSession.findUnique({
      where: {
        id,
        userId: locals.user.id
      }
    });

    if (!session) {
      return json({ error: 'Session not found' }, { status: 404 });
    }

    return json({ session });
  } catch (error) {
    console.error('Error fetching interview coaching session:', error);
    return json({ error: 'Failed to fetch interview coaching session' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ params, request, locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = params;

  try {
    const { questionIndex, response } = await request.json();

    if (questionIndex === undefined || !response) {
      return json({ error: 'Question index and response are required' }, { status: 400 });
    }

    // Get the interview coaching session
    const session = await prisma.interviewCoachingSession.findUnique({
      where: {
        id,
        userId: locals.user.id
      }
    });

    if (!session) {
      return json({ error: 'Session not found' }, { status: 404 });
    }

    // Check if the question exists
    if (!session.questions[questionIndex]) {
      return json({ error: 'Question not found' }, { status: 404 });
    }

    // Generate feedback for the response
    const feedback = await generateFeedback(
      session.questions[questionIndex].question,
      response,
      session.jobTitle
    );

    // Update the session with the response and feedback
    const updatedSession = await prisma.interviewCoachingSession.update({
      where: {
        id
      },
      data: {
        responses: [
          ...(session.responses || []),
          {
            questionIndex,
            response,
            timestamp: new Date().toISOString()
          }
        ],
        feedback: [
          ...(session.feedback || []),
          {
            questionIndex,
            feedback,
            timestamp: new Date().toISOString()
          }
        ],
        status: 'in_progress',
        updatedAt: new Date()
      }
    });

    return json({
      success: true,
      feedback,
      session: updatedSession
    });
  } catch (error) {
    console.error('Error submitting interview response:', error);
    return json({ error: 'Failed to submit interview response' }, { status: 500 });
  }
};

export const PATCH: RequestHandler = async ({ params, request, locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = params;

  try {
    const { status } = await request.json();

    if (!status) {
      return json({ error: 'Status is required' }, { status: 400 });
    }

    // Update the session status
    const updatedSession = await prisma.interviewCoachingSession.update({
      where: {
        id,
        userId: locals.user.id
      },
      data: {
        status,
        updatedAt: new Date()
      }
    });

    return json({
      success: true,
      session: updatedSession
    });
  } catch (error) {
    console.error('Error updating interview session:', error);
    return json({ error: 'Failed to update interview session' }, { status: 500 });
  }
};

// Helper function to generate feedback for a response
async function generateFeedback(question: string, response: string, jobTitle: string): Promise<string> {
  try {
    const prompt = `
    Question: "${question}"
    
    Candidate's Response: "${response}"
    
    Please provide constructive feedback on this interview response for a ${jobTitle} position. 
    Evaluate the response based on:
    1. Content and relevance
    2. Structure and clarity
    3. Specific examples and details
    4. Areas for improvement
    
    Keep the feedback concise, constructive, and actionable.
    `;

    const aiResponse = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: 'You are an expert interview coach providing feedback on interview responses.' },
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 1000
    });

    return aiResponse.choices[0]?.message?.content || 'No feedback available.';
  } catch (error) {
    console.error('Error generating feedback:', error);
    return 'Unable to generate feedback at this time. Please try again later.';
  }
}
