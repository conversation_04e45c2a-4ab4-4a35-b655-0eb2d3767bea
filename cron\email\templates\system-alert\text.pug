- var severityIcon = (severity === 'critical') ? '🔴' : (severity === 'warning') ? '🟠' : (severity === 'info') ? '🔵' : '⚪'
- var baseUrl = appUrl || 'https://hirli.co'
- var systemStatusUrl = baseUrl + '/system-status'

| #{severityIcon} #{alertTitle || 'System Alert'}
|
| #{alertMessage || 'An important system alert has been triggered.'}
|
| ALERT DETAILS:
| Timestamp: #{timestamp || new Date().toISOString()}
| Severity: #{severity || 'warning'} #{severityIcon}
| Component: #{component || 'System'}
if errorCode
  | Error Code: #{errorCode}
if affectedServices && affectedServices.length
  | Affected Services: #{affectedServices.join(', ')}
if jobType
  | Job Type: #{jobType}
if exitCode
  | Exit Code: #{exitCode}
|
if alertDetails
  | TECHNICAL DETAILS:
  | #{alertDetails}
  |
else if details && Object.keys(details).length > 0
  | TECHNICAL DETAILS:
  each value, key in details
    | #{key}: #{value}
  |
|
if systemInfo
  | SYSTEM INFORMATION:
  if systemInfo.memoryUsage
    | Memory Usage: #{systemInfo.memoryUsage}
  if systemInfo.cpuUsage
    | CPU Usage: #{systemInfo.cpuUsage}
  if systemInfo.nodeVersion
    | Node Version: #{systemInfo.nodeVersion}
  |
|
if recommendedActions && recommendedActions.length
  | RECOMMENDED ACTIONS:
  each action in recommendedActions
    | ▶ #{action}
  |
else if actionRequired
  | RECOMMENDED ACTIONS:
  | ▶ #{actionRequired}
  |
|
| QUICK LINKS:
| View System Status Dashboard: #{systemStatusUrl}
| View Incident History: #{systemStatusUrl}/history
if severity === 'critical' || severity === 'warning'
  | Manage Maintenance Events: #{baseUrl}/admin/maintenance
|

if error || errorMessage || errorStack
  | ERROR DETAILS:
  if error || errorMessage
    | #{error || errorMessage}
  if errorStack
    | #{errorStack}
  |
if severity === 'critical'
  | ⚠️ THIS IS A CRITICAL ALERT THAT REQUIRES IMMEDIATE ATTENTION. ⚠️
  |
|
| This is an automated alert. Please take appropriate action.
|
| Regards,
| The #{appName || 'Hirli'} Team
|
| © #{year || new Date().getFullYear()} #{appName || 'Hirli'}. All rights reserved.
