<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';

  import { X, Save, Plus } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import type { SkillsSchema } from '$lib/validators/profile';

  // Props
  const {
    open,
    skills,
    onClose,
    onSave,
    disabled = false,
  } = $props<{
    open: boolean;
    skills: string[];
    onClose: () => void;
    onSave: (skills: string[]) => Promise<boolean>;
    disabled?: boolean;
  }>();

  // Local state for editing
  let editedSkills = $state<string[]>([]);
  let newSkill = $state('');
  let submitting = $state(false);
  let apiSkills = $state<{ name: string }[]>([]);
  let filteredSkills = $state<{ name: string }[]>([]);
  let searchQuery = $state('');

  // Initialize local state when modal opens
  $effect(() => {
    if (open) {
      editedSkills = [...skills];
      newSkill = '';
      searchQuery = '';
      fetchSkills();
    }
  });

  // Fetch skills from API
  async function fetchSkills() {
    try {
      const response = await fetch('/api/skills');
      if (response.ok) {
        const data = await response.json();
        apiSkills = data;
        updateFilteredSkills();
      }
    } catch (error) {
      console.error('Error fetching skills:', error);
    }
  }

  // Update filtered skills based on search query
  function updateFilteredSkills() {
    if (!searchQuery) {
      filteredSkills = apiSkills.slice(0, 20); // Show first 20 skills by default
    } else {
      filteredSkills = apiSkills
        .filter((skill) => skill.name.toLowerCase().includes(searchQuery.toLowerCase()))
        .slice(0, 20);
    }
  }

  // Handle search input
  $effect(() => {
    updateFilteredSkills();
  });

  // Add skill
  function addSkill() {
    if (newSkill && !editedSkills.includes(newSkill)) {
      editedSkills = [...editedSkills, newSkill];
      newSkill = '';
    }
  }

  // Add skill from suggestions
  function addSuggestedSkill(skill: string) {
    if (!editedSkills.includes(skill)) {
      editedSkills = [...editedSkills, skill];
    }
  }

  // Remove skill
  function removeSkill(skill: string) {
    editedSkills = editedSkills.filter((s) => s !== skill);
  }

  // Handle save
  async function handleSave() {
    submitting = true;
    try {
      const success = await onSave(editedSkills);
      if (success) {
        toast.success('Skills updated successfully');
        onClose();
      }
    } catch (error) {
      console.error('Error saving skills:', error);
      toast.error('Failed to save skills');
    } finally {
      submitting = false;
    }
  }
</script>

<Dialog.Root {open} onOpenChange={onClose}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="sm:max-w-[500px]">
      <Dialog.Header>
        <Dialog.Title>Edit Skills</Dialog.Title>
        <Dialog.Description>Add or remove skills from your profile.</Dialog.Description>
      </Dialog.Header>

      <div class="py-4">
        <div class="space-y-4">
          <div class="space-y-2">
            <Label>Add Skills</Label>
            <div class="flex items-center space-x-2">
              <Input
                bind:value={newSkill}
                placeholder="Enter a skill"
                {disabled}
                onkeydown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addSkill();
                  }
                }} />
              <Button type="button" variant="outline" onclick={addSkill} {disabled}>
                <Plus class="mr-2 h-4 w-4" />
                Add
              </Button>
            </div>
          </div>

          {#if editedSkills.length > 0}
            <div class="space-y-2">
              <Label>Your Skills</Label>
              <div class="flex flex-wrap gap-2">
                {#each editedSkills as skill}
                  <Badge variant="secondary" class="flex items-center gap-1">
                    {skill}
                    <button
                      type="button"
                      class="hover:bg-primary/20 ml-1 rounded-full p-0.5"
                      onclick={() => removeSkill(skill)}>
                      <X class="h-3 w-3" />
                    </button>
                  </Badge>
                {/each}
              </div>
            </div>
          {:else}
            <p class="text-muted-foreground text-sm">No skills added yet</p>
          {/if}

          <div class="space-y-2">
            <Label>Suggested Skills</Label>
            <Input bind:value={searchQuery} placeholder="Search for skills" {disabled} />
            <div class="flex max-h-40 flex-wrap gap-2 overflow-y-auto">
              {#each filteredSkills as skill}
                <Button
                  type="button"
                  variant={editedSkills.includes(skill.name) ? 'default' : 'outline'}
                  size="sm"
                  onclick={() => addSuggestedSkill(skill.name)}
                  {disabled}>
                  {skill.name}
                </Button>
              {/each}
            </div>
          </div>
        </div>
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={onClose} {disabled}>Cancel</Button>
        <Button onclick={handleSave} disabled={submitting || disabled} class="ml-2">
          <Save class="mr-2 h-4 w-4" />
          {submitting ? 'Saving...' : 'Save Changes'}
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
