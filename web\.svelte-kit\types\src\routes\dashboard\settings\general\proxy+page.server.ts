// @ts-nocheck
import { redirect, fail } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { getUserFromToken } from '$lib/server/auth.js';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import type { PageServerLoad, Actions } from './$types.js';

// Define the schema with Zod for validation
const generalSettingsSchema = z.object({
  siteName: z.string().min(1, 'Site name is required'),
  siteDescription: z.string().optional(),
  contactEmail: z.string().email('Invalid email address'),
  timezone: z.string(),
  dateFormat: z.string(),
  timeFormat: z.string(),
  language: z.string(),
});

export const load = async ({ cookies, locals }: Parameters<PageServerLoad>[0]) => {
  const tokenData = getUserFromToken(cookies);

  if (!tokenData || !tokenData.email) {
    throw redirect(302, '/auth/sign-in');
  }

  // Get user data
  const userData = await prisma.user.findUnique({
    where: { email: tokenData.email },
  });

  if (!userData) {
    throw redirect(302, '/auth/sign-in');
  }

  locals.user = userData;

  // Get user preferences or set defaults
  const preferences = userData.preferences ? userData.preferences : {};

  // Create the form with initial values
  const form = await superValidate(
    {
      siteName: preferences.siteName || 'Hirli',
      siteDescription: preferences.siteDescription || 'Your job application automation assistant',
      contactEmail: preferences.contactEmail || userData.email,
      timezone: preferences.timezone || 'UTC',
      dateFormat: preferences.dateFormat || 'MM/DD/YYYY',
      timeFormat: preferences.timeFormat || '12h',
      language: preferences.language || 'en',
    },
    zod(generalSettingsSchema)
  );

  return {
    user: userData,
    form,
  };
};

export const actions = {
  default: async ({ request, cookies, locals }: import('./$types').RequestEvent) => {
    const tokenData = getUserFromToken(cookies);

    if (!tokenData || !tokenData.email) {
      throw redirect(302, '/auth/sign-in');
    }

    // Get user data
    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email },
    });

    if (!userData) {
      throw redirect(302, '/auth/sign-in');
    }

    const form = await superValidate(request, zod(generalSettingsSchema));

    if (!form.valid) {
      return fail(400, { form });
    }

    try {
      // Update user preferences
      await prisma.user.update({
        where: { id: userData.id },
        data: {
          preferences: {
            ...userData.preferences,
            siteName: form.data.siteName,
            siteDescription: form.data.siteDescription,
            contactEmail: form.data.contactEmail,
            timezone: form.data.timezone,
            dateFormat: form.data.dateFormat,
            timeFormat: form.data.timeFormat,
            language: form.data.language,
          },
        },
      });

      return { form, success: true };
    } catch (error) {
      console.error('Error updating general settings:', error);
      return fail(500, { form, error: 'Failed to update general settings' });
    }
  },
};
;null as any as Actions;