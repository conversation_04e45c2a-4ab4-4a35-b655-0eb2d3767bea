<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog';
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import * as Select from '$lib/components/ui/select';
  import { Switch } from '$lib/components/ui/switch';
  import { toast } from 'svelte-sonner';
  import { createForm } from 'svelte-forms-lib';
  import * as yup from 'yup';
  import { BellIcon, InfoIcon, LoaderIcon } from 'lucide-svelte';

  export let onClose: () => void;
  export let onCreated: () => void;
  export const userId: string = '';

  // Form validation schema
  const schema = yup.object().shape({
    name: yup.string().required('Alert name is required'),
    keywords: yup.string(),
    location: yup.string(),
    jobType: yup.string(),
    remote: yup.boolean(),
    frequency: yup.string().required('Frequency is required'),
    enabled: yup.boolean(),
  });

  // Initialize form
  const { form, errors, handleSubmit, isSubmitting } = createForm({
    initialValues: {
      name: '',
      keywords: '',
      location: '',
      jobType: '',
      remote: false,
      frequency: 'daily',
      enabled: true,
    },
    validationSchema: schema,
    onSubmit: async (values) => {
      try {
        // Create search params object
        const searchParams = {
          keywords: values.keywords || undefined,
          location: values.location || undefined,
          jobType: values.jobType || undefined,
          remote: values.remote || undefined,
        };

        // Remove undefined values
        Object.keys(searchParams).forEach((key) => {
          if (searchParams[key] === undefined) {
            delete searchParams[key];
          }
        });

        // Create alert data
        const alertData = {
          name: values.name,
          searchParams,
          frequency: values.frequency,
          enabled: values.enabled,
        };

        // Call API to create alert
        const response = await fetch('/api/job-alerts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(alertData),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to create job alert');
        }

        toast.success('Job alert created successfully');
        onCreated();
      } catch (error) {
        console.error('Error creating job alert:', error);
        toast.error('Failed to create job alert');
      }
    },
  });

  // Job type options
  const jobTypeOptions = [
    { value: '', label: 'Any' },
    { value: 'full_time', label: 'Full-time' },
    { value: 'part_time', label: 'Part-time' },
    { value: 'contract', label: 'Contract' },
    { value: 'temporary', label: 'Temporary' },
    { value: 'internship', label: 'Internship' },
  ];

  // Frequency options
  const frequencyOptions = [
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
  ];
</script>

<Dialog.Root open={true}>
  <Dialog.Overlay />
  <Dialog.Content class="sm:max-w-[500px]">
    <Dialog.Header>
      <Dialog.Title class="mb-3">Create Job Alert</Dialog.Title>
      <Dialog.Description>
        Set up a new job alert to get notified when new jobs matching your criteria are available.
        You'll receive email notifications based on your selected frequency.
      </Dialog.Description>
    </Dialog.Header>

    <div class="bg-primary/80 text-background/80 mb-2 rounded-lg p-3 text-sm">
      <div class="flex items-start">
        <InfoIcon class="mr-2 mt-0.5 h-4 w-4" />
        <div>
          <p class="font-medium">Tips for effective job alerts:</p>
          <ul class="ml-5 mt-1 list-disc">
            <li>Use specific keywords related to your skills</li>
            <li>Include location preferences or select "Remote"</li>
            <li>Choose a frequency that works best for your job search</li>
          </ul>
        </div>
      </div>
    </div>

    <form on:submit|preventDefault={handleSubmit}>
      <div class="grid gap-4 py-4">
        <div class="grid grid-cols-3 items-center gap-4">
          <Label for="name" class="text-left">Alert Name</Label>
          <Input
            id="name"
            placeholder="e.g., Software Developer Jobs"
            bind:value={$form.name}
            class="col-span-2 {$errors.name ? 'border-red-500' : ''}" />
          {#if $errors.name}
            <p class="mt-1 text-xs text-red-500">{$errors.name}</p>
          {/if}
        </div>

        <div class="grid grid-cols-3 items-center gap-4">
          <Label for="keywords" class="text-left">Keywords</Label>
          <Input
            class="col-span-2"
            id="keywords"
            placeholder="e.g., javascript, react"
            bind:value={$form.keywords} />
        </div>

        <div class="grid grid-cols-3 items-center gap-4">
          <Label for="location" class="text-left">Location</Label>
          <Input
            class="col-span-2"
            id="location"
            placeholder="e.g., New York, Remote"
            bind:value={$form.location} />
        </div>

        <div class="grid grid-cols-3 items-center gap-4">
          <Label for="jobType" class="text-left">Job Type</Label>
          <Select.Root
            type="single"
            name="jobType"
            value={$form.jobType}
            onValueChange={(value) => {
              $form.jobType = value;
            }}>
            <Select.Trigger class="col-span-2 w-full px-3 py-2">
              <Select.Value
                placeholder={jobTypeOptions.find((o) => o.value === $form.jobType)?.label ||
                  'Select job type'} />
            </Select.Trigger>
            <Select.Content class="max-h-60">
              {#each jobTypeOptions as option}
                <Select.Item value={option.value}>{option.label}</Select.Item>
              {/each}
            </Select.Content>
          </Select.Root>
        </div>

        <div class="grid grid-cols-3 items-center gap-4">
          <Label for="remote" class="text-left">Remote Only</Label>
          <div class="items-right align-right col-span-2 flex items-center space-x-2">
            <Switch
              id="remote"
              name="remote"
              checked={$form.remote}
              onCheckedChange={(checked) => {
                $form.remote = checked;
              }} />
            <span class="text-muted-foreground text-sm">Only show remote jobs</span>
          </div>
        </div>

        <div class="grid grid-cols-3 items-center gap-4">
          <Label for="frequency" class="text-left">Frequency</Label>
          <div class="col-span-2">
            <Select.Root
              type="single"
              name="frequency"
              value={$form.frequency}
              onValueChange={(value: string) => {
                $form.frequency = value;
              }}>
              <Select.Trigger class="w-full px-3 py-2">
                <Select.Value
                  placeholder={frequencyOptions.find((o) => o.value === $form.frequency)?.label ||
                    'Select frequency'} />
              </Select.Trigger>
              <Select.Content class="max-h-60">
                {#each frequencyOptions as option}
                  <Select.Item value={option.value}>{option.label}</Select.Item>
                {/each}
              </Select.Content>
            </Select.Root>
            {#if $errors.frequency}
              <p class="mt-1 text-xs text-red-500">{$errors.frequency}</p>
            {/if}
          </div>
        </div>

        <div class="grid grid-cols-3 items-center gap-4">
          <Label for="enabled" class="text-left">Enabled</Label>
          <div class="col-span-2 flex items-center space-x-2">
            <Switch
              id="enabled"
              name="enabled"
              checked={$form.enabled}
              onCheckedChange={(checked) => {
                $form.enabled = checked;
              }} />
            <span class="text-muted-foreground text-sm">Receive emails with new job matches</span>
          </div>
        </div>
      </div>

      <Dialog.Footer class="mt-2 sm:justify-between">
        <Button type="button" variant="outline" onclick={onClose}>Cancel</Button>
        <Button
          type="submit"
          disabled={$isSubmitting ||
            !$form.name.trim() ||
            !$form.frequency.trim() ||
            !$form.keywords.trim() ||
            !$form.location.trim() ||
            !$form.jobType.trim()}
          class="flex items-center gap-2">
          {#if $isSubmitting}
            <LoaderIcon class="h-4 w-4 animate-spin" />
            <span>Creating...</span>
          {:else}
            <BellIcon class="h-4 w-4" />
            <span>Create Alert</span>
          {/if}
        </Button>
      </Dialog.Footer>
    </form>
  </Dialog.Content>
</Dialog.Root>
