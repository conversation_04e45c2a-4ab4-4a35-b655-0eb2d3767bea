// cron/scripts/sendDailySummary.ts

import { PrismaClient } from "@prisma/client";
import { logger, globalJobStats } from "../utils/logger";
import {
  sendEmailNotification,
  EmailNotificationType,
} from "../utils/emailService";
import dotenv from "dotenv";
import { formatDuration } from "utils/formatters";
import { CircuitState } from "../utils/improvedImprovedCircuitBreaker";

// Load environment variables
dotenv.config();

// Initialize Prisma client
const prisma = new PrismaClient();

/**
 * Send a daily job summary email with proper formatting
 */
async function sendDailySummary() {
  const startTime = new Date();
  logger.info(`📊 Generating daily job summary at ${startTime.toISOString()}`);

  try {
    // Get the formatted stats from the global tracker
    const jobStats = globalJobStats.getFormattedStats();

    // Format the date for the report
    const reportDate = new Date().toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    // Log the current stats
    logger.info(`📊 Daily summary stats: ${JSON.stringify(jobStats)}`);

    // Only send email if we have actual data
    if (jobStats.totalProcessed > 0) {
      // Send the email notification
      await sendEmailNotification(EmailNotificationType.JOB_SUMMARY, {
        reportTitle: "Daily Job Processing Summary",
        reportDate,
        jobsProcessed: jobStats.totalProcessed,
        jobsSucceeded: jobStats.totalSucceeded,
        jobsFailed: jobStats.totalFailed,
        processingTime: formatDuration(jobStats.totalDuration),
        jobTypes: jobStats.jobTypes,
        failureReasons: jobStats.failureReasons,
        status: "Completed",
        startTime: new Date(globalJobStats.dailyStats.lastReset).toISOString(),
        endTime: new Date().toISOString(),
      });

      logger.info(
        `✅ Daily job summary email sent successfully with actual data`
      );
    } else {
      logger.warn(`⚠️ No job data found for the period.`);

      // Send a notification about no data to the developer audience
      await sendEmailNotification(EmailNotificationType.JOB_SUMMARY, {
        reportTitle: "Daily Job Summary - No Data",
        reportDate,
        jobsProcessed: 0,
        jobsSucceeded: 0,
        jobsFailed: 0,
        processingTime: "0 minutes",
        status: "Completed",
        message:
          "No job data was found for the daily summary period. This is just an informational message.",
        startTime: new Date(globalJobStats.dailyStats.lastReset).toISOString(),
        endTime: new Date().toISOString(),
      });

      logger.info(`📊 Sent empty job data notification to developer audience`);
    }
  } catch (error) {
    logger.error(`❌ Error sending daily job summary:`, error);
  }
}

/**
 * Get job statistics for the specified date range
 */
async function getJobStatistics(startDate: Date, endDate: Date) {
  // Initialize statistics
  const stats = {
    totalProcessed: 0,
    totalSucceeded: 0,
    totalFailed: 0,
    totalDuration: 0,
    jobTypes: [] as {
      type: string;
      count: number;
      success: number;
      failure: number;
    }[],
    failureReasons: [] as { reason: string; count: number }[],
  };

  try {
    // Get job listings created or updated in the date range
    const jobListings = await prisma.jobListing.findMany({
      where: {
        OR: [
          { createdAt: { gte: startDate, lte: endDate } },
          { lastCheckedAt: { gte: startDate, lte: endDate } },
        ],
        isProcessing: false, // Only get jobs that aren't currently being processed
      },
    });

    // Count total jobs processed
    stats.totalProcessed = jobListings.length;

    // Count successful jobs (those with descriptions)
    stats.totalSucceeded = jobListings.filter(
      (job) => job.description && job.description.length > 0
    ).length;

    // Count failed jobs
    stats.totalFailed = jobListings.filter(
      (job) => !job.description || job.description.length === 0
    ).length;

    // Group by job type (platform)
    const jobTypeMap = new Map<
      string,
      { count: number; success: number; failure: number }
    >();

    // Group by failure reason
    const failureReasonMap = new Map<string, number>();

    // Process each job
    for (const job of jobListings) {
      // Track by platform/source
      const jobType = job.platform || "Unknown";
      if (!jobTypeMap.has(jobType)) {
        jobTypeMap.set(jobType, { count: 0, success: 0, failure: 0 });
      }
      const typeStats = jobTypeMap.get(jobType)!;
      typeStats.count++;

      // Check if job was successful
      if (job.description && job.description.length > 0) {
        typeStats.success++;
      } else {
        typeStats.failure++;

        // Track failure reason if available
        let failureReason = "Unknown";

        // Check for common failure patterns in the description or error fields
        if (job.description) {
          if (job.description.startsWith("Failed to scrape")) {
            // Extract the specific error message
            const errorParts = job.description.split(": ");
            if (errorParts.length > 1) {
              failureReason = errorParts[1];

              // Further categorize common errors
              if (failureReason.includes("timeout")) {
                failureReason = "Timeout Error";
              } else if (
                failureReason.includes("ECONNREFUSED") ||
                failureReason.includes("ECONNRESET")
              ) {
                failureReason = "Connection Error";
              } else if (failureReason.includes("403")) {
                failureReason = "Access Denied (403)";
              } else if (failureReason.includes("404")) {
                failureReason = "Page Not Found (404)";
              } else if (
                failureReason.includes("captcha") ||
                failureReason.includes("CAPTCHA")
              ) {
                failureReason = "CAPTCHA Challenge";
              } else if (
                failureReason.includes("login") ||
                failureReason.includes("sign in")
              ) {
                failureReason = "Login Wall";
              } else if (failureReason.includes("cloudflare")) {
                failureReason = "Cloudflare Protection";
              }
            }
          } else if (
            job.description.includes("expired") ||
            job.description.includes("no longer available")
          ) {
            failureReason = "Job Expired";
          } else if (job.description.length === 0) {
            failureReason = "Empty Description";
          }
        }

        // Check job status if available
        if ("status" in job) {
          if (job.status === "EXPIRED") {
            failureReason = "Job Expired";
          } else if (job.status === "DELETED") {
            failureReason = "Job Deleted";
          } else if (job.status === "ERROR") {
            failureReason =
              "errorMessage" in job && typeof job.errorMessage === "string"
                ? job.errorMessage
                : "Processing Error";
          }
        }

        // Check error message if available
        if ("errorMessage" in job) {
          if (
            typeof job.errorMessage === "string" &&
            job.errorMessage.includes("timeout")
          ) {
            failureReason = "Timeout Error";
          } else if (
            (typeof job.errorMessage === "string" &&
              job.errorMessage.includes("ECONNREFUSED")) ||
            (typeof job.errorMessage === "string" &&
              job.errorMessage.includes("ECONNRESET"))
          ) {
            failureReason = "Connection Error";
          } else if (
            typeof job.errorMessage === "string" &&
            job.errorMessage.includes("403")
          ) {
            failureReason = "Access Denied (403)";
          } else if (
            typeof job.errorMessage === "string" &&
            job.errorMessage.includes("404")
          ) {
            failureReason = "Page Not Found (404)";
          } else if (
            (typeof job.errorMessage === "string" &&
              job.errorMessage.includes("captcha")) ||
            (typeof job.errorMessage === "string" &&
              job.errorMessage.includes("CAPTCHA"))
          ) {
            failureReason = "CAPTCHA Challenge";
          } else if (
            (typeof job.errorMessage === "string" &&
              job.errorMessage.includes("login")) ||
            (typeof job.errorMessage === "string" &&
              job.errorMessage.includes("sign in"))
          ) {
            failureReason = "Login Wall";
          } else if (
            typeof job.errorMessage === "string" &&
            job.errorMessage.includes("cloudflare")
          ) {
            failureReason = "Cloudflare Protection";
          } else {
            failureReason =
              typeof job.errorMessage === "string"
                ? job.errorMessage.substring(0, 50) +
                  (job.errorMessage.length > 50 ? "..." : "")
                : "Unknown Error";
          }
        }

        // Track the failure reason
        if (!failureReasonMap.has(failureReason)) {
          failureReasonMap.set(failureReason, 0);
        }
        failureReasonMap.set(
          failureReason,
          failureReasonMap.get(failureReason)! + 1
        );
      }
    }

    // Convert maps to arrays for the email template
    stats.jobTypes = Array.from(jobTypeMap.entries()).map(([type, data]) => ({
      type,
      count: data.count,
      success: data.success,
      failure: data.failure,
    }));

    stats.failureReasons = Array.from(failureReasonMap.entries()).map(
      ([reason, count]) => ({
        reason,
        count,
      })
    );

    // Sort by count (descending)
    stats.jobTypes.sort((a, b) => b.count - a.count);
    stats.failureReasons.sort((a, b) => b.count - a.count);

    // Estimate total duration (assuming 5 seconds per job on average)
    stats.totalDuration = stats.totalProcessed * 5;

    return stats;
  } catch (error) {
    logger.error(`❌ Error getting job statistics:`, error);
    return stats;
  }
}

// Execute the function
sendDailySummary()
  .then(() => {
    logger.info("📊 Daily summary process completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Error in daily summary process:", error);
    process.exit(1);
  });
