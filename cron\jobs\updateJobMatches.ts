// cron/jobs/updateJobMatches.ts

import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";
import { scoreJobAgainstResume } from "../lib/resume/scoreJobAgainstResume";
import { saveJobSearchResults } from "../lib/saveJobSearchResults";
import { getPrismaClient, executeRawQuery } from "../utils/prismaClient";
import { redis } from "../utils/redis.js";
import {
  queueJobMatchNotification,
  queueJobMatchesNotification,
} from "../lib/notifications/jobMatchNotifier.js";
// Email notification is now handled by the scheduler
import dotenv from "dotenv";
import { CircuitState } from "../utils/improvedImprovedCircuitBreaker";

// Load environment variables
dotenv.config();

// Initialize Prisma client with error handling
let prisma: PrismaClient;

/**
 * Updates job matches for all profiles with parsed resumes
 * This job should be run periodically to keep job matches up to date
 */
export async function updateJobMatches() {
  const startTime = new Date();
  logger.info(`🔄 Starting job matches update at ${startTime.toISOString()}`);

  // Initialize counters for tracking progress
  let processedCount = 0;
  let failedCount = 0;
  let profiles: any[] = [];

  try {
    // Initialize the Prisma client
    prisma = await getPrismaClient("cron");

    // Get profiles with parsed resumes using raw SQL query
    logger.info("🔍 Fetching profiles with parsed resumes...");

    // Use the web schema for this query
    const profiles = await executeRawQuery(
      `
      SELECT p.id as "profileId", p."userId",
             d.id as "documentId",
             r.id as "resumeId", r."rawText", r."parsedData"
      FROM "Profile" p
      JOIN "Document" d ON p.id = d."profileId"
      JOIN "Resume" r ON d.id = r."documentId"
      WHERE r."isParsed" = true AND r."rawText" IS NOT NULL
      LIMIT 100
    `,
      [],
      "web"
    );

    logger.info(`📊 Found ${profiles.length} profiles with parsed resumes`);

    // Process each profile
    for (const profile of profiles) {
      try {
        // Check if we have resume text
        if (!profile.rawText) {
          logger.warn(
            `⚠️ No parsed resume text found for profile ${profile.profileId}`
          );
          continue;
        }

        // Increment processed counter
        processedCount++;

        const resumeText = profile.rawText;

        // Get active job listings using Prisma's API
        // Note: JobListing model is in the cron schema
        const jobs = await prisma.jobListing.findMany({
          where: {
            isActive: true,
            description: { not: null },
          },
          select: {
            id: true,
            title: true,
            company: true,
            description: true,
          },
          take: 500,
        });

        logger.info(
          `🔍 Scoring ${jobs.length} jobs for profile ${profile.profileId}`
        );

        // Score each job against the resume
        const scoredJobs = jobs.map((job) => ({
          id: job.id,
          matchScore: scoreJobAgainstResume(resumeText, {
            title: job.title,
            company: job.company,
            description: job.description,
          }),
        }));

        // Sort by match score and take the top 50 with score >= 0.6
        const topJobs = scoredJobs
          .sort((a, b) => b.matchScore - a.matchScore)
          .filter((job) => job.matchScore >= 0.6)
          .slice(0, 50);

        // Save the job matches
        if (topJobs.length > 0) {
          await saveJobSearchResults({
            scoredJobs: topJobs,
            userId: profile.userId,
            profileId: profile.profileId,
          });

          logger.info(
            `✅ Saved ${topJobs.length} job matches for profile ${profile.profileId}`
          );

          // Check for job alerts and queue notifications
          await processJobAlerts(profile.userId, topJobs);
        } else {
          logger.info(
            `ℹ️ No matching jobs found for profile ${profile.profileId}`
          );
        }
      } catch (error) {
        logger.error(
          `❌ Error processing profile ${profile.profileId}:`,
          error
        );
        // Increment failed counter
        failedCount++;
      }
    }

    const endTime = new Date();
    const durationMs = endTime.getTime() - startTime.getTime();
    const durationSec = durationMs / 1000;

    // Log job statistics in standardized format for easier parsing
    logger.jobStats({
      jobType: "updateJobMatches",
      processed: profiles.length,
      succeeded: processedCount,
      failed: failedCount,
      duration: durationMs,
      details: {
        totalProfiles: profiles.length,
        profilesWithMatches: processedCount - failedCount,
        totalJobsScored: profiles.reduce(
          (total: number, profile: any, index: number) => {
            // Only count jobs for profiles that were processed successfully
            if (index < processedCount && !failedCount) {
              // Use jobs.length as a proxy for jobsScored since we don't track it directly
              const jobsScored = profile.jobsScored || 0;
              return total + jobsScored;
            }
            return total;
          },
          0
        ),
      },
    });

    logger.info(
      `✅ Job matches update completed successfully in ${durationSec.toFixed(2)} seconds`
    );

    // Email notification is now handled by the scheduler
    // Removed duplicate email notification from here
  } catch (error: any) {
    const endTime = new Date();
    const durationMs = endTime.getTime() - startTime.getTime();

    // Log error with standardized job statistics
    logger.error("❌ Error updating job matches:", error);

    // Log job statistics even for failed jobs
    logger.jobStats({
      jobType: "updateJobMatches",
      processed: profiles?.length || 0,
      succeeded: processedCount,
      failed: failedCount + 1, // Add 1 for the overall job failure
      duration: durationMs,
      details: {
        error: error.message || String(error),
        errorStack: error.stack,
        totalProfiles: profiles?.length || 0,
        profilesProcessed: processedCount,
        profilesFailed: failedCount,
      },
    });

    // Error notification is now handled by the scheduler
    // Removed duplicate error notification from here

    throw error;
  } finally {
    // Disconnect Prisma client
    if (prisma) {
      await prisma.$disconnect();
    }
  }
}

/**
 * Process job alerts for a user and queue notifications for matching jobs
 */
async function processJobAlerts(userId: string, matchedJobs: any[]) {
  try {
    // Get user's job alerts from web schema using a direct SQL query
    // This avoids the need for a separate Prisma client for the web schema
    const alertsResult = await executeRawQuery(
      `
      SELECT
        ja.id, ja.name, ja."userId", ja.enabled, ja.frequency, ja."lastSentAt", ja."searchParams",
        u.email, u."firstName"
      FROM "JobAlert" ja
      JOIN "User" u ON ja."userId" = u.id
      WHERE ja."userId" = $1 AND ja.enabled = true
      `,
      [userId],
      "web"
    );

    // Transform the raw SQL result to match the expected format
    const alerts = alertsResult.map((row: any) => ({
      id: row.id,
      name: row.name,
      userId: row.userId,
      enabled: row.enabled,
      frequency: row.frequency,
      lastSentAt: row.lastSentAt,
      searchParams: row.searchParams,
      user: {
        email: row.email,
        firstName: row.firstName,
      },
    }));

    if (alerts.length === 0) {
      return;
    }

    logger.info(`📬 Processing ${alerts.length} job alerts for user ${userId}`);

    // Get full job details for matched jobs
    const jobIds = matchedJobs.map((job) => job.id);
    const jobDetails = await prisma.jobListing.findMany({
      where: {
        id: { in: jobIds },
      },
    });

    // Create a map for quick lookup
    const jobDetailsMap = new Map();
    jobDetails.forEach((job) => {
      jobDetailsMap.set(job.id, job);
    });

    // Process each alert
    for (const alert of alerts) {
      // Skip alerts that were sent recently based on frequency
      if (alert.lastSentAt) {
        const lastSent = new Date(alert.lastSentAt);
        const now = new Date();

        // Check if enough time has passed based on frequency
        if (
          alert.frequency === "daily" &&
          now.getTime() - lastSent.getTime() < 24 * 60 * 60 * 1000
        ) {
          continue;
        }
        if (
          alert.frequency === "weekly" &&
          now.getTime() - lastSent.getTime() < 7 * 24 * 60 * 60 * 1000
        ) {
          continue;
        }
        if (
          alert.frequency === "monthly" &&
          now.getTime() - lastSent.getTime() < 30 * 24 * 60 * 60 * 1000
        ) {
          continue;
        }
      }

      // Filter jobs based on alert criteria
      const filteredJobs = matchedJobs.filter((job) => {
        const jobDetail = jobDetailsMap.get(job.id);
        if (!jobDetail) return false;

        const searchParams = alert.searchParams as any;

        // Check keywords
        if (
          searchParams.keywords &&
          typeof searchParams.keywords === "string"
        ) {
          const keywords = searchParams.keywords
            .toLowerCase()
            .split(",")
            .map((k: string) => k.trim());
          const jobText =
            `${jobDetail.title} ${jobDetail.company} ${jobDetail.description || ""}`.toLowerCase();

          if (!keywords.some((keyword: string) => jobText.includes(keyword))) {
            return false;
          }
        }

        // Check location
        if (
          searchParams.location &&
          typeof searchParams.location === "string"
        ) {
          const locations = searchParams.location
            .toLowerCase()
            .split(",")
            .map((l: string) => l.trim());
          const jobLocation = (jobDetail.location || "").toLowerCase();

          if (
            !locations.some((location: string) =>
              jobLocation.includes(location)
            )
          ) {
            return false;
          }
        }

        // Check job type
        if (
          searchParams.jobType &&
          jobDetail.jobType !== searchParams.jobType
        ) {
          return false;
        }

        // Check remote
        if (searchParams.remote === true && !jobDetail.isRemote) {
          return false;
        }

        return true;
      });

      // If we have matching jobs, queue notifications
      if (filteredJobs.length > 0) {
        logger.info(
          `📧 Queueing job match notification for alert "${alert.name}" with ${filteredJobs.length} matches`
        );

        // Get the top match
        const topMatch = filteredJobs[0];
        const topMatchDetails = jobDetailsMap.get(topMatch.id);

        // Prepare other matches
        const otherMatches = filteredJobs.slice(1, 4).map((job) => {
          const details = jobDetailsMap.get(job.id);
          return {
            title: details.title,
            company: details.company,
            location: details.location || "Location not specified",
            url: `${process.env.PUBLIC_BASE_URL || "https://hirli.co"}/dashboard/jobs/${job.id}`,
          };
        });

        // Queue email notification using Redis
        const emailJob = {
          type: "job_match_notification",
          alertId: alert.id,
          userId: userId,
          to: alert.user.email,
          firstName: alert.user.firstName,
          alertName: alert.name,
          jobData: {
            jobTitle: topMatchDetails.title,
            companyName: topMatchDetails.company,
            location: topMatchDetails.location || "Location not specified",
            matchScore: topMatch.matchScore,
            jobDescription: topMatchDetails.description,
            salary: topMatchDetails.salary,
            jobUrl: `${process.env.PUBLIC_BASE_URL || "https://hirli.co"}/dashboard/jobs/${topMatch.id}`,
            otherJobs: otherMatches,
          },
        };

        // Add to Redis queue for email notification
        await redis.xadd(
          "email:job-alerts",
          "*",
          "job",
          JSON.stringify(emailJob)
        );

        // Queue in-app notification for multiple job matches
        await queueJobMatchesNotification({
          userId,
          alertName: alert.name,
          matchCount: filteredJobs.length,
          alertId: alert.id,
        });

        // Also queue individual notification for the top match
        await queueJobMatchNotification({
          userId,
          jobId: topMatch.id,
          jobTitle: topMatchDetails.title,
          companyName: topMatchDetails.company,
          matchScore: topMatch.matchScore,
          alertName: alert.name,
        });

        // Update the lastSentAt timestamp using a direct SQL query
        await executeRawQuery(
          `
          UPDATE "JobAlert"
          SET "lastSentAt" = $1
          WHERE id = $2
          `,
          [new Date(), alert.id],
          "web"
        );
      }
    }
  } catch (error) {
    logger.error(`❌ Error processing job alerts for user ${userId}:`, error);
  }
}

// If this file is run directly, execute the job
if (import.meta.url === import.meta.resolve("./updateJobMatches.ts")) {
  updateJobMatches()
    .then(() => {
      logger.info("✅ Job matches update completed");
      process.exit(0);
    })
    .catch((error) => {
      logger.error("❌ Job matches update failed:", error);
      process.exit(1);
    });
}
