var a = {};
/**
 * @license React
 * react-jsx-dev-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var r = Symbol.for("react.fragment"), e = a.Fragment = r, t = a.jsxDEV = void 0;
export {
  e as Fragment,
  a as default,
  t as jsxDEV
};
