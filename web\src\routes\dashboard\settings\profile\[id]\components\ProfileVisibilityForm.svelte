<script lang="ts">
  import type { ProfileVisibilitySchema } from '$lib/validators/profile';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import * as Switch from '$lib/components/ui/switch/index.js';
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Edit, Save } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';

  // Props
  const { data, onSave } = $props<{
    data: ProfileVisibilitySchema;
    onSave: (data: ProfileVisibilitySchema) => Promise<boolean>;
  }>();

  // Create a local copy of the data for editing
  let formData = $state({
    showToRecruiters: data.showToRecruiters || false,
    getDiscovered: data.getDiscovered || true,
    hideFromCurrentEmployer: data.hideFromCurrentEmployer || false,
  });

  // Form submission state
  let submitting = $state(false);

  // Modal state
  let modalOpen = $state(false);

  // Open edit modal
  function openEditModal() {
    // Reset form data when opening modal
    formData = {
      showToRecruiters: data.showToRecruiters || false,
      getDiscovered: data.getDiscovered || true,
      hideFromCurrentEmployer: data.hideFromCurrentEmployer || false,
    };
    modalOpen = true;
  }

  // Handle form submission
  async function handleSubmit(event: Event) {
    event.preventDefault();

    // Submit the form
    submitting = true;
    try {
      const success = await onSave(formData);
      if (success) {
        toast.success('Profile visibility updated successfully');
        modalOpen = false;
      }
    } catch (error) {
      console.error('Error saving profile visibility:', error);
      toast.error('Failed to save profile visibility');
    } finally {
      submitting = false;
    }
  }
</script>

<div class="rounded-lg border p-6">
  <div class="flex items-center justify-between">
    <h2 class="text-xl font-semibold">Profile Visibility</h2>
    <Button variant="ghost" size="sm" onclick={openEditModal}>
      <Edit class="mr-2 h-4 w-4" />
      Edit
    </Button>
  </div>

  <p class="text-muted-foreground mt-2 text-sm">
    Control who can see your profile and job search status
  </p>

  <div class="mt-4 space-y-4">
    <div class="flex items-center justify-between space-x-2">
      <div>
        <p class="font-medium">Recruiter Visibility</p>
        <p class="text-muted-foreground text-sm">
          {data.showToRecruiters ? 'Visible to recruiters' : 'Hidden from recruiters'}
        </p>
      </div>
      <Switch.Root checked={data.showToRecruiters} disabled />
    </div>

    <div class="flex items-center justify-between space-x-2">
      <div>
        <p class="font-medium">Discovery Status</p>
        <p class="text-muted-foreground text-sm">
          {data.getDiscovered ? 'Companies can find you' : 'Not discoverable by companies'}
        </p>
      </div>
      <Switch.Root checked={data.getDiscovered} disabled />
    </div>

    <div class="flex items-center justify-between space-x-2">
      <div>
        <p class="font-medium">Current Employer</p>
        <p class="text-muted-foreground text-sm">
          {data.hideFromCurrentEmployer
            ? 'Hidden from current employer'
            : 'Visible to current employer'}
        </p>
      </div>
      <Switch.Root checked={data.hideFromCurrentEmployer} disabled />
    </div>
  </div>
</div>

<!-- Edit Modal -->
<Dialog.Root bind:open={modalOpen}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="sm:max-w-[500px]">
      <Dialog.Header>
        <Dialog.Title>Edit Profile Visibility</Dialog.Title>
        <Dialog.Description>
          Control who can see your profile and job search status
        </Dialog.Description>
      </Dialog.Header>

      <form onsubmit={handleSubmit} class="space-y-4 py-4">
        <div class="flex items-center justify-between space-x-2">
          <div>
            <Label for="showToRecruiters" class="font-medium">Show Profile to Recruiters</Label>
            <p class="text-muted-foreground text-sm">Allow recruiters to find and contact you</p>
          </div>
          <Switch.Root
            id="showToRecruiters"
            checked={formData.showToRecruiters}
            onCheckedChange={(checked) => (formData.showToRecruiters = checked)} />
        </div>

        <div class="flex items-center justify-between space-x-2">
          <div>
            <Label for="getDiscovered" class="font-medium">Get Discovered by Companies</Label>
            <p class="text-muted-foreground text-sm">Allow companies to find your profile</p>
          </div>
          <Switch.Root
            id="getDiscovered"
            checked={formData.getDiscovered}
            onCheckedChange={(checked) => (formData.getDiscovered = checked)} />
        </div>

        <div class="flex items-center justify-between space-x-2">
          <div>
            <Label for="hideFromCurrentEmployer" class="font-medium">
              Hide from Current Employer
            </Label>
            <p class="text-muted-foreground text-sm">
              Keep your job search private from current employer
            </p>
          </div>
          <Switch.Root
            id="hideFromCurrentEmployer"
            checked={formData.hideFromCurrentEmployer}
            onCheckedChange={(checked) => (formData.hideFromCurrentEmployer = checked)} />
        </div>

        <Dialog.Footer>
          <Button variant="outline" type="button" onclick={() => (modalOpen = false)}
            >Cancel</Button>
          <Button type="submit" disabled={submitting} class="ml-2">
            <Save class="mr-2 h-4 w-4" />
            {submitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </Dialog.Footer>
      </form>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
