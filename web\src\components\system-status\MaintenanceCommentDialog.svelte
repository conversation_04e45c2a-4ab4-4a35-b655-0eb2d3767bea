<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import { Checkbox } from '$lib/components/ui/checkbox/index.js';
  import StatusTag from '$components/system-status/StatusTag.svelte';
  import type { StatusTagType, MaintenanceEvent } from '$lib/types';

  // Props
  export let open: boolean;
  export let commentAction: 'start' | 'complete';
  // commentEvent is used in the parent component but not here
  export let commentEvent: MaintenanceEvent | null;
  export let commentText: string;
  export let onClose: () => void;
  export let onSubmit: () => void;
  // onCommentTextChange is used in the parent component but not here
  export let onCommentTextChange: (text: string) => void;

  // Local state
  let sendNotification = false;

  // Update parent when checkbox changes
  $: onCommentTextChange(commentText);

  // Computed values
  $: actionTitle = commentAction === 'start' ? 'Start Maintenance' : 'Complete Maintenance';
  $: statusTag = commentAction === 'start' ? 'in-progress' : 'resolved';
  $: statusText = commentAction === 'start' ? 'In Progress' : 'Resolved';
  $: buttonText = commentAction === 'start' ? 'Start Maintenance' : 'Complete Maintenance';
  $: currentDate = new Date().toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
</script>

<Dialog.Root bind:open>
  <Dialog.Overlay />
  <Dialog.Content class="sm:max-w-[500px]">
    <Dialog.Header>
      <Dialog.Title>{actionTitle}</Dialog.Title>
      <Dialog.Description>
        Update the maintenance status and add a comment. This will be recorded in the history.
      </Dialog.Description>
    </Dialog.Header>

    <div class="py-4">
      <div class="grid gap-4">
        <div class="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div class="grid gap-2">
            <Label>Status Update</Label>
            <div class="flex items-center gap-2">
              <StatusTag status={statusTag as StatusTagType} />
              <p class="text-sm">{statusText}</p>
            </div>
          </div>

          <div class="grid gap-2">
            <Label for="comment-date">Comment Date</Label>
            <div class="bg-muted flex h-9 items-center rounded-md border px-3 py-1">
              {currentDate}
            </div>
            <p class="text-muted-foreground text-xs">
              Current date and time will be used for this comment
            </p>
          </div>
        </div>

        <div class="grid gap-2">
          <Label for="comment">Comment</Label>
          <Textarea bind:value={commentText} />
          <p class="text-muted-foreground text-xs">
            This comment will be recorded in the maintenance history along with the status change
          </p>
        </div>

        <div class="mt-4 flex items-center space-x-2">
          <Checkbox id="comment-sendNotification" bind:checked={sendNotification} />
          <Label for="comment-sendNotification" class="text-sm font-normal">
            Send notification to all users about this update
          </Label>
        </div>
      </div>
    </div>

    <Dialog.Footer>
      <Button type="button" variant="outline" onclick={onClose}>Cancel</Button>
      <Button type="button" onclick={onSubmit}>
        {buttonText}
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
