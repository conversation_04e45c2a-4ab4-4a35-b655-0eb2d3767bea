// @ts-nocheck
/**
 * Email Settings Layout Server
 *
 * This module handles server-side logic for the email settings layout.
 */

import { redirect } from '@sveltejs/kit';
import { dev } from '$app/environment';
import type { LayoutServerLoad } from './$types';

export const load = async ({ locals }: Parameters<LayoutServerLoad>[0]) => {
  // In development mode, allow access to the page
  if (dev) {
    return {};
  }

  // Get user from session
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  // Check if user is admin
  if (!user.isAdmin) {
    throw redirect(302, '/dashboard');
  }

  return {};
};
