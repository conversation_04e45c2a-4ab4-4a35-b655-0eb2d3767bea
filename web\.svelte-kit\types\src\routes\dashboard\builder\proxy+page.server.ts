// @ts-nocheck
// +page.server.ts
import { superValidate } from 'sveltekit-superforms/server';
import {
  designFormSchema,
  designDefaultValues,
  resumeFormSchema,
} from '$lib/validators/buildResume.js';
import type { Actions, PageServerLoad } from '../$types.js';
import { zod } from 'sveltekit-superforms/adapters';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth.js';
import { redirect } from '@sveltejs/kit';

// Using the shared Prisma client from $lib/server/prisma

export const load = async ({ locals }) => {
  const user = locals.user;

  if (!user) throw redirect(302, '/auth/sign-in');

  // Get the validated forms with defaults.
  const design = await superValidate(designDefaultValues, zod(designFormSchema));
  const resume = await superValidate({}, zod(resumeFormSchema));

  // Return an object with a "form" key and also include user data.
  return { form: { resume, design }, user };
};

export const actions = {
  default: async (event: import('./$types').RequestEvent) => {
    const resume = await superValidate(event, zod(resumeFormSchema));
    const design = await superValidate(event, zod(designFormSchema));

    if (!resume.valid || !design.valid) {
      return {
        status: 400,
        body: { form: { resume, design } },
      };
    }

    // Process valid data here (e.g. save to a database)
    return { form: { resume, design } };
  },
};
;null as any as Actions;