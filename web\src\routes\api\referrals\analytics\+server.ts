import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';

export const GET: RequestHandler = async ({ locals }) => {
  const user = locals.user;
  if (!user?.email) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const userData = await prisma.user.findUnique({
      where: { email: user.email },
    });

    if (!userData) {
      return json({ error: 'User not found' }, { status: 404 });
    }

    // Get all referrals for this user
    const referralsByCode = await prisma.referral.findMany({
      where: { referrerId: userData.id },
      include: {
        referred: {
          select: {
            id: true,
            name: true,
            email: true,
            createdAt: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Get unique referral codes that actually have data
    const codesWithData = [...new Set(referralsByCode.map((r) => r.referralCode))];

    // Get referral code history only for codes that have data
    const referralCodeHistory = await prisma.referralCodeHistory.findMany({
      where: {
        userId: userData.id,
        referralCode: { in: codesWithData },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Group referrals by referral code (only codes with actual referrals)
    const codeAnalytics = codesWithData
      .map((code) => {
        const referralsForCode = referralsByCode.filter(
          (referral) => referral.referralCode === code
        );

        const historyEntry = referralCodeHistory.find((h) => h.referralCode === code);
        const isCurrentCode = userData.referralCode === code;

        return {
          referralCode: code,
          isActive: isCurrentCode,
          createdAt: historyEntry?.createdAt || referralsForCode[0]?.createdAt,
          deactivatedAt: historyEntry?.deactivatedAt,
          reason: historyEntry?.reason || (isCurrentCode ? 'current' : 'historical'),
          referralCount: referralsForCode.length,
          completedReferrals: referralsForCode.filter((r) => r.status === 'completed').length,
          pendingReferrals: referralsForCode.filter((r) => r.status === 'pending').length,
          referrals: referralsForCode,
        };
      })
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // Calculate monthly analytics
    const monthlyAnalytics = [];
    const now = new Date();

    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const nextMonthDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);

      const monthReferrals = referralsByCode.filter((referral) => {
        const referralDate = new Date(referral.createdAt);
        return referralDate >= monthDate && referralDate < nextMonthDate;
      });

      monthlyAnalytics.push({
        month: monthDate.toLocaleDateString('en-US', { month: 'short' }),
        year: monthDate.getFullYear(),
        referrals: monthReferrals.length,
        completed: monthReferrals.filter((r) => r.status === 'completed').length,
        pending: monthReferrals.filter((r) => r.status === 'pending').length,
      });
    }

    // Calculate cumulative data
    let cumulative = 0;
    const cumulativeData = monthlyAnalytics.map((month) => {
      cumulative += month.referrals;
      return {
        ...month,
        cumulative,
      };
    });

    return json({
      codeHistory: codeAnalytics,
      monthlyAnalytics: cumulativeData,
      totalReferrals: referralsByCode.length,
      totalCompleted: referralsByCode.filter((r) => r.status === 'completed').length,
      totalPending: referralsByCode.filter((r) => r.status === 'pending').length,
      conversionRate:
        referralsByCode.length > 0
          ? Math.round(
              (referralsByCode.filter((r) => r.status === 'completed').length /
                referralsByCode.length) *
                100
            )
          : 0,
    });
  } catch (error) {
    console.error('Error getting referral analytics:', error);
    return json({ error: 'Failed to get referral analytics' }, { status: 500 });
  }
};
