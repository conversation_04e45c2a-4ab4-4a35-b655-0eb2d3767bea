<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { X, Save, Edit } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import type { LanguageSchema } from '$lib/validators/profile';

  // Props
  const { open, languages, onClose, onSave } = $props<{
    open: boolean;
    languages: LanguageSchema[];
    onClose: () => void;
    onSave: (languages: LanguageSchema[]) => Promise<boolean>;
  }>();

  // Local state for editing
  let editedLanguages = $state<LanguageSchema[]>([]);
  let formData = $state<LanguageSchema>({
    language: '',
    proficiency: 'intermediate',
  });
  let submitting = $state(false);
  let apiLanguages = $state<{ name: string; code: string }[]>([]);
  let filteredLanguages = $state<{ name: string; code: string }[]>([]);
  let searchQuery = $state('');
  let editingIndex = $state<number | null>(null);

  // Proficiency options
  const proficiencyOptions = [
    { value: 'beginner', label: 'Beginner' },
    { value: 'intermediate', label: 'Intermediate' },
    { value: 'advanced', label: 'Advanced' },
    { value: 'native', label: 'Native' },
  ];

  // Initialize local state when modal opens
  $effect(() => {
    if (open) {
      editedLanguages = [...languages];
      resetForm();
      searchQuery = '';
      editingIndex = null;
      fetchLanguages();
    }
  });

  // Reset form
  function resetForm() {
    formData = {
      language: '',
      proficiency: 'intermediate',
    };
  }

  // Fetch languages from API
  async function fetchLanguages() {
    try {
      const response = await fetch('/api/languages');
      if (response.ok) {
        const data = await response.json();
        apiLanguages = data;
        updateFilteredLanguages();
      } else {
        // Fallback to common languages if API fails
        apiLanguages = commonLanguages.map((lang) => ({ name: lang, code: lang.toLowerCase() }));
        updateFilteredLanguages();
      }
    } catch (error) {
      console.error('Error fetching languages:', error);
      // Fallback to common languages
      apiLanguages = commonLanguages.map((lang) => ({ name: lang, code: lang.toLowerCase() }));
      updateFilteredLanguages();
    }
  }

  // Common languages for fallback
  const commonLanguages = [
    'English',
    'Spanish',
    'French',
    'German',
    'Chinese',
    'Japanese',
    'Korean',
    'Russian',
    'Arabic',
    'Portuguese',
    'Italian',
    'Hindi',
  ];

  // Update filtered languages based on search query
  function updateFilteredLanguages() {
    if (!searchQuery) {
      filteredLanguages = apiLanguages.slice(0, 20); // Show first 20 languages by default
    } else {
      filteredLanguages = apiLanguages
        .filter((lang) => lang.name.toLowerCase().includes(searchQuery.toLowerCase()))
        .slice(0, 20);
    }
  }

  // Handle search input
  $effect(() => {
    updateFilteredLanguages();
  });

  // Add language
  function addLanguage() {
    if (!formData.language) {
      toast.error('Please enter a language');
      return;
    }

    if (editingIndex !== null) {
      // Update existing language
      editedLanguages[editingIndex] = { ...formData };
      editingIndex = null;
    } else {
      // Add new language
      const exists = editedLanguages.some(
        (lang) => lang.language.toLowerCase() === formData.language.toLowerCase()
      );

      if (exists) {
        toast.error('This language is already added');
        return;
      }

      editedLanguages = [...editedLanguages, { ...formData }];
    }

    resetForm();
  }

  // Edit language
  function editLanguage(index: number) {
    const language = editedLanguages[index];
    formData = { ...language };
    editingIndex = index;
  }

  // Remove language
  function removeLanguage(index: number) {
    editedLanguages = editedLanguages.filter((_, i) => i !== index);
    if (editingIndex === index) {
      editingIndex = null;
      resetForm();
    }
  }

  // Select language from suggestions
  function selectLanguage(name: string) {
    formData.language = name;
  }

  // Handle save
  async function handleSave() {
    submitting = true;
    try {
      const success = await onSave(editedLanguages);
      if (success) {
        toast.success('Languages updated successfully');
        onClose();
      }
    } catch (error) {
      console.error('Error saving languages:', error);
      toast.error('Failed to save languages');
    } finally {
      submitting = false;
    }
  }
</script>

<Dialog.Root {open} onOpenChange={onClose}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="sm:max-w-[500px]">
      <Dialog.Header>
        <Dialog.Title>Edit Languages</Dialog.Title>
        <Dialog.Description>Add or remove languages from your profile.</Dialog.Description>
      </Dialog.Header>

      <div class="py-4">
        <div class="space-y-4">
          <div class="space-y-2">
            <Label>Add Language</Label>
            <div class="flex flex-col space-y-2">
              <Input bind:value={formData.language} placeholder="Enter a language" />

              <div class="grid grid-cols-2 gap-2 sm:grid-cols-4">
                {#each proficiencyOptions as option}
                  <Button
                    type="button"
                    variant={formData.proficiency === option.value ? 'default' : 'outline'}
                    onclick={() =>
                      (formData.proficiency = option.value as
                        | 'beginner'
                        | 'intermediate'
                        | 'advanced'
                        | 'native')}>
                    {option.label}
                  </Button>
                {/each}
              </div>

              <Button type="button" variant="outline" onclick={addLanguage}>
                {editingIndex !== null ? 'Update' : 'Add'} Language
              </Button>
            </div>
          </div>

          {#if editedLanguages.length > 0}
            <div class="space-y-2">
              <Label>Your Languages</Label>
              <div class="space-y-2">
                {#each editedLanguages as language, index}
                  <div class="flex items-center justify-between rounded-md border p-2">
                    <div>
                      <p class="font-medium">{language.language}</p>
                      <p class="text-muted-foreground text-sm">
                        {proficiencyOptions.find((opt) => opt.value === language.proficiency)
                          ?.label || language.proficiency}
                      </p>
                    </div>
                    <div class="flex space-x-1">
                      <Button variant="ghost" size="icon" onclick={() => editLanguage(index)}>
                        <Edit class="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onclick={() => removeLanguage(index)}>
                        <X class="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                {/each}
              </div>
            </div>
          {:else}
            <p class="text-muted-foreground text-sm">No languages added yet</p>
          {/if}

          <div class="space-y-2">
            <Label>Suggested Languages</Label>
            <Input bind:value={searchQuery} placeholder="Search for languages" />
            <div class="flex max-h-40 flex-wrap gap-2 overflow-y-auto">
              {#each filteredLanguages as language}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onclick={() => selectLanguage(language.name)}>
                  {language.name}
                </Button>
              {/each}
            </div>
          </div>
        </div>
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={onClose}>Cancel</Button>
        <Button onclick={handleSave} disabled={submitting} class="ml-2">
          <Save class="mr-2 h-4 w-4" />
          {submitting ? 'Saving...' : 'Save Changes'}
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
