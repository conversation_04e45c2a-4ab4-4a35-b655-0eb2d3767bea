<!-- src/routes/help/category/[slug]/+page.svelte -->
<script lang="ts">
  import SEO from '$components/shared/SEO.svelte';
  import HelpSearch from '$components/help/HelpSearch.svelte';
  import HelpSidebar from '$components/help/HelpSidebar.svelte';
  import HelpArticleCard from '$components/help/HelpArticleCard.svelte';
  import * as lucideIcons from 'lucide-svelte';
  import { ArrowLeft } from 'lucide-svelte';

  // Get data from server using Svelte 5 runes
  export let data;

  // Get the icon component from the icon name
  let iconComponent = data.category?.icon
    ? lucideIcons[data.category.icon as keyof typeof lucideIcons]
    : lucideIcons.HelpCircle;
</script>

<SEO
  title="{data.category.name} | Help Center"
  description="Browse help articles in the {data.category.name} category."
  keywords="help center, {data.category.name.toLowerCase()}, support, guides, tutorials" />

<div class="container mx-auto px-4 py-12">
  <div class="grid gap-8 lg:grid-cols-4">
    <!-- Sidebar -->
    <div class="lg:col-span-1">
      <HelpSidebar categories={data.categories} />
    </div>

    <!-- Main Content -->
    <div class="lg:col-span-3">
      <div class="mb-6">
        <a href="/help" class="text-primary inline-flex items-center text-sm hover:underline">
          <ArrowLeft class="mr-1 h-4 w-4" />
          Back to Help Center
        </a>
      </div>

      <div class="mb-8 flex items-center gap-3">
        <div class="bg-primary/10 text-primary rounded-full p-3">
          <svelte:component this={iconComponent} class="h-6 w-6" />
        </div>
        <h1 class="text-3xl font-bold">{data.category.name}</h1>
      </div>

      <!-- Search Bar -->
      <div class="mb-8">
        <HelpSearch className="w-full" />
      </div>

      <!-- Articles -->
      {#if data.articles.length > 0}
        <div class="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
          {#each data.articles as article}
            <HelpArticleCard {article} />
          {/each}
        </div>
      {:else}
        <div class="bg-muted rounded-lg border p-8 text-center">
          <p class="text-muted-foreground mb-2">No articles found in this category.</p>
          <p>Check back later or browse other categories.</p>
        </div>
      {/if}
    </div>
  </div>
</div>
