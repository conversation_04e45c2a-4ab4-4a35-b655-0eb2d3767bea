// cron/utils/proxyChecker.ts

import { logger } from "./logger";
import { chromium } from "playwright";
import { sendEmailNotification, EmailNotificationType } from "./emailService";

// Configuration
const PROXY_CHECK_INTERVAL = 12 * 60 * 60 * 1000; // 12 hours in milliseconds
const PROXY_CHECK_TIMEOUT = 30000; // 30 seconds timeout for proxy check

/**
 * Check if the proxy is working
 * @returns Promise<boolean> - true if proxy is working, false otherwise
 */
export async function isProxyWorking(): Promise<boolean> {
  const SMARTPROXY_USERNAME = process.env.SMARTPROXY_USERNAME!;
  const SMARTPROXY_PASSWORD = process.env.SMARTPROXY_PASSWORD!;
  const SMARTPROXY_HOST = process.env.SMARTPROXY_HOST || "us.smartproxy.com";
  const SMARTPROXY_PORT = process.env.SMARTPROXY_PORT || "10001";

  const proxyServer = `http://${SMARTPROXY_HOST}:${SMARTPROXY_PORT}`;

  logger.info(`🔍 Checking if proxy is working (${proxyServer})...`);

  let browser;
  try {
    // Launch browser with proxy - always use headless mode for proxy checking
    browser = await chromium.launch({
      headless: true,
      proxy: {
        server: proxyServer,
      },
      timeout: PROXY_CHECK_TIMEOUT,
      args: ["--no-sandbox"], // Required for Docker environments
    });

    // Create context with proxy credentials
    const context = await browser.newContext({
      httpCredentials: {
        username: SMARTPROXY_USERNAME,
        password: SMARTPROXY_PASSWORD,
      },
    });

    // Create page and navigate to test URL
    const page = await context.newPage();
    const testUrl = "https://ipinfo.io/json";

    const start = Date.now();
    const response = await page.goto(testUrl, {
      waitUntil: "domcontentloaded",
      timeout: PROXY_CHECK_TIMEOUT,
    });

    const latency = Date.now() - start;
    const status = response?.status() ?? "unknown";

    // Check if response is valid
    if (response && response.ok()) {
      const content = await response.text();
      logger.info(
        `✅ Proxy is working! Status: ${status}, Latency: ${latency}ms`
      );
      logger.info(`📊 IP Info: ${content}`);
      return true;
    } else {
      logger.error(
        `❌ Proxy check failed. Status: ${status}, Latency: ${latency}ms`
      );
      return false;
    }
  } catch (error) {
    logger.error(`❌ Error checking proxy: ${error}`);
    return false;
  } finally {
    if (browser) {
      await browser.close().catch((err) => {
        logger.error(`❌ Error closing browser: ${err}`);
      });
    }
  }
}

/**
 * Schedule a proxy check after the specified interval
 * @param callback Function to call when proxy is back up
 */
export function scheduleProxyCheck(
  callback: () => Promise<void>
): NodeJS.Timeout {
  logger.info(
    `⏰ Scheduling proxy check in ${PROXY_CHECK_INTERVAL / (60 * 60 * 1000)} hours...`
  );

  return setTimeout(async () => {
    logger.info(`⏰ Running scheduled proxy check...`);

    try {
      const proxyWorking = await isProxyWorking();

      if (proxyWorking) {
        logger.info(`🎉 Proxy is back up! Resuming normal operation...`);

        // Send email notification that proxy is back up
        await sendEmailNotification(EmailNotificationType.PROXY_DOWN, {
          error: "Proxy is back up and working!",
          timestamp: new Date().toISOString(),
          status: "RESOLVED",
        });

        // Call the callback function to resume normal operation
        await callback();
      } else {
        logger.error(`❌ Proxy is still down. Scheduling another check...`);

        // Schedule another check
        scheduleProxyCheck(callback);
      }
    } catch (error) {
      logger.error(`❌ Error during proxy check: ${error}`);

      // Schedule another check even if there was an error
      scheduleProxyCheck(callback);
    }
  }, PROXY_CHECK_INTERVAL);
}
