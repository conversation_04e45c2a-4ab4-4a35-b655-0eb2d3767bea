<script lang="ts">
  import { type PortfolioLinksSchema } from '$lib/validators/profile';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Edit, Linkedin, Github, Globe, Link } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import PortfolioLinksModal from './PortfolioLinksModal.svelte';

  // Props
  const {
    data,
    onSave,
    disabled = false,
  } = $props<{
    data: PortfolioLinksSchema;
    onSave: (data: PortfolioLinksSchema) => Promise<boolean>;
    disabled?: boolean;
  }>();

  // Modal state
  let modalOpen = $state(false);

  // Open edit modal
  function openEditModal() {
    modalOpen = true;
  }

  // Handle modal save
  async function handleSave(updatedData: PortfolioLinksSchema): Promise<boolean> {
    try {
      const success = await onSave(updatedData);
      return success;
    } catch (error) {
      console.error('Error saving portfolio links:', error);
      toast.error('Failed to save portfolio links');
      return false;
    }
  }

  // Format URL for display
  function formatUrl(url: string | undefined): string {
    if (!url) return '';
    return url.replace(/^https?:\/\/(www\.)?/, '');
  }
</script>

<div class="rounded-lg border p-6">
  <div class="flex items-center justify-between">
    <h2 class="text-xl font-semibold">Portfolio & Links</h2>
    <Button variant="ghost" size="sm" onclick={openEditModal} {disabled}>
      <Edit class="mr-2 h-4 w-4" />
      Edit
    </Button>
  </div>

  <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
    <div class="rounded-md border p-4">
      <div class="flex items-center">
        <div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full">
          <Linkedin class="text-primary h-5 w-5" />
        </div>
        <div>
          <h3 class="font-medium">LinkedIn URL</h3>
          {#if data?.linkedinUrl}
            <a
              href={data.linkedinUrl}
              target="_blank"
              rel="noopener noreferrer"
              class="text-primary text-sm hover:underline">
              {formatUrl(data.linkedinUrl)}
            </a>
          {:else}
            <p class="text-muted-foreground text-sm">Not specified</p>
          {/if}
        </div>
      </div>
    </div>

    <div class="rounded-md border p-4">
      <div class="flex items-center">
        <div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full">
          <Github class="text-primary h-5 w-5" />
        </div>
        <div>
          <h3 class="font-medium">GitHub URL</h3>
          {#if data?.githubUrl}
            <a
              href={data.githubUrl}
              target="_blank"
              rel="noopener noreferrer"
              class="text-primary text-sm hover:underline">
              {formatUrl(data.githubUrl)}
            </a>
          {:else}
            <p class="text-muted-foreground text-sm">Not specified</p>
          {/if}
        </div>
      </div>
    </div>

    <div class="rounded-md border p-4">
      <div class="flex items-center">
        <div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full">
          <Globe class="text-primary h-5 w-5" />
        </div>
        <div>
          <h3 class="font-medium">Portfolio URL</h3>
          {#if data?.portfolioUrl}
            <a
              href={data.portfolioUrl}
              target="_blank"
              rel="noopener noreferrer"
              class="text-primary text-sm hover:underline">
              {formatUrl(data.portfolioUrl)}
            </a>
          {:else}
            <p class="text-muted-foreground text-sm">Not specified</p>
          {/if}
        </div>
      </div>
    </div>

    <div class="rounded-md border p-4">
      <div class="flex items-center">
        <div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full">
          <Link class="text-primary h-5 w-5" />
        </div>
        <div>
          <h3 class="font-medium">Other URL</h3>
          {#if data?.otherUrl}
            <a
              href={data.otherUrl}
              target="_blank"
              rel="noopener noreferrer"
              class="text-primary text-sm hover:underline">
              {formatUrl(data.otherUrl)}
            </a>
          {:else}
            <p class="text-muted-foreground text-sm">Not specified</p>
          {/if}
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Portfolio Links Modal -->
<PortfolioLinksModal
  open={modalOpen}
  {data}
  onClose={() => (modalOpen = false)}
  onSave={handleSave}
  {disabled} />
