// src/routes/api/ws/+server.ts
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

/**
 * WebSocket status endpoint
 *
 * This endpoint returns the status of the WebSocket server.
 * The actual WebSocket connection is handled by the server.ts file.
 * WebSocket clients should connect to the /ws endpoint, not /api/ws.
 */
export const GET: RequestHandler = async ({ request }) => {
  console.log('WebSocket status endpoint accessed');

  // Determine if we're in development or production
  const isDev = process.env.NODE_ENV === 'development';
  const wsHost = isDev ? 'localhost:3000' : request.headers.get('host');
  const wsProtocol = request.headers.get('x-forwarded-proto') === 'https' ? 'wss' : 'ws';

  return json({
    status: 'ok',
    message: 'WebSocket server is running',
    endpoint: '/ws',
    wsUrl: `${wsProtocol}://${wsHost}/ws`,
    environment: isDev ? 'development' : 'production',
    instructions:
      'Connect to the WebSocket endpoint with a WebSocket client, not this HTTP endpoint',
    timestamp: new Date().toISOString(),
  });
};
