// cron/scripts/syncOnetSkills.ts

import { downloadOnetTechSkills } from "../lib/onet/downloadTechSkills";
import { fetchOnetSkills } from "../lib/onet/fetchSkills";
import { logger } from "../utils/logger";
import fs from "fs/promises";
import path from "path";

const SKILLS_PATH = path.resolve("../data/onet/Technology%20Skills.txt");

async function syncOnetSkills() {
  try {
    logger.info("🔄 Starting O*NET sync...");

    const skillsExists = await fs.stat(SKILLS_PATH).catch(() => null);
    if (!skillsExists) {
      logger.error(`❌ Missing file: ${SKILLS_PATH}`);
      await downloadOnetTechSkills();
    }

    await fetchOnetSkills();

    logger.info("✅ O*NET technology skills sync complete.");
  } catch (err) {
    logger.error("❌ Failed to sync O*NET skills:", err);
  }
}

await syncOnetSkills();
