<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Save } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import type { JobPreferencesData } from './JobPreferencesForm.svelte';

  // Props
  const { open, data, onClose, onSave } = $props<{
    open: boolean;
    data: JobPreferencesData;
    onClose: () => void;
    onSave: (data: JobPreferencesData) => Promise<boolean>;
  }>();

  // Initialize form data
  let formData = $state<JobPreferencesData>({
    interestedRoles: data?.interestedRoles || [],
    preferredLocations: data?.preferredLocations || [],
    remotePreference: data?.remotePreference || 'hybrid',
    salary: data?.salary || '',
    industries: data?.industries || [],
    jobTypes: data?.jobTypes || [],
    experienceLevels: data?.experienceLevels || [],
    valueInRole: data?.valueInRole || [],
    idealCompanySize: data?.idealCompanySize || 'medium',
    avoidIndustries: data?.avoidIndustries || [],
    avoidSkills: data?.avoidSkills || [],
    securityClearance: data?.securityClearance || false,
    jobSearchStatus: data?.jobSearchStatus || 'actively_looking',
  });

  // Form state
  let submitting = $state(false);
  let errors = $state<Record<string, string>>({});

  // Reset form when modal opens
  $effect(() => {
    if (open) {
      formData = {
        interestedRoles: data?.interestedRoles || [],
        preferredLocations: data?.preferredLocations || [],
        remotePreference: data?.remotePreference || 'hybrid',
        salary: data?.salary || '',
        industries: data?.industries || [],
        jobTypes: data?.jobTypes || [],
        experienceLevels: data?.experienceLevels || [],
        valueInRole: data?.valueInRole || [],
        idealCompanySize: data?.idealCompanySize || 'medium',
        avoidIndustries: data?.avoidIndustries || [],
        avoidSkills: data?.avoidSkills || [],
        securityClearance: data?.securityClearance || false,
        jobSearchStatus: data?.jobSearchStatus || 'actively_looking',
      };
      errors = {};
    }
  });

  // Remote preference options
  const remoteOptions = [
    { value: 'remote', label: 'Remote Only' },
    { value: 'hybrid', label: 'Hybrid' },
    { value: 'onsite', label: 'On-site Only' },
    { value: 'flexible', label: 'Flexible' },
  ];

  // Job type options
  const jobTypeOptions = [
    { value: 'full_time', label: 'Full-time' },
    { value: 'part_time', label: 'Part-time' },
    { value: 'contract', label: 'Contract' },
    { value: 'temporary', label: 'Temporary' },
    { value: 'internship', label: 'Internship' },
  ];

  // Experience level options
  const experienceLevelOptions = [
    { value: 'entry', label: 'Entry Level' },
    { value: 'mid', label: 'Mid Level' },
    { value: 'senior', label: 'Senior Level' },
    { value: 'executive', label: 'Executive' },
  ];

  // Company size options
  const companySizeOptions = [
    { value: 'startup', label: 'Startup (1-10 employees)' },
    { value: 'small', label: 'Small (11-50 employees)' },
    { value: 'medium', label: 'Medium (51-200 employees)' },
    { value: 'large', label: 'Large (201-1000 employees)' },
    { value: 'enterprise', label: 'Enterprise (1000+ employees)' },
  ];

  // Job search status options
  const jobSearchStatusOptions = [
    { value: 'actively_looking', label: 'Actively Looking' },
    { value: 'open_to_opportunities', label: 'Open to Opportunities' },
    { value: 'not_looking', label: 'Not Looking' },
  ];

  // Handle form submission
  async function handleSubmit() {
    // Set submitting state
    submitting = true;

    try {
      // Save job preferences
      const success = await onSave(formData);
      if (success) {
        toast.success('Job preferences updated successfully');
        onClose();
      }
    } catch (error) {
      console.error('Error saving job preferences:', error);
      toast.error('Failed to save job preferences');
    } finally {
      submitting = false;
    }
  }

  // Handle role input
  let newRole = $state('');
  function addRole() {
    if (newRole.trim()) {
      formData.interestedRoles = [...(formData.interestedRoles || []), newRole.trim()];
      newRole = '';
    }
  }
  function removeRole(index: number) {
    if (formData.interestedRoles) {
      formData.interestedRoles = formData.interestedRoles.filter((_, i) => i !== index);
    }
  }

  // Handle location input
  let newLocation = $state('');
  function addLocation() {
    if (newLocation.trim()) {
      formData.preferredLocations = [...(formData.preferredLocations || []), newLocation.trim()];
      newLocation = '';
    }
  }
  function removeLocation(index: number) {
    if (formData.preferredLocations) {
      formData.preferredLocations = formData.preferredLocations.filter((_, i) => i !== index);
    }
  }

  // Handle job type input
  function toggleJobType(value: string) {
    if (!formData.jobTypes) formData.jobTypes = [];

    if (formData.jobTypes.includes(value)) {
      formData.jobTypes = formData.jobTypes.filter((type) => type !== value);
    } else {
      formData.jobTypes = [...formData.jobTypes, value];
    }
  }

  // Handle experience level input
  function toggleExperienceLevel(value: string) {
    if (!formData.experienceLevels) formData.experienceLevels = [];

    if (formData.experienceLevels.includes(value)) {
      formData.experienceLevels = formData.experienceLevels.filter((level) => level !== value);
    } else {
      formData.experienceLevels = [...formData.experienceLevels, value];
    }
  }

  // Handle company size selection
  function setCompanySize(value: string) {
    formData.idealCompanySize = value as 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  }

  // Handle job search status selection
  function setJobSearchStatus(value: string) {
    formData.jobSearchStatus = value as
      | 'actively_looking'
      | 'open_to_opportunities'
      | 'not_looking';
  }

  // Handle security clearance toggle
  function toggleSecurityClearance() {
    formData.securityClearance = !formData.securityClearance;
  }

  // Handle value in role input
  let newValue = $state('');
  function addValue() {
    if (newValue.trim()) {
      formData.valueInRole = [...(formData.valueInRole || []), newValue.trim()];
      newValue = '';
    }
  }
  function removeValue(index: number) {
    if (formData.valueInRole) {
      formData.valueInRole = formData.valueInRole.filter((_, i) => i !== index);
    }
  }

  // Handle avoid industries input
  let newAvoidIndustry = $state('');
  function addAvoidIndustry() {
    if (newAvoidIndustry.trim()) {
      formData.avoidIndustries = [...(formData.avoidIndustries || []), newAvoidIndustry.trim()];
      newAvoidIndustry = '';
    }
  }
  function removeAvoidIndustry(index: number) {
    if (formData.avoidIndustries) {
      formData.avoidIndustries = formData.avoidIndustries.filter((_, i) => i !== index);
    }
  }

  // Handle avoid skills input
  let newAvoidSkill = $state('');
  function addAvoidSkill() {
    if (newAvoidSkill.trim()) {
      formData.avoidSkills = [...(formData.avoidSkills || []), newAvoidSkill.trim()];
      newAvoidSkill = '';
    }
  }
  function removeAvoidSkill(index: number) {
    if (formData.avoidSkills) {
      formData.avoidSkills = formData.avoidSkills.filter((_, i) => i !== index);
    }
  }
</script>

<Dialog.Root {open} onOpenChange={(isOpen) => !isOpen && onClose()}>
  <Dialog.Content class="sm:max-w-[600px]">
    <Dialog.Header>
      <Dialog.Title>Edit Job Preferences</Dialog.Title>
      <Dialog.Description>
        Update your job preferences to help us find the right opportunities for you.
      </Dialog.Description>
    </Dialog.Header>

    <div class="max-h-[60vh] overflow-y-auto">
      <div class="grid gap-4 py-4">
        <div class="grid gap-2">
          <Label>Desired Roles</Label>
          <div class="flex gap-2">
            <Input bind:value={newRole} placeholder="Add a role" class="flex-1" />
            <Button type="button" onclick={addRole}>Add</Button>
          </div>
          {#if formData.interestedRoles && formData.interestedRoles.length > 0}
            <div class="mt-2 flex flex-wrap gap-2">
              {#each formData.interestedRoles as role, index}
                <div
                  class="bg-secondary text-secondary-foreground flex items-center gap-1 rounded-full px-3 py-1 text-sm">
                  {role}
                  <button
                    type="button"
                    class="text-secondary-foreground/70 hover:text-secondary-foreground ml-1 rounded-full p-1"
                    onclick={() => removeRole(index)}>
                    &times;
                  </button>
                </div>
              {/each}
            </div>
          {/if}
        </div>

        <div class="grid gap-2">
          <Label>Preferred Locations</Label>
          <div class="flex gap-2">
            <Input bind:value={newLocation} placeholder="Add a location" class="flex-1" />
            <Button type="button" onclick={addLocation}>Add</Button>
          </div>
          {#if formData.preferredLocations && formData.preferredLocations.length > 0}
            <div class="mt-2 flex flex-wrap gap-2">
              {#each formData.preferredLocations as location, index}
                <div
                  class="bg-secondary text-secondary-foreground flex items-center gap-1 rounded-full px-3 py-1 text-sm">
                  {location}
                  <button
                    type="button"
                    class="text-secondary-foreground/70 hover:text-secondary-foreground ml-1 rounded-full p-1"
                    onclick={() => removeLocation(index)}>
                    &times;
                  </button>
                </div>
              {/each}
            </div>
          {/if}
        </div>

        <div class="grid gap-2">
          <Label>Remote Preference</Label>
          <div class="flex flex-wrap gap-2">
            {#each remoteOptions as option}
              <Button
                type="button"
                variant={formData.remotePreference === option.value ? 'default' : 'outline'}
                class="flex-grow-0"
                onclick={() =>
                  (formData.remotePreference = option.value as
                    | 'remote'
                    | 'hybrid'
                    | 'onsite'
                    | 'flexible')}>
                {option.label}
              </Button>
            {/each}
          </div>
        </div>

        <div class="grid gap-2">
          <Label>Job Types</Label>
          <div class="flex flex-wrap gap-2">
            {#each jobTypeOptions as option}
              <Button
                type="button"
                variant={formData.jobTypes?.includes(option.value) ? 'default' : 'outline'}
                class="flex-grow-0"
                onclick={() => toggleJobType(option.value)}>
                {option.label}
              </Button>
            {/each}
          </div>
        </div>

        <div class="grid gap-2">
          <Label>Experience Level</Label>
          <div class="flex flex-wrap gap-2">
            {#each experienceLevelOptions as option}
              <Button
                type="button"
                variant={formData.experienceLevels?.includes(option.value) ? 'default' : 'outline'}
                class="flex-grow-0"
                onclick={() => toggleExperienceLevel(option.value)}>
                {option.label}
              </Button>
            {/each}
          </div>
        </div>

        <div class="grid gap-2">
          <Label for="salary">Desired Salary</Label>
          <Input id="salary" bind:value={formData.salary} placeholder="e.g. $80,000 - $100,000" />
        </div>

        <div class="grid gap-2">
          <Label>What do you value in a new role?</Label>
          <div class="flex gap-2">
            <Input bind:value={newValue} placeholder="Add a value" class="flex-1" />
            <Button type="button" onclick={addValue}>Add</Button>
          </div>
          {#if formData.valueInRole && formData.valueInRole.length > 0}
            <div class="mt-2 flex flex-wrap gap-2">
              {#each formData.valueInRole as value, index}
                <div
                  class="bg-secondary text-secondary-foreground flex items-center gap-1 rounded-full px-3 py-1 text-sm">
                  {value}
                  <button
                    type="button"
                    class="text-secondary-foreground/70 hover:text-secondary-foreground ml-1 rounded-full p-1"
                    onclick={() => removeValue(index)}>
                    &times;
                  </button>
                </div>
              {/each}
            </div>
          {/if}
        </div>

        <div class="grid gap-2">
          <Label>Ideal Company Size</Label>
          <div class="flex flex-wrap gap-2">
            {#each companySizeOptions as option}
              <Button
                type="button"
                variant={formData.idealCompanySize === option.value ? 'default' : 'outline'}
                class="flex-grow-0"
                onclick={() => setCompanySize(option.value)}>
                {option.label}
              </Button>
            {/each}
          </div>
        </div>

        <div class="grid gap-2">
          <Label>Industries to avoid</Label>
          <div class="flex gap-2">
            <Input bind:value={newAvoidIndustry} placeholder="Add an industry" class="flex-1" />
            <Button type="button" onclick={addAvoidIndustry}>Add</Button>
          </div>
          {#if formData.avoidIndustries && formData.avoidIndustries.length > 0}
            <div class="mt-2 flex flex-wrap gap-2">
              {#each formData.avoidIndustries as industry, index}
                <div
                  class="bg-secondary text-secondary-foreground flex items-center gap-1 rounded-full px-3 py-1 text-sm">
                  {industry}
                  <button
                    type="button"
                    class="text-secondary-foreground/70 hover:text-secondary-foreground ml-1 rounded-full p-1"
                    onclick={() => removeAvoidIndustry(index)}>
                    &times;
                  </button>
                </div>
              {/each}
            </div>
          {/if}
        </div>

        <div class="grid gap-2">
          <Label>Skills to avoid</Label>
          <div class="flex gap-2">
            <Input bind:value={newAvoidSkill} placeholder="Add a skill" class="flex-1" />
            <Button type="button" onclick={addAvoidSkill}>Add</Button>
          </div>
          {#if formData.avoidSkills && formData.avoidSkills.length > 0}
            <div class="mt-2 flex flex-wrap gap-2">
              {#each formData.avoidSkills as skill, index}
                <div
                  class="bg-secondary text-secondary-foreground flex items-center gap-1 rounded-full px-3 py-1 text-sm">
                  {skill}
                  <button
                    type="button"
                    class="text-secondary-foreground/70 hover:text-secondary-foreground ml-1 rounded-full p-1"
                    onclick={() => removeAvoidSkill(index)}>
                    &times;
                  </button>
                </div>
              {/each}
            </div>
          {/if}
        </div>

        <div class="grid gap-2">
          <Label>Security Clearance</Label>
          <div class="flex items-center space-x-2">
            <Button
              type="button"
              variant={formData.securityClearance ? 'default' : 'outline'}
              onclick={toggleSecurityClearance}>
              {formData.securityClearance ? 'Yes' : 'No'}
            </Button>
            <span class="text-muted-foreground text-sm">
              Would you like to see roles that require top security clearance?
            </span>
          </div>
        </div>

        <div class="grid gap-2">
          <Label>Job Search Status</Label>
          <div class="flex flex-wrap gap-2">
            {#each jobSearchStatusOptions as option}
              <Button
                type="button"
                variant={formData.jobSearchStatus === option.value ? 'default' : 'outline'}
                class="flex-grow-0"
                onclick={() => setJobSearchStatus(option.value)}>
                {option.label}
              </Button>
            {/each}
          </div>
        </div>
      </div>
    </div>

    <Dialog.Footer>
      <Button variant="outline" onclick={onClose}>Cancel</Button>
      <Button onclick={handleSubmit} disabled={submitting}>
        {#if submitting}
          <span class="mr-2">Saving...</span>
        {:else}
          <Save class="mr-2 h-4 w-4" />
          Save Changes
        {/if}
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
