import { json } from '@sveltejs/kit';
import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';

// Generate a unique referral code
function generateReferralCode(name?: string, email?: string): string {
  const prefix = name
    ? name
        .replace(/[^a-zA-Z]/g, '')
        .substring(0, 3)
        .toUpperCase()
    : email?.substring(0, 3).toUpperCase() || 'REF';

  const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `${prefix}${randomSuffix}`;
}

// GET - Get user's referral information
export const GET: RequestHandler = async ({ locals }) => {
  const user = locals.user;
  if (!user?.email) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Get user with referral data
    const userData = await prisma.user.findUnique({
      where: { email: user.email },
      include: {
        referralsMade: {
          include: {
            referred: {
              select: {
                id: true,
                name: true,
                email: true,
                createdAt: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        referrals: {
          select: {
            id: true,
            name: true,
            email: true,
            createdAt: true,
          },
        },
        referredBy: {
          select: {
            id: true,
            name: true,
            email: true,
            referralCode: true,
          },
        },
      },
    });

    if (!userData) {
      return json({ error: 'User not found' }, { status: 404 });
    }

    // Generate referral code if user doesn't have one
    let referralCode = userData.referralCode;
    if (!referralCode) {
      referralCode = generateReferralCode(userData.name, userData.email);

      // Ensure uniqueness
      let attempts = 0;
      while (attempts < 5) {
        const existing = await prisma.user.findUnique({
          where: { referralCode },
        });

        if (!existing) break;

        referralCode = generateReferralCode(userData.name, userData.email);
        attempts++;
      }

      // Update user with referral code
      await prisma.user.update({
        where: { id: userData.id },
        data: { referralCode },
      });
    }

    const baseUrl = process.env.PUBLIC_BASE_URL || 'http://localhost:5173';
    const referralLink = `${baseUrl}/auth/sign-up?ref=${referralCode}`;

    return json({
      referralCode,
      referralLink,
      referralCount: userData.referralCount ?? 0,
      referralRewards: userData.referralRewards ?? 0,
      referrals: userData.referralsMade ?? [],
      referredBy: userData.referredBy,
    });
  } catch (error) {
    console.error('Error getting referral data:', error);
    return json({ error: 'Failed to get referral data' }, { status: 500 });
  }
};

// POST - Create or update referral code
export const POST: RequestHandler = async ({ request, locals }) => {
  const user = locals.user;
  if (!user?.email) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const userData = await prisma.user.findUnique({
      where: { email: user.email },
    });

    if (!userData) {
      return json({ error: 'User not found' }, { status: 404 });
    }

    const { action, customCode } = await request.json();

    if (action === 'regenerate' || action === 'create') {
      const user = await prisma.user.findUnique({
        where: { id: userData.id },
      });

      if (!user) {
        return json({ error: 'User not found' }, { status: 404 });
      }

      let newReferralCode: string;

      if (customCode && customCode.length >= 4 && customCode.length <= 12) {
        // Validate custom code
        if (!/^[A-Za-z0-9]+$/.test(customCode)) {
          return json(
            { error: 'Referral code can only contain letters and numbers' },
            { status: 400 }
          );
        }

        // Check if custom code is available
        const existing = await prisma.user.findUnique({
          where: { referralCode: customCode.toUpperCase() },
        });

        if (existing && existing.id !== userData.id) {
          return json({ error: 'This referral code is already taken' }, { status: 400 });
        }

        newReferralCode = customCode.toUpperCase();
      } else {
        newReferralCode = generateReferralCode(user.name, user.email);

        // Ensure uniqueness
        let attempts = 0;
        while (attempts < 5) {
          const existing = await prisma.user.findUnique({
            where: { referralCode: newReferralCode },
          });

          if (!existing) break;

          newReferralCode = generateReferralCode(user.name, user.email);
          attempts++;
        }
      }

      // Use transaction to preserve referral code history (only if there's data)
      await prisma.$transaction(async (tx) => {
        // Check if current referral code has any referrals
        let shouldPreserveOldCode = false;
        if (userData.referralCode) {
          const existingReferrals = await tx.referral.count({
            where: {
              referrerId: userData.id,
              referralCode: userData.referralCode,
            },
          });
          shouldPreserveOldCode = existingReferrals > 0;
        }

        // Only preserve old code if it has referrals
        if (shouldPreserveOldCode && userData.referralCode) {
          // Check if history entry already exists
          const existingHistory = await tx.referralCodeHistory.findFirst({
            where: {
              userId: userData.id,
              referralCode: userData.referralCode,
            },
          });

          if (!existingHistory) {
            // Create history entry for the old code
            await tx.referralCodeHistory.create({
              data: {
                userId: userData.id,
                referralCode: userData.referralCode,
                isActive: false,
                deactivatedAt: new Date(),
                reason: 'preserved_with_data',
                metadata: {
                  referralCount: await tx.referral.count({
                    where: {
                      referrerId: userData.id,
                      referralCode: userData.referralCode,
                    },
                  }),
                  replacedBy: newReferralCode,
                },
              },
            });
          } else {
            // Update existing history entry
            await tx.referralCodeHistory.update({
              where: { id: existingHistory.id },
              data: {
                isActive: false,
                deactivatedAt: new Date(),
                metadata: {
                  ...existingHistory.metadata,
                  replacedBy: newReferralCode,
                },
              },
            });
          }
        }

        // Update user with new referral code (no history for new code until it has data)
        await tx.user.update({
          where: { id: userData.id },
          data: { referralCode: newReferralCode },
        });
      });

      const baseUrl = process.env.PUBLIC_BASE_URL || 'http://localhost:5173';
      const referralLink = `${baseUrl}/auth/sign-up?ref=${newReferralCode}`;

      return json({
        referralCode: newReferralCode,
        referralLink,
        message: 'Referral code updated successfully',
      });
    }

    return json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Error updating referral code:', error);
    return json({ error: 'Failed to update referral code' }, { status: 500 });
  }
};
