// src/routes/api/notifications/history/+server.ts
import { json } from '@sveltejs/kit';
import { getUserFromToken } from '$lib/server/auth';
import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types';

/**
 * Get notification history for admin users
 *
 * This endpoint returns a list of sent notifications for admin users.
 * It requires authentication and the user must have admin privileges.
 */
export const GET: RequestHandler = async ({ cookies, url }) => {
  // Authenticate the user
  const user = await getUserFromToken(cookies);

  if (!user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Check if user is an admin
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
    select: { isAdmin: true, role: true },
  });

  // Allow access if either isAdmin is true OR role is 'admin'
  if (!userData || (!userData.isAdmin && userData.role !== 'admin')) {
    return json({ error: 'Unauthorized' }, { status: 403 });
  }

  try {
    // Get query parameters
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const type = url.searchParams.get('type') || undefined;

    // Check if the Notification model exists
    let notifications = [];
    let totalCount = 0;

    try {
      // Build the where clause
      const whereClause: any = {};

      // Filter by type if specified
      if (type) {
        whereClause.type = type;
      }

      // Get notifications with sender information
      try {
        // Get notifications from the database with pagination and filtering
        notifications = await prisma.notification.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset,
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
              },
            },
          },
        });

        // Get total count for pagination
        totalCount = await prisma.notification.count({
          where: whereClause,
        });

        console.log(`Found ${notifications.length} notifications in database`);
        if (notifications.length > 0) {
          console.log('First notification:', JSON.stringify(notifications[0]));
        }
      } catch (modelError) {
        console.warn('Error getting notifications:', modelError);
        // Return empty data if there's an error
        return json({
          success: true,
          notifications: [],
          pagination: {
            total: 0,
            limit,
            offset,
          },
        });
      }
    } catch (error) {
      console.error('Error getting notification history:', error);
      return json({ error: 'Failed to get notification history' }, { status: 500 });
    }

    // Format the response
    const formattedNotifications = notifications.map((notification) => {
      // Count recipients for non-global notifications
      let recipients = 'Unknown';
      if (notification.global) {
        recipients = 'All Users';
      } else if (notification.userId) {
        recipients = '1 User';
      }

      return {
        id: notification.id,
        title: notification.title,
        message: notification.message,
        url: notification.url,
        type: notification.type,
        global: notification.global,
        sentAt: notification.createdAt,
        sentBy: notification.user?.email || user.email, // Use the notification user or current user as the sender
        recipients,
        metadata: notification.metadata ? JSON.parse(notification.metadata as string) : null,
      };
    });

    return json({
      success: true,
      notifications: formattedNotifications,
      pagination: {
        total: totalCount,
        limit,
        offset,
      },
    });
  } catch (error) {
    console.error('Error getting notification history:', error);
    return json({ error: 'Failed to get notification history' }, { status: 500 });
  }
};
