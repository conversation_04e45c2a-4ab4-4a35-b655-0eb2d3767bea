// File: web/src/routes/api/resume/manual-parse/+server.ts
import { json } from '@sveltejs/kit';
import { getRedisClient } from '$lib/server/redis';
import { prisma } from '$lib/server/prisma';
import type { <PERSON>questHand<PERSON> } from './$types';

/**
 * POST handler to manually trigger resume parsing with detailed error reporting
 */
export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    // Get the user from locals
    const user = locals.user;
    if (!user) return new Response('Unauthorized', { status: 401 });

    // Get the resume ID from the request body
    const { resumeId } = await request.json();
    
    if (!resumeId) {
      return json({ error: 'Resume ID is required' }, { status: 400 });
    }

    console.log(`Manually parsing resume ${resumeId}`);

    // Check if the resume exists and belongs to the user
    const resume = await prisma.resume.findUnique({
      where: { id: resumeId },
      include: {
        document: true,
      },
    });

    if (!resume) {
      return json({ error: 'Resume not found' }, { status: 404 });
    }

    if (resume.document.userId !== user.id) {
      return json({ error: 'Unauthorized access to resume' }, { status: 403 });
    }

    // Get the profile ID if the document is associated with a profile
    const profileId = resume.document.profileId;

    // Reset the resume parsing status
    await prisma.resume.update({
      where: { id: resumeId },
      data: {
        isParsed: false,
        parsedAt: null,
      },
    });

    // Create a WorkerProcess record for this resume parsing job
    const workerProcessId = `resume-parsing-${resumeId}-${Date.now()}`;
    
    try {
      // Create a new record
      await prisma.workerProcess.create({
        data: {
          id: workerProcessId,
          type: 'resume-parsing',
          status: 'pending',
          createdAt: new Date(),
          updatedAt: new Date(),
          data: {
            resumeId,
            profileId: profileId || null,
            userId: user.id,
            documentId: resume.documentId,
            timestamp: new Date().toISOString(),
            fileUrl: resume.document?.fileUrl ?? null,
            filePath: resume.document?.filePath ?? null,
          },
        },
      });
      console.log(`Created WorkerProcess record ${workerProcessId}`);
    } catch (workerProcessError) {
      console.error('Error creating WorkerProcess record:', workerProcessError);
      return json({ 
        error: 'Failed to create worker process record',
        details: String(workerProcessError)
      }, { status: 500 });
    }

    // Add to Redis queue
    try {
      const redis = await getRedisClient();
      if (!redis) {
        throw new Error('Redis client not available');
      }

      // Use the correct stream name
      const streamName = 'resume-parsing::stream';
      
      // Create stream group if it doesn't exist
      try {
        await redis.xgroup('CREATE', streamName, 'resume-parsing::group', '$', 'MKSTREAM');
        console.log(`Created ${streamName} stream group`);
      } catch (err: any) {
        // Ignore BUSYGROUP error (group already exists)
        if (!err.message.includes('BUSYGROUP')) {
          console.error(`Error creating stream group for ${streamName}:`, err);
        }
      }

      // Add job to Redis stream
      const messageId = await redis.xadd(
        streamName,
        '*',
        'job',
        JSON.stringify({
          jobId: workerProcessId,
          resumeId,
          fileUrl: resume.document?.fileUrl ?? null,
          filePath: resume.document?.filePath ?? null,
          userId: user.id,
          profileId: profileId || null,
          timestamp: new Date().toISOString(),
        })
      );
      console.log(`Added job to ${streamName} with message ID ${messageId}`);

      // Disconnect from Redis
      redis.disconnect();
      
      return json({
        success: true,
        message: 'Resume parsing job added to the stream',
        resumeId,
        profileId: profileId || null,
        streamName,
        messageId,
        workerProcessId,
      });
    } catch (redisError) {
      console.error('Redis error:', redisError);
      
      // Update the WorkerProcess record to failed
      try {
        await prisma.workerProcess.update({
          where: { id: workerProcessId },
          data: {
            status: 'failed',
            error: String(redisError),
            updatedAt: new Date(),
          },
        });
      } catch (updateError) {
        console.error('Error updating WorkerProcess record:', updateError);
      }
      
      return json(
        { 
          error: 'Failed to add job to Redis', 
          details: String(redisError),
          workerProcessId
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error manually parsing resume:', error);
    return json(
      { error: 'Failed to manually parse resume', details: String(error) },
      { status: 500 }
    );
  }
};
