<script lang="ts">
  import { store, type NotificationType, type NotificationChannel } from '$lib/stores/store';
  import * as Card from '$lib/components/ui/card';
  import { Switch } from '$lib/components/ui/switch';
  import { Button } from '$lib/components/ui/button';
  import { Bell, Mail, BellRing, MessageSquare } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';

  // State
  let isSaving = $state(false);

  // Notification type labels and icons
  const notificationTypes: Array<{
    id: NotificationType;
    label: string;
    description: string;
    icon: any;
  }> = [
    {
      id: 'job_alerts',
      label: 'Job Alerts',
      description: 'Notifications about new job matches and opportunities',
      icon: Bell,
    },
    {
      id: 'application_updates',
      label: 'Application Updates',
      description: 'Updates on your job applications and status changes',
      icon: BellRing,
    },
    {
      id: 'messages',
      label: 'Messages',
      description: 'Notifications about new messages and responses',
      icon: MessageSquare,
    },
    {
      id: 'system',
      label: 'System Notifications',
      description: 'Important system updates and announcements',
      icon: Mail,
    },
  ];

  // Channel labels
  const channelLabels = {
    email: 'Email',
    push: 'Push Notifications',
    in_app: 'In-App Notifications',
  };

  // Toggle notification type
  function toggleNotificationType(type: NotificationType, enabled?: boolean) {
    store.update((state: any) => ({
      ...state,
      notificationPreferences: {
        ...state.notificationPreferences,
        [type]: {
          ...state.notificationPreferences[type],
          enabled: enabled !== undefined ? enabled : !state.notificationPreferences[type].enabled,
        },
      },
    }));
  }

  // Toggle notification channel
  function toggleNotificationChannel(
    type: NotificationType,
    channel: NotificationChannel,
    enabled?: boolean
  ) {
    store.update((state: any) => ({
      ...state,
      notificationPreferences: {
        ...state.notificationPreferences,
        [type]: {
          ...state.notificationPreferences[type],
          channels: {
            ...state.notificationPreferences[type].channels,
            [channel]:
              enabled !== undefined
                ? enabled
                : !state.notificationPreferences[type].channels[channel],
          },
        },
      },
    }));
  }

  // Save preferences
  async function handleSave() {
    isSaving = true;
    try {
      // Get current preferences from store
      const preferences = $store.notificationPreferences;

      // Save to API
      const response = await fetch('/api/notification-preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ preferences }),
      });

      if (response.ok) {
        toast.success('Notification preferences saved');
      } else {
        toast.error('Failed to save notification preferences');
      }
    } catch (error) {
      toast.error('An error occurred while saving preferences');
      console.error('Error saving notification preferences:', error);
    } finally {
      isSaving = false;
    }
  }
</script>

<Card.Root>
  <Card.Header>
    <Card.Title>Notification Preferences</Card.Title>
    <Card.Description>Manage how and when you receive notifications</Card.Description>
  </Card.Header>
  <Card.Content class="space-y-6">
    {#each notificationTypes as type}
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            {#if type.id === 'job_alerts'}
              <Bell class="h-5 w-5" />
            {:else if type.id === 'application_updates'}
              <BellRing class="h-5 w-5" />
            {:else if type.id === 'messages'}
              <MessageSquare class="h-5 w-5" />
            {:else if type.id === 'system'}
              <Mail class="h-5 w-5" />
            {/if}
            <div>
              <h4 class="font-medium">{type.label}</h4>
              <p class="text-muted-foreground text-sm">{type.description}</p>
            </div>
          </div>
          <Switch
            checked={$store.notificationPreferences[type.id].enabled}
            onCheckedChange={(checked) => toggleNotificationType(type.id, checked)} />
        </div>

        {#if $store.notificationPreferences[type.id].enabled}
          <div class="ml-7 space-y-2 border-l pl-4">
            {#each Object.entries(channelLabels) as [channel, label]}
              <div class="flex items-center justify-between">
                <span class="text-sm">{label}</span>
                <Switch
                  checked={$store.notificationPreferences[type.id].channels[channel]}
                  onCheckedChange={(checked) =>
                    toggleNotificationChannel(type.id, channel as NotificationChannel, checked)} />
              </div>
            {/each}
          </div>
        {/if}
      </div>
    {/each}
  </Card.Content>
  <Card.Footer class="flex justify-end">
    <Button onclick={handleSave} disabled={isSaving}>
      {isSaving ? 'Saving...' : 'Save Preferences'}
    </Button>
  </Card.Footer>
</Card.Root>
