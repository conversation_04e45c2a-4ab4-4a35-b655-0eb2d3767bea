// src/routes/api/feature-usage/reset/+server.ts
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { verifySessionToken } from '$lib/server/auth';
import { prisma } from '$lib/server/prisma';
import { featureTablesExist } from '$lib/server/feature-usage';

/**
 * Reset feature usage for the current user
 * This is useful for testing and debugging
 */
export const POST: RequestHandler = async ({ cookies, request }) => {
  const token = cookies.get('auth_token');

  if (!token) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userData = verifySessionToken(token);

  if (!userData?.id) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Check if the tables exist
    const tablesExist = await featureTablesExist();
    if (!tablesExist) {
      return json({ error: 'Feature usage tables do not exist yet' }, { status: 400 });
    }

    const { featureId, limitId } = await request.json();

    // If featureId and limitId are provided, reset only that specific usage
    if (featureId && limitId) {
      await prisma.featureUsage.updateMany({
        where: {
          userId: userData.id,
          featureId,
          limitId,
        },
        data: {
          used: 0,
          updatedAt: new Date(),
        },
      });

      return json({
        success: true,
        message: `Usage for feature ${featureId} and limit ${limitId} has been reset`,
      });
    }
    
    // If only featureId is provided, reset all usage for that feature
    else if (featureId) {
      await prisma.featureUsage.updateMany({
        where: {
          userId: userData.id,
          featureId,
        },
        data: {
          used: 0,
          updatedAt: new Date(),
        },
      });

      return json({
        success: true,
        message: `All usage for feature ${featureId} has been reset`,
      });
    }
    
    // If no specific feature or limit is provided, reset all usage
    else {
      await prisma.featureUsage.updateMany({
        where: {
          userId: userData.id,
        },
        data: {
          used: 0,
          updatedAt: new Date(),
        },
      });

      return json({
        success: true,
        message: 'All feature usage has been reset',
      });
    }
  } catch (error) {
    console.error('Error resetting feature usage:', error);
    return json(
      {
        success: false,
        error: error.message || 'An error occurred while resetting feature usage',
      },
      { status: 500 }
    );
  }
};
