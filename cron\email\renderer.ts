// cron/email/renderer.ts
import path from "path";
import fs from "fs";
import { logger } from "../utils/logger";

// Path to email templates
const TEMPLATES_DIR = path.join(process.cwd(), "email/templates");

/**
 * Email template types
 */
export enum EmailTemplate {
  SYSTEM_ALERT = "system-alert",
  ERROR_REPORT = "error-report",
  USAGE_REPORT = "usage-report",
  JOB_SUMMARY = "job-summary",
}

/**
 * Email template result
 */
export interface EmailTemplateResult {
  subject: string;
  html: string;
  text?: string;
}

/**
 * Render an email template with the provided data
 *
 * @param template The email template to render
 * @param data Data to populate the template
 * @returns Promise with the rendered email (subject, html, text)
 */
export async function renderEmailTemplate(
  template: EmailTemplate,
  data: Record<string, any> = {}
): Promise<EmailTemplateResult | null> {
  try {
    logger.info(`🔄 Rendering email template: ${template}`);

    // Log template data for debugging
    if (template === EmailTemplate.JOB_SUMMARY) {
      logger.info(`📊 Job Summary Template Data:`, {
        jobsProcessed: data.jobsProcessed,
        jobsSucceeded: data.jobsSucceeded,
        jobsFailed: data.jobsFailed,
        processingTime: data.processingTime,
        reportDate: data.reportDate,
      });
    }

    // Add common data
    const templateData = {
      ...data,
      appName: process.env.APP_NAME || "Hirli",
      appUrl: process.env.PUBLIC_BASE_URL || "https://hirli.co",
      supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
      year: new Date().getFullYear(),
    };

    // Check if template directory exists
    const templateDir = path.join(TEMPLATES_DIR, template);
    if (!fs.existsSync(templateDir)) {
      logger.error(`Template directory not found: ${templateDir}`);
      throw new Error(`Template directory not found: ${templateDir}`);
    }

    // Check if template files exist
    const htmlTemplate = path.join(templateDir, "html.pug");
    if (!fs.existsSync(htmlTemplate)) {
      logger.error(`HTML template file not found: ${htmlTemplate}`);
      throw new Error(`HTML template file not found: ${htmlTemplate}`);
    }

    // Try to render the template with pug
    try {
      // Import pug dynamically
      const pug = await import("pug");

      // Get the template files
      const htmlTemplatePath = path.join(templateDir, "html.pug");
      const subjectTemplatePath = path.join(templateDir, "subject.pug");

      logger.info(`Rendering Pug template for ${template}`);

      // Compile and render the HTML template
      const htmlFn = pug.compileFile(htmlTemplatePath, {
        basedir: TEMPLATES_DIR,
        filename: htmlTemplatePath,
      });
      const html = htmlFn(templateData);

      // Compile and render the subject template if it exists
      let subject = "";
      if (fs.existsSync(subjectTemplatePath)) {
        const subjectFn = pug.compileFile(subjectTemplatePath, {
          basedir: TEMPLATES_DIR,
          filename: subjectTemplatePath,
        });
        subject = subjectFn(templateData);
      } else {
        // If no subject template, use a default subject
        subject = getDefaultSubject(template, templateData);
      }

      logger.info(`✅ Template rendered successfully: ${template}`);

      // Create a result object
      return {
        html,
        subject,
        text: html
          .replace(/<[^>]*>/g, " ")
          .replace(/\s+/g, " ")
          .trim(), // Simple HTML to text conversion
      };
    } catch (pugError) {
      logger.error(`Error rendering Pug template ${template}:`, pugError);
      throw pugError;
    }
  } catch (error) {
    logger.error(`❌ Error rendering email template ${template}:`, error);

    // Return a fallback template in case of error
    return {
      subject: getDefaultSubject(template, data),
      html: `<p>Error rendering email template. Please contact support.</p>`,
      text: `Error rendering email template. Please contact support.`,
    };
  }
}

/**
 * Get a default subject for a template
 *
 * @param template The email template
 * @param data Template data
 * @returns Default subject for the template
 */
function getDefaultSubject(
  template: EmailTemplate,
  data: Record<string, any>
): string {
  const appName = process.env.APP_NAME || "Hirli";
  const dateStr = data.reportDate ? ` | ${data.reportDate}` : "";

  switch (template) {
    case EmailTemplate.SYSTEM_ALERT:
      return data.alertTitle || `⚠️ ${appName} | System Alert`;
    case EmailTemplate.ERROR_REPORT:
      return data.errorTitle || `❌ ${appName} | Error Report${dateStr}`;
    case EmailTemplate.USAGE_REPORT:
      return data.reportTitle || `📈 ${appName} | Usage Report${dateStr}`;
    case EmailTemplate.JOB_SUMMARY:
      // Get job counts for subject if available
      const countStr =
        data.jobsProcessed !== undefined ? ` | ${data.jobsProcessed} Jobs` : "";
      return (
        data.reportTitle || `📊 ${appName} | Job Summary${dateStr}${countStr}`
      );
    default:
      return `${appName} | Notification`;
  }
}
