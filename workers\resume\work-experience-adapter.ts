/**
 * Work Experience Adapter
 *
 * This module provides an adapter to use the improved work experience extractor
 * from the work-experience-extractor.cjs file in the TypeScript codebase.
 */

import { extractSkillsFromDescriptions } from "./extraction-utils";

/**
 * Extract work experience from text using the improved extractor
 * @param text Text to extract work experience from
 * @returns Array of work experience objects
 */
export function extractWorkExperience(text: string): any[] {
  try {
    // First try the generic resume parser
    try {
      const genericResumeParser = require("./generic-resume-parser.cjs");
      const genericExtractedExperience =
        genericResumeParser.extractWorkExperience(text);

      if (genericExtractedExperience && genericExtractedExperience.length > 0) {
        console.log(
          `Found ${genericExtractedExperience.length} work experiences using generic resume parser`
        );
        return genericExtractedExperience.map((job) => ({
          title: job.title || "",
          company: job.company || "",
          location: job.location || "",
          startDate: job.startDate || "",
          endDate: job.endDate || "",
          date:
            job.startDate && job.endDate
              ? `${job.startDate} - ${job.endDate}`
              : "",
          descriptions: job.descriptions || [],
          skills: job.skills || [],
        }));
      }
    } catch (genericError) {
      console.error("Error using generic resume parser:", genericError);
    }

    // If the generic parser failed, try the final resume extractor
    try {
      const finalResumeExtractor = require("./final-resume-extractor.cjs");
      const finalExtractedExperience =
        finalResumeExtractor.extractWorkExperience(text);

      if (finalExtractedExperience && finalExtractedExperience.length > 0) {
        console.log(
          `Found ${finalExtractedExperience.length} work experiences using final resume extractor`
        );
        return finalExtractedExperience.map((job) => ({
          title: job.title || "",
          company: job.company || "",
          location: job.location || "",
          startDate: job.startDate || "",
          endDate: job.endDate || "",
          date:
            job.startDate && job.endDate
              ? `${job.startDate} - ${job.endDate}`
              : "",
          descriptions: job.descriptions || [],
          skills: job.skills || [],
        }));
      }
    } catch (finalError) {
      console.error("Error using final resume extractor:", finalError);
    }

    // If both parsers failed, try the improved work experience extractor
    try {
      const workExperienceExtractor = require("./improved-work-experience-extractor.cjs");
      const extractedExperience =
        workExperienceExtractor.extractWorkExperience(text);

      // If we got results, return them
      if (extractedExperience && extractedExperience.length > 0) {
        console.log(
          `Found ${extractedExperience.length} work experiences using improved work experience extractor`
        );
        return extractedExperience.map((job) => ({
          title: job.title || "",
          company: job.company || "",
          location: job.location || "",
          startDate: job.startDate || "",
          endDate: job.endDate || "",
          date:
            job.startDate && job.endDate
              ? `${job.startDate} - ${job.endDate}`
              : "",
          descriptions: job.descriptions || [],
          skills: job.skills || [],
        }));
      }
    } catch (improvedError) {
      console.error(
        "Error using improved work experience extractor:",
        improvedError
      );
    }

    // If all extractors failed, try the original work experience extractor
    try {
      const originalExtractor = require("./work-experience-extractor.cjs");
      const originalExperience = originalExtractor.extractWorkExperience(text);

      if (originalExperience && originalExperience.length > 0) {
        console.log(
          `Found ${originalExperience.length} work experiences using original work experience extractor`
        );
        return originalExperience.map((job) => ({
          title: job.title || "",
          company: job.company || "",
          location: job.location || "",
          startDate: job.startDate || "",
          endDate: job.endDate || "",
          date:
            job.startDate && job.endDate
              ? `${job.startDate} - ${job.endDate}`
              : "",
          descriptions: job.descriptions || [],
          skills: job.skills || [],
        }));
      }
    } catch (originalError) {
      console.error(
        "Error using original work experience extractor:",
        originalError
      );
    }
  } catch (error) {
    console.error("Error using work experience extractors:", error);
    // Fall back to the original implementation if there's an error
  }

  // If all extractors failed or returned no results, return an empty array
  // The original implementation in improved-extractors.ts will be used
  return [];
}

/**
 * Improve work experience entries by adding missing information
 * @param experience Work experience entries
 * @returns Improved work experience entries
 */
export function improveWorkExperienceEntries(experience: any[]): any[] {
  // Enhance existing work experience entries
  for (const entry of experience) {
    // If skills are missing, extract them from descriptions
    if (!entry.skills || entry.skills.length === 0) {
      entry.skills = extractSkillsFromDescriptions(entry.descriptions || []);
    }

    // If date is missing but we have startDate and endDate, create it
    if (!entry.date && entry.startDate && entry.endDate) {
      entry.date = `${entry.startDate} - ${entry.endDate}`;
    }

    // If we have date but no startDate/endDate, try to extract them
    if (entry.date && (!entry.startDate || !entry.endDate)) {
      const dateParts = entry.date.split(/\s*[-–]\s*/);
      if (dateParts.length === 2) {
        entry.startDate = dateParts[0].trim();
        entry.endDate = dateParts[1].trim();
      }
    }
  }

  return experience;
}
