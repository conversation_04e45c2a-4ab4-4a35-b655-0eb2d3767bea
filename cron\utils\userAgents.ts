// Browser fingerprinting utilities

// Note: We're using the puppeteer-extra-plugin-stealth library for user agents
// This file only contains the viewport size function

/**
 * Generate a random viewport size to help with fingerprinting evasion
 * Limited to max 960x960 as requested
 */
export function getRandomViewport() {
  // Define viewport sizes that are common but limited to max 960x960
  const desktopViewports = [
    { width: 800, height: 600 }, // Classic size
    { width: 960, height: 720 }, // 4:3 aspect ratio within limits
    { width: 960, height: 540 }, // 16:9 aspect ratio within limits
    { width: 800, height: 450 }, // 16:9 aspect ratio smaller
    { width: 960, height: 600 }, // 16:10 aspect ratio
    { width: 900, height: 640 }, // Common size within limits
  ];

  const mobileViewports = [
    { width: 375, height: 667 }, // iPhone 8
    { width: 390, height: 844 }, // iPhone 12/13/14 (will be scaled down)
    { width: 360, height: 740 }, // Samsung Galaxy (will be scaled down)
    { width: 320, height: 568 }, // iPhone 5/SE
    { width: 412, height: 915 }, // Pixel 6 (will be scaled down)
  ];

  // 80% chance of desktop, 20% chance of mobile
  const viewports = Math.random() < 0.8 ? desktopViewports : mobileViewports;

  // Add some random variation to the exact dimensions
  const baseViewport = viewports[Math.floor(Math.random() * viewports.length)];

  // Calculate dimensions with random variation
  let width = baseViewport.width + Math.floor(Math.random() * 20) - 10; // +/- 10px
  let height = baseViewport.height + Math.floor(Math.random() * 20) - 10; // +/- 10px

  // Ensure dimensions don't exceed 960x960
  width = Math.min(width, 960);
  height = Math.min(height, 960);

  return { width, height };
}

// Headers are now handled by the puppeteer-extra-plugin-stealth library
