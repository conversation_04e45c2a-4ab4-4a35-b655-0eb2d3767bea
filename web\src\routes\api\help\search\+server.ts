// src/routes/api/help/search/+server.ts
import { json } from '@sveltejs/kit';
import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';

// Search help articles
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    const query = url.searchParams.get('q') || '';
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const page = parseInt(url.searchParams.get('page') || '1');
    const skip = (page - 1) * limit;
    const user = locals.user;

    if (!query || query.length < 2) {
      return json({
        articles: [],
        pagination: {
          total: 0,
          page,
          limit,
          pages: 0,
        },
      });
    }

    // Build the search query
    const searchQuery = {
      OR: [
        {
          title: {
            contains: query,
            mode: 'insensitive',
          },
        },
        {
          content: {
            contains: query,
            mode: 'insensitive',
          },
        },
        {
          excerpt: {
            contains: query,
            mode: 'insensitive',
          },
        },
        {
          category: {
            name: {
              contains: query,
              mode: 'insensitive',
            },
          },
        },
        {
          tags: {
            some: {
              name: {
                contains: query,
                mode: 'insensitive',
              },
            },
          },
        },
      ],
      published: true,
    };

    // Get search results from the database
    const [articles, totalCount] = await Promise.all([
      prisma.helpArticle.findMany({
        where: searchQuery,
        orderBy: [
          {
            title: {
              // If title contains the exact query, prioritize it
              similarity: query,
            },
          },
          {
            viewCount: 'desc',
          },
        ],
        skip,
        take: limit,
        include: {
          category: true,
          tags: true,
        },
      }),
      prisma.helpArticle.count({
        where: searchQuery,
      }),
    ]);

    // Log the search query for analytics
    if (query.trim()) {
      await prisma.helpSearch.create({
        data: {
          query: query.trim(),
          userId: user?.id,
          resultCount: totalCount,
        },
      });
    }

    return json({
      articles,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error('Error searching help articles:', error);
    return json({ error: 'Failed to search help articles' }, { status: 500 });
  }
};
