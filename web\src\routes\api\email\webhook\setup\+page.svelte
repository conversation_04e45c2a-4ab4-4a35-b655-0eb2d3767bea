<!--
  Resend Webhook Setup Guide
  
  This page provides instructions for setting up Resend webhooks.
-->
<script>
  import { page } from '$app/stores';
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { Separator } from '$lib/components/ui/separator';
  import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
  import { Tabs, TabsContent, TabsList, TabsTrigger } from '$lib/components/ui/tabs';
  import { Clipboard } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  
  // State
  let baseUrl = '';
  let webhookUrl = '';
  let webhookSecret = '';
  let testResult = null;
  let isLoading = false;
  let activeTab = 'setup';
  
  // Generate a random webhook secret
  function generateSecret() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    webhookSecret = Array.from(array)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }
  
  // Copy text to clipboard
  function copyToClipboard(text) {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  }
  
  // Test the webhook endpoint
  async function testWebhook() {
    isLoading = true;
    testResult = null;
    
    try {
      const response = await fetch('/api/email/webhook/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'email.delivered',
          email: '<EMAIL>',
        }),
      });
      
      const data = await response.json();
      testResult = {
        success: response.ok,
        status: response.status,
        data,
      };
      
      if (response.ok) {
        toast.success('Test webhook processed successfully');
      } else {
        toast.error('Test webhook failed');
      }
    } catch (error) {
      testResult = {
        success: false,
        error: error.message,
      };
      toast.error('Error testing webhook');
    } finally {
      isLoading = false;
    }
  }
  
  // Check if the EmailEvent table exists
  async function checkTable() {
    isLoading = true;
    
    try {
      const response = await fetch('/api/email/analytics/check');
      const data = await response.json();
      
      if (data.exists) {
        toast.success(`EmailEvent table exists with ${data.count} records`);
      } else {
        toast.error('EmailEvent table does not exist');
      }
      
      return data;
    } catch (error) {
      toast.error('Error checking EmailEvent table');
      return { exists: false, error: error.message };
    } finally {
      isLoading = false;
    }
  }
  
  // Initialize the page
  onMount(() => {
    // Get the base URL
    baseUrl = `${window.location.protocol}//${window.location.host}`;
    webhookUrl = `${baseUrl}/api/email/webhook`;
    
    // Generate a webhook secret
    if (!webhookSecret) {
      generateSecret();
    }
  });
</script>

<div class="container mx-auto py-8 max-w-4xl">
  <h1 class="text-3xl font-bold mb-6">Resend Webhook Setup</h1>
  
  <Tabs bind:value={activeTab}>
    <TabsList class="grid w-full grid-cols-3">
      <TabsTrigger value="setup">Setup Guide</TabsTrigger>
      <TabsTrigger value="test">Test Webhook</TabsTrigger>
      <TabsTrigger value="events">Event Types</TabsTrigger>
    </TabsList>
    
    <TabsContent value="setup">
      <Card>
        <CardHeader>
          <CardTitle>Setup Resend Webhooks</CardTitle>
          <CardDescription>
            Follow these steps to configure webhooks in your Resend account.
          </CardDescription>
        </CardHeader>
        
        <CardContent class="space-y-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium">1. Generate a Webhook Secret</h3>
            <div class="flex gap-2">
              <Input value={webhookSecret} readonly />
              <Button variant="outline" on:click={() => copyToClipboard(webhookSecret)}>
                <Clipboard class="h-4 w-4 mr-2" />
                Copy
              </Button>
              <Button variant="outline" on:click={generateSecret}>Regenerate</Button>
            </div>
            <p class="text-sm text-muted-foreground">
              This secret will be used to verify webhook requests from Resend.
            </p>
          </div>
          
          <Separator />
          
          <div class="space-y-2">
            <h3 class="text-lg font-medium">2. Set Environment Variable</h3>
            <Alert>
              <AlertTitle>Add this to your .env file</AlertTitle>
              <AlertDescription>
                <code class="bg-muted p-2 rounded block">
                  RESEND_WEBHOOK_SECRET={webhookSecret}
                </code>
                <Button variant="outline" class="mt-2" on:click={() => copyToClipboard(`RESEND_WEBHOOK_SECRET=${webhookSecret}`)}>
                  <Clipboard class="h-4 w-4 mr-2" />
                  Copy
                </Button>
              </AlertDescription>
            </Alert>
          </div>
          
          <Separator />
          
          <div class="space-y-2">
            <h3 class="text-lg font-medium">3. Configure Webhook in Resend Dashboard</h3>
            <p>Go to the <a href="https://resend.com/webhooks" target="_blank" class="text-primary underline">Resend Webhooks page</a> and add a new webhook with these details:</p>
            
            <div class="space-y-4 mt-4">
              <div>
                <Label for="webhook-url">Webhook URL</Label>
                <div class="flex gap-2">
                  <Input id="webhook-url" value={webhookUrl} readonly />
                  <Button variant="outline" on:click={() => copyToClipboard(webhookUrl)}>
                    <Clipboard class="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
              </div>
              
              <div>
                <Label for="webhook-secret">Webhook Secret</Label>
                <div class="flex gap-2">
                  <Input id="webhook-secret" value={webhookSecret} readonly />
                  <Button variant="outline" on:click={() => copyToClipboard(webhookSecret)}>
                    <Clipboard class="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
              </div>
            </div>
          </div>
          
          <Separator />
          
          <div class="space-y-2">
            <h3 class="text-lg font-medium">4. Select Event Types</h3>
            <p>Enable these event types in the Resend dashboard:</p>
            <ul class="list-disc pl-6 space-y-1">
              <li>email.delivered</li>
              <li>email.opened</li>
              <li>email.clicked</li>
              <li>email.bounced</li>
              <li>email.complained</li>
            </ul>
          </div>
        </CardContent>
        
        <CardFooter class="flex justify-between">
          <Button variant="outline" on:click={checkTable}>
            Check EmailEvent Table
          </Button>
          <Button on:click={() => activeTab = 'test'}>
            Test Webhook
          </Button>
        </CardFooter>
      </Card>
    </TabsContent>
    
    <TabsContent value="test">
      <Card>
        <CardHeader>
          <CardTitle>Test Webhook</CardTitle>
          <CardDescription>
            Send a test webhook event to verify your setup.
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div class="space-y-4">
            <Button on:click={testWebhook} disabled={isLoading}>
              {#if isLoading}
                Testing...
              {:else}
                Send Test Event
              {/if}
            </Button>
            
            {#if testResult}
              <div class="mt-4">
                <h3 class="text-lg font-medium mb-2">Test Result</h3>
                <div class="bg-muted p-4 rounded overflow-auto max-h-96">
                  <pre>{JSON.stringify(testResult, null, 2)}</pre>
                </div>
              </div>
            {/if}
          </div>
        </CardContent>
      </Card>
    </TabsContent>
    
    <TabsContent value="events">
      <Card>
        <CardHeader>
          <CardTitle>Resend Event Types</CardTitle>
          <CardDescription>
            These are the event types that Resend can send to your webhook endpoint.
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="border rounded p-4">
                <h3 class="font-medium">email.delivered</h3>
                <p class="text-sm text-muted-foreground">Triggered when an email is successfully delivered to the recipient's mail server.</p>
              </div>
              
              <div class="border rounded p-4">
                <h3 class="font-medium">email.opened</h3>
                <p class="text-sm text-muted-foreground">Triggered when a recipient opens an email (requires tracking pixel).</p>
              </div>
              
              <div class="border rounded p-4">
                <h3 class="font-medium">email.clicked</h3>
                <p class="text-sm text-muted-foreground">Triggered when a recipient clicks a link in an email (requires link tracking).</p>
              </div>
              
              <div class="border rounded p-4">
                <h3 class="font-medium">email.bounced</h3>
                <p class="text-sm text-muted-foreground">Triggered when an email permanently bounces.</p>
              </div>
              
              <div class="border rounded p-4">
                <h3 class="font-medium">email.complained</h3>
                <p class="text-sm text-muted-foreground">Triggered when a recipient marks an email as spam.</p>
              </div>
              
              <div class="border rounded p-4">
                <h3 class="font-medium">email.delivery_delayed</h3>
                <p class="text-sm text-muted-foreground">Triggered when email delivery is temporarily delayed.</p>
              </div>
            </div>
            
            <div class="mt-4">
              <a href="https://resend.com/docs/dashboard/webhooks/event-types" target="_blank" class="text-primary underline">
                View Resend Webhook Documentation
              </a>
            </div>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  </Tabs>
</div>
