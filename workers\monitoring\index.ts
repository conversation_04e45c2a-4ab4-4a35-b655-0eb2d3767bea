/**
 * Worker Monitoring System
 *
 * This module provides a centralized monitoring system for all worker types.
 * It tracks worker health, collects metrics, and implements circuit breaker patterns
 * to prevent cascading failures.
 *
 * Features:
 * - Worker registry for tracking all active workers
 * - Health metrics collection and reporting
 * - Circuit breaker implementation
 * - Worker ping responder
 * - Resource usage monitoring
 */

import os from "node:os";
import { redis } from "../redis.js";
import { createLogger } from "../utils/logger.js";
import { getContainerMetrics } from "../utils/containerMetrics.js";

// Create a logger for the monitoring system
const logger = createLogger("worker-monitoring");

// Redis keys
const WORKER_REGISTRY_KEY = "worker:registry";
const WORKER_HEALTH_KEY = "worker:health";
const WORKER_METRICS_KEY = "worker:metrics";
const WORKER_CIRCUIT_KEY = "worker:circuit";
const WORKER_PING_CHANNEL = "worker-ping";

// Worker types
export enum WorkerType {
  RESUME_PARSING = "resume-parsing",
  RESUME_OPTIMIZATION = "resume-optimization",
  SEARCH = "search",
  ATS_ANALYSIS = "ats-analysis",
  JOB_SPECIFIC_ANALYSIS = "job-specific-analysis",
  EMAIL = "email",
  AUTOMATION = "automation",
  // Cron job types for monitoring integration
  PARALLEL_JOB_SCRAPER = "parallelJobScraper",
  ENRICH_JOB_DETAILS = "enrichJobDetails",
  SCRAPE_JOB_DETAILS = "scrapeJobDetails",
}

// Worker status types
export enum WorkerStatus {
  HEALTHY = "healthy",
  DEGRADED = "degraded",
  UNHEALTHY = "unhealthy",
  UNKNOWN = "unknown",
}

// Circuit breaker states
export enum CircuitState {
  CLOSED = "closed", // Normal operation
  OPEN = "open", // Not accepting requests
  HALF_OPEN = "half-open", // Testing if service is healthy again
}

// Worker health interface
export interface WorkerHealth {
  status: WorkerStatus;
  healthy: boolean;
  lastHeartbeat: string;
  hostname: string;
  pid: number;
}

// Worker metrics interface
export interface WorkerMetrics {
  cpu: number; // CPU usage percentage
  memory: number; // Memory usage percentage
  queueSize: number; // Number of items in queue
  processingCount: number; // Number of items being processed
  responseTime: number; // Average response time in ms
  errorRate: number; // Error rate percentage
  successRate: number; // Success rate percentage
  capacity: number; // Available capacity percentage (0-100)
  uptime: number; // Worker uptime in seconds
}

// Worker registry interface
export interface WorkerRegistration {
  id: string; // Unique worker ID
  type: WorkerType; // Worker type
  hostname: string; // Hostname
  pid: number; // Process ID
  startTime: string; // Start time
  version: string; // Worker version
}

// Performance metrics
const performanceMetrics: Record<
  string,
  {
    requestCount: number;
    errorCount: number;
    totalResponseTime: number;
    queueSize: number;
    processingCount: number;
  }
> = {};

// Metrics collection state
let cpuUsage = process.cpuUsage();
let lastCpuUsageTime = process.hrtime.bigint();

// Track last circuit breaker log time to reduce logging frequency
let lastCircuitBreakerLogTime = 0;

// Track last metrics for circuit breaker decisions
let lastMetrics: { memory: number; cpu: number } | null = null;

// Track last health report time to reduce log frequency
let lastHealthReportTime = 0;

// Track last container metrics log time to reduce debug spam
let lastContainerMetricsLogTime = 0;

/**
 * Helper function to check if we should log circuit breaker status
 * Only log when the circuit is in a non-closed state or when there are running jobs
 */
function shouldLogCircuitBreaker(circuit: any): boolean {
  // Always log if the circuit is in a non-closed state
  if (circuit && circuit.state !== CircuitState.CLOSED) {
    return true;
  }

  // Check if there are any running jobs
  const hasRunningJobs = Object.values(performanceMetrics).some(
    (metrics) => metrics.processingCount > 0 || metrics.queueSize > 0
  );

  // Only log if there are running jobs
  return hasRunningJobs;
}

// Worker registry
const workerRegistry: Record<string, WorkerRegistration> = {};

/**
 * Initialize the worker monitoring system
 */
export async function initWorkerMonitoring() {
  // Reduced logging to avoid spam
  try {
    // Set up ping responder
    await setupPingResponder();

    // Register this worker process
    await registerWorker();

    // Start periodic health reporting
    startPeriodicHealthReporting();

    return true;
  } catch (error) {
    logger.error("Error initializing worker monitoring system:", error);
    return false;
  }
}

/**
 * Set up ping responder for worker health checks
 */
async function setupPingResponder() {
  try {
    // Create a duplicate Redis client for subscriptions
    const subscriber = redis.duplicate();

    // Subscribe to ping channel
    await subscriber.subscribe(WORKER_PING_CHANNEL);

    // Listen for ping messages
    subscriber.on("message", async (channel, message) => {
      if (channel === WORKER_PING_CHANNEL) {
        try {
          // Parse the ping message
          const ping = JSON.parse(message);
          const { id: pingId } = ping;

          // Get all worker health statuses
          const healthData = await redis.hgetall(WORKER_HEALTH_KEY);

          // Prepare response with worker statuses
          const workers: Record<string, boolean> = {};

          // Process each worker type
          for (const workerType of Object.values(WorkerType)) {
            const healthJson = healthData[workerType];
            let isHealthy = false;

            if (healthJson) {
              try {
                const health = JSON.parse(healthJson);
                isHealthy = health.healthy === true;
              } catch (error) {
                logger.error(
                  `Error parsing health data for ${workerType}:`,
                  error
                );
              }
            }

            workers[workerType] = isHealthy;
          }

          // Send response
          const responseChannel = `worker-pong-${pingId}`;
          await redis.publish(responseChannel, JSON.stringify({ workers }));
        } catch (error) {
          logger.error("Error handling ping message:", error);
        }
      }
    });

    return true;
  } catch (error) {
    logger.error("Error setting up ping responder:", error);
    return false;
  }
}

/**
 * Register this worker process
 */
async function registerWorker() {
  try {
    // Generate a unique worker ID
    const workerId = `${os.hostname()}-${process.pid}-${Date.now()}`;

    // Create registration data
    const registration: WorkerRegistration = {
      id: workerId,
      type: getWorkerType(),
      hostname: os.hostname(),
      pid: process.pid,
      startTime: new Date().toISOString(),
      version: process.env.npm_package_version ?? "unknown",
    };

    // Store in Redis
    await redis.hset(
      WORKER_REGISTRY_KEY,
      workerId,
      JSON.stringify(registration)
    );

    // Store locally
    workerRegistry[workerId] = registration;

    // Set up cleanup on exit
    process.on("exit", () => {
      // This won't work with async functions, but we'll try
      redis.hdel(WORKER_REGISTRY_KEY, workerId).catch(() => {});
    });

    return workerId;
  } catch (error) {
    logger.error("Error registering worker:", error);
    return null;
  }
}

/**
 * Determine worker type based on environment or command line arguments
 */
function getWorkerType(): WorkerType {
  // Try to determine worker type from environment
  const workerType = process.env.WORKER_TYPE;
  if (
    workerType &&
    Object.values(WorkerType).includes(workerType as WorkerType)
  ) {
    return workerType as WorkerType;
  }

  // Try to determine from command line arguments or file path
  const scriptPath = process.argv[1] || "";

  if (scriptPath.includes("resume")) {
    return WorkerType.RESUME_PARSING;
  } else if (scriptPath.includes("email")) {
    return WorkerType.EMAIL;
  } else if (scriptPath.includes("search")) {
    return WorkerType.SEARCH;
  } else if (scriptPath.includes("ats")) {
    return WorkerType.ATS_ANALYSIS;
  } else if (scriptPath.includes("automation")) {
    return WorkerType.AUTOMATION;
  }

  // Default to resume parsing worker type without excessive logging
  return WorkerType.RESUME_PARSING;
}

/**
 * Start periodic health reporting
 */
function startPeriodicHealthReporting() {
  // Report health every 10 minutes for better monitoring
  const interval = setInterval(async () => {
    try {
      const now = Date.now();

      // Only log full health report every 30 minutes to reduce spam
      if (now - lastHealthReportTime >= 1800000) {
        // 30 minutes
        logger.info(`📊 Scheduled health report - ${new Date().toISOString()}`);
        lastHealthReportTime = now;
      }

      await reportWorkerHealth();
    } catch (error) {
      logger.error("Error reporting worker health:", error);
    }
  }, 600000); // 10 minutes

  // Make sure interval is cleared on exit
  process.on("exit", () => {
    clearInterval(interval);
  });

  return interval;
}

/**
 * Report worker health metrics
 */
export async function reportWorkerHealth(
  queueSize?: number,
  processingCount?: number
): Promise<boolean> {
  try {
    const workerType = getWorkerType();

    // Initialize performance metrics for this worker type if not exists
    if (!performanceMetrics[workerType]) {
      performanceMetrics[workerType] = {
        requestCount: 0,
        errorCount: 0,
        totalResponseTime: 0,
        queueSize: queueSize ?? 0,
        processingCount: processingCount ?? 0,
      };
    }

    // Update metrics if provided
    if (queueSize !== undefined) {
      performanceMetrics[workerType].queueSize = queueSize;
    }

    if (processingCount !== undefined) {
      performanceMetrics[workerType].processingCount = processingCount;
    }

    // Collect system metrics
    const metrics = await collectMetrics(workerType);

    // Determine worker status based on metrics
    let status = WorkerStatus.HEALTHY;

    if (metrics.cpu > 90 || metrics.memory > 90 || metrics.errorRate > 20) {
      status = WorkerStatus.UNHEALTHY;
    } else if (
      metrics.cpu > 70 ||
      metrics.memory > 70 ||
      metrics.errorRate > 5
    ) {
      status = WorkerStatus.DEGRADED;
    }

    // Update health data
    const healthData: WorkerHealth = {
      status,
      healthy: status === WorkerStatus.HEALTHY,
      lastHeartbeat: new Date().toISOString(),
      hostname: os.hostname(),
      pid: process.pid,
    };

    // Store health data in Redis
    await redis.hset(WORKER_HEALTH_KEY, workerType, JSON.stringify(healthData));

    // Store metrics data in Redis
    await redis.hset(WORKER_METRICS_KEY, workerType, JSON.stringify(metrics));

    // Check if there are any running jobs
    const hasRunningJobs = Object.values(performanceMetrics).some(
      (metrics) => metrics.processingCount > 0 || metrics.queueSize > 0
    );

    // Log job status if there are running jobs
    if (hasRunningJobs) {
      const runningJobs = Object.entries(performanceMetrics)
        .filter(
          ([_, metrics]) => metrics.processingCount > 0 || metrics.queueSize > 0
        )
        .map(
          ([type, metrics]) =>
            `${type}(${metrics.processingCount}/${metrics.queueSize})`
        )
        .join(", ");

      logger.info(`ℹ️ Job status: ${runningJobs}`);
    }

    // Update circuit breaker if needed
    await updateCircuitBreaker(workerType, metrics);

    return true;
  } catch (error) {
    logger.error("Error reporting worker health:", error);
    return false;
  }
}

/**
 * Collect system metrics
 */
async function collectMetrics(workerType: WorkerType): Promise<WorkerMetrics> {
  // Try to get container metrics first
  try {
    const containerMetrics = await getContainerMetrics();

    if (containerMetrics) {
      // Use container metrics
      const cpuPercent = containerMetrics.cpuUsagePercent;
      const processMemPercent = containerMetrics.memoryUsagePercent;

      // Store last metrics for circuit breaker decisions
      lastMetrics = {
        memory: processMemPercent,
        cpu: cpuPercent,
      };

      // Get metrics from performance metrics
      const {
        requestCount,
        errorCount,
        totalResponseTime,
        queueSize,
        processingCount,
      } = performanceMetrics[workerType] || {
        requestCount: 0,
        errorCount: 0,
        totalResponseTime: 0,
        queueSize: 0,
        processingCount: 0,
      };

      // Calculate error rate
      const errorRate =
        requestCount > 0 ? (errorCount / requestCount) * 100 : 0;

      // Calculate success rate
      const successRate =
        requestCount > 0
          ? ((requestCount - errorCount) / requestCount) * 100
          : 100;

      // Calculate average response time
      const avgResponseTime =
        requestCount > 0 ? totalResponseTime / requestCount : 0;

      // Calculate available capacity based on CPU, memory, and queue size
      const cpuCapacity = Math.max(0, 100 - cpuPercent);
      const memCapacity = Math.max(0, 100 - processMemPercent);

      // Capacity is the minimum of CPU and memory capacity
      const capacity = Math.min(cpuCapacity, memCapacity);

      return {
        cpu: Math.round(cpuPercent * 10) / 10,
        memory: Math.round(processMemPercent * 10) / 10,
        queueSize,
        processingCount,
        responseTime: Math.round(avgResponseTime * 10) / 10,
        errorRate: Math.round(errorRate * 10) / 10,
        successRate: Math.round(successRate * 10) / 10,
        capacity: Math.round(capacity * 10) / 10,
        uptime: Math.floor(process.uptime()),
      };
    }
  } catch (error) {
    logger.error(`Error getting container metrics: ${error}`);
  }

  // Fall back to process metrics if container metrics failed
  // Calculate CPU usage
  const currentCpuUsage = process.cpuUsage();
  const currentTime = process.hrtime.bigint();

  const userDiff = currentCpuUsage.user - cpuUsage.user;
  const sysDiff = currentCpuUsage.system - cpuUsage.system;
  const timeDiff = Number(currentTime - lastCpuUsageTime) / 1000000; // Convert to ms

  // Update for next calculation
  cpuUsage = currentCpuUsage;
  lastCpuUsageTime = currentTime;

  // Calculate CPU percentage (process-specific)
  const cpuPercent = Math.min(
    100,
    ((userDiff + sysDiff) / 1000 / timeDiff) * 100
  );

  // Get system memory usage
  const totalMem = os.totalmem();

  // Calculate process memory usage
  const memoryUsage = process.memoryUsage();
  const processMemPercent = Math.min(100, (memoryUsage.rss / totalMem) * 100);

  // Store last metrics for circuit breaker decisions
  lastMetrics = {
    memory: processMemPercent,
    cpu: cpuPercent,
  };

  // Get metrics from performance metrics
  const {
    requestCount,
    errorCount,
    totalResponseTime,
    queueSize,
    processingCount,
  } = performanceMetrics[workerType] || {
    requestCount: 0,
    errorCount: 0,
    totalResponseTime: 0,
    queueSize: 0,
    processingCount: 0,
  };

  // Calculate error rate
  const errorRate = requestCount > 0 ? (errorCount / requestCount) * 100 : 0;

  // Calculate success rate
  const successRate =
    requestCount > 0 ? ((requestCount - errorCount) / requestCount) * 100 : 100;

  // Calculate average response time
  const avgResponseTime =
    requestCount > 0 ? totalResponseTime / requestCount : 0;

  // Calculate available capacity based on CPU, memory, and queue size
  const cpuCapacity = Math.max(0, 100 - cpuPercent);
  const memCapacity = Math.max(0, 100 - processMemPercent);

  // Capacity is the minimum of CPU and memory capacity
  const capacity = Math.min(cpuCapacity, memCapacity);

  return {
    cpu: Math.round(cpuPercent * 10) / 10,
    memory: Math.round(processMemPercent * 10) / 10,
    queueSize,
    processingCount,
    responseTime: Math.round(avgResponseTime * 10) / 10,
    errorRate: Math.round(errorRate * 10) / 10,
    successRate: Math.round(successRate * 10) / 10,
    capacity: Math.round(capacity * 10) / 10,
    uptime: Math.floor(process.uptime()),
  };
}

/**
 * Update circuit breaker status based on metrics
 */
async function updateCircuitBreaker(
  workerType: WorkerType,
  metrics: WorkerMetrics
): Promise<void> {
  try {
    // Get current circuit state
    const circuit = await getCurrentCircuitState(workerType);

    // Log circuit breaker status if it's time or if it's in a non-closed state
    if (shouldLogCircuitBreaker(circuit)) {
      const now = Date.now();

      // Only log circuit breaker status every 30 minutes unless it's in a critical state
      const shouldLogNow =
        circuit.state !== CircuitState.CLOSED ||
        now - lastCircuitBreakerLogTime >= 1800000; // 30 minutes

      if (shouldLogNow) {
        // Get information about running jobs
        const runningJobs = Object.entries(performanceMetrics)
          .filter(
            ([_, metrics]) =>
              metrics.processingCount > 0 || metrics.queueSize > 0
          )
          .map(
            ([type, metrics]) =>
              `${type}(${metrics.processingCount}/${metrics.queueSize})`
          )
          .join(", ");

        const jobStatus =
          runningJobs.length > 0
            ? `Jobs running: ${runningJobs}`
            : "No jobs running";

        // Use appropriate log level based on circuit state
        const logLevel =
          circuit.state === CircuitState.CLOSED ? "info" : "warn";
        const emoji = circuit.state === CircuitState.CLOSED ? "🟢" : "🧠";

        logger[logLevel](
          `${emoji} Circuit breaker status - Memory: ${metrics.memory.toFixed(2)}%, ` +
            `CPU: ${metrics.cpu.toFixed(2)}%, ` +
            `State: ${circuit.state}, ` +
            `${jobStatus}, ` +
            `Failure count: ${circuit.failureCount}`
        );

        // Update last log time
        lastCircuitBreakerLogTime = now;
      }
    }

    // Update circuit state based on metrics
    const newCircuit = { ...circuit };

    // Update failure count based on metrics
    updateFailureCount(newCircuit, metrics);

    // Update circuit state based on current state and metrics
    updateCircuitState(circuit, newCircuit, metrics, workerType);

    // Store updated circuit state if changed
    await saveCircuitState(circuit, newCircuit, workerType);
  } catch (error) {
    logger.error(`Error updating circuit breaker for ${workerType}:`, error);
  }
}

/**
 * Get the current circuit state for a worker
 */
async function getCurrentCircuitState(workerType: WorkerType) {
  const defaultCircuit = {
    state: CircuitState.CLOSED,
    failureCount: 0,
    lastStateChange: new Date().toISOString(),
  };

  const circuitJson = await redis.hget(WORKER_CIRCUIT_KEY, workerType);
  if (!circuitJson) return defaultCircuit;

  try {
    return JSON.parse(circuitJson);
  } catch (error) {
    logger.error(`Error parsing circuit data for ${workerType}:`, error);
    return defaultCircuit;
  }
}

/**
 * Update failure count based on metrics
 */
function updateFailureCount(circuit: any, metrics: WorkerMetrics) {
  // Check if metrics indicate a problem
  if (metrics.errorRate > 50 || metrics.capacity < 10) {
    circuit.failureCount++;
  } else {
    circuit.failureCount = 0;
  }
}

/**
 * Update circuit state based on current state and metrics
 */
function updateCircuitState(
  currentCircuit: any,
  newCircuit: any,
  metrics: WorkerMetrics,
  workerType: WorkerType
) {
  // Handle closed state
  if (currentCircuit.state === CircuitState.CLOSED) {
    handleClosedState(newCircuit, workerType);
  }
  // Handle open state
  else if (currentCircuit.state === CircuitState.OPEN) {
    handleOpenState(newCircuit, workerType);
  }
  // Handle half-open state
  else if (currentCircuit.state === CircuitState.HALF_OPEN) {
    handleHalfOpenState(newCircuit, metrics, workerType);
  }
}

/**
 * Handle closed circuit state
 */
function handleClosedState(circuit: any, workerType: WorkerType) {
  if (circuit.failureCount >= 5) {
    // Open the circuit after 5 consecutive failures
    circuit.state = CircuitState.OPEN;
    circuit.lastStateChange = new Date().toISOString();

    // Always log state changes regardless of interval
    logger.warn(`Circuit breaker opened for worker type: ${workerType}`);

    // Update last log time
    lastCircuitBreakerLogTime = Date.now();
  }
}

/**
 * Check if there are any running jobs
 */
function areJobsRunning(): boolean {
  return Object.values(performanceMetrics).some(
    (metrics) => metrics.processingCount > 0 || metrics.queueSize > 0
  );
}

/**
 * Get the count of running jobs
 */
function getRunningJobCount(): number {
  return Object.values(performanceMetrics).reduce(
    (total, metrics) => total + metrics.processingCount + metrics.queueSize,
    0
  );
}

/**
 * Handle open circuit state
 */
function handleOpenState(circuit: any, workerType: WorkerType) {
  // Check if it's time to try half-open
  const lastChange = new Date(circuit.lastStateChange).getTime();
  const now = Date.now();

  // Check if there are any running jobs
  const jobsRunning = areJobsRunning();
  const runningJobCount = getRunningJobCount();

  // If no jobs are running, consider resetting the circuit breaker
  if (!jobsRunning) {
    logger.info(
      `🔄 No jobs running (count: ${runningJobCount}), considering circuit reset`
    );

    // Check if resources are below thresholds
    if (lastMetrics && lastMetrics.memory < 85 && lastMetrics.cpu < 85) {
      logger.info(
        "✅ No jobs running and resources are good, resetting circuit breaker"
      );
      circuit.state = CircuitState.CLOSED;
      circuit.failureCount = 0;
      circuit.lastStateChange = new Date().toISOString();

      // Always log state changes
      logger.info(`Circuit breaker closed for worker type: ${workerType}`);

      // Update last log time
      lastCircuitBreakerLogTime = Date.now();
      return;
    } else {
      logger.info(
        "⚠️ No jobs running but resources still constrained, not resetting yet"
      );
    }
  }

  // Try half-open after 30 seconds if we didn't reset
  if (now - lastChange > 30000) {
    circuit.state = CircuitState.HALF_OPEN;
    circuit.lastStateChange = new Date().toISOString();

    // Always log state changes regardless of interval
    logger.info(`Circuit breaker half-opened for worker type: ${workerType}`);

    // Update last log time
    lastCircuitBreakerLogTime = Date.now();
  }
}

/**
 * Handle half-open circuit state
 */
function handleHalfOpenState(
  circuit: any,
  metrics: WorkerMetrics,
  workerType: WorkerType
) {
  if (circuit.failureCount > 0) {
    // If any failure in half-open state, go back to open
    circuit.state = CircuitState.OPEN;
    circuit.lastStateChange = new Date().toISOString();

    // Always log state changes regardless of interval
    logger.warn(`Circuit breaker re-opened for worker type: ${workerType}`);

    // Update last log time
    lastCircuitBreakerLogTime = Date.now();
  } else if (metrics.successRate > 90 && metrics.capacity > 50) {
    // If success in half-open state, close the circuit
    circuit.state = CircuitState.CLOSED;
    circuit.failureCount = 0;
    circuit.lastStateChange = new Date().toISOString();

    // Always log state changes regardless of interval
    logger.info(`Circuit breaker closed for worker type: ${workerType}`);

    // Update last log time
    lastCircuitBreakerLogTime = Date.now();
  }
}

/**
 * Save circuit state if changed
 */
async function saveCircuitState(
  oldCircuit: any,
  newCircuit: any,
  workerType: WorkerType
) {
  // Store updated circuit state if changed
  if (
    newCircuit.state !== oldCircuit.state ||
    newCircuit.failureCount !== oldCircuit.failureCount
  ) {
    await redis.hset(
      WORKER_CIRCUIT_KEY,
      workerType,
      JSON.stringify(newCircuit)
    );
  }
}

/**
 * Record a request processed by the worker
 * @param workerType The worker type
 * @param responseTime Response time in milliseconds
 * @param isError Whether the request resulted in an error
 */
export function recordRequest(
  workerType: WorkerType,
  responseTime: number,
  isError: boolean = false
): void {
  // Initialize performance metrics for this worker type if not exists
  if (!performanceMetrics[workerType]) {
    performanceMetrics[workerType] = {
      requestCount: 0,
      errorCount: 0,
      totalResponseTime: 0,
      queueSize: 0,
      processingCount: 0,
    };
  }

  performanceMetrics[workerType].requestCount++;
  performanceMetrics[workerType].totalResponseTime += responseTime;

  if (isError) {
    performanceMetrics[workerType].errorCount++;
  }
}

/**
 * Update queue metrics for a worker
 * @param workerType The worker type
 * @param queueSize Current queue size
 * @param processingCount Number of items being processed
 */
export function updateQueueMetrics(
  workerType: WorkerType,
  queueSize: number,
  processingCount: number
): void {
  // Initialize performance metrics for this worker type if not exists
  if (!performanceMetrics[workerType]) {
    performanceMetrics[workerType] = {
      requestCount: 0,
      errorCount: 0,
      totalResponseTime: 0,
      queueSize: 0,
      processingCount: 0,
    };
  }

  performanceMetrics[workerType].queueSize = queueSize;
  performanceMetrics[workerType].processingCount = processingCount;
}

/**
 * Monitor cron job timeout and integrate with circuit breaker
 * @param jobName The cron job name
 * @param timeoutMinutes The timeout in minutes
 * @param startTime The job start time
 */
export function monitorCronJobTimeout(
  jobName: string,
  timeoutMinutes: number,
  startTime: Date
): void {
  const timeoutMs = timeoutMinutes * 60 * 1000;
  const elapsedMs = Date.now() - startTime.getTime();
  const remainingMs = timeoutMs - elapsedMs;

  if (remainingMs <= 0) {
    logger.warn(
      `⚠️ Cron job ${jobName} has exceeded its ${timeoutMinutes} minute timeout`
    );

    // Record this as an error for circuit breaker consideration
    const workerType = jobName as WorkerType;
    recordRequest(workerType, elapsedMs, true);

    return;
  }

  // Log timeout warning when 80% of timeout is reached
  const warningThreshold = timeoutMs * 0.8;
  if (elapsedMs >= warningThreshold) {
    const remainingMinutes = Math.floor(remainingMs / (60 * 1000));
    logger.warn(
      `⏰ Cron job ${jobName} approaching timeout: ${remainingMinutes} minutes remaining`
    );
  }
}

/**
 * Get system load level for adaptive timeout calculation
 * @returns Promise<'low' | 'medium' | 'high'>
 */
export async function getSystemLoadLevel(): Promise<"low" | "medium" | "high"> {
  try {
    const containerMetrics = await getContainerMetrics();

    if (containerMetrics) {
      const { memoryUsagePercent, cpuUsagePercent } = containerMetrics;
      const now = Date.now();

      // Only log container metrics every 30 minutes to reduce debug spam
      if (now - lastContainerMetricsLogTime >= 1800000) {
        // 30 minutes
        logger.info(
          `📊 System load assessment: Memory ${memoryUsagePercent.toFixed(1)}%, CPU ${cpuUsagePercent.toFixed(1)}%`
        );
        lastContainerMetricsLogTime = now;
      }

      // High load: Memory > 80% OR CPU > 80%
      if (memoryUsagePercent > 80 || cpuUsagePercent > 80) {
        // Always log high load conditions
        if (now - lastContainerMetricsLogTime >= 300000) {
          // 5 minutes for high load
          logger.warn(
            `🔴 High system load detected: Memory ${memoryUsagePercent.toFixed(1)}%, CPU ${cpuUsagePercent.toFixed(1)}%`
          );
          lastContainerMetricsLogTime = now;
        }
        return "high";
      }

      // Medium load: Memory > 60% OR CPU > 60%
      if (memoryUsagePercent > 60 || cpuUsagePercent > 60) {
        return "medium";
      }

      // Low load: Everything else
      return "low";
    }
  } catch (error) {
    // Only log container metrics errors occasionally to avoid spam
    const now = Date.now();
    if (now - lastContainerMetricsLogTime >= 1800000) {
      // 30 minutes
      logger.warn(
        `⚠️ Could not get container metrics for load assessment: ${error}`
      );
      lastContainerMetricsLogTime = now;
    }
  }

  // Fallback to medium load if we can't determine system metrics
  return "medium";
}
