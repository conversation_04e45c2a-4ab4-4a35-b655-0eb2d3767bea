// scraper/config.ts
// Centralized configuration for the scraper service

/**
 * Configuration for the scraper service
 * Values are loaded from environment variables with sensible defaults
 */
export const config = {
  // Database configuration
  database: {
    url: process.env.DATABASE_URL,
    schemas: ["web", "workers"],
  },

  // Redis configuration
  redis: {
    url: process.env.REDIS_URL,
    streamPrefix: "auto-apply::",
    consumerGroup: "scraper-service",
    consumerName: `scraper-worker-${process.pid}`,
  },

  // Circuit breaker configuration
  // UPDATED: Reduced thresholds to prevent 100% resource usage
  circuitBreaker: {
    // Reduced memory threshold to prevent system overload
    memoryThresholdPercent: process.env.CIRCUIT_BREAKER_MEMORY_THRESHOLD
      ? parseInt(process.env.CIRCUIT_BREAKER_MEMORY_THRESHOLD)
      : 75, // Reduced from 90% to 75% to prevent overload
    // Reduced CPU threshold to prevent system overload
    cpuThresholdPercent: process.env.CIRCUIT_BREAKER_CPU_THRESHOLD
      ? parseInt(process.env.CIRCUIT_BREAKER_CPU_THRESHOLD)
      : 75, // Reduced from 90% to 75% to prevent overload
    errorThresholdCount: process.env.CIRCUIT_BREAKER_ERROR_THRESHOLD
      ? parseInt(process.env.CIRCUIT_BREAKER_ERROR_THRESHOLD)
      : 5,
    resetTimeoutMs: process.env.CIRCUIT_BREAKER_RESET_TIMEOUT
      ? parseInt(process.env.CIRCUIT_BREAKER_RESET_TIMEOUT)
      : 60000, // 1 minute
    checkIntervalMs: process.env.CIRCUIT_BREAKER_CHECK_INTERVAL
      ? parseInt(process.env.CIRCUIT_BREAKER_CHECK_INTERVAL)
      : 300000, // Increased to 5 minutes to drastically reduce resource usage
    consecutiveReadingsForOpen: process.env
      .CIRCUIT_BREAKER_CONSECUTIVE_READINGS_OPEN
      ? parseInt(process.env.CIRCUIT_BREAKER_CONSECUTIVE_READINGS_OPEN)
      : 2,
    consecutiveReadingsForClose: process.env
      .CIRCUIT_BREAKER_CONSECUTIVE_READINGS_CLOSE
      ? parseInt(process.env.CIRCUIT_BREAKER_CONSECUTIVE_READINGS_CLOSE)
      : 5, // Increased from 3 to 5 for more stability
    consecutiveReadingsForDegraded: process.env
      .CIRCUIT_BREAKER_CONSECUTIVE_READINGS_DEGRADED
      ? parseInt(process.env.CIRCUIT_BREAKER_CONSECUTIVE_READINGS_DEGRADED)
      : 3, // Increased from 1 to 3 to prevent rapid oscillation
    // Adjusted degraded thresholds to be lower than main thresholds
    degradedMemoryThresholdPercent: process.env
      .CIRCUIT_BREAKER_DEGRADED_MEMORY_THRESHOLD
      ? parseInt(process.env.CIRCUIT_BREAKER_DEGRADED_MEMORY_THRESHOLD)
      : 60, // Reduced from 85% to 60% to be well below main threshold
    degradedCpuThresholdPercent: process.env
      .CIRCUIT_BREAKER_DEGRADED_CPU_THRESHOLD
      ? parseInt(process.env.CIRCUIT_BREAKER_DEGRADED_CPU_THRESHOLD)
      : 60, // Reduced from 85% to 60% to be well below main threshold
  },

  // Job configurations
  jobs: {
    // Domain update job configuration
    domainUpdate: {
      batchSize: process.env.DOMAIN_UPDATE_BATCH_SIZE
        ? parseInt(process.env.DOMAIN_UPDATE_BATCH_SIZE)
        : 2,
      delayBetweenBatchesMs: process.env.DOMAIN_UPDATE_BATCH_DELAY
        ? parseInt(process.env.DOMAIN_UPDATE_BATCH_DELAY)
        : 15000, // 15 seconds
      maxCompanies: process.env.DOMAIN_UPDATE_MAX_COMPANIES
        ? parseInt(process.env.DOMAIN_UPDATE_MAX_COMPANIES)
        : 100,
      delayAfterSuccessMs: process.env.DOMAIN_UPDATE_SUCCESS_DELAY
        ? parseInt(process.env.DOMAIN_UPDATE_SUCCESS_DELAY)
        : 10000, // 10 seconds
      delayAfterFailureMs: process.env.DOMAIN_UPDATE_FAILURE_DELAY
        ? parseInt(process.env.DOMAIN_UPDATE_FAILURE_DELAY)
        : 5000, // 5 seconds
      delayAfterErrorMs: process.env.DOMAIN_UPDATE_ERROR_DELAY
        ? parseInt(process.env.DOMAIN_UPDATE_ERROR_DELAY)
        : 3000, // 3 seconds
      lockTimeoutSeconds: process.env.DOMAIN_UPDATE_LOCK_TIMEOUT
        ? parseInt(process.env.DOMAIN_UPDATE_LOCK_TIMEOUT)
        : 3600, // 1 hour
    },

    // Job scraper configuration
    jobScraper: {
      batchSize: process.env.JOB_SCRAPER_BATCH_SIZE
        ? parseInt(process.env.JOB_SCRAPER_BATCH_SIZE)
        : 10,
      maxConcurrentRequests: process.env.JOB_SCRAPER_MAX_CONCURRENT
        ? parseInt(process.env.JOB_SCRAPER_MAX_CONCURRENT)
        : 5,
      maxJobsPerRun: process.env.JOB_SCRAPER_MAX_JOBS
        ? parseInt(process.env.JOB_SCRAPER_MAX_JOBS)
        : 1000,
      requestTimeoutMs: process.env.JOB_SCRAPER_REQUEST_TIMEOUT
        ? parseInt(process.env.JOB_SCRAPER_REQUEST_TIMEOUT)
        : 30000, // 30 seconds
    },

    // Job details scraper configuration
    jobDetailsScraper: {
      batchSize: process.env.JOB_DETAILS_BATCH_SIZE
        ? parseInt(process.env.JOB_DETAILS_BATCH_SIZE)
        : 3, // Reduced from 5 to 3
      maxConcurrentRequests: process.env.JOB_DETAILS_MAX_CONCURRENT
        ? parseInt(process.env.JOB_DETAILS_MAX_CONCURRENT)
        : 1, // Reduced from 3 to 1 to prevent DB connection pool exhaustion
      maxJobsPerRun: process.env.JOB_DETAILS_MAX_JOBS
        ? parseInt(process.env.JOB_DETAILS_MAX_JOBS)
        : 200, // Reduced from 500 to 200 to lower resource usage
      lockTimeoutSeconds: process.env.JOB_DETAILS_LOCK_TIMEOUT
        ? parseInt(process.env.JOB_DETAILS_LOCK_TIMEOUT)
        : 3600, // 1 hour default
    },

    // Cleanup job configuration
    cleanup: {
      jobAgeDays: process.env.CLEANUP_JOB_AGE_DAYS
        ? parseInt(process.env.CLEANUP_JOB_AGE_DAYS)
        : 30,
      batchSize: process.env.CLEANUP_BATCH_SIZE
        ? parseInt(process.env.CLEANUP_BATCH_SIZE)
        : 1000,
    },
  },

  // Email configuration
  email: {
    fromAddress: process.env.EMAIL_FROM_ADDRESS ?? "<EMAIL>",
    adminEmail: process.env.ADMIN_EMAIL ?? "<EMAIL>",
    sendGridApiKey: process.env.SENDGRID_API_KEY,
  },

  // System configuration
  system: {
    timezone: process.env.TIMEZONE ?? "America/New_York",
    environment: process.env.NODE_ENV ?? "development",
    isProduction: process.env.NODE_ENV === "production",
    logLevel: process.env.LOG_LEVEL ?? "info",
  },
};

export default config;
