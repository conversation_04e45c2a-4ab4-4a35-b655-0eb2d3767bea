/**
 * <PERSON><PERSON><PERSON> to fetch plans from <PERSON>e and save them to the database
 *
 * This script:
 * 1. Fetches all active products from Stripe
 * 2. Filters products that have plan_id in metadata
 * 3. Gets prices for each product
 * 4. Creates or updates plans in the database
 * 5. Associates features with plans based on metadata
 *
 * Usage:
 * npm run fetch-plans-from-stripe
 */

import { PrismaClient } from '@prisma/client';
import <PERSON><PERSON> from 'stripe';
import dotenv from 'dotenv';
import { FeatureAccessLevel } from '../src/lib/models/features/features';

// Load environment variables
dotenv.config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Initialize Stripe
const isProd = process.env.NODE_ENV === 'production';
const stripeSecret = isProd
  ? process.env.STRIPE_SECRET_KEY_LIVE || 'sk_live_placeholder'
  : process.env.STRIPE_SECRET_KEY_TEST || 'sk_test_placeholder';

const stripe = new Stripe(stripeSecret);

/**
 * Add default features to a plan
 * @param planId The plan ID
 */
async function addDefaultFeaturesToPlan(planId: string): Promise<void> {
  try {
    console.log(`Adding default features to plan ${planId}...`);

    // Get all features from the database
    const features = await prisma.feature.findMany();

    // Core features that all plans should have
    const coreFeatureIds = ['dashboard', 'profile'];

    // Add core features to the plan
    for (const featureId of coreFeatureIds) {
      const feature = features.find((f) => f.id === featureId);

      if (feature) {
        // Create plan feature
        await prisma.planFeature.create({
          data: {
            id: `${planId}_${featureId}`,
            planId: planId,
            featureId: feature.id,
            accessLevel: FeatureAccessLevel.Included,
            updatedAt: new Date(),
          },
        });

        console.log(`Added core feature ${featureId} to plan ${planId}`);
      }
    }

    console.log(`Successfully added default features to plan ${planId}`);
  } catch (error) {
    console.error(`Error adding default features to plan ${planId}:`, error);
  }
}

/**
 * Add feature to plan based on Stripe product metadata
 * @param planId The plan ID
 * @param metadata The product metadata
 */
async function addFeaturesToPlanFromMetadata(
  planId: string,
  metadata: Stripe.Metadata
): Promise<void> {
  try {
    console.log(`Adding features to plan ${planId} from metadata...`);

    // Get all features from the database
    const features = await prisma.feature.findMany();

    // Process metadata to find features
    for (const [key, value] of Object.entries(metadata)) {
      // Check if the key starts with 'feature_'
      if (key.startsWith('feature_')) {
        const featureId = key.replace('feature_', '');
        const feature = features.find((f) => f.id === featureId);

        if (feature) {
          // Parse the value to determine access level and limits
          const [accessLevel, ...limitValues] = value.split(',');

          // Create plan feature
          const planFeature = await prisma.planFeature.create({
            data: {
              id: `${planId}_${featureId}`,
              planId: planId,
              featureId: feature.id,
              accessLevel: accessLevel as FeatureAccessLevel,
              updatedAt: new Date(),
            },
          });

          // Process limits if the feature is limited
          if (accessLevel === FeatureAccessLevel.Limited && limitValues.length > 0) {
            // Get limits for this feature
            const limits = await prisma.featureLimit.findMany({
              where: { featureId: feature.id },
            });

            // Add limits
            for (const limitValue of limitValues) {
              const [limitId, value] = limitValue.split(':');
              const limit = limits.find((l) => l.id === limitId);

              if (limit) {
                await prisma.planFeatureLimit.create({
                  data: {
                    id: `${planFeature.id}_${limitId}`,
                    planFeatureId: planFeature.id,
                    limitId: limit.id,
                    value: value,
                    updatedAt: new Date(),
                  },
                });

                console.log(
                  `Added limit ${limitId} with value ${value} to feature ${featureId} for plan ${planId}`
                );
              }
            }
          }

          console.log(
            `Added feature ${featureId} with access level ${accessLevel} to plan ${planId}`
          );
        }
      }
    }

    console.log(`Successfully added features to plan ${planId} from metadata`);
  } catch (error) {
    console.error(`Error adding features to plan ${planId} from metadata:`, error);
  }
}

/**
 * Load plans from Stripe and save them to the database
 */
async function fetchPlansFromStripe(): Promise<void> {
  try {
    console.log('Fetching plans from Stripe...');

    // Get all active products from Stripe
    const products = await stripe.products.list({
      active: true,
      expand: ['data.default_price'],
    });

    console.log(`Found ${products.data.length} active products in Stripe`);

    // Filter products that have plan_id in metadata
    const planProducts = products.data.filter((product) => product.metadata.plan_id);

    console.log(`Found ${planProducts.length} products with plan_id metadata`);

    let count = 0;

    // Process each plan product
    for (const product of planProducts) {
      const planId = product.metadata.plan_id;
      const section = product.metadata.section || 'pro';

      // Get all prices for this product
      const prices = await stripe.prices.list({
        product: product.id,
        active: true,
      });

      // Find monthly and yearly prices
      const monthlyPrice = prices.data.find(
        (price) => price.recurring?.interval === 'month' && price.recurring?.interval_count === 1
      );

      const yearlyPrice = prices.data.find(
        (price) => price.recurring?.interval === 'year' && price.recurring?.interval_count === 1
      );

      if (!monthlyPrice && !yearlyPrice) {
        console.log(`Skipping product ${product.id} (${product.name}) - no valid prices found`);
        continue;
      }

      // Check if plan already exists in database
      const existingPlan = await prisma.plan.findUnique({
        where: { id: planId },
      });

      const planData = {
        name: product.name,
        description: product.description || '',
        section: section,
        monthlyPrice: monthlyPrice?.unit_amount || 0,
        annualPrice: yearlyPrice?.unit_amount || 0,
        stripePriceMonthlyId: monthlyPrice?.id || null,
        stripePriceYearlyId: yearlyPrice?.id || null,
        popular: product.metadata.popular === 'true',
        updatedAt: new Date(),
      };

      if (existingPlan) {
        // Update existing plan
        await prisma.plan.update({
          where: { id: planId },
          data: planData,
        });

        console.log(`Updated plan ${planId} (${product.name}) in database`);
      } else {
        // Create new plan
        await prisma.plan.create({
          data: {
            id: planId,
            ...planData,
            createdAt: new Date(),
          },
        });

        // Add default features for new plans
        await addDefaultFeaturesToPlan(planId);

        // Add features from metadata
        await addFeaturesToPlanFromMetadata(planId, product.metadata);

        console.log(`Created plan ${planId} (${product.name}) in database`);
      }

      count++;
    }

    console.log(`Successfully loaded ${count} plans from Stripe`);
  } catch (error) {
    console.error('Error loading plans from Stripe:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
fetchPlansFromStripe()
  .then(() => {
    console.log('Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
