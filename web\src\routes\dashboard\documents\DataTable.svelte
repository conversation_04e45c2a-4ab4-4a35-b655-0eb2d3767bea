<script lang="ts">
  import { FlexRender } from '$lib/components/ui/data-table';
  import { renderComponent } from '$lib/components/ui/data-table/render-helpers';
  import { browser } from '$app/environment';
  import { onMount } from 'svelte';
  import {
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
  } from '@tanstack/table-core';
  import DataTableCheckbox from '$lib/components/ui/checkbox/checkbox.svelte';
  import DataTableColumnHeader from './data-table-column-header.svelte';
  import DataTableRowActions from './data-table-row-actions.svelte';
  import DataTablePagination from './data-table-pagination.svelte';
  import DataTableToolbar from './data-table-toolbar.svelte';
  import DataTableTitleCell from './data-table-title-cell.svelte';
  import * as TableUI from '$lib/components/ui/table';

  // Regular props instead of $props
  export let data = [];
  export let searchTerm = '';
  
  // State variables
  let typeFilter = [];
  let sourceFilter = [];
  let sorting = [{ id: 'createdAt', desc: true }];
  let columnFilters = [];
  let columnVisibility = {
    document: true,
    type: true,
    createdAt: true,
    updatedAt: true,
  };
  let rowSelection = {};
  let pagination = { pageIndex: 0, pageSize: 10 };
  let tableInstance = null;
  let filteredData = [];

  // Function to filter data based on search term
  function filterData(data, searchTerm) {
    if (!searchTerm || searchTerm.trim() === '') {
      return data;
    }
    
    const term = searchTerm.toLowerCase().trim();
    return data.filter((doc) => {
      // Search in document label/title
      if (doc.label && doc.label.toLowerCase().includes(term)) {
        return true;
      }
      
      // Search in document type
      if (doc.type && doc.type.toLowerCase().includes(term)) {
        return true;
      }
      
      // Search in document content if available
      if (doc.content && doc.content.toLowerCase().includes(term)) {
        return true;
      }
      
      return false;
    });
  }

  // Function to update column filters
  function updateColumnFilters() {
    // Create a new array to trigger reactivity
    const newFilters = [];
    
    // Add type filter if active
    if (typeFilter && typeFilter.length > 0) {
      newFilters.push({
        id: 'type',
        value: typeFilter,
      });
    }
    
    // Update column filters
    columnFilters = newFilters;
    
    // Update source filter in document column meta
    if (tableInstance) {
      const documentColumn = tableInstance.getColumn('document');
      if (documentColumn) {
        documentColumn.columnDef.meta = {
          ...documentColumn.columnDef.meta,
          sourceFilter: sourceFilter || [],
        };
      }
    }
  }

  // Function to handle document deletion
  function handleDeleted(id) {
    console.log('Document deleted:', id);
    
    // Remove the document from the data array
    data = data.filter((doc) => doc.id !== id);
    
    // Update filtered data
    filteredData = filterData(data, searchTerm);
    
    // Dispatch an event to notify other components
    const event = new CustomEvent('documentDeleted', { detail: id });
    document.dispatchEvent(event);
  }

  // Initialize table
  function initializeTable() {
    if (browser && filteredData.length > 0) {
      console.log('Initializing table instance with', filteredData.length, 'documents');
      
      // Create the table instance
      tableInstance = createSvelteTable({
        data: filteredData,
        columns: [
          // Select column
          {
            id: 'select',
            header: () => {
              return renderComponent(DataTableCheckbox, {
                checked: filteredData.length > 0 && 
                  filteredData.every((row) => rowSelection[row.id]),
                indeterminate: filteredData.some((row) => rowSelection[row.id]) && 
                  !(filteredData.length > 0 && filteredData.every((row) => rowSelection[row.id])),
                onCheckedChange: (value) => {
                  const newSelection = { ...rowSelection };
                  filteredData.forEach((row) => {
                    if (value) {
                      newSelection[row.id] = true;
                    } else {
                      delete newSelection[row.id];
                    }
                  });
                  rowSelection = newSelection;
                },
                'aria-label': 'Select all',
              });
            },
            cell: ({ row }) => {
              return renderComponent(DataTableCheckbox, {
                checked: rowSelection[row.id] || false,
                onCheckedChange: (value) => {
                  const newSelection = { ...rowSelection };
                  if (value) {
                    newSelection[row.id] = true;
                  } else {
                    delete newSelection[row.id];
                  }
                  rowSelection = newSelection;
                },
                'aria-label': 'Select row',
                class: 'translate-y-[2px]',
              });
            },
            enableSorting: false,
            width: '15px',
          },
          // Document column
          {
            accessorKey: 'label',
            header: 'Document Name',
            id: 'document',
            enableSorting: true,
            width: '60%',
            minWidth: '200px',
            cell: ({ row }) => {
              return renderComponent(DataTableTitleCell, { document: row.original });
            },
          },
          // Type column
          {
            accessorKey: 'type',
            header: 'Type',
            id: 'type',
            enableSorting: true,
            width: '120px',
            cell: ({ row }) => row.getValue('type'),
          },
          // Created column
          {
            accessorKey: 'createdAt',
            header: 'Created',
            id: 'createdAt',
            enableSorting: true,
            sortingFn: 'datetime',
            sortDescFirst: true,
            width: '190px',
            cell: ({ row }) => {
              const dateStr = row.getValue('createdAt');
              if (!dateStr) return 'N/A';
              
              const date = new Date(dateStr);
              if (isNaN(date.getTime())) return 'Invalid date';
              
              return new Intl.DateTimeFormat('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
              }).format(date);
            },
          },
          // Updated column
          {
            accessorKey: 'updatedAt',
            header: 'Edited',
            id: 'updatedAt',
            enableSorting: true,
            sortingFn: 'datetime',
            sortDescFirst: true,
            width: '190px',
            cell: ({ row }) => {
              const dateStr = row.getValue('updatedAt');
              if (!dateStr) return 'N/A';
              
              const date = new Date(dateStr);
              if (isNaN(date.getTime())) return 'Invalid date';
              
              return new Intl.DateTimeFormat('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
              }).format(date);
            },
          },
          // Actions column
          {
            id: 'actions',
            width: '10%',
            minWidth: '60px',
            cell: ({ row }) => {
              return renderComponent(DataTableRowActions, {
                row: row.original,
                on_deleted: (id) => handleDeleted(id),
              });
            },
          },
        ],
        state: {
          sorting,
          columnFilters,
          rowSelection,
          pagination,
          columnVisibility,
          globalFilter: '',
        },
        onSortingChange: (updater) => {
          sorting = typeof updater === 'function' ? updater(sorting) : updater;
        },
        onColumnFiltersChange: (updater) => {
          columnFilters = typeof updater === 'function' ? updater(columnFilters) : updater;
        },
        onRowSelectionChange: (updater) => {
          rowSelection = typeof updater === 'function' ? updater(rowSelection) : updater;
        },
        onPaginationChange: (updater) => {
          pagination = typeof updater === 'function' ? updater(pagination) : updater;
        },
        onColumnVisibilityChange: (updater) => {
          columnVisibility = typeof updater === 'function' ? updater(columnVisibility) : updater;
        },
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        enableRowSelection: true,
      });
    }
  }

  // Define createSvelteTable function
  function createSvelteTable(options) {
    const {
      data,
      columns,
      state,
      onSortingChange,
      onColumnFiltersChange,
      onRowSelectionChange,
      onPaginationChange,
      onColumnVisibilityChange,
      getCoreRowModel,
      getFilteredRowModel,
      getPaginationRowModel,
      getSortedRowModel,
      enableRowSelection,
    } = options;

    // Create a basic table instance
    const table = {
      data,
      columns,
      state,
      onSortingChange,
      onColumnFiltersChange,
      onRowSelectionChange,
      onPaginationChange,
      onColumnVisibilityChange,
      getCoreRowModel,
      getFilteredRowModel,
      getPaginationRowModel,
      getSortedRowModel,
      enableRowSelection,
      
      // Methods
      getHeaderGroups: () => {
        return columns.map((column) => ({
          headers: [{ column, colSpan: 1, isPlaceholder: false, id: column.id }],
        }));
      },
      
      getRowModel: () => {
        return {
          rows: data.map((row, index) => ({
            id: row.id || `row-${index}`,
            original: row,
            getVisibleCells: () => {
              return columns.map((column) => ({
                column,
                getContext: () => ({ row: { original: row, id: row.id, getValue: (id) => row[id] } }),
              }));
            },
          })),
        };
      },
      
      getAllColumns: () => {
        return columns;
      },
      
      getColumn: (id) => {
        return columns.find((column) => column.id === id);
      },
      
      getState: () => {
        return state;
      },
      
      setState: (newState) => {
        Object.assign(state, newState);
        
        // Call appropriate change handlers
        if (newState.sorting && onSortingChange) {
          onSortingChange(newState.sorting);
        }
        
        if (newState.columnFilters && onColumnFiltersChange) {
          onColumnFiltersChange(newState.columnFilters);
        }
        
        if (newState.rowSelection && onRowSelectionChange) {
          onRowSelectionChange(newState.rowSelection);
        }
        
        if (newState.pagination && onPaginationChange) {
          onPaginationChange(newState.pagination);
        }
        
        if (newState.columnVisibility && onColumnVisibilityChange) {
          onColumnVisibilityChange(newState.columnVisibility);
        }
      },
      
      setColumnVisibility: (visibility) => {
        if (onColumnVisibilityChange) {
          onColumnVisibilityChange(visibility);
        }
      },
    };
    
    return table;
  }

  // Initialize on mount
  onMount(() => {
    if (browser) {
      // Process initial data
      if (data && data.length > 0) {
        // Make sure all required fields are present
        const processedData = data.map((doc) => ({
          ...doc,
          id: doc.id ?? `temp-${Math.random().toString(36).substring(2, 9)}`,
          label: doc.label ?? 'Untitled Document',
          type: doc.type ?? 'document',
          createdAt: doc.createdAt ?? new Date().toISOString(),
          updatedAt: doc.updatedAt ?? doc.createdAt ?? new Date().toISOString(),
          source: doc.source ?? 'uploaded',
        }));
        
        // Update data
        data = processedData;
      }
      
      // Initialize filtered data
      filteredData = filterData(data, searchTerm);
      
      // Initialize table
      initializeTable();
      
      // Set up event listeners
      const handleSearchChange = ((e) => {
        if (searchTerm !== e.detail) {
          searchTerm = e.detail;
          filteredData = filterData(data, searchTerm);
          
          // Update table data
          if (tableInstance) {
            tableInstance.data = filteredData;
          }
        }
      }) as EventListener;
      
      const handleTypeFilterChange = ((e) => {
        if (e.detail && Array.isArray(e.detail.values)) {
          if (JSON.stringify(typeFilter) !== JSON.stringify(e.detail.values)) {
            typeFilter = e.detail.values;
            updateColumnFilters();
            
            // Update filtered data
            filteredData = filterData(data, searchTerm);
            
            // Update table data
            if (tableInstance) {
              tableInstance.data = filteredData;
            }
          }
        }
      }) as EventListener;
      
      const handleSourceFilterChange = ((e) => {
        if (e.detail && Array.isArray(e.detail.values)) {
          if (JSON.stringify(sourceFilter) !== JSON.stringify(e.detail.values)) {
            sourceFilter = e.detail.values;
            updateColumnFilters();
            
            // Update filtered data
            filteredData = filterData(data, searchTerm);
            
            // Update table data
            if (tableInstance) {
              tableInstance.data = filteredData;
            }
          }
        }
      }) as EventListener;
      
      const handleColumnVisibilityChange = ((e) => {
        if (e.detail && e.detail.visibility) {
          const newVisibility = e.detail.visibility;
          if (JSON.stringify(columnVisibility) !== JSON.stringify(newVisibility)) {
            columnVisibility = newVisibility;
            
            // Update table
            if (tableInstance) {
              if (tableInstance.setColumnVisibility) {
                tableInstance.setColumnVisibility(newVisibility);
              } else {
                tableInstance.setState({ columnVisibility: newVisibility });
              }
            }
          }
        }
      }) as EventListener;
      
      // Add event listeners
      document.addEventListener('searchChange', handleSearchChange);
      document.addEventListener('typeFilterChange', handleTypeFilterChange);
      document.addEventListener('sourceFilterChange', handleSourceFilterChange);
      document.addEventListener('columnVisibilityChange', handleColumnVisibilityChange);
      
      // Return cleanup function
      return () => {
        document.removeEventListener('searchChange', handleSearchChange);
        document.removeEventListener('typeFilterChange', handleTypeFilterChange);
        document.removeEventListener('sourceFilterChange', handleSourceFilterChange);
        document.removeEventListener('columnVisibilityChange', handleColumnVisibilityChange);
      };
    }
  });
</script>

{#if tableInstance}
  <div class="w-full space-y-4">
    <DataTableToolbar tableModel={tableInstance} {data} {searchTerm} />
    <div class="border-border w-full rounded-md border">
      <TableUI.Root class="w-full">
        <TableUI.Header>
          {#each tableInstance.getHeaderGroups() as headerGroup}
            <TableUI.Row>
              {#each headerGroup.headers as header}
                <TableUI.Head
                  colSpan={header.colSpan}
                  style={header.column.columnDef.width
                    ? `width: ${header.column.columnDef.width}; min-width: ${header.column.columnDef.minWidth || '50px'};`
                    : ''}>
                  {#if !header.isPlaceholder}
                    <FlexRender
                      content={header.column.columnDef.header}
                      context={header.getContext ? header.getContext() : {}} />
                  {/if}
                </TableUI.Head>
              {/each}
            </TableUI.Row>
          {/each}
        </TableUI.Header>
        <TableUI.Body>
          {#if tableInstance.getRowModel().rows.length}
            {#each tableInstance.getRowModel().rows as row}
              <TableUI.Row class={rowSelection[row.id] ? 'bg-muted' : ''}>
                {#each row.getVisibleCells() as cell}
                  <TableUI.Cell
                    style={cell.column.columnDef.width
                      ? `width: ${cell.column.columnDef.width}; min-width: ${cell.column.columnDef.minWidth || '50px'};`
                      : ''}>
                    <FlexRender
                      content={cell.column.columnDef.cell}
                      context={cell.getContext()} />
                  </TableUI.Cell>
                {/each}
              </TableUI.Row>
            {/each}
          {:else}
            <TableUI.Row>
              <TableUI.Cell colspan={tableInstance.getAllColumns().length} class="h-24 text-center">
                No results.
              </TableUI.Cell>
            </TableUI.Row>
          {/if}
        </TableUI.Body>
      </TableUI.Root>
    </div>
    <DataTablePagination tableModel={tableInstance} />
  </div>
{:else}
  <div class="flex h-24 items-center justify-center">
    <p>Loading...</p>
  </div>
{/if}
