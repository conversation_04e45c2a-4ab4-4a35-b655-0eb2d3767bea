/**
 * Job Alerts Email Worker
 *
 * This module processes job alert notifications from the Redis queue
 * and sends emails to users when new job matches are found.
 */

import { redis } from "../redis.js";
import { logger } from "./utils/logger.js";
import { sendJobMatchEmail } from "./transactional.js";
import { PrismaClient } from "@prisma/client";

// Initialize Prisma client
const prisma = new PrismaClient();

// Define queue keys
const JOB_ALERTS_QUEUE = "email:job-alerts";
const JOB_ALERTS_GROUP = "email-workers";
const JOB_ALERTS_CONSUMER = `worker-${Math.random().toString(36).substring(2, 9)}`;

/**
 * Start the job alerts worker
 */
export async function startJobAlertsWorker() {
  logger.info("🚀 [Job Alerts Worker] Starting job alerts worker...");

  try {
    // Ensure Redis connection is ready before processing
    if (redis.status !== "ready") {
      logger.info("⏳ [Job Alerts Worker] Waiting for Redis connection...");
      await new Promise<void>((resolve) => {
        const checkInterval = setInterval(() => {
          if (redis.status === "ready") {
            clearInterval(checkInterval);
            logger.info("✅ [Job Alerts Worker] Redis connection established");
            resolve();
          }
        }, 500);
      });
    }

    // Create consumer group if it doesn't exist
    try {
      await redis.xgroup(
        "CREATE",
        JOB_ALERTS_QUEUE,
        JOB_ALERTS_GROUP,
        "0",
        "MKSTREAM"
      );
      logger.info(
        `✅ [Job Alerts Worker] Created consumer group ${JOB_ALERTS_GROUP}`
      );
    } catch (error: any) {
      // Ignore BUSYGROUP error (group already exists)
      if (!error.message.includes("BUSYGROUP")) {
        throw error;
      }
      logger.info(
        `ℹ️ [Job Alerts Worker] Consumer group ${JOB_ALERTS_GROUP} already exists`
      );
    }

    // Start processing loop
    processJobAlerts();

    logger.info(
      "✅ [Job Alerts Worker] Job alerts worker started successfully"
    );
  } catch (error) {
    logger.error(
      "❌ [Job Alerts Worker] Failed to start job alerts worker:",
      error
    );
    throw error;
  }
}

/**
 * Process job alerts from the Redis queue (event-driven, non-polling)
 */
async function processJobAlerts() {
  try {
    // Set up a subscriber for job alert events
    const subscriber = redis.duplicate();

    // Subscribe to job alert events channel
    await subscriber.subscribe("email:job-alerts:events");
    logger.info(
      "✅ [Job Alerts Worker] Subscribed to email:job-alerts:events channel"
    );

    // Process any existing messages in the stream first
    await processJobAlertBatch();

    // Listen for job alert events
    subscriber.on("message", async (channel, message) => {
      if (channel === "email:job-alerts:events") {
        logger.info(
          `📬 [Job Alerts Worker] Received job alert event: ${message}`
        );
        await processJobAlertBatch();
      }
    });

    logger.info(
      "✅ [Job Alerts Worker] Job alerts worker is now listening for events"
    );
  } catch (error) {
    logger.error(
      "❌ [Job Alerts Worker] Error setting up job alerts worker:",
      error
    );
  }
}

/**
 * Process a batch of job alert messages from the Redis stream
 */
async function processJobAlertBatch() {
  try {
    // Read messages from the stream (one-time read, not continuous polling)
    const streams = await redis.xreadgroup(
      "GROUP",
      JOB_ALERTS_GROUP,
      JOB_ALERTS_CONSUMER,
      "COUNT",
      10, // Process more messages at once for efficiency
      "STREAMS",
      JOB_ALERTS_QUEUE,
      ">"
    );

    // If no messages, just return
    if (!streams || streams.length === 0) {
      return;
    }

    // Process each message
    const [, messages] = streams[0];
    logger.info(
      `📬 [Job Alerts Worker] Processing ${messages.length} job alerts`
    );

    for (const [id, fields] of messages) {
      try {
        // Get the job data
        const jobStr = fields[1];
        const job = JSON.parse(jobStr);

        // Send the email
        await sendJobMatchEmail(job.to, job.jobData, {
          firstName: job.firstName,
          alertName: job.alertName,
        });

        logger.info(
          `✅ [Job Alerts Worker] Sent job alert notification: ${id}`
        );

        // Acknowledge the message
        await redis.xack(JOB_ALERTS_QUEUE, JOB_ALERTS_GROUP, id);
      } catch (error) {
        logger.error(
          `❌ [Job Alerts Worker] Error processing job alert: ${id}`,
          error
        );
        // Still acknowledge the message to avoid reprocessing
        await redis.xack(JOB_ALERTS_QUEUE, JOB_ALERTS_GROUP, id);
      }
    }
  } catch (error) {
    logger.error(
      "❌ [Job Alerts Worker] Error processing job alert batch:",
      error
    );
  }
}

// Start the worker if this file is run directly
if (import.meta.url === import.meta.resolve("./job-alerts.ts")) {
  startJobAlertsWorker().catch((error) => {
    logger.error(
      "❌ [Job Alerts Worker] Failed to start job alerts worker:",
      error
    );
    process.exit(1);
  });
}
