<!-- MaintenanceHistoryDialog.svelte -->
<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { Button } from '$lib/components/ui/button/index.js';

  import { MessageSquare, RefreshCw, Clock } from 'lucide-svelte';
  import StatusTag from '$components/system-status/StatusTag.svelte';
  import type { StatusTagType } from '../../routes/system-status/types';

  // Props
  export let eventId: string;
  export let open = false;
  export let onClose = () => {};

  // State
  let history: any[] = [];
  let isLoading = false;
  let error: string | null = null;

  // Format date
  function formatDate(date: string | Date): string {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    }).format(new Date(date));
  }

  // Get change type icon
  function getChangeTypeIcon(changeType: string) {
    switch (changeType) {
      case 'status_change':
        return RefreshCw;
      case 'comment':
        return MessageSquare;
      case 'update':
        return Clock;
      default:
        return Clock;
    }
  }

  // Fetch history
  async function fetchHistory() {
    if (!eventId) return;

    isLoading = true;
    error = null;

    try {
      const response = await fetch(`/api/maintenance/${eventId}/history`);

      if (!response.ok) {
        throw new Error(`Failed to fetch history: ${response.status}`);
      }

      const data = await response.json();
      history = data;
    } catch (err) {
      console.error('Error fetching maintenance history:', err);
      error = err instanceof Error ? err.message : 'Failed to load history';
    } finally {
      isLoading = false;
    }
  }

  // Load history when dialog opens
  $: if (open && eventId) {
    fetchHistory();
  }
</script>

<Dialog.Root bind:open>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="sm:max-w-[600px]">
      <Dialog.Header>
        <Dialog.Title>Maintenance History</Dialog.Title>
        <Dialog.Description>
          View the history of updates and status changes for this maintenance event.
        </Dialog.Description>
      </Dialog.Header>

      <div class="py-4">
        {#if isLoading}
          <div class="flex justify-center py-8">
            <div
              class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent">
            </div>
          </div>
        {:else if error}
          <div class="border-destructive/50 bg-destructive/10 rounded-lg border p-4 text-center">
            <p class="text-destructive">{error}</p>
            <Button variant="outline" class="mt-2" onclick={fetchHistory}>Retry</Button>
          </div>
        {:else if history.length === 0}
          <div class="rounded-lg border p-6 text-center">
            <p class="text-muted-foreground">No history available for this maintenance event.</p>
          </div>
        {:else}
          <div class="space-y-4">
            {#each history as item}
              <div class="rounded-lg border p-4">
                <div class="flex items-start gap-3">
                  <div class="mt-0.5">
                    {#key item.changeType}
                      <svelte:component
                        this={getChangeTypeIcon(item.changeType)}
                        class="text-muted-foreground h-5 w-5" />
                    {/key}
                  </div>
                  <div class="flex-1">
                    <div class="mb-1 flex items-center justify-between">
                      <p class="text-sm font-medium">
                        {#if item.changeType === 'status_change'}
                          Status changed from
                          {#if item.previousStatus}
                            <StatusTag status={item.previousStatus as StatusTagType} />
                          {/if}
                          to
                          {#if item.newStatus}
                            <StatusTag status={item.newStatus as StatusTagType} />
                          {/if}
                        {:else if item.changeType === 'comment'}
                          Comment added
                        {:else}
                          Event updated
                        {/if}
                      </p>
                      <p class="text-muted-foreground text-xs">{formatDate(item.createdAt)}</p>
                    </div>

                    {#if item.comment}
                      <div class="bg-muted mt-2 rounded-md p-3">
                        <p class="text-sm">{item.comment}</p>
                      </div>
                    {/if}

                    {#if item.changeType === 'update' && item.metadata?.changedFields?.length > 0}
                      <div class="mt-2">
                        <p class="text-muted-foreground text-xs">Changed fields:</p>
                        <div class="mt-1 flex flex-wrap gap-1">
                          {#each item.metadata.changedFields as field}
                            <Badge variant="outline" class="text-xs">{field}</Badge>
                          {/each}
                        </div>
                      </div>
                    {/if}
                  </div>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={onClose}>Close</Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
