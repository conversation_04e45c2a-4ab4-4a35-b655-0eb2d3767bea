
<PERSON>
Senior Software Engineer
john<PERSON><EMAIL>
(555) 987-6543
San Francisco, CA
https://linkedin.com/in/johnsmith
https://github.com/johnsmith

SUMMARY
Experienced software engineer with 8+ years of expertise in full-stack development, cloud architecture, and DevOps. Passionate about building scalable applications and mentoring junior developers. Strong background in JavaScript, TypeScript, React, Node.js, and AWS.

SKILLS
Programming Languages: JavaScript, TypeScript, Python, Java, C#, SQL
Frameworks & Libraries: React, Angular, Vue.js, Node.js, Express, Django, Spring Boot
Cloud & DevOps: AWS, Azure, Docker, Kubernetes, Terraform, CI/CD
Databases: PostgreSQL, MongoDB, MySQL, Redis, Elasticsearch
Tools: Git, JIRA, Confluence, Jenkins, GitHub Actions, Webpack, Babel

WORK EXPERIENCE
Senior Software Engineer
Acme Technologies, San Francisco, CA
January 2020 - Present
• Led a team of 5 engineers in developing a microservices architecture that improved system reliability by 35%
• Implemented CI/CD pipelines using GitHub Actions, reducing deployment time by 70%
• Architected and built a real-time analytics dashboard using React, Node.js, and WebSockets
• Mentored junior developers through code reviews, pair programming, and technical workshops
• Optimized database queries and implemented caching strategies, improving API response times by 60%
