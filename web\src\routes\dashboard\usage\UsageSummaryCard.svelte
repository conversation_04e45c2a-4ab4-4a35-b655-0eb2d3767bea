<script lang="ts">
  import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '$lib/components/ui/card';

  export let title: string;
  export let value: number;
  export let subtitle: string = '';
  export let warning: string = '';
  export let showWarning: boolean = false;
</script>

<Card>
  <CardHeader class="pb-2">
    <CardTitle class="text-sm font-medium">{title}</CardTitle>
  </CardHeader>
  <CardContent>
    <div class="text-2xl font-bold">{value !== undefined ? value : 'N/A'}</div>
    {#if subtitle}
      <p class="text-muted-foreground text-xs">{subtitle}</p>
    {/if}
    {#if showWarning && warning}
      <p class="text-destructive text-xs">{warning}</p>
    {/if}
  </CardContent>
</Card>
