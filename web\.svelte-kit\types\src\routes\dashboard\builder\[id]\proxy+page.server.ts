// @ts-nocheck
import { redirect } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth.js';
import type { PageServerLoad } from './$types.js';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import {
  resumeFormSchema,
  designFormSchema,
  designDefaultValues,
} from '$lib/validators/buildResume.js';

// Using the shared Prisma client from $lib/server/prisma

export const load = async ({ params, cookies, locals }: Parameters<PageServerLoad>[0]) => {
  const token = cookies.get('auth_token');
  const user = token && verifySessionToken(token);
  if (!user) throw redirect(302, '/auth/sign-in');

  const id = params.id;

  // Get the resume data
  console.log('Looking up with ID:', id);

  // First try to find by resume ID
  let resume = await prisma.resume.findUnique({
    where: { id },
    include: {
      document: {
        include: {
          profile: true,
        },
      },
    },
  });

  // If not found, try to find by document ID
  if (!resume) {
    console.log('Resume not found by ID, trying to find by document ID');

    // Try to find the document
    const document = await prisma.document.findUnique({
      where: { id },
      include: {
        profile: true,
      },
    });

    if (document) {
      console.log('Document found, looking for associated resume');

      // Find the resume associated with this document
      resume = await prisma.resume.findUnique({
        where: { documentId: document.id },
        include: {
          document: {
            include: {
              profile: true,
            },
          },
        },
      });
    }
  }

  console.log('Resume found:', resume ? 'Yes' : 'No');

  if (!resume) {
    console.log('No resume found for ID:', id);
    throw redirect(302, '/dashboard/documents');
  }

  // Check if the user has access to this resume
  // For development, temporarily disable strict user check
  console.log('Resume document userId:', resume.document.userId);
  console.log('Current user id:', user.id);

  // In production, uncomment this check
  // if (resume.document.userId !== user.id) {
  //   throw redirect(302, '/dashboard/documents');
  // }

  // Get the validated forms with defaults
  const design = await superValidate(designDefaultValues, zod(designFormSchema));

  // If the resume has parsed data, use it as the initial form data
  // Cast to any to avoid TypeScript errors
  let initialResumeData = (resume.parsedData || {}) as any;
  console.log('Initial resume data:', JSON.stringify(initialResumeData));

  // Ensure the parsed data has the expected structure
  const defaultData = {
    header: {
      name: '',
      email: '',
      phone: '',
    },
    summary: {
      content: '',
    },
    experience: [],
    education: [],
    skills: [],
    projects: [],
    certifications: [],
  };

  let needsUpdate = false;

  // Check if any required fields are missing
  if (!initialResumeData.header) {
    console.log('Resume data missing header, using default');
    initialResumeData.header = defaultData.header;
    needsUpdate = true;
  }

  if (!initialResumeData.summary) {
    console.log('Resume data missing summary, using default');
    initialResumeData.summary = defaultData.summary;
    needsUpdate = true;
  }

  if (!initialResumeData.experience) {
    console.log('Resume data missing experience, using default');
    initialResumeData.experience = defaultData.experience;
    needsUpdate = true;
  }

  if (!initialResumeData.education) {
    console.log('Resume data missing education, using default');
    initialResumeData.education = defaultData.education;
    needsUpdate = true;
  }

  if (!initialResumeData.skills) {
    console.log('Resume data missing skills, using default');
    initialResumeData.skills = defaultData.skills;
    needsUpdate = true;
  }

  if (!initialResumeData.projects) {
    console.log('Resume data missing projects, using default');
    initialResumeData.projects = defaultData.projects;
    needsUpdate = true;
  }

  if (!initialResumeData.certifications) {
    console.log('Resume data missing certifications, using default');
    initialResumeData.certifications = defaultData.certifications;
    needsUpdate = true;
  }

  // Update the resume with the default structure if needed
  if (needsUpdate) {
    console.log('Updating resume with default structure');
    await prisma.resume.update({
      where: { id: resume.id },
      data: {
        parsedData: initialResumeData,
      },
    });
  }

  const resumeForm = await superValidate(initialResumeData, zod(resumeFormSchema));

  return {
    form: {
      resume: resumeForm,
      design,
    },
    resumeData: resume,
    user,
  };
};
