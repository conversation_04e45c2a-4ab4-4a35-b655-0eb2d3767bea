import { PrismaClient } from "@prisma/client";
import { logger } from "../../utils/logger";
import path from "path";
import { read, utils } from "xlsx";

// Set your Excel file location (make sure it’s in your project's data folder)
const DATA_PATH = path.resolve("data/Alternate Titles.xlsx");

const prisma = new PrismaClient();

// Helper to treat blank or 'n/a' values as missing.
const isNA = (v?: string) =>
  !v || v.trim() === "" || v.trim().toLowerCase() === "n/a";

export async function fetchAlternateTitles() {
  logger.info("📥 Reading O*NET alternate‑titles Excel file…");

  const seen = new Set<string>();
  let count = 0;

  try {
    // Read the workbook from disk.
    const workbook = read(
      await import("fs").then((fs) => fs.readFileSync(DATA_PATH))
    );
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];

    // Convert the sheet to JSON. Each row is now an object keyed by the header row.
    const rows = utils.sheet_to_json<Record<string, any>>(sheet, {
      defval: "",
    });

    // Process each row
    for (const row of rows) {
      // Expected headers (exact names as on the Excel file):
      // "O*NET-SOC Code", "Title", "Alternate Title", "Short Title", "Source(s)"
      const socCode = row["O*NET-SOC Code"]?.toString().trim();
      const category = row["Title"]?.toString().trim(); // for our 'category'
      const altTitle = row["Alternate Title"]?.toString().trim(); // main title field
      const rawShort = row["Short Title"]?.toString().trim();
      const shortTitle = isNA(rawShort) ? undefined : rawShort;
      const rawSource = row["Source(s)"]?.toString().trim();
      const source = isNA(rawSource) ? null : rawSource;

      if (!socCode || isNA(altTitle)) continue; // Skip invalid rows

      const key = `${socCode}::${altTitle}`;
      if (seen.has(key)) continue;

      // Skip if the record already exists in the database.
      const exists = await prisma.occupations.findFirst({
        where: { socCode, title: altTitle },
        select: { id: true },
      });
      if (exists) continue;

      await prisma.occupations.create({
        data: {
          socCode,
          title: altTitle, // the alternate title from Excel
          shortTitle: shortTitle || null,
          category, // category is taken from the "Title" column
          source,
        },
      });

      seen.add(key);
      count++;
      logger.debug(
        `📌 Inserted occupation: ${socCode} | category="${category}" | title="${altTitle}"` +
          (shortTitle ? ` | shortTitle="${shortTitle}"` : "")
      );
    }
  } catch (err) {
    logger.error(`❌ Error processing Excel file: ${err}`);
    throw err;
  }

  logger.info(`✅ Stored ${count} new alternate titles in Occupations schema`);
}
