// src/routes/api/help/[slug]/+server.ts
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { prisma } from '$lib/server/prisma';

// Get a specific help article by slug
export const GET: RequestHandler = async ({ params }) => {
  try {
    const { slug } = params;

    // Get the article from the database
    const article = await prisma.helpArticle.findUnique({
      where: {
        slug,
      },
      include: {
        category: true,
        tags: true,
        relatedArticles: {
          include: {
            category: true,
          },
        },
      },
    });

    if (!article) {
      return json({ error: 'Article not found' }, { status: 404 });
    }

    // Increment view count
    await prisma.helpArticle.update({
      where: {
        id: article.id,
      },
      data: {
        viewCount: {
          increment: 1,
        },
      },
    });

    return json(article);
  } catch (error) {
    console.error('Error fetching help article:', error);
    return json({ error: 'Failed to fetch help article' }, { status: 500 });
  }
};

// Update a help article (admin only)
export const PUT: RequestHandler = async ({ params, request, locals }) => {
  const user = locals.user;
  if (!user || user.role !== 'ADMIN') {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { slug } = params;
    const { title, content, excerpt, categoryId, tagIds, published, relatedArticleIds } = await request.json();

    // Check if article exists
    const existingArticle = await prisma.helpArticle.findUnique({
      where: {
        slug,
      },
    });

    if (!existingArticle) {
      return json({ error: 'Article not found' }, { status: 404 });
    }

    // Update the article
    const article = await prisma.helpArticle.update({
      where: {
        slug,
      },
      data: {
        title,
        content,
        excerpt,
        published,
        categoryId,
        tags: {
          set: [], // Clear existing tags
          connect: tagIds?.map((id: string) => ({ id })) || [],
        },
        relatedArticles: {
          set: [], // Clear existing related articles
          connect: relatedArticleIds?.map((id: string) => ({ id })) || [],
        },
      },
      include: {
        category: true,
        tags: true,
        relatedArticles: true,
      },
    });

    return json(article);
  } catch (error) {
    console.error('Error updating help article:', error);
    return json({ error: 'Failed to update help article' }, { status: 500 });
  }
};

// Delete a help article (admin only)
export const DELETE: RequestHandler = async ({ params, locals }) => {
  const user = locals.user;
  if (!user || user.role !== 'ADMIN') {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { slug } = params;

    // Check if article exists
    const existingArticle = await prisma.helpArticle.findUnique({
      where: {
        slug,
      },
    });

    if (!existingArticle) {
      return json({ error: 'Article not found' }, { status: 404 });
    }

    // Delete the article
    await prisma.helpArticle.delete({
      where: {
        slug,
      },
    });

    return json({ success: true });
  } catch (error) {
    console.error('Error deleting help article:', error);
    return json({ error: 'Failed to delete help article' }, { status: 500 });
  }
};
