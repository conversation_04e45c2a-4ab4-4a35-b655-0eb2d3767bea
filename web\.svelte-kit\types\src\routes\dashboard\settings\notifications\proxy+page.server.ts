// @ts-nocheck
import { redirect, fail } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import type { PageServerLoad, Actions } from './$types.js';
import {
  notificationFormSchema,
  formToNotificationSettings,
  notificationSettingsToForm,
  formToDbModel,
  dbModelToForm,
} from '$lib/schemas/notification-schema';
import type { User, NotificationSettings } from '@prisma/client';

export const load = async ({ locals }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user || !user.email) {
    throw redirect(302, '/auth/sign-in');
  }

  // Get user data with notification settings
  const userData = await prisma.user.findUnique({
    where: { email: user.email },
    include: {
      notifications: true,
    },
  });

  if (!userData) {
    throw redirect(302, '/auth/sign-in');
  }

  locals.user = userData;

  let userPreferences;

  // Check if user has notification settings in the database
  if (userData.notifications) {
    // Convert database model to form data
    userPreferences = dbModelToForm(userData.notifications);
  } else {
    // If no database settings exist, check for legacy settings in preferences
    const preferences = (userData.preferences as any) || {};
    const notificationPrefs = preferences.notifications || {};

    // Create default notification settings structure
    const defaultSettings = {
      email: {
        enabled: true,
        digest: 'daily',
        format: 'html',
      },
      jobs: {
        matches: true,
        matchFrequency: 'daily',
        applicationStatus: true,
        newJobs: true,
        newJobsFrequency: 'daily',
        interviewReminders: true,
        savedJobsUpdates: true,
        emailNotifications: true,
        browserNotifications: true,
        mobileNotifications: false,
      },
      marketing: {
        enabled: true,
        productUpdates: true,
        newsletterSubscription: false,
        eventInvitations: false,
      },
      platform: {
        browser: true,
        desktop: false,
        mobile: false,
        push: true,
      },
    };

    // Merge user preferences with defaults
    const userSettings = {
      email: { ...defaultSettings.email, ...notificationPrefs.email },
      jobs: { ...defaultSettings.jobs, ...notificationPrefs.jobs },
      marketing: { ...defaultSettings.marketing, ...notificationPrefs.marketing },
      platform: { ...defaultSettings.platform, ...notificationPrefs.platform },
    };

    // Convert settings to form data format
    userPreferences = notificationSettingsToForm(userSettings);
  }

  // Create the form with initial values
  const form = await superValidate(userPreferences, zod(notificationFormSchema));

  return {
    user: userData,
    form,
  };
};

export const actions = {
  default: async ({ request, locals }: import('./$types').RequestEvent) => {
    // Use the user from locals which is already authenticated
    const userData = locals.user;

    if (!userData || !userData.email) {
      // Only redirect if user is not in locals
      throw redirect(302, '/auth/sign-in');
    }

    // Get user data with notification settings
    const userWithNotifications = await prisma.user.findUnique({
      where: { email: userData.email },
      include: {
        notifications: true,
      },
    });

    if (!userWithNotifications) {
      throw redirect(302, '/auth/sign-in');
    }

    const form = await superValidate(request, zod(notificationFormSchema));

    if (!form.valid) {
      return fail(400, { form });
    }

    try {
      // Convert form data to database model
      const dbNotificationSettings = formToDbModel(form.data, userWithNotifications.id);

      // Also update the legacy preferences for backward compatibility
      const preferences = (userWithNotifications.preferences as any) || {};
      const notificationSettings = formToNotificationSettings(form.data);
      const updatedPreferences = {
        ...preferences,
        notifications: notificationSettings,
      };

      // Start a transaction to update both the notification settings and user preferences
      await prisma.$transaction(async (tx) => {
        // Update or create notification settings in the database
        if (userWithNotifications.notifications) {
          // Update existing notification settings
          await tx.notificationSettings.update({
            where: { id: userWithNotifications.notifications.id },
            data: {
              // Email notifications
              emailEnabled: dbNotificationSettings.emailEnabled,
              emailDigest: dbNotificationSettings.emailDigest,
              emailFormat: dbNotificationSettings.emailFormat,

              // Job notifications
              jobMatchEnabled: dbNotificationSettings.jobMatchEnabled,
              jobMatchFrequency: dbNotificationSettings.jobMatchFrequency,
              applicationStatusEnabled: dbNotificationSettings.applicationStatusEnabled,
              newJobsEnabled: dbNotificationSettings.newJobsEnabled,
              newJobsFrequency: dbNotificationSettings.newJobsFrequency,
              interviewRemindersEnabled: dbNotificationSettings.interviewRemindersEnabled,
              savedJobsUpdatesEnabled: dbNotificationSettings.savedJobsUpdatesEnabled,

              // Job notification channels
              jobEmailEnabled: dbNotificationSettings.jobEmailEnabled,
              jobBrowserEnabled: dbNotificationSettings.jobBrowserEnabled,
              jobMobileEnabled: dbNotificationSettings.jobMobileEnabled,

              // Marketing notifications
              marketingEnabled: dbNotificationSettings.marketingEnabled,
              productUpdatesEnabled: dbNotificationSettings.productUpdatesEnabled,
              newsletterEnabled: dbNotificationSettings.newsletterEnabled,
              eventInvitationsEnabled: dbNotificationSettings.eventInvitationsEnabled,

              // Platform notifications
              browserEnabled: dbNotificationSettings.browserEnabled,
              desktopEnabled: dbNotificationSettings.desktopEnabled,
              mobileEnabled: dbNotificationSettings.mobileEnabled,
              pushEnabled: dbNotificationSettings.pushEnabled,
            },
          });
        } else {
          // Create new notification settings
          await tx.notificationSettings.create({
            data: {
              userId: userWithNotifications.id,

              // Email notifications
              emailEnabled: dbNotificationSettings.emailEnabled,
              emailDigest: dbNotificationSettings.emailDigest,
              emailFormat: dbNotificationSettings.emailFormat,

              // Job notifications
              jobMatchEnabled: dbNotificationSettings.jobMatchEnabled,
              jobMatchFrequency: dbNotificationSettings.jobMatchFrequency,
              applicationStatusEnabled: dbNotificationSettings.applicationStatusEnabled,
              newJobsEnabled: dbNotificationSettings.newJobsEnabled,
              newJobsFrequency: dbNotificationSettings.newJobsFrequency,
              interviewRemindersEnabled: dbNotificationSettings.interviewRemindersEnabled,
              savedJobsUpdatesEnabled: dbNotificationSettings.savedJobsUpdatesEnabled,

              // Job notification channels
              jobEmailEnabled: dbNotificationSettings.jobEmailEnabled,
              jobBrowserEnabled: dbNotificationSettings.jobBrowserEnabled,
              jobMobileEnabled: dbNotificationSettings.jobMobileEnabled,

              // Marketing notifications
              marketingEnabled: dbNotificationSettings.marketingEnabled,
              productUpdatesEnabled: dbNotificationSettings.productUpdatesEnabled,
              newsletterEnabled: dbNotificationSettings.newsletterEnabled,
              eventInvitationsEnabled: dbNotificationSettings.eventInvitationsEnabled,

              // Platform notifications
              browserEnabled: dbNotificationSettings.browserEnabled,
              desktopEnabled: dbNotificationSettings.desktopEnabled,
              mobileEnabled: dbNotificationSettings.mobileEnabled,
              pushEnabled: dbNotificationSettings.pushEnabled,
            },
          });
        }

        // Update user preferences for backward compatibility
        await tx.user.update({
          where: { id: userWithNotifications.id },
          data: {
            preferences: updatedPreferences,
          },
        });
      });

      return { form, success: true };
    } catch (error) {
      console.error('Error updating notification settings:', error);
      return fail(500, { form, error: 'Failed to update notification settings' });
    }
  },
};
;null as any as Actions;