/**
 * Job Service
 * 
 * This service provides functions for interacting with job-related API endpoints.
 */

/**
 * Interface for job search parameters
 */
export interface JobSearchParams {
  title?: string;
  location?: string;
  locationType?: string[];
  experience?: string[];
  category?: string[];
  education?: string[];
  salary?: string;
  datePosted?: string;
  companies?: string[];
  easyApply?: boolean;
  saveSearch?: boolean;
  [key: string]: any;
}

/**
 * Interface for job search results
 */
export interface JobSearchResult {
  searchId?: string | null;
  results: JobListing[];
  saved?: boolean;
  error?: string;
  limitReached?: boolean;
}

/**
 * Interface for job listing
 */
export interface JobListing {
  id: string;
  title: string;
  company: string;
  location: string;
  url: string;
  employmentType?: string;
  remoteType?: string;
  postedDate?: string;
  salary?: string;
  applyLink?: string;
  companyLogo?: string;
  description?: string;
  [key: string]: any;
}

/**
 * Search for jobs using the API
 * 
 * @param params Search parameters
 * @returns Search results
 */
export async function searchJobs(params: JobSearchParams): Promise<JobSearchResult> {
  try {
    // Validate required parameters
    if (!params.title && !params.location && !params.companies?.length) {
      throw new Error('At least one search parameter is required');
    }

    // Make the API request
    const response = await fetch('/api/jobs/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    // Parse the response
    const data = await response.json();

    // Check for errors
    if (!response.ok) {
      console.error('Job search error:', data.error);
      return {
        results: [],
        error: data.error || 'Failed to search jobs',
        limitReached: data.limitReached || false,
      };
    }

    // Return the search results
    return {
      searchId: data.searchId || null,
      results: data.results || [],
      saved: data.saved || false,
    };
  } catch (error) {
    console.error('Error searching jobs:', error);
    return {
      results: [],
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Get job details by ID
 * 
 * @param id Job ID
 * @returns Job details
 */
export async function getJobById(id: string): Promise<JobListing | null> {
  try {
    // Make the API request
    const response = await fetch(`/api/jobs/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Parse the response
    const data = await response.json();

    // Check for errors
    if (!response.ok) {
      console.error('Error fetching job:', data.error);
      return null;
    }

    // Return the job details
    return data;
  } catch (error) {
    console.error(`Error fetching job with ID ${id}:`, error);
    return null;
  }
}
