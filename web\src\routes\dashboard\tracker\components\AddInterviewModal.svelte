<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import { CalendarIcon, Loader2, Check } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import { Calendar } from '$lib/components/ui/calendar';
  import * as Popover from '$lib/components/ui/popover';
  import { DateFormatter, type DateValue, getLocalTimeZone, today } from '@internationalized/date';
  import { cn } from '$lib/utils';
  import { superForm } from 'sveltekit-superforms/client';
  import { zodClient } from 'sveltekit-superforms/adapters';
  import { interviewSchema, interviewDefaultValues } from '$lib/validators/interview';

  // Props
  let {
    applicationId,
    open = false,
    onClose,
    onSuccess,
  } = $props<{
    applicationId: string;
    open?: boolean;
    onClose: () => void;
    onSuccess: () => void;
  }>();

  // Date formatter
  const df = new DateFormatter('en-US', {
    dateStyle: 'long',
  });

  // Initialize form with Superforms
  const { form, errors, enhance, submitting } = superForm(interviewDefaultValues, {
    validators: zodClient(interviewSchema),
    dataType: 'json',
    onSubmit: async () => {
      try {
        const jsDate = dateValue ? dateValue.toDate(getLocalTimeZone()) : new Date();

        const response = await fetch(`/api/applications/${applicationId}/interviews`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            stageName: $form.stageName,
            stageDate: jsDate,
            outcome: $form.outcome || null,
            feedback: $form.feedback || null,
            interviewers: $form.interviewers || null,
            duration: $form.duration ? parseInt($form.duration, 10) : null,
            notes: $form.notes || null,
            nextAction: $form.nextAction || null,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create interview');
        }

        toast.success('Interview added successfully');
        onSuccess();
        return;
      } catch (error) {
        console.error('Error creating interview:', error);
        toast.error(error.message || 'Failed to create interview');
        return;
      }
    },
  });

  // Date value for the calendar
  let dateValue = $state<DateValue | undefined>(today(getLocalTimeZone()));

  // Handle date selection
  function handleDateChange(date: DateValue | undefined) {
    if (date) {
      $form.stageDate = date.toString();
    } else {
      $form.stageDate = '';
    }
  }

  // Initialize with today's date
  $effect(() => {
    if (dateValue) {
      handleDateChange(dateValue);
    }
  });

  // Initialize date handling

  // Interview stage options
  const stageOptions = [
    { value: 'Phone Screen', label: 'Phone Screen' },
    { value: 'Technical Interview', label: 'Technical Interview' },
    { value: 'Behavioral Interview', label: 'Behavioral Interview' },
    { value: 'Onsite Interview', label: 'Onsite Interview' },
    { value: 'Final Interview', label: 'Final Interview' },
    { value: 'HR Interview', label: 'HR Interview' },
    { value: 'Case Study', label: 'Case Study' },
    { value: 'Coding Challenge', label: 'Coding Challenge' },
    { value: 'Take-home Assignment', label: 'Take-home Assignment' },
    { value: 'Other', label: 'Other' },
  ];

  // Outcome options
  const outcomeOptions = [
    { value: 'Scheduled', label: 'Scheduled' },
    { value: 'Pending', label: 'Pending' },
    { value: 'Passed', label: 'Passed' },
    { value: 'Failed', label: 'Failed' },
  ];
</script>

<Dialog.Root {open}>
  <Dialog.Overlay />
  <Dialog.Content class="sm:max-w-[500px]">
    <Dialog.Header>
      <Dialog.Title>Add Interview</Dialog.Title>
      <Dialog.Description>
        Record details about an interview stage for this application.
      </Dialog.Description>
    </Dialog.Header>

    <form
      method="POST"
      use:enhance={{
        onSubmit: () => {
          if (!$form.stageName || $form.stageName.trim() === '') {
            toast.error('Please select an interview stage');
            return false;
          }
          return true;
        },
      }}
      class="space-y-4">
      <p class="text-muted-foreground mb-2 text-sm">Fields marked with * are required</p>
      <!-- Debug info -->
      {#if import.meta.env.DEV}
        <div class="text-muted-foreground bg-muted mb-2 rounded p-2 text-xs">
          <p>
            Debug: Form valid: {$form.stageName && $form.stageName.trim() !== '' ? 'Yes' : 'No'}
          </p>
          <p>
            Stage name: "{$form.stageName}" - Valid: {$form.stageName &&
            $form.stageName.trim() !== ''
              ? 'Yes'
              : 'No'}
          </p>
          <p>Stage date: "{$form.stageDate}" (Auto-filled with today's date)</p>
        </div>
      {/if}
      <div class="space-y-2">
        <Label for="stageName">Interview Stage*</Label>
        <Select.Root type="single" bind:value={$form.stageName}>
          <Select.Trigger class="h-10 w-full px-3 py-2">
            <Select.Value placeholder="Select interview stage" />
          </Select.Trigger>
          <Select.Content class="z-50 max-h-60 w-full overflow-y-auto">
            {#each stageOptions as option}
              <Select.Item value={option.value}>{option.label}</Select.Item>
            {/each}
          </Select.Content>
        </Select.Root>
        {#if $errors.stageName}
          <p class="text-destructive text-sm">{$errors.stageName}</p>
        {/if}
      </div>

      <div class="space-y-2">
        <Label for="stageDate">Date*</Label>
        <input type="hidden" name="stageDate" bind:value={$form.stageDate} />
        <Popover.Root>
          <Popover.Trigger>
            <Button
              id="stageDate"
              variant="outline"
              class={cn(
                'w-full justify-start text-left font-normal',
                !dateValue && 'text-muted-foreground'
              )}>
              <CalendarIcon class="mr-2 h-4 w-4" />
              {dateValue ? df.format(dateValue.toDate(getLocalTimeZone())) : 'Select date'}
            </Button>
          </Popover.Trigger>
          <Popover.Content class="w-auto p-0">
            <Calendar
              type="single"
              value={dateValue}
              onValueChange={(v) => {
                dateValue = v;
                handleDateChange(v);
                // Close the popover after selecting a date
                setTimeout(() => {
                  const trigger = document.getElementById('stageDate');
                  if (trigger) trigger.click();
                }, 100);
              }}
              initialFocus />
          </Popover.Content>
        </Popover.Root>
        {#if $errors.stageDate}
          <p class="text-destructive text-sm">{$errors.stageDate}</p>
        {/if}
      </div>

      <div class="space-y-2">
        <Label for="outcome">Outcome</Label>
        <Select.Root type="single" bind:value={$form.outcome}>
          <Select.Trigger class="h-10 w-full px-3 py-2">
            <Select.Value placeholder="Select outcome" />
          </Select.Trigger>
          <Select.Content class="z-50 max-h-60 w-full overflow-y-auto">
            {#each outcomeOptions as option}
              <Select.Item value={option.value}>{option.label}</Select.Item>
            {/each}
          </Select.Content>
        </Select.Root>
      </div>

      <div class="space-y-2">
        <Label for="interviewers">Interviewers</Label>
        <Input bind:value={$form.interviewers} />
      </div>

      <div class="space-y-2">
        <Label for="duration">Duration (minutes)</Label>
        <Input type="number" bind:value={$form.duration} />
      </div>

      <div class="space-y-2">
        <Label for="feedback">Feedback</Label>
        <Textarea bind:value={$form.feedback} />
      </div>

      <div class="space-y-2">
        <Label for="nextAction">Next Action</Label>
        <Textarea bind:value={$form.nextAction} />
      </div>

      <div class="space-y-2">
        <Label for="notes">Notes</Label>
        <Textarea bind:value={$form.notes} />
      </div>

      <Dialog.Footer>
        <Button type="button" variant="outline" onclick={onClose}>Cancel</Button>
        <Button
          type="submit"
          disabled={$submitting || !$form.stageName || $form.stageName.trim() === ''}
          class={!$form.stageName || $form.stageName.trim() === ''
            ? 'cursor-not-allowed opacity-50'
            : ''}>
          {#if $submitting}
            <Loader2 class="mr-2 h-4 w-4 animate-spin" />
            Saving...
          {:else}
            Save Interview
          {/if}
        </Button>
      </Dialog.Footer>
    </form>
  </Dialog.Content>
</Dialog.Root>
