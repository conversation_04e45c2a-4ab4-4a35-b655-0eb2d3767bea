/**
 * Keyboard Shortcuts Registry
 *
 * This file serves as the central registry for all keyboard shortcuts in the application.
 * Shortcuts are organized by groups and follow the protocol of using Alt+ or Windows key+ combinations.
 */

import { goto } from '$app/navigation';
import { browser } from '$app/environment';
import type { ShortcutGroup } from '$lib/types/keyboard-shortcuts';
import { ShortcutPage, ModifierKey } from '$lib/types/keyboard-shortcuts';

// Navigation shortcuts
const navigationShortcuts: ShortcutGroup = {
  id: 'navigation',
  name: 'Navigation',
  activePages: [ShortcutPage.GLOBAL],
  shortcuts: [
    {
      id: 'nav-dashboard',
      action: 'Go to Dashboard',
      keys: `${ModifierKey.ALT}+D`,
      handler: () => {
        if (browser) goto('/dashboard');
      },
      description: 'Navigate to the dashboard page',
    },
    {
      id: 'nav-jobs',
      action: 'Go to Jobs',
      keys: `${ModifierKey.ALT}+J`,
      handler: () => {
        if (browser) goto('/dashboard/jobs');
      },
      description: 'Navigate to the jobs page',
    },
    {
      id: 'nav-applications',
      action: 'Go to Applications',
      keys: `${ModifierKey.ALT}+A`,
      handler: () => {
        if (browser) goto('/dashboard/applications');
      },
      description: 'Navigate to the applications page',
    },
    {
      id: 'nav-matches',
      action: 'Go to Matches',
      keys: `${ModifierKey.ALT}+M`,
      handler: () => {
        if (browser) goto('/dashboard/matches');
      },
      description: 'Navigate to the job matches page',
    },
    {
      id: 'nav-tracker',
      action: 'Go to Job Tracker',
      keys: `${ModifierKey.ALT}+T`,
      handler: () => {
        if (browser) goto('/dashboard/tracker');
      },
      description: 'Navigate to the job tracker page',
    },
    {
      id: 'nav-documents',
      action: 'Go to Documents',
      keys: `${ModifierKey.ALT}+O`,
      handler: () => {
        if (browser) goto('/dashboard/documents');
      },
      description: 'Navigate to the documents page',
    },
    {
      id: 'nav-automation',
      action: 'Go to Automation',
      keys: `${ModifierKey.ALT}+U`,
      handler: () => {
        if (browser) goto('/dashboard/automation');
      },
      description: 'Navigate to the automation page',
    },
    {
      id: 'nav-settings',
      action: 'Go to Settings',
      keys: `${ModifierKey.ALT}+S`,
      handler: () => {
        if (browser) goto('/dashboard/settings');
      },
      description: 'Navigate to the settings page',
    },
    {
      id: 'nav-profile',
      action: 'Go to Profile',
      keys: `${ModifierKey.ALT}+P`,
      handler: () => {
        if (browser) goto('/dashboard/settings/profile');
      },
      description: 'Navigate to the profile page',
    },
    {
      id: 'nav-billing',
      action: 'Go to Billing',
      keys: `${ModifierKey.ALT}+B`,
      handler: () => {
        if (browser) goto('/dashboard/settings/billing');
      },
      description: 'Navigate to the billing page',
    },
  ],
};

// UI shortcuts
const uiShortcuts: ShortcutGroup = {
  id: 'ui',
  name: 'User Interface',
  activePages: [ShortcutPage.GLOBAL],
  shortcuts: [
    {
      id: 'ui-search',
      action: 'Open Search',
      keys: `${ModifierKey.ALT}+K`,
      handler: (event) => {
        // This will be handled by the global search component
        event.preventDefault();
        // Dispatch a custom event that the search component can listen for
        if (browser) {
          document.dispatchEvent(new CustomEvent('toggle-global-search'));
        }
      },
      description: 'Open the global search dialog',
    },
    {
      id: 'ui-shortcuts',
      action: 'Show Keyboard Shortcuts',
      keys: `${ModifierKey.ALT}+/`,
      handler: (event) => {
        event.preventDefault();
        // Dispatch a custom event that the shortcuts dialog can listen for
        if (browser) {
          document.dispatchEvent(new CustomEvent('toggle-keyboard-shortcuts'));
        }
      },
      description: 'Show this keyboard shortcuts dialog',
    },
    {
      id: 'ui-notifications',
      action: 'Open Notifications',
      keys: `${ModifierKey.ALT}+N`,
      handler: () => {
        // Dispatch a custom event that the notifications component can listen for
        if (browser) {
          document.dispatchEvent(new CustomEvent('toggle-notifications'));
        }
      },
      description: 'Open the notifications panel',
    },
    {
      id: 'ui-feedback',
      action: 'Open Feedback',
      keys: `${ModifierKey.ALT}+F`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('toggle-feedback'));
        }
      },
      description: 'Open the feedback panel',
    },
    {
      id: 'ui-user-menu',
      action: 'Open User Menu',
      keys: `${ModifierKey.ALT}+U`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('toggle-user-menu'));
        }
      },
      description: 'Open the user menu',
    },
    {
      id: 'ui-logout',
      action: 'Log Out',
      keys: `${ModifierKey.ALT}+Q`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('logout-user'));
        }
      },
      description: 'Log out of the application',
    },
    {
      id: 'ui-refresh',
      action: 'Refresh Page',
      keys: `${ModifierKey.ALT}+R`,
      handler: () => {
        if (browser) {
          window.location.reload();
        }
      },
      description: 'Refresh the current page',
    },
  ],
};

// Job-related shortcuts
const jobShortcuts: ShortcutGroup = {
  id: 'jobs',
  name: 'Job Search',
  activePages: [ShortcutPage.JOBS],
  shortcuts: [
    {
      id: 'job-save',
      action: 'Save Job',
      keys: `${ModifierKey.ALT}+S`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('save-current-job'));
        }
      },
      description: 'Save the currently selected job',
    },
    {
      id: 'job-apply',
      action: 'Apply to Job',
      keys: `${ModifierKey.ALT}+Y`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('apply-to-current-job'));
        }
      },
      description: 'Apply to the currently selected job',
    },
    {
      id: 'job-filter',
      action: 'Toggle Filters',
      keys: `${ModifierKey.ALT}+F`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('toggle-job-filters'));
        }
      },
      description: 'Toggle job search filters',
    },
    {
      id: 'job-refresh',
      action: 'Refresh Jobs',
      keys: `${ModifierKey.ALT}+R`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('refresh-jobs'));
        }
      },
      description: 'Refresh job listings',
    },
    {
      id: 'job-next',
      action: 'Next Job',
      keys: `${ModifierKey.ALT}+ArrowDown`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('next-job'));
        }
      },
      description: 'Navigate to the next job in the list',
    },
    {
      id: 'job-prev',
      action: 'Previous Job',
      keys: `${ModifierKey.ALT}+ArrowUp`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('previous-job'));
        }
      },
      description: 'Navigate to the previous job in the list',
    },
    {
      id: 'job-details',
      action: 'View Job Details',
      keys: `${ModifierKey.ALT}+Enter`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('view-job-details'));
        }
      },
      description: 'View details of the selected job',
    },
    {
      id: 'job-share',
      action: 'Share Job',
      keys: `${ModifierKey.ALT}+H`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('share-job'));
        }
      },
      description: 'Share the selected job',
    },
    {
      id: 'job-clear-filters',
      action: 'Clear Filters',
      keys: `${ModifierKey.ALT}+C`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('clear-job-filters'));
        }
      },
      description: 'Clear all job filters',
    },
  ],
};

// Application-related shortcuts
const applicationShortcuts: ShortcutGroup = {
  id: 'applications',
  name: 'Applications',
  activePages: [ShortcutPage.APPLICATIONS],
  shortcuts: [
    {
      id: 'app-view',
      action: 'View Application Details',
      keys: `${ModifierKey.ALT}+V`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('view-application-details'));
        }
      },
      description: 'View details of the selected application',
    },
    {
      id: 'app-status',
      action: 'Update Status',
      keys: `${ModifierKey.ALT}+U`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('update-application-status'));
        }
      },
      description: 'Update the status of the selected application',
    },
    {
      id: 'app-note',
      action: 'Add Note',
      keys: `${ModifierKey.ALT}+E`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('add-application-note'));
        }
      },
      description: 'Add a note to the selected application',
    },
    {
      id: 'app-filter',
      action: 'Toggle Filters',
      keys: `${ModifierKey.ALT}+F`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('toggle-application-filters'));
        }
      },
      description: 'Toggle application filters',
    },
    {
      id: 'app-next',
      action: 'Next Application',
      keys: `${ModifierKey.ALT}+ArrowDown`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('next-application'));
        }
      },
      description: 'Navigate to the next application in the list',
    },
    {
      id: 'app-prev',
      action: 'Previous Application',
      keys: `${ModifierKey.ALT}+ArrowUp`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('previous-application'));
        }
      },
      description: 'Navigate to the previous application in the list',
    },
    {
      id: 'app-withdraw',
      action: 'Withdraw Application',
      keys: `${ModifierKey.ALT}+W`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('withdraw-application'));
        }
      },
      description: 'Withdraw the selected application',
    },
    {
      id: 'app-refresh',
      action: 'Refresh Applications',
      keys: `${ModifierKey.ALT}+R`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('refresh-applications'));
        }
      },
      description: 'Refresh application listings',
    },
    {
      id: 'app-clear-filters',
      action: 'Clear Filters',
      keys: `${ModifierKey.ALT}+C`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('clear-application-filters'));
        }
      },
      description: 'Clear all application filters',
    },
  ],
};

// Document-related shortcuts
const documentShortcuts: ShortcutGroup = {
  id: 'documents',
  name: 'Documents',
  activePages: [ShortcutPage.RESUMES],
  shortcuts: [
    {
      id: 'doc-new',
      action: 'New Document',
      keys: `${ModifierKey.ALT}+N`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('create-new-document'));
        }
      },
      description: 'Create a new document',
    },
    {
      id: 'doc-save',
      action: 'Save Document',
      keys: `${ModifierKey.ALT}+S`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('save-current-document'));
        }
      },
      description: 'Save the current document',
    },
    {
      id: 'doc-preview',
      action: 'Preview Document',
      keys: `${ModifierKey.ALT}+V`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('preview-document'));
        }
      },
      description: 'Preview the current document',
    },
    {
      id: 'doc-download',
      action: 'Download Document',
      keys: `${ModifierKey.ALT}+W`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('download-document'));
        }
      },
      description: 'Download the current document',
    },
    {
      id: 'doc-next',
      action: 'Next Document',
      keys: `${ModifierKey.ALT}+ArrowDown`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('next-document'));
        }
      },
      description: 'Navigate to the next document in the list',
    },
    {
      id: 'doc-prev',
      action: 'Previous Document',
      keys: `${ModifierKey.ALT}+ArrowUp`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('previous-document'));
        }
      },
      description: 'Navigate to the previous document in the list',
    },
    {
      id: 'doc-delete',
      action: 'Delete Document',
      keys: `${ModifierKey.ALT}+Delete`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('delete-document'));
        }
      },
      description: 'Delete the selected document',
    },
    {
      id: 'doc-duplicate',
      action: 'Duplicate Document',
      keys: `${ModifierKey.ALT}+D`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('duplicate-document'));
        }
      },
      description: 'Duplicate the selected document',
    },
    {
      id: 'doc-rename',
      action: 'Rename Document',
      keys: `${ModifierKey.ALT}+R`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('rename-document'));
        }
      },
      description: 'Rename the selected document',
    },
    {
      id: 'doc-share',
      action: 'Share Document',
      keys: `${ModifierKey.ALT}+H`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('share-document'));
        }
      },
      description: 'Share the selected document',
    },
  ],
};

// Job Tracker shortcuts
const trackerShortcuts: ShortcutGroup = {
  id: 'tracker',
  name: 'Job Tracker',
  activePages: [ShortcutPage.DASHBOARD],
  shortcuts: [
    {
      id: 'tracker-new',
      action: 'Add New Job',
      keys: `${ModifierKey.ALT}+N`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('add-new-job-to-tracker'));
        }
      },
      description: 'Add a new job to the tracker',
    },
    {
      id: 'tracker-update',
      action: 'Update Job Status',
      keys: `${ModifierKey.ALT}+U`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('update-job-status'));
        }
      },
      description: 'Update the status of the selected job',
    },
    {
      id: 'tracker-note',
      action: 'Add Note',
      keys: `${ModifierKey.ALT}+E`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('add-job-note'));
        }
      },
      description: 'Add a note to the selected job',
    },
    {
      id: 'tracker-delete',
      action: 'Delete Job',
      keys: `${ModifierKey.ALT}+Delete`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('delete-job-from-tracker'));
        }
      },
      description: 'Delete the selected job from the tracker',
    },
    {
      id: 'tracker-filter',
      action: 'Toggle Filters',
      keys: `${ModifierKey.ALT}+F`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('toggle-tracker-filters'));
        }
      },
      description: 'Toggle job tracker filters',
    },
    {
      id: 'tracker-next',
      action: 'Next Job',
      keys: `${ModifierKey.ALT}+ArrowDown`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('next-tracker-job'));
        }
      },
      description: 'Navigate to the next job in the tracker',
    },
    {
      id: 'tracker-prev',
      action: 'Previous Job',
      keys: `${ModifierKey.ALT}+ArrowUp`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('previous-tracker-job'));
        }
      },
      description: 'Navigate to the previous job in the tracker',
    },
    {
      id: 'tracker-refresh',
      action: 'Refresh Tracker',
      keys: `${ModifierKey.ALT}+R`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('refresh-job-tracker'));
        }
      },
      description: 'Refresh the job tracker',
    },
  ],
};

// Automation shortcuts
const automationShortcuts: ShortcutGroup = {
  id: 'automation',
  name: 'Automation',
  activePages: [ShortcutPage.DASHBOARD],
  shortcuts: [
    {
      id: 'auto-new',
      action: 'New Automation',
      keys: `${ModifierKey.ALT}+N`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('create-new-automation'));
        }
      },
      description: 'Create a new automation',
    },
    {
      id: 'auto-start',
      action: 'Start Automation',
      keys: `${ModifierKey.ALT}+G`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('start-automation'));
        }
      },
      description: 'Start the selected automation',
    },
    {
      id: 'auto-stop',
      action: 'Stop Automation',
      keys: `${ModifierKey.ALT}+X`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('stop-automation'));
        }
      },
      description: 'Stop the selected automation',
    },
    {
      id: 'auto-edit',
      action: 'Edit Automation',
      keys: `${ModifierKey.ALT}+E`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('edit-automation'));
        }
      },
      description: 'Edit the selected automation',
    },
    {
      id: 'auto-delete',
      action: 'Delete Automation',
      keys: `${ModifierKey.ALT}+Delete`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('delete-automation'));
        }
      },
      description: 'Delete the selected automation',
    },
    {
      id: 'auto-logs',
      action: 'View Logs',
      keys: `${ModifierKey.ALT}+L`,
      handler: () => {
        if (browser) {
          document.dispatchEvent(new CustomEvent('view-automation-logs'));
        }
      },
      description: 'View logs for the selected automation',
    },
  ],
};

// Export all shortcut groups
export const shortcutGroups: ShortcutGroup[] = [
  navigationShortcuts,
  uiShortcuts,
  jobShortcuts,
  applicationShortcuts,
  documentShortcuts,
  trackerShortcuts,
  automationShortcuts,
];

// Helper function to get shortcuts for a specific page
export function getShortcutsForPage(page: ShortcutPage): ShortcutGroup[] {
  return shortcutGroups.filter(
    (group) => group.activePages?.includes(ShortcutPage.GLOBAL) || group.activePages?.includes(page)
  );
}

// Helper function to get all shortcuts as a flat array
export function getAllShortcuts() {
  return shortcutGroups.flatMap((group) => group.shortcuts);
}
