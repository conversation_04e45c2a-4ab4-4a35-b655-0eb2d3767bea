import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';

/**
 * Apply an AI suggestion to a resume
 * 
 * This endpoint marks a suggestion as applied and updates the resume content.
 */
export const POST: RequestHandler = async ({ params, cookies }) => {
  try {
    // Verify authentication
    const token = cookies.get('auth_token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tokenData = verifySessionToken(token);
    if (!tokenData || !tokenData.id) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // Get user ID from token
    const userId = tokenData.id;

    // Get suggestion ID from params
    const suggestionId = params.id;
    if (!suggestionId) {
      return json({ error: 'Missing suggestion ID' }, { status: 400 });
    }

    // Find the suggestion
    const suggestion = await prisma.resumeAISuggestion.findFirst({
      where: {
        id: suggestionId,
        userId
      },
      include: {
        resume: true
      }
    });

    if (!suggestion) {
      return json({ error: 'Suggestion not found or access denied' }, { status: 404 });
    }

    // Mark suggestion as applied
    await prisma.resumeAISuggestion.update({
      where: { id: suggestionId },
      data: { applied: true }
    });

    // In a real implementation, we would update the resume content here
    // For now, we'll just mark the suggestion as applied

    return json({ success: true, message: 'Suggestion applied successfully' });
  } catch (error) {
    console.error('Error applying resume suggestion:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
