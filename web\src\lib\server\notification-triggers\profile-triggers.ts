// src/lib/server/notification-triggers/profile-triggers.ts
import { prisma } from '$lib/server/prisma';
import {
  sendNotificationToUser,
  NotificationType,
  NotificationPriority,
} from '$lib/server/notification-triggers/notification-service';
import { logger } from '$lib/server/logger';

/**
 * Send a notification when a user's profile is incomplete
 */
export async function notifyProfileIncomplete(
  userId: string,
  missingFields: string[]
): Promise<boolean> {
  try {
    // Get user notification preferences from user preferences
    const userRecord = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        preferences: true,
      },
    });

    // Check if user has disabled notifications in preferences
    const userPrefs = (userRecord?.preferences as any) || {};
    const notificationPrefs = userPrefs.notifications || {};

    if (notificationPrefs.platform && notificationPrefs.platform.browser === false) {
      logger.info(`User ${userId} has disabled browser notifications in preferences`);
      return false;
    }

    // Also check NotificationSettings for backward compatibility
    const settings = await prisma.notificationSettings.findUnique({
      where: { userId },
    });

    if (settings && !settings.browserEnabled) {
      logger.info(`User ${userId} has disabled browser notifications in settings`);
      return false;
    }

    // Format missing fields for display
    const formattedFields = missingFields.map((field) => {
      // Convert camelCase to Title Case with spaces
      return field.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase());
    });

    // Create message based on number of missing fields
    let message: string;
    if (formattedFields.length === 1) {
      message = `Your profile is missing: ${formattedFields[0]}. Complete your profile to improve your job matches.`;
    } else if (formattedFields.length === 2) {
      message = `Your profile is missing: ${formattedFields[0]} and ${formattedFields[1]}. Complete your profile to improve your job matches.`;
    } else {
      const lastField = formattedFields.pop();
      message = `Your profile is missing: ${formattedFields.join(', ')}, and ${lastField}. Complete your profile to improve your job matches.`;
    }

    // Create notification
    const result = await sendNotificationToUser(userId, {
      title: 'Complete Your Profile',
      message,
      url: '/dashboard/profile',
      type: NotificationType.SYSTEM,
      priority: NotificationPriority.MEDIUM,
      metadata: {
        missingFields,
      },
    });

    return result;
  } catch (error) {
    logger.error('Error sending profile incomplete notification:', error);
    return false;
  }
}

/**
 * Send a notification when a user's resume needs updating
 */
export async function notifyResumeOutdated(
  userId: string,
  daysSinceUpdate: number
): Promise<boolean> {
  try {
    // Get user notification preferences from user preferences
    const userRecord = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        preferences: true,
      },
    });

    // Check if user has disabled notifications in preferences
    const userPrefs = (userRecord?.preferences as any) || {};
    const notificationPrefs = userPrefs.notifications || {};

    if (notificationPrefs.platform && notificationPrefs.platform.browser === false) {
      logger.info(`User ${userId} has disabled browser notifications in preferences`);
      return false;
    }

    // Also check NotificationSettings for backward compatibility
    const settings = await prisma.notificationSettings.findUnique({
      where: { userId },
    });

    if (settings && !settings.browserEnabled) {
      logger.info(`User ${userId} has disabled browser notifications in settings`);
      return false;
    }

    // Create notification
    const result = await sendNotificationToUser(userId, {
      title: 'Update Your Resume',
      message: `Your resume hasn't been updated in ${daysSinceUpdate} days. Keep your resume current to improve your job matches.`,
      url: '/dashboard/resume',
      type: NotificationType.SYSTEM,
      priority: NotificationPriority.MEDIUM,
      metadata: {
        daysSinceUpdate,
      },
    });

    return result;
  } catch (error) {
    logger.error('Error sending resume outdated notification:', error);
    return false;
  }
}

/**
 * Send a notification when a user's profile strength has been calculated
 */
export async function notifyProfileStrength(
  userId: string,
  score: number,
  improvements: string[]
): Promise<boolean> {
  try {
    // Get user notification preferences from user preferences
    const userRecord = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        preferences: true,
      },
    });

    // Check if user has disabled notifications in preferences
    const userPrefs = (userRecord?.preferences as any) || {};
    const notificationPrefs = userPrefs.notifications || {};

    if (notificationPrefs.platform && notificationPrefs.platform.browser === false) {
      logger.info(`User ${userId} has disabled browser notifications in preferences`);
      return false;
    }

    // Also check NotificationSettings for backward compatibility
    const settings = await prisma.notificationSettings.findUnique({
      where: { userId },
    });

    if (settings && !settings.browserEnabled) {
      logger.info(`User ${userId} has disabled browser notifications in settings`);
      return false;
    }

    // Format score as percentage
    const scorePercent = Math.round(score * 100);

    // Create message based on score
    let message: string;
    if (score >= 0.8) {
      message = `Your profile strength is ${scorePercent}%. Great job! Your profile is looking strong.`;
    } else if (score >= 0.5) {
      message = `Your profile strength is ${scorePercent}%. There's room for improvement.`;
    } else {
      message = `Your profile strength is ${scorePercent}%. Your profile needs attention to improve your job matches.`;
    }

    // Add improvement suggestions if available
    if (improvements && improvements.length > 0) {
      message += ` Suggestions: ${improvements.join(', ')}.`;
    }

    // Create notification
    const result = await sendNotificationToUser(userId, {
      title: 'Profile Strength Updated',
      message,
      url: '/dashboard/profile',
      type: NotificationType.SYSTEM,
      priority: NotificationPriority.MEDIUM,
      metadata: {
        score,
        improvements,
      },
    });

    return result;
  } catch (error) {
    logger.error('Error sending profile strength notification:', error);
    return false;
  }
}

/**
 * Send a notification when a user's skills assessment is available
 */
export async function notifySkillsAssessmentAvailable(
  userId: string,
  skillName: string
): Promise<boolean> {
  try {
    // Get user notification preferences from user preferences
    const userRecord = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        preferences: true,
      },
    });

    // Check if user has disabled notifications in preferences
    const userPrefs = (userRecord?.preferences as any) || {};
    const notificationPrefs = userPrefs.notifications || {};

    if (notificationPrefs.platform && notificationPrefs.platform.browser === false) {
      logger.info(`User ${userId} has disabled browser notifications in preferences`);
      return false;
    }

    // Also check NotificationSettings for backward compatibility
    const settings = await prisma.notificationSettings.findUnique({
      where: { userId },
    });

    if (settings && !settings.browserEnabled) {
      logger.info(`User ${userId} has disabled browser notifications in settings`);
      return false;
    }

    // Create notification
    const result = await sendNotificationToUser(userId, {
      title: 'Skills Assessment Available',
      message: `A new skills assessment for ${skillName} is available. Take the assessment to verify your skills and improve your job matches.`,
      url: '/dashboard/skills',
      type: NotificationType.SYSTEM,
      priority: NotificationPriority.MEDIUM,
      metadata: {
        skillName,
      },
    });

    return result;
  } catch (error) {
    logger.error('Error sending skills assessment notification:', error);
    return false;
  }
}
