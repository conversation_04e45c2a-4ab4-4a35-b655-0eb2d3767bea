// cron/scripts/update-resource-manager.ts
// <PERSON>ript to update the adaptive resource manager in the parallel job scraper

import { logger } from "../utils/logger";
import { startScheduledJobs, stopScheduledJobs } from "../jobs/scheduledJobs";
import fs from "fs";
import path from "path";
import os from "os";

// Function to get detailed system resource information
function getSystemResourceInfo() {
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsagePercent = (usedMemory / totalMemory) * 100;
  
  const cpuCount = os.cpus().length;
  const loadAvg = os.loadavg()[0]; // 1 minute load average
  const loadPerCpu = loadAvg / cpuCount;
  const cpuUsagePercent = loadPerCpu * 100;
  
  return {
    memory: {
      total: formatBytes(totalMemory),
      free: formatBytes(freeMemory),
      used: formatBytes(usedMemory),
      usagePercent: memoryUsagePercent.toFixed(2) + "%"
    },
    cpu: {
      count: cpuCount,
      loadAverage: loadAvg.toFixed(2),
      loadPerCpu: loadPerCpu.toFixed(2),
      usagePercent: cpuUsagePercent.toFixed(2) + "%"
    }
  };
}

// Helper function to format bytes to human-readable format
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Function to update the adaptive resource manager in the parallel job scraper
function updateResourceManager(filePath: string): boolean {
  try {
    // Read the file
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check if the file already uses the enhanced adaptive resource manager
    if (content.includes('EnhancedAdaptiveResourceManager')) {
      logger.info(`✅ File ${filePath} already uses EnhancedAdaptiveResourceManager`);
      return true;
    }
    
    // Replace imports
    let updatedContent = content.replace(
      /import\s+{\s*AdaptiveResourceManager\s*}\s+from\s+["']\.\.\/utils\/adaptiveResourceManager["'];?/g,
      `import { EnhancedAdaptiveResourceManager } from "../utils/enhancedAdaptiveResourceManager";`
    );
    
    // If the import wasn't found, add it
    if (!updatedContent.includes('EnhancedAdaptiveResourceManager')) {
      // Find the last import statement
      const lastImportIndex = updatedContent.lastIndexOf('import');
      const lastImportEndIndex = updatedContent.indexOf(';', lastImportIndex) + 1;
      
      // Insert the new import after the last import
      updatedContent = 
        updatedContent.substring(0, lastImportEndIndex) + 
        `\nimport { EnhancedAdaptiveResourceManager } from "../utils/enhancedAdaptiveResourceManager";` +
        updatedContent.substring(lastImportEndIndex);
    }
    
    // Replace class instantiation
    updatedContent = updatedContent.replace(
      /const\s+resourceManager\s+=\s+new\s+AdaptiveResourceManager\(/g,
      `const resourceManager = new EnhancedAdaptiveResourceManager(`
    );
    
    // Replace any other references to AdaptiveResourceManager
    updatedContent = updatedContent.replace(
      /AdaptiveResourceManager(?!\s*,)/g,
      'EnhancedAdaptiveResourceManager'
    );
    
    // Update the memory threshold values to be more appropriate
    updatedContent = updatedContent.replace(
      /memoryThresholdPercent:\s*\d+/g,
      'memoryThresholdPercent: 85'
    );
    
    updatedContent = updatedContent.replace(
      /criticalMemoryThresholdPercent:\s*\d+/g,
      'criticalMemoryThresholdPercent: 95'
    );
    
    // Write the updated content back to the file
    fs.writeFileSync(filePath, updatedContent, 'utf8');
    
    logger.info(`✅ Updated adaptive resource manager in ${filePath}`);
    return true;
  } catch (error) {
    logger.error(`❌ Error updating adaptive resource manager in ${filePath}:`, error);
    return false;
  }
}

// Main function to update the adaptive resource manager
async function updateAdaptiveResourceManager() {
  logger.info("🔄 Starting adaptive resource manager update...");
  
  try {
    // Check system resources before update
    const beforeResources = getSystemResourceInfo();
    logger.info(`📊 System resources before update: ${JSON.stringify(beforeResources, null, 2)}`);
    
    // Stop any running scheduled jobs
    logger.info("🛑 Stopping any running scheduled jobs...");
    stopScheduledJobs();
    
    // Wait a moment for jobs to stop
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Update the parallel job scraper
    const parallelJobScraperPath = path.join(process.cwd(), 'jobs', 'parallelJobScraper.ts');
    const success = updateResourceManager(parallelJobScraperPath);
    
    if (success) {
      logger.info(`✅ Successfully updated adaptive resource manager in parallel job scraper`);
    } else {
      logger.error(`❌ Failed to update adaptive resource manager in parallel job scraper`);
    }
    
    // Restart scheduled jobs
    logger.info("🚀 Restarting scheduled jobs...");
    startScheduledJobs();
    
    // Check system resources after update
    const afterResources = getSystemResourceInfo();
    logger.info(`📊 System resources after update: ${JSON.stringify(afterResources, null, 2)}`);
    
    logger.info("✅ Adaptive resource manager update completed");
    
    // Log next steps
    logger.info("📋 Next steps:");
    logger.info("1. Monitor the logs to ensure jobs are running correctly with the enhanced adaptive resource manager");
    logger.info("2. Check the scheduler status with 'npm run check-scheduler'");
    logger.info("3. If problems persist, adjust the thresholds in enhancedAdaptiveResourceManager.ts");
    
  } catch (error) {
    logger.error("❌ Error updating adaptive resource manager:", error);
  }
}

// Run the update
updateAdaptiveResourceManager()
  .then(() => {
    logger.info("✅ Adaptive resource manager update completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Adaptive resource manager update failed with error:", error);
    process.exit(1);
  });
