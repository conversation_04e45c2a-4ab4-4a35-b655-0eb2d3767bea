/**
 * Feature Utilities
 * 
 * Common utility functions for working with features and their display.
 */

import { FeatureAccessLevel } from '$lib/models/features/features';

/**
 * Format a limit value for display
 * @param value The limit value
 * @param unit Optional unit to display
 * @returns Formatted limit value string
 */
export function formatLimitValue(value: number | 'unlimited', unit?: string): string {
  if (value === 'unlimited') return 'Unlimited';
  return unit ? `${value} ${unit}` : `${value}`;
}

/**
 * Get the color for a progress bar based on usage percentage
 * @param percentUsed The percentage of usage
 * @returns CSS class for the progress bar
 */
export function getProgressColor(percentUsed?: number): string {
  if (percentUsed === undefined) return 'bg-primary';
  if (percentUsed >= 90) return 'bg-destructive';
  if (percentUsed >= 70) return 'bg-warning';
  return 'bg-primary';
}

/**
 * Get the access level badge color
 * @param accessLevel The feature access level
 * @returns CSS class for the badge
 */
export function getAccessLevelColor(accessLevel: FeatureAccessLevel): string {
  switch (accessLevel) {
    case FeatureAccessLevel.Included:
      return 'bg-primary';
    case FeatureAccessLevel.Limited:
      return 'bg-warning';
    case FeatureAccessLevel.Unlimited:
      return 'bg-success';
    case FeatureAccessLevel.NotIncluded:
      return 'bg-destructive';
    default:
      return 'bg-muted';
  }
}

/**
 * Format the access level for display
 * @param accessLevel The feature access level
 * @returns Formatted access level string
 */
export function formatAccessLevel(accessLevel: FeatureAccessLevel): string {
  switch (accessLevel) {
    case FeatureAccessLevel.Included:
      return 'Included';
    case FeatureAccessLevel.Limited:
      return 'Limited';
    case FeatureAccessLevel.Unlimited:
      return 'Unlimited';
    case FeatureAccessLevel.NotIncluded:
      return 'Not Included';
    default:
      return 'Unknown';
  }
}

/**
 * Format a category name for display
 * @param category The category name
 * @returns Formatted category name
 */
export function formatCategoryName(category: string): string {
  return category
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}
