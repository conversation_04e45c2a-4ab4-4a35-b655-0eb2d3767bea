// cron/lib/search/bingJobSearch.ts
import { Page } from "playwright";
import { logger } from "../../utils/logger";
import { humanDelay } from "../../utils/humanBehavior";
import { JobData } from "../../types/jobData";

/**
 * Search Bing for jobs and extract job listings
 * @param page Playwright page object
 * @param jobTitle Job title to search for
 * @param city City to search in
 * @param stateCode State code (e.g., CA, NY) to include in the search
 * @returns Array of job objects
 */
export async function searchBingForJobs(
  page: Page,
  jobTitle: string,
  city: string,
  stateCode?: string
): Promise<JobData[]> {
  try {
    // Always navigate to Bing Jobs with the new search query for each occupation
    // Construct the Bing jobs URL with language parameters
    // Include state code in the search query if provided
    const locationString = stateCode ? `${city}, ${stateCode}` : city;
    const searchQuery = encodeURIComponent(
      `${jobTitle} jobs ${locationString}`
    );
    const bingJobsUrl = `https://www.bing.com/jobs?q=${searchQuery}&setlang=en-US&cc=US&ensearch=1`;

    logger.info(`🔍 Searching for: ${jobTitle} jobs in ${locationString}`);

    logger.info(`🌐 Navigating to Bing Jobs: ${bingJobsUrl}`);

    // Navigate to Bing Jobs
    await page.goto(bingJobsUrl, {
      waitUntil: "domcontentloaded",
      timeout: 90000, // Increase timeout for Bing navigation
    });

    // Add a small delay after navigation to ensure page loads properly
    await humanDelay("navigation");

    // Check if there are job results
    const hasJobResults = await page.evaluate(() => {
      return (
        document.querySelector(".jb_l2_cardlist, div[id^='JCL_']") !== null
      );
    });

    if (!hasJobResults) {
      // Check for "no results" message
      const noResultsText = await page.evaluate(() => {
        const noResultsElement = document.querySelector(
          ".b_no_results, .no-results-message"
        );
        return noResultsElement ? noResultsElement.textContent : "";
      });

      // Log only once with all necessary information
      if (noResultsText) {
        logger.info(`ℹ️ No job results found: ${noResultsText.trim()}`);
      } else {
        logger.info(`ℹ️ No job results found for "${jobTitle}" in "${city}"`);
      }

      // Return empty array immediately
      return [];
    }

    // Extract job listings using our dedicated analyzer
    logger.info("🔍 Extracting job listings from Bing...");

    const jobs = await extractAllBingJobs(page);

    // Add a small delay before returning
    await humanDelay();

    // The job count will be logged by the worker, so we don't need to log it here

    return jobs;
  } catch (error) {
    logger.error(`❌ Error searching Bing for jobs: ${error}`);
    return [];
  }
}

async function extractAllBingJobs(page: Page): Promise<JobData[]> {
  logger.info("🔍 Collecting all job cards first...");

  const jobs: JobData[] = [];
  const isProduction = process.env.NODE_ENV === "production";

  // Wait for the job card list to be available
  try {
    await page.waitForSelector(".jb_l2_cardlist", { timeout: 10000 });
  } catch (error) {
    logger.error(`❌ Could not find job card list (.jb_l2_cardlist): ${error}`);
    return [];
  }

  // Get all job cards using the exact selector
  const jobCards = await page.$$(".jb_jlc");

  if (jobCards.length === 0) {
    logger.warn("⚠️ No job cards found with selector .jb_jlc");
    return [];
  }

  logger.info(`✅ Found ${jobCards.length} job cards with selector .jb_jlc`);

  // Determine batch size based on environment
  // Process more cards at once in production for better performance
  const batchSize = isProduction ? 5 : 2;

  // Process cards in batches for better performance
  for (let i = 0; i < jobCards.length; i += batchSize) {
    const batchEnd = Math.min(i + batchSize, jobCards.length);
    logger.info(
      `👆 Processing job cards ${i + 1}-${batchEnd}/${jobCards.length}`
    );

    // Process this batch of cards in parallel
    const batchPromises = [];

    for (let j = i; j < batchEnd; j++) {
      batchPromises.push(processJobCard(page, j, jobCards.length));
    }

    // Wait for all cards in this batch to be processed
    const batchResults = await Promise.allSettled(batchPromises);

    // Add successful results to jobs array
    for (const result of batchResults) {
      if (result.status === "fulfilled" && result.value) {
        jobs.push(result.value);
      }
    }

    // Short delay between batches to avoid overwhelming the page
    await humanDelay("navigation");
  }

  const uniqueJobs = await deduplicateJobs(jobs);
  logger.info(
    `🧹 Deduplicated jobs: ${uniqueJobs.length} unique of ${jobs.length} total`
  );

  return uniqueJobs;
}

// Helper function to process a single job card
async function processJobCard(
  page: Page,
  index: number,
  totalCards: number
): Promise<JobData | null> {
  try {
    // Get a fresh reference to the job card
    const freshJobCards = await page.$$(".jb_jlc");

    // Skip if we can't find the card anymore
    if (index >= freshJobCards.length) {
      logger.warn(`⚠️ Job card ${index + 1} is no longer available, skipping`);
      return null;
    }

    const card = freshJobCards[index];

    // Try to scroll the card into view with error handling
    try {
      await card.scrollIntoViewIfNeeded();
      await humanDelay("scroll");
    } catch (scrollError) {
      // Try to scroll using JavaScript as a fallback
      await page.evaluate((idx) => {
        const cards = document.querySelectorAll(".jb_jlc");
        if (cards[idx]) {
          (cards[idx] as HTMLElement).scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }, index);
      await humanDelay("scroll");
    }

    // Try to click the card with error handling
    await page.evaluate((idx) => {
      const el = document.querySelectorAll(".jb_jlc")[idx];
      if (el instanceof HTMLElement) el.click();
    }, index);

    // Add a small delay to let the details load
    await humanDelay("reading");

    // Extract basic job details from the Bing job panel
    let job;
    let applyUrl = null;
    try {
      job = await page.evaluate(() => {
        try {
          // Get the raw text content
          const rawTitle =
            document
              .querySelector(".jb_title, .jbpnl_title")
              ?.textContent?.trim() || "";

          // Clean the title by removing "SaveSaved" and similar text
          const title = rawTitle.replace(/save\s*saved.*$/i, "").trim();

          const company =
            document.querySelector(".jbpnl_coLoc__co")?.textContent?.trim() ||
            "";
          const location =
            document.querySelector(".jbpnl_coLoc__loc")?.textContent?.trim() ||
            "";
          const postedDate =
            document.querySelector(".jbpnl_postDate")?.textContent?.trim() ||
            "";

          // Get the URL from the apply button container
          let url = window.location.href; // Default fallback

          // Find the apply button container
          const applyBtnContainer = document.querySelector(
            ".jb_applyBtnContainer a"
          );

          if (
            applyBtnContainer &&
            applyBtnContainer instanceof HTMLAnchorElement
          ) {
            // Store the URL from the apply button
            url = applyBtnContainer.href || url;
          }

          return {
            title,
            company,
            location,
            postedDate,
            url,
          };
        } catch (e) {
          // Try to get the URL from the apply button container even in error case
          let url = window.location.href; // Default fallback
          try {
            const applyBtnContainer = document.querySelector(
              ".jb_applyBtnContainer a"
            );
            if (
              applyBtnContainer &&
              applyBtnContainer instanceof HTMLAnchorElement
            ) {
              url = applyBtnContainer.href || url;
            }
          } catch (urlError) {
            // Silently fail
          }

          return {
            title: "",
            company: "",
            location: "",
            postedDate: "",
            url,
          };
        }
      });
    } catch (evaluateError) {
      job = {
        title: "",
        company: "",
        location: "",
        postedDate: "",
        url: page.url(),
      };
    }

    if (job.title && job.company) {
      // Try to get the apply URL
      try {
        const applyButton = await page.$(".jb_applyBtnContainer a");
        if (applyButton) {
          applyUrl = await applyButton.getAttribute("href");
          if (applyUrl && applyUrl.startsWith("http")) {
            job.url = applyUrl;
          }
        }
      } catch (applyError) {
        // Silently continue with the current URL
      }

      const jobData = {
        ...job,
        source: "Bing",
        dateFound: new Date().toISOString(),
      };

      logger.info(`✅ Extracted: ${job.title} at ${job.company}`);
      return jobData;
    } else {
      logger.warn(
        `⚠️ Skipped job card ${index + 1} — missing title or company`
      );
      return null;
    }
  } catch (error) {
    logger.error(`❌ Error processing job card ${index + 1}: ${error}`);
    return null;
  }
}

async function deduplicateJobs(jobs: JobData[]) {
  const seen = new Set<string>();
  const uniqueJobs: JobData[] = [];

  for (const job of jobs) {
    const key = jobKey(job);
    if (!seen.has(key)) {
      seen.add(key);
      uniqueJobs.push(job);
    }
  }

  return uniqueJobs;
}

function jobKey(job: JobData): string {
  const normalize = (s?: string) =>
    s
      ?.trim()
      .toLowerCase()
      .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, "") || "";

  return [
    normalize(job.title),
    normalize(job.company),
    normalize(job.location),
  ].join("::");
}
