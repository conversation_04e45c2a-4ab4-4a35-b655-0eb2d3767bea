<script lang="ts">
  import * as Card from '$lib/components/ui/card';
  import { Badge } from '$lib/components/ui/badge';
  import * as Tabs from '$lib/components/ui/tabs';
  import {
    Briefcase,
    GraduationCap,
    Sparkles,
    CheckCircle,
    XCircle,
    RefreshCw,
    FileText,
  } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import type { JobMatchDetails } from '$lib/services/ai-service';
  import { getJobMatchDetails } from '$lib/services/ai-service';

  // Props
  const { profileId, jobId, jobTitle, company } = $props<{
    profileId: string;
    jobId: string;
    jobTitle: string;
    company: string;
  }>();

  // State
  let matchDetails = $state<JobMatchDetails | null>(null);
  let isLoading = $state(false);
  let error = $state<string | null>(null);
  let activeTab = $state('overview');

  // Load match details when props change
  $effect(() => {
    if (profileId && jobId) {
      loadMatchDetails();
    }
  });

  // Load match details
  async function loadMatchDetails() {
    isLoading = true;
    error = null;

    try {
      matchDetails = await getJobMatchDetails(profileId, jobId);
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load match details';
      console.error('Error loading match details:', err);
    } finally {
      isLoading = false;
    }
  }

  // Refresh match details
  async function refreshMatchDetails() {
    await loadMatchDetails();
    toast.success('Match details refreshed');
  }

  // Get score color
  function getScoreColor(score: number): string {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-amber-600';
    return 'text-red-600';
  }

  // Get progress color
  function getProgressColor(score: number): string {
    if (score >= 80) return 'bg-green-600';
    if (score >= 60) return 'bg-amber-600';
    return 'bg-red-600';
  }

  // Format score for display
  function formatScore(score: number): string {
    return Math.round(score).toString();
  }

  // Get match quality label
  function getMatchQualityLabel(score: number): string {
    if (score >= 80) return 'Excellent Match';
    if (score >= 60) return 'Good Match';
    if (score >= 40) return 'Fair Match';
    return 'Poor Match';
  }
</script>

<Card.Root class="w-full">
  <Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
    <Card.Title class="text-md font-medium">
      <div class="flex items-center">
        <Sparkles class="mr-2 h-4 w-4 text-blue-500" />
        Match Analysis: {jobTitle} at {company}
      </div>
    </Card.Title>
    <button
      class="inline-flex h-8 w-8 items-center justify-center rounded-md p-0 text-gray-700 hover:bg-gray-100"
      onclick={refreshMatchDetails}>
      <RefreshCw class="h-4 w-4" />
      <span class="sr-only">Refresh</span>
    </button>
  </Card.Header>

  <Card.Content>
    {#if isLoading}
      <div class="flex items-center justify-center py-6">
        <div class="h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
        <span class="ml-2">Analyzing match...</span>
      </div>
    {:else if error}
      <div class="rounded-md bg-red-50 p-4 text-sm text-red-500">
        <p>Error: {error}</p>
        <button
          class="mt-2 inline-flex h-9 items-center justify-center rounded-md border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100"
          onclick={loadMatchDetails}>
          Try Again
        </button>
      </div>
    {:else if !matchDetails}
      <div class="py-4 text-center text-sm text-gray-500">
        <p>No match details available.</p>
        <button
          class="mt-2 inline-flex h-9 items-center justify-center rounded-md border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100"
          onclick={refreshMatchDetails}>
          Analyze Match
        </button>
      </div>
    {:else}
      <Tabs.Root value={activeTab} onValueChange={(value) => (activeTab = value)}>
        <Tabs.List class="grid w-full grid-cols-3">
          <Tabs.Trigger value="overview">Overview</Tabs.Trigger>
          <Tabs.Trigger value="skills">Skills</Tabs.Trigger>
          <Tabs.Trigger value="recommendations">Recommendations</Tabs.Trigger>
        </Tabs.List>

        <!-- Overview Tab -->
        <Tabs.Content value="overview" class="mt-4">
          <!-- Overall Match Score -->
          <div class="mb-6 rounded-lg bg-gray-50 p-4">
            <div class="mb-2 text-center">
              <h3 class="text-lg font-medium">Overall Match Score</h3>
              <div
                class="mt-2 text-4xl font-bold {getScoreColor(
                  matchDetails.overallMatchScore * 100
                )}">
                {formatScore(matchDetails.overallMatchScore * 100)}%
              </div>
              <div class="mt-1 text-sm text-gray-500">
                {getMatchQualityLabel(matchDetails.overallMatchScore * 100)}
              </div>
            </div>

            <div class="mt-2 h-2 w-full rounded-full bg-gray-200">
              <div
                class="h-2 rounded-full {getProgressColor(matchDetails.overallMatchScore * 100)}"
                style="width: {matchDetails.overallMatchScore * 100}%">
              </div>
            </div>
          </div>

          <!-- Score Breakdown -->
          <div class="mb-4">
            <h3 class="mb-2 text-sm font-medium">Score Breakdown</h3>
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <Briefcase class="mr-2 h-4 w-4 text-gray-500" />
                  <span class="text-sm">Skills Match</span>
                </div>
                <div class="flex items-center">
                  <div class="mr-2 h-2 w-24 rounded-full bg-gray-200">
                    <div
                      class="h-2 rounded-full {getProgressColor(
                        matchDetails.skillsMatchScore * 100
                      )}"
                      style="width: {matchDetails.skillsMatchScore * 100}%">
                    </div>
                  </div>
                  <span
                    class="text-sm font-medium {getScoreColor(
                      matchDetails.skillsMatchScore * 100
                    )}">
                    {formatScore(matchDetails.skillsMatchScore * 100)}%
                  </span>
                </div>
              </div>

              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <FileText class="mr-2 h-4 w-4 text-gray-500" />
                  <span class="text-sm">Experience Match</span>
                </div>
                <div class="flex items-center">
                  <div class="mr-2 h-2 w-24 rounded-full bg-gray-200">
                    <div
                      class="h-2 rounded-full {getProgressColor(
                        matchDetails.experienceMatchScore * 100
                      )}"
                      style="width: {matchDetails.experienceMatchScore * 100}%">
                    </div>
                  </div>
                  <span
                    class="text-sm font-medium {getScoreColor(
                      matchDetails.experienceMatchScore * 100
                    )}">
                    {formatScore(matchDetails.experienceMatchScore * 100)}%
                  </span>
                </div>
              </div>

              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <GraduationCap class="mr-2 h-4 w-4 text-gray-500" />
                  <span class="text-sm">Education Match</span>
                </div>
                <div class="flex items-center">
                  <div class="mr-2 h-2 w-24 rounded-full bg-gray-200">
                    <div
                      class="h-2 rounded-full {getProgressColor(
                        matchDetails.educationMatchScore * 100
                      )}"
                      style="width: {matchDetails.educationMatchScore * 100}%">
                    </div>
                  </div>
                  <span
                    class="text-sm font-medium {getScoreColor(
                      matchDetails.educationMatchScore * 100
                    )}">
                    {formatScore(matchDetails.educationMatchScore * 100)}%
                  </span>
                </div>
              </div>

              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <Sparkles class="mr-2 h-4 w-4 text-gray-500" />
                  <span class="text-sm">Keyword Match</span>
                </div>
                <div class="flex items-center">
                  <div class="mr-2 h-2 w-24 rounded-full bg-gray-200">
                    <div
                      class="h-2 rounded-full {getProgressColor(
                        matchDetails.keywordMatchScore * 100
                      )}"
                      style="width: {matchDetails.keywordMatchScore * 100}%">
                    </div>
                  </div>
                  <span
                    class="text-sm font-medium {getScoreColor(
                      matchDetails.keywordMatchScore * 100
                    )}">
                    {formatScore(matchDetails.keywordMatchScore * 100)}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Match Summary -->
          <div class="rounded-md border p-4">
            <h3 class="mb-2 text-sm font-medium">Match Summary</h3>
            <p class="text-sm text-gray-700">
              {#if matchDetails.overallMatchScore >= 0.8}
                You're an excellent match for this position! Your skills and experience align very
                well with the job requirements.
              {:else if matchDetails.overallMatchScore >= 0.6}
                You're a good match for this position. With some minor improvements, you could be an
                excellent candidate.
              {:else if matchDetails.overallMatchScore >= 0.4}
                You're a fair match for this position. Consider addressing the missing skills and
                experience to improve your chances.
              {:else}
                This position may not be the best fit for your current skills and experience.
                Consider exploring other opportunities or addressing the skill gaps.
              {/if}
            </p>
          </div>
        </Tabs.Content>

        <!-- Skills Tab -->
        <Tabs.Content value="skills" class="mt-4">
          <div class="space-y-4">
            <!-- Matched Skills -->
            <div>
              <h3 class="mb-2 flex items-center text-sm font-medium">
                <CheckCircle class="mr-2 h-4 w-4 text-green-500" />
                Matched Skills ({matchDetails.matchedSkills.length})
              </h3>

              {#if matchDetails.matchedSkills.length === 0}
                <p class="text-sm text-gray-500">No matched skills found.</p>
              {:else}
                <div class="flex flex-wrap gap-1">
                  {#each matchDetails.matchedSkills as skill}
                    <Badge variant="outline" class="bg-green-50 text-xs">
                      {skill}
                    </Badge>
                  {/each}
                </div>
              {/if}
            </div>

            <!-- Missing Skills -->
            <div>
              <h3 class="mb-2 flex items-center text-sm font-medium">
                <XCircle class="mr-2 h-4 w-4 text-red-500" />
                Missing Skills ({matchDetails.missingSkills.length})
              </h3>

              {#if matchDetails.missingSkills.length === 0}
                <p class="text-sm text-gray-500">No missing skills found.</p>
              {:else}
                <div class="flex flex-wrap gap-1">
                  {#each matchDetails.missingSkills as skill}
                    <Badge variant="outline" class="bg-red-50 text-xs">
                      {skill}
                    </Badge>
                  {/each}
                </div>
              {/if}
            </div>

            <!-- Skills Gap Analysis -->
            <div class="rounded-md border p-4">
              <h3 class="mb-2 text-sm font-medium">Skills Gap Analysis</h3>
              <p class="text-sm text-gray-700">
                {#if matchDetails.missingSkills.length === 0}
                  You have all the required skills for this position. Great job!
                {:else if matchDetails.missingSkills.length <= 2}
                  You're missing only a few skills for this position. Consider adding these to your
                  profile or resume.
                {:else}
                  You're missing several key skills for this position. Consider focusing on
                  acquiring these skills to improve your match.
                {/if}
              </p>
            </div>
          </div>
        </Tabs.Content>

        <!-- Recommendations Tab -->
        <Tabs.Content value="recommendations" class="mt-4">
          <div class="space-y-4">
            <h3 class="text-sm font-medium">Recommendations to Improve Your Match</h3>

            {#if matchDetails.recommendations.length === 0}
              <p class="text-sm text-gray-500">No recommendations available.</p>
            {:else}
              <ul class="space-y-2">
                {#each matchDetails.recommendations as recommendation}
                  <li class="flex items-start rounded-md border p-3">
                    <Sparkles class="mr-2 mt-0.5 h-4 w-4 flex-shrink-0 text-blue-500" />
                    <span class="text-sm">{recommendation}</span>
                  </li>
                {/each}
              </ul>
            {/if}

            <!-- Action Items -->
            <div class="rounded-md bg-blue-50 p-4">
              <h3 class="mb-2 text-sm font-medium text-blue-700">Suggested Actions</h3>
              <ul class="space-y-1 text-sm text-blue-700">
                {#if matchDetails.missingSkills.length > 0}
                  <li class="flex items-start">
                    <CheckCircle class="mr-2 mt-0.5 h-3 w-3 flex-shrink-0" />
                    <span>Add missing skills to your profile</span>
                  </li>
                {/if}

                {#if matchDetails.experienceMatchScore < 0.6}
                  <li class="flex items-start">
                    <CheckCircle class="mr-2 mt-0.5 h-3 w-3 flex-shrink-0" />
                    <span>Highlight relevant experience in your resume</span>
                  </li>
                {/if}

                {#if matchDetails.keywordMatchScore < 0.6}
                  <li class="flex items-start">
                    <CheckCircle class="mr-2 mt-0.5 h-3 w-3 flex-shrink-0" />
                    <span>Optimize your resume with relevant keywords</span>
                  </li>
                {/if}

                <li class="flex items-start">
                  <CheckCircle class="mr-2 mt-0.5 h-3 w-3 flex-shrink-0" />
                  <span>Tailor your cover letter to address specific job requirements</span>
                </li>
              </ul>
            </div>
          </div>
        </Tabs.Content>
      </Tabs.Root>
    {/if}
  </Card.Content>
</Card.Root>
