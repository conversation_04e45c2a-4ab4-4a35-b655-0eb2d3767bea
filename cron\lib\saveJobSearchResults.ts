import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";

const prisma = new PrismaClient();

type ScoredJob = {
  id: string;
  matchScore: number;
};

export async function saveJobSearchResults({
  scoredJobs,
  userId,
  profileId,
}: {
  scoredJobs: ScoredJob[];
  userId: string;
  profileId: string;
}) {
  const entries = scoredJobs.map((job) => ({
    where: {
      userId_jobId_profileId: {
        userId,
        jobId: job.id,
        profileId,
      },
    },
    update: {
      matchScore: job.matchScore,
    },
    create: {
      userId,
      jobId: job.id,
      profileId,
      matchScore: job.matchScore,
    },
  }));

  const results = await Promise.allSettled(
    entries.map((entry) => prisma.jobMatchResult.upsert(entry))
  );

  const failed = results.filter(
    (r) => r.status === "rejected"
  ) as PromiseRejectedResult[];
  if (failed.length > 0) {
    failed.forEach((f, i) => {
      logger.warn(`❌ Failed to upsert match ${i}: ${f.reason}`);
    });
  } else {
    logger.info(`✅ Saved ${entries.length} matched jobs`);
  }
}
