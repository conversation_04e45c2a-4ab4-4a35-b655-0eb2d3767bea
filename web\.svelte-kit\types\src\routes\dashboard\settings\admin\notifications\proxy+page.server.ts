// @ts-nocheck
// src/routes/dashboard/settings/admin/notifications/+page.server.ts
import { redirect } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import type { PageServerLoad } from './$types';

export const load = async ({ locals }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  // Check if user is an admin
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
    select: { isAdmin: true, role: true },
  });

  // Allow access if either isAdmin is true OR role is 'admin'
  if (!userData || (!userData.isAdmin && userData.role !== 'admin')) {
    throw redirect(302, '/dashboard/settings');
  }

  // Get all users for the user selector
  const users = await prisma.user.findMany({
    select: {
      id: true,
      email: true,
      name: true,
      role: true,
      image: true,
    },
    orderBy: {
      email: 'asc',
    },
  });

  return {
    users,
    currentUser: {
      id: user.id,
      email: user.email,
      name: user.name,
      role: userData?.role || 'admin',
    },
  };
};
