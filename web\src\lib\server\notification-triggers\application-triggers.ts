// src/lib/server/notification-triggers/application-triggers.ts
import { prisma } from '$lib/server/prisma';
import {
  sendApplicationNotification,
  NotificationType,
  NotificationPriority,
} from '$lib/server/notification-triggers/notification-service';
import { logger } from '$lib/server/logger';

/**
 * Send a notification when an application status changes
 */
export async function notifyApplicationStatusChange(
  userId: string,
  applicationId: string,
  newStatus: string,
  oldStatus: string
): Promise<boolean> {
  try {
    // Get application details
    const application = await prisma.application.findUnique({
      where: { id: applicationId },
      select: {
        company: true,
        position: true,
      },
    });

    if (!application) {
      logger.error(`Application not found: ${applicationId}`);
      return false;
    }

    // Get user notification preferences from user preferences
    const userRecord = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        preferences: true,
      },
    });

    // Check if user has disabled notifications in preferences
    const userPrefs = (userRecord?.preferences as any) || {};
    const notificationPrefs = userPrefs.notifications || {};

    if (notificationPrefs.jobs && notificationPrefs.jobs.applicationStatus === false) {
      logger.info(`User ${userId} has disabled application status notifications in preferences`);
      return false;
    }

    // Also check NotificationSettings for backward compatibility
    const settings = await prisma.notificationSettings.findUnique({
      where: { userId },
    });

    if (settings && !settings.applicationStatusEnabled) {
      logger.info(`User ${userId} has disabled application status notifications in settings`);
      return false;
    }

    // Create notification
    const result = await sendApplicationNotification(userId, {
      title: 'Application Status Updated',
      message: `Your application for ${application.position} at ${application.company} has been updated from ${oldStatus} to ${newStatus}`,
      url: `/dashboard/tracker/${applicationId}`,
      type: NotificationType.APPLICATION,
      priority: NotificationPriority.HIGH,
      metadata: {
        applicationId,
        newStatus,
        oldStatus,
        company: application.company,
        position: application.position,
      },
    });

    return result;
  } catch (error) {
    logger.error('Error sending application status notification:', error);
    return false;
  }
}

/**
 * Send a notification when an interview is scheduled
 */
export async function notifyInterviewScheduled(
  userId: string,
  interviewId: string,
  applicationId: string,
  interviewDate: Date,
  interviewType: string
): Promise<boolean> {
  try {
    // Get application details
    const application = await prisma.application.findUnique({
      where: { id: applicationId },
      select: {
        company: true,
        position: true,
      },
    });

    if (!application) {
      logger.error(`Application not found: ${applicationId}`);
      return false;
    }

    // Get user notification preferences from user preferences
    const userRecord = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        preferences: true,
      },
    });

    // Check if user has disabled notifications in preferences
    const userPrefs = (userRecord?.preferences as any) || {};
    const notificationPrefs = userPrefs.notifications || {};

    if (notificationPrefs.jobs && notificationPrefs.jobs.interviewReminders === false) {
      logger.info(`User ${userId} has disabled interview notifications in preferences`);
      return false;
    }

    // Also check NotificationSettings for backward compatibility
    const settings = await prisma.notificationSettings.findUnique({
      where: { userId },
    });

    if (settings && !settings.interviewRemindersEnabled) {
      logger.info(`User ${userId} has disabled interview notifications in settings`);
      return false;
    }

    // Format date
    const formattedDate = interviewDate.toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    });

    // Create notification
    const result = await sendApplicationNotification(userId, {
      title: 'Interview Scheduled',
      message: `You have a ${interviewType} interview for ${application.position} at ${application.company} on ${formattedDate}`,
      url: `/dashboard/tracker/${applicationId}`,
      type: NotificationType.INTERVIEW,
      priority: NotificationPriority.URGENT,
      metadata: {
        interviewId,
        applicationId,
        interviewDate: interviewDate.toISOString(),
        interviewType,
        company: application.company,
        position: application.position,
      },
    });

    return result;
  } catch (error) {
    logger.error('Error sending interview notification:', error);
    return false;
  }
}

/**
 * Send a notification when an application is rejected
 */
export async function notifyApplicationRejected(
  userId: string,
  applicationId: string
): Promise<boolean> {
  try {
    // Get application details
    const application = await prisma.application.findUnique({
      where: { id: applicationId },
      select: {
        company: true,
        position: true,
      },
    });

    if (!application) {
      logger.error(`Application not found: ${applicationId}`);
      return false;
    }

    // Get user notification preferences from user preferences
    const userRecord = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        preferences: true,
      },
    });

    // Check if user has disabled notifications in preferences
    const userPrefs = (userRecord?.preferences as any) || {};
    const notificationPrefs = userPrefs.notifications || {};

    if (notificationPrefs.jobs && notificationPrefs.jobs.applicationStatus === false) {
      logger.info(`User ${userId} has disabled application status notifications in preferences`);
      return false;
    }

    // Also check NotificationSettings for backward compatibility
    const settings = await prisma.notificationSettings.findUnique({
      where: { userId },
    });

    if (settings && !settings.applicationStatusEnabled) {
      logger.info(`User ${userId} has disabled application status notifications in settings`);
      return false;
    }

    // Create notification
    const result = await sendApplicationNotification(userId, {
      title: 'Application Update',
      message: `We're sorry, your application for ${application.position} at ${application.company} was not selected to move forward.`,
      url: `/dashboard/tracker/${applicationId}`,
      type: NotificationType.APPLICATION,
      priority: NotificationPriority.MEDIUM,
      metadata: {
        applicationId,
        status: 'rejected',
        company: application.company,
        position: application.position,
      },
    });

    return result;
  } catch (error) {
    logger.error('Error sending application rejected notification:', error);
    return false;
  }
}

/**
 * Send a notification when an application receives an offer
 */
export async function notifyApplicationOffer(
  userId: string,
  applicationId: string,
  salary?: string
): Promise<boolean> {
  try {
    // Get application details
    const application = await prisma.application.findUnique({
      where: { id: applicationId },
      select: {
        company: true,
        position: true,
      },
    });

    if (!application) {
      logger.error(`Application not found: ${applicationId}`);
      return false;
    }

    // Get user notification preferences from user preferences
    const userRecord = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        preferences: true,
      },
    });

    // Check if user has disabled notifications in preferences
    const userPrefs = (userRecord?.preferences as any) || {};
    const notificationPrefs = userPrefs.notifications || {};

    if (notificationPrefs.jobs && notificationPrefs.jobs.applicationStatus === false) {
      logger.info(`User ${userId} has disabled application status notifications in preferences`);
      return false;
    }

    // Also check NotificationSettings for backward compatibility
    const settings = await prisma.notificationSettings.findUnique({
      where: { userId },
    });

    if (settings && !settings.applicationStatusEnabled) {
      logger.info(`User ${userId} has disabled application status notifications in settings`);
      return false;
    }

    // Create notification message
    let message = `Congratulations! You've received an offer for ${application.position} at ${application.company}.`;
    if (salary) {
      message += ` The offered salary is ${salary}.`;
    }

    // Create notification
    const result = await sendApplicationNotification(userId, {
      title: 'Job Offer Received!',
      message,
      url: `/dashboard/tracker/${applicationId}`,
      type: NotificationType.APPLICATION,
      priority: NotificationPriority.URGENT,
      metadata: {
        applicationId,
        status: 'offer',
        company: application.company,
        position: application.position,
        salary,
      },
    });

    return result;
  } catch (error) {
    logger.error('Error sending application offer notification:', error);
    return false;
  }
}
