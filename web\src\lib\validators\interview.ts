import { z } from 'zod';

/**
 * Schema for interview stage form
 */
export const interviewSchema = z.object({
  stageName: z.string().min(1, { message: 'Interview stage is required' }),
  stageDate: z.string().min(1, { message: 'Date is required' }),
  outcome: z.string().optional().nullable(),
  feedback: z.string().optional().nullable(),
  interviewers: z.string().optional().nullable(),
  duration: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  nextAction: z.string().optional().nullable(),
});

export const interviewDefaultValues = {
  stageName: '',
  stageDate: new Date().toISOString().split('T')[0],
  outcome: '',
  feedback: '',
  interviewers: '',
  duration: '',
  notes: '',
  nextAction: '',
};

export type InterviewSchema = z.infer<typeof interviewSchema>;

/**
 * Schema for interview question form
 */
export const questionSchema = z.object({
  question: z.string().min(1, { message: 'Question is required' }),
  category: z.string().min(1, { message: 'Category is required' }),
  difficulty: z.string().optional().nullable(),
  userResponse: z.string().optional().nullable(),
  userConfidence: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
});

export const questionDefaultValues = {
  question: '',
  category: '',
  difficulty: '',
  userResponse: '',
  userConfidence: '',
  notes: '',
};

export type QuestionSchema = z.infer<typeof questionSchema>;
