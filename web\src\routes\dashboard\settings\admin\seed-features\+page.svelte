<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { toast } from 'svelte-sonner';
  import { goto } from '$app/navigation';

  let isLoading = $state(false);

  async function seedServiceFeatures() {
    isLoading = true;
    try {
      toast.loading('Seeding service features...');
      
      const response = await fetch('/api/admin/features/seed-service', {
        method: 'POST',
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.dismiss();
        toast.success('Service features seeded successfully!');
        console.log('Seed results:', result);
      } else {
        toast.dismiss();
        toast.error(`Failed to seed service features: ${result.error}`);
      }
    } catch (error) {
      console.error('Error seeding service features:', error);
      toast.dismiss();
      toast.error(`Error seeding service features: ${error.message}`);
    } finally {
      isLoading = false;
    }
  }

  async function seedAllFeatures() {
    isLoading = true;
    try {
      toast.loading('Seeding all features...');
      
      const response = await fetch('/api/admin/features/seed-all', {
        method: 'POST',
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.dismiss();
        toast.success('All features seeded successfully!');
        console.log('Seed results:', result);
      } else {
        toast.dismiss();
        toast.error(`Failed to seed all features: ${result.error}`);
      }
    } catch (error) {
      console.error('Error seeding all features:', error);
      toast.dismiss();
      toast.error(`Error seeding all features: ${error.message}`);
    } finally {
      isLoading = false;
    }
  }

  async function seedAnalysisFeatures() {
    isLoading = true;
    try {
      toast.loading('Seeding analysis features...');
      
      const response = await fetch('/api/admin/features/seed-analysis', {
        method: 'POST',
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.dismiss();
        toast.success('Analysis features seeded successfully!');
        console.log('Seed results:', result);
      } else {
        toast.dismiss();
        toast.error(`Failed to seed analysis features: ${result.error}`);
      }
    } catch (error) {
      console.error('Error seeding analysis features:', error);
      toast.dismiss();
      toast.error(`Error seeding analysis features: ${error.message}`);
    } finally {
      isLoading = false;
    }
  }
</script>

<div class="container mx-auto p-6">
  <div class="mb-6 flex items-center justify-between">
    <h1 class="text-2xl font-bold">Seed Features</h1>
    <Button variant="outline" onclick={() => goto('/dashboard/settings/admin')}>
      Back to Admin
    </Button>
  </div>

  <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
    <!-- Service Features -->
    <div class="rounded-lg border p-6 shadow-sm">
      <h2 class="mb-4 text-xl font-semibold">Service Features</h2>
      <p class="text-muted-foreground mb-4 text-sm">
        Seed service features including document storage with storage limits.
      </p>
      <Button onclick={seedServiceFeatures} disabled={isLoading} class="w-full">
        Seed Service Features
      </Button>
    </div>

    <!-- All Features -->
    <div class="rounded-lg border p-6 shadow-sm">
      <h2 class="mb-4 text-xl font-semibold">All Features</h2>
      <p class="text-muted-foreground mb-4 text-sm">
        Seed all features including core, resume, job search, and application features.
      </p>
      <Button onclick={seedAllFeatures} disabled={isLoading} class="w-full">
        Seed All Features
      </Button>
    </div>

    <!-- Analysis Features -->
    <div class="rounded-lg border p-6 shadow-sm">
      <h2 class="mb-4 text-xl font-semibold">Analysis Features</h2>
      <p class="text-muted-foreground mb-4 text-sm">
        Seed analysis features for job market insights and resume analysis.
      </p>
      <Button onclick={seedAnalysisFeatures} disabled={isLoading} class="w-full">
        Seed Analysis Features
      </Button>
    </div>
  </div>
</div>
