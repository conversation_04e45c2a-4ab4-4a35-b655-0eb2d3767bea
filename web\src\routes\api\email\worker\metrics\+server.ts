/**
 * Email Worker Metrics API Endpoint
 *
 * This endpoint provides metrics about the email worker.
 */

import { json } from '@sveltejs/kit';
import { logger } from '$lib/server/logger';
import { getPrismaClient } from '$lib/server/prisma';
import { RedisConnection as redis } from '$lib/server/redis';

// Get prisma client
const prisma = getPrismaClient();

export async function GET() {
  try {
    // Default metrics
    const metrics = {
      processedLast24h: 0,
      failedLast24h: 0,
      averageProcessingTime: 0,
      oldestJobInQueue: null,
      totalProcessed: 0,
      totalFailed: 0
    };

    // Try to get metrics from database
    try {
      if (prisma && typeof prisma.emailMetrics?.findFirst === 'function') {
        const dbMetrics = await prisma.emailMetrics.findFirst({
          orderBy: { timestamp: 'desc' }
        });

        if (dbMetrics) {
          metrics.processedLast24h = dbMetrics.processedLast24h || 0;
          metrics.failedLast24h = dbMetrics.failedLast24h || 0;
          metrics.averageProcessingTime = dbMetrics.averageProcessingTime || 0;
          metrics.totalProcessed = dbMetrics.totalProcessed || 0;
          metrics.totalFailed = dbMetrics.totalFailed || 0;
        }
      }
    } catch (dbError) {
      logger.warn('Error fetching email metrics from database:', dbError);
      // Continue with default metrics
    }

    // Try to get oldest job from Redis
    if (redis) {
      try {
        // Get the oldest job in the queue
        const jobs = await redis.zrange('email:queue', 0, 0, 'WITHSCORES');
        if (jobs && jobs.length >= 2) {
          const oldestTimestamp = parseInt(jobs[1]);
          if (!isNaN(oldestTimestamp)) {
            metrics.oldestJobInQueue = new Date(oldestTimestamp).toISOString();
          }
        }
      } catch (redisError) {
        logger.warn('Error fetching oldest job from Redis:', redisError);
      }
    }

    return json(metrics);
  } catch (error) {
    logger.error('Error fetching worker metrics:', error);
    return json({ 
      error: 'Failed to fetch worker metrics',
      processedLast24h: 0,
      failedLast24h: 0,
      averageProcessingTime: 0,
      oldestJobInQueue: null,
      totalProcessed: 0,
      totalFailed: 0
    }, { status: 500 });
  }
}
