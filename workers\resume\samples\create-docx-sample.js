/**
 * <PERSON><PERSON><PERSON> to create a DOCX sample file from a text file
 */
const fs = require('fs');
const path = require('path');
const docx = require('docx');
const { Document, Packer, Paragraph, TextRun } = docx;

// Read the text file
const textFilePath = path.resolve(__dirname, 'comprehensive-resume-sample.txt');
const textContent = fs.readFileSync(textFilePath, 'utf8');

// Create a new document
const doc = new Document({
  sections: [
    {
      properties: {},
      children: textContent.split('\n').map(line => {
        return new Paragraph({
          children: [
            new TextRun({
              text: line,
              size: 24, // 12pt
            }),
          ],
        });
      }),
    },
  ],
});

// Save the document
Packer.toBuffer(doc).then((buffer) => {
  fs.writeFileSync(path.resolve(__dirname, 'sample-resume.docx'), buffer);
  console.log('Document created successfully');
});
