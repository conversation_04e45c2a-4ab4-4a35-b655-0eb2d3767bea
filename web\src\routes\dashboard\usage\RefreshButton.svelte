<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { RefreshCw } from 'lucide-svelte';

  export let loading: boolean = false;
  export let onRefresh: () => void;
</script>

<Button 
  variant="outline" 
  size="sm" 
  on:click={onRefresh} 
  disabled={loading}
  class="ml-auto"
>
  <RefreshCw class="mr-2 h-4 w-4" class:animate-spin={loading} />
  {loading ? 'Refreshing...' : 'Refresh'}
</Button>
