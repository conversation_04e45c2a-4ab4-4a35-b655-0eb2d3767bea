import natural from 'natural';
import { logger } from '../../utils/logger.js';
import { generateCompletion } from '../llm/index.js';
import { compilePrompt, KEYWORD_EXTRACTION_PROMPT, INDUSTRY_KEYWORDS_PROMPT } from '../llm/prompts.js';

const tokenizer = new natural.WordTokenizer();
const TfIdf = natural.TfIdf;

interface KeywordAnalysisResult {
  score: number;
  matches: string[];
  missing: string[];
}

export async function extractKeywords(
  resumeText: string,
  jobDescription?: string
): Promise<KeywordAnalysisResult> {
  try {
    // Extract keywords from resume using TF-IDF
    const resumeKeywords = await extractStatisticalKeywords(resumeText);
    
    // If job description is provided, extract keywords from it
    if (jobDescription) {
      const jobKeywords = await extractStatisticalKeywords(jobDescription);
      
      // Try to enhance with LLM
      try {
        const enhancedJobKeywords = await extractSemanticKeywords(jobDescription);
        jobKeywords.push(...enhancedJobKeywords);
      } catch (error) {
        logger.warn('Failed to extract semantic keywords from job description');
      }
      
      // Find matching keywords
      const matches = resumeKeywords.filter(keyword => 
        jobKeywords.some(jobKeyword => 
          jobKeyword.toLowerCase() === keyword.toLowerCase()
        )
      );
      
      // Find missing keywords
      const missing = jobKeywords.filter(keyword => 
        !resumeKeywords.some(resumeKeyword => 
          resumeKeyword.toLowerCase() === keyword.toLowerCase()
        )
      );
      
      // Calculate score based on match percentage
      const score = Math.min(100, Math.round((matches.length / Math.max(1, jobKeywords.length)) * 100));
      
      return {
        score,
        matches,
        missing: missing.slice(0, 10), // Limit to top 10 missing keywords
      };
    } else {
      // If no job description, use industry-standard keywords
      const industryKeywords = await getIndustryKeywords(resumeText);
      
      // Find matching keywords
      const matches = resumeKeywords.filter(keyword => 
        industryKeywords.some(industryKeyword => 
          industryKeyword.toLowerCase() === keyword.toLowerCase()
        )
      );
      
      // Find missing keywords
      const missing = industryKeywords.filter(keyword => 
        !resumeKeywords.some(resumeKeyword => 
          resumeKeyword.toLowerCase() === keyword.toLowerCase()
        )
      );
      
      // Calculate score based on match percentage
      const score = Math.min(100, Math.round((matches.length / Math.max(1, industryKeywords.length)) * 100));
      
      return {
        score,
        matches,
        missing: missing.slice(0, 10), // Limit to top 10 missing keywords
      };
    }
  } catch (error) {
    logger.error('Error extracting keywords:', error);
    return {
      score: 50, // Default score
      matches: [],
      missing: [],
    };
  }
}

async function extractStatisticalKeywords(text: string): Promise<string[]> {
  // Use TF-IDF to extract statistically significant keywords
  const tfidf = new TfIdf();
  
  // Add the document
  tfidf.addDocument(text);
  
  // Extract terms with highest TF-IDF scores
  const terms: string[] = [];
  tfidf.listTerms(0).slice(0, 20).forEach(item => {
    // Filter out common words and short terms
    if (item.term.length > 3 && !isCommonWord(item.term)) {
      terms.push(item.term);
    }
  });
  
  return terms;
}

async function extractSemanticKeywords(text: string): Promise<string[]> {
  try {
    // Use LLM to extract semantically meaningful keywords
    const prompt = compilePrompt(KEYWORD_EXTRACTION_PROMPT, { text });
    
    const completion = await generateCompletion(prompt, {
      temperature: 0.2,
    });
    
    // Parse comma-separated list
    return completion.text
      .split(',')
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 0);
  } catch (error) {
    logger.error('Error extracting semantic keywords:', error);
    return [];
  }
}

async function getIndustryKeywords(resumeText: string): Promise<string[]> {
  try {
    // Use LLM to extract industry from resume and suggest relevant keywords
    const prompt = compilePrompt(INDUSTRY_KEYWORDS_PROMPT, { resumeText });
    
    const completion = await generateCompletion(prompt, {
      temperature: 0.3,
    });
    
    // Extract keywords from result
    const keywordsMatch = completion.text.match(/Keywords:(.*)/i);
    if (keywordsMatch && keywordsMatch[1]) {
      return keywordsMatch[1]
        .split(',')
        .map(keyword => keyword.trim())
        .filter(keyword => keyword.length > 0);
    }
    
    return getDefaultKeywords();
  } catch (error) {
    logger.error('Error getting industry keywords:', error);
    return getDefaultKeywords();
  }
}

function getDefaultKeywords(): string[] {
  // Default keywords that are common across many industries
  return [
    'leadership',
    'communication',
    'teamwork',
    'problem-solving',
    'project management',
    'analytical',
    'detail-oriented',
    'time management',
    'organization',
    'customer service',
  ];
}

function isCommonWord(word: string): boolean {
  const commonWords = [
    'the', 'and', 'that', 'have', 'for', 'not', 'with', 'you', 'this', 'but',
    'his', 'her', 'they', 'from', 'she', 'will', 'one', 'all', 'would', 'there',
    'their', 'what', 'out', 'about', 'who', 'get', 'which', 'when', 'make', 'can',
    'like', 'time', 'just', 'him', 'know', 'take', 'person', 'into', 'year', 'your',
    'good', 'some', 'could', 'them', 'see', 'other', 'than', 'then', 'now', 'look',
    'only', 'come', 'its', 'over', 'think', 'also', 'back', 'after', 'use', 'two',
    'how', 'our', 'work', 'first', 'well', 'way', 'even', 'new', 'want', 'because',
  ];
  
  return commonWords.includes(word.toLowerCase());
}
