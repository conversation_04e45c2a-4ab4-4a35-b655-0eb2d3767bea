// @ts-nocheck
import { redirect } from '@sveltejs/kit';
import { dev } from '$app/environment';
import { prisma } from '$lib/server/prisma';
import type { PageServerLoad } from './$types.js';

export const load = async ({ locals }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  // Check if user is an admin
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
    select: { isAdmin: true, role: true },
  });

  // Allow access if either isAdmin is true OR role is 'admin'
  if (!userData || (!userData.isAdmin && userData.role !== 'admin')) {
    throw redirect(302, '/dashboard/settings');
  }

  // Don't load plans in the backend, let the client handle it
  return {};
};
