<script lang="ts">
  import SEO from '$components/shared/SEO.svelte';
  import HistoryComponent from '../HistoryComponent.svelte';

  // Import page data
  export let data;
</script>

<SEO
  title="System Status History | Hirli"
  description="View the history of system status notices and maintenance events."
  keywords="system status, maintenance history, incident history, Hirli status" />

<div class="flex flex-col gap-4">
  <div class="border-border flex items-center justify-between border-b p-6">
    <div class="flex flex-col">
      <h1 class="text-3xl font-bold">Notice History</h1>
      <p class="text-muted-foreground">Past system notices and maintenance events</p>
    </div>
    <div>
      <a href="/system-status" class="text-primary hover:underline">Back to Status Page</a>
    </div>
  </div>

  <HistoryComponent 
    maintenance={data.maintenance} 
    currentMonth={data.currentMonth}
    currentYear={data.currentYear}
    hasNextMonth={data.hasNextMonth}
    hasPrevMonth={data.hasPrevMonth}
  />
</div>
