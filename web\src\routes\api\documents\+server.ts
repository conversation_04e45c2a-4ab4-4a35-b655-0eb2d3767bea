import { prisma } from '$lib/server/prisma';
import type { RequestHand<PERSON> } from './$types.js';

export const GET: RequestHandler = async ({ url, locals }) => {
  const user = locals.user;
  if (!user) return new Response('Unauthorized', { status: 401 });

  const type = url.searchParams.get('type');
  console.log('Document type requested:', type);

  try {
    console.log('Fetching documents for user:', user.id, 'with type:', type);

    // Get documents for the user
    const documents = await prisma.document.findMany({
      where: {
        userId: user.id,
        ...(type ? { type: type } : {}),
      },
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        resume: {
          select: {
            id: true,
            isParsed: true,
            parsedAt: true,
          },
        },
      },
    });

    console.log(`Found ${documents.length} documents`);

    return new Response(
      JSON.stringify({
        documents,
      }),
      {
        headers: { 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    console.error('Error fetching documents:', error);
    // Return more detailed error for debugging
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
};
