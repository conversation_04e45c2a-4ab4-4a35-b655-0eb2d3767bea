<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import * as RadioGroup from '$lib/components/ui/radio-group/index.js';
  import { toast } from 'svelte-sonner';
  import type { ProfileData } from '$lib/types/profile';

  // Props
  const { profileData, onSave, onCancel } = $props<{
    profileData: ProfileData;
    onSave: (data: Partial<ProfileData>) => Promise<boolean>;
    onCancel: () => void;
  }>();

  // Form data
  let gender = $state(profileData.personalInfo?.gender || '');
  let ethnicity = $state(profileData.personalInfo?.ethnicity || '');
  let veteranStatus = $state(profileData.personalInfo?.veteranStatus || '');
  let disabilityStatus = $state(profileData.personalInfo?.disabilityStatus || '');

  // Options
  const genderOptions = [
    { value: 'male', label: 'Male' },
    { value: 'female', label: 'Female' },
    { value: 'non-binary', label: 'Non-binary' },
    { value: 'prefer-not-to-say', label: 'Prefer not to say' },
  ];

  const ethnicityOptions = [
    { value: 'asian', label: 'Asian' },
    { value: 'black', label: 'Black or African American' },
    { value: 'hispanic', label: 'Hispanic or Latino' },
    { value: 'native-american', label: 'Native American or Alaska Native' },
    { value: 'pacific-islander', label: 'Native Hawaiian or Pacific Islander' },
    { value: 'white', label: 'White' },
    { value: 'two-or-more', label: 'Two or more races' },
    { value: 'prefer-not-to-say', label: 'Prefer not to say' },
  ];

  // Handle form submission
  async function handleSubmit() {
    try {
      // Prepare data
      const updatedData: Partial<ProfileData> = {
        personalInfo: {
          ...profileData.personalInfo,
          gender,
          ethnicity,
          veteranStatus,
          disabilityStatus,
        },
      };

      // Save data
      const success = await onSave(updatedData);
      if (success) {
        toast.success('Demographics updated successfully');
      }
    } catch (error) {
      console.error('Error saving demographics:', error);
      toast.error('Failed to save demographics');
    }
  }
</script>

<form on:submit|preventDefault={handleSubmit} class="space-y-4">
  <div class="space-y-4">
    <div class="space-y-2">
      <Label for="gender">Gender</Label>
      <Select.Root value={gender} onValueChange={(value) => (gender = value)}>
        <Select.Trigger id="gender" class="w-full">
          <Select.Value placeholder="Select gender" />
        </Select.Trigger>
        <Select.Content>
          <Select.Group>
            {#each genderOptions as option}
              <Select.Item value={option.value}>{option.label}</Select.Item>
            {/each}
          </Select.Group>
        </Select.Content>
      </Select.Root>
    </div>

    <div class="space-y-2">
      <Label for="ethnicity">Ethnicity</Label>
      <Select.Root value={ethnicity} onValueChange={(value) => (ethnicity = value)}>
        <Select.Trigger id="ethnicity" class="w-full">
          <Select.Value placeholder="Select ethnicity" />
        </Select.Trigger>
        <Select.Content>
          <Select.Group>
            {#each ethnicityOptions as option}
              <Select.Item value={option.value}>{option.label}</Select.Item>
            {/each}
          </Select.Group>
        </Select.Content>
      </Select.Root>
    </div>

    <div class="space-y-2">
      <Label>Veteran Status</Label>
      <RadioGroup.Root value={veteranStatus} onValueChange={(value) => (veteranStatus = value)}>
        <div class="flex items-center space-x-2">
          <RadioGroup.Item value="yes" id="veteran-yes" />
          <Label for="veteran-yes" class="cursor-pointer">Yes</Label>
        </div>
        <div class="flex items-center space-x-2">
          <RadioGroup.Item value="no" id="veteran-no" />
          <Label for="veteran-no" class="cursor-pointer">No</Label>
        </div>
        <div class="flex items-center space-x-2">
          <RadioGroup.Item value="prefer-not-to-say" id="veteran-prefer-not-to-say" />
          <Label for="veteran-prefer-not-to-say" class="cursor-pointer">Prefer not to say</Label>
        </div>
      </RadioGroup.Root>
    </div>

    <div class="space-y-2">
      <Label>Disability Status</Label>
      <RadioGroup.Root
        value={disabilityStatus}
        onValueChange={(value) => (disabilityStatus = value)}>
        <div class="flex items-center space-x-2">
          <RadioGroup.Item value="yes" id="disability-yes" />
          <Label for="disability-yes" class="cursor-pointer">Yes</Label>
        </div>
        <div class="flex items-center space-x-2">
          <RadioGroup.Item value="no" id="disability-no" />
          <Label for="disability-no" class="cursor-pointer">No</Label>
        </div>
        <div class="flex items-center space-x-2">
          <RadioGroup.Item value="prefer-not-to-say" id="disability-prefer-not-to-say" />
          <Label for="disability-prefer-not-to-say" class="cursor-pointer">Prefer not to say</Label>
        </div>
      </RadioGroup.Root>
    </div>
  </div>

  <div class="flex justify-end space-x-2 pt-4">
    <Button variant="outline" type="button" on:click={onCancel}>Cancel</Button>
    <Button type="submit">Save Changes</Button>
  </div>
</form>
