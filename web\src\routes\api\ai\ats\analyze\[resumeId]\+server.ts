import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';

/**
 * Get ATS analysis for a resume
 * 
 * This endpoint retrieves or generates an ATS analysis for a resume.
 */
export const GET: RequestHandler = async ({ params, cookies }) => {
  try {
    // Verify authentication
    const token = cookies.get('auth_token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tokenData = verifySessionToken(token);
    if (!tokenData || !tokenData.id) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // Get user ID from token
    const userId = tokenData.id;

    // Get resume ID from params
    const resumeId = params.resumeId;
    if (!resumeId) {
      return json({ error: 'Missing resume ID' }, { status: 400 });
    }

    // Check if resume exists and belongs to the user
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        document: {
          userId
        }
      }
    });

    if (!resume) {
      return json({ error: 'Resume not found or access denied' }, { status: 404 });
    }

    // Check if user has access to the resume_analysis feature
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscriptions: {
          where: { status: 'active' },
          include: {
            plan: {
              include: {
                features: {
                  where: { featureId: 'resume_analysis' }
                }
              }
            }
          }
        }
      }
    });

    const hasAccess = user?.subscriptions.some(sub => 
      sub.plan.features.some(feature => feature.featureId === 'resume_analysis')
    );

    if (!hasAccess && process.env.NODE_ENV === 'production') {
      return json({ error: 'Feature not available in your plan' }, { status: 403 });
    }

    // Check if analysis already exists
    let analysis = await prisma.atsAnalysis.findUnique({
      where: { resumeId }
    });

    // If analysis doesn't exist, generate a new one
    if (!analysis) {
      // In a real implementation, this would call the ATS analysis worker
      // For now, we'll generate mock analysis
      const mockAnalysis = generateMockATSAnalysis();
      
      analysis = await prisma.atsAnalysis.create({
        data: {
          id: `ats-${Date.now()}`,
          resumeId,
          overallScore: mockAnalysis.overallScore,
          keywordScore: mockAnalysis.keywordScore,
          formatScore: mockAnalysis.formatScore,
          contentScore: mockAnalysis.contentScore,
          readabilityScore: mockAnalysis.readabilityScore,
          detectedIssues: mockAnalysis.detectedIssues,
          suggestedKeywords: mockAnalysis.suggestedKeywords,
          analysisDetails: mockAnalysis.analysisDetails,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }

    return json({ success: true, analysis });
  } catch (error) {
    console.error('Error getting ATS analysis:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

/**
 * Generate mock ATS analysis
 * 
 * In a real implementation, this would call the ATS analysis worker
 */
function generateMockATSAnalysis() {
  return {
    overallScore: 75,
    keywordScore: 80,
    formatScore: 70,
    contentScore: 85,
    readabilityScore: 65,
    detectedIssues: [
      { section: 'format', message: 'Resume exceeds one page', severity: 'medium' },
      { section: 'content', message: 'Summary section is too generic', severity: 'medium' },
      { section: 'readability', message: 'Some sentences are too long', severity: 'low' }
    ],
    suggestedKeywords: [
      'JavaScript',
      'React',
      'Node.js',
      'TypeScript',
      'REST API',
      'GraphQL',
      'CI/CD',
      'Agile',
      'AWS',
      'Docker'
    ],
    analysisDetails: {
      format: {
        pageCount: 2,
        sectionCount: 5,
        hasProperHeadings: true,
        hasBulletPoints: true
      },
      content: {
        hasQuantifiableResults: true,
        hasActionVerbs: true,
        hasTooManyBuzzwords: false
      },
      readability: {
        averageSentenceLength: 15,
        complexWordPercentage: 12,
        passiveVoiceCount: 3
      }
    }
  };
}
