<script lang="ts">
  import {
    <PERSON>,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
  } from '$lib/components/ui/card';
  import { Badge } from '$lib/components/ui/badge';
  import { Progress } from '$lib/components/ui/progress';
  import type { FeatureWithDetailedUsage } from '$lib/services/feature-service';
  import {
    formatLimitValue,
    getProgressColor,
    getAccessLevelColor,
    formatAccessLevel,
  } from './feature-utils';
  import ResetButton from './ResetButton.svelte';

  export let feature: FeatureWithDetailedUsage;
  export let onReset: () => void;
</script>

<Card>
  <CardHeader>
    <div class="flex items-start justify-between">
      <CardTitle>{feature.name}</CardTitle>
      <Badge variant="outline" class={getAccessLevelColor(feature.accessLevel)}>
        {formatAccessLevel(feature.accessLevel)}
      </Badge>
    </div>
    <CardDescription>{feature.description}</CardDescription>
  </CardHeader>
  <CardContent>
    {#if feature.limits && feature.limits.length > 0}
      <div class="space-y-4">
        {#each feature.limits as limit}
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span>{limit.name}</span>
              <span>
                {limit.used} / {formatLimitValue(limit.value, limit.unit)}
              </span>
            </div>
            {#if limit.value !== 'unlimited' && typeof limit.value === 'number'}
              <Progress
                value={limit.percentUsed || 0}
                max={100}
                class={getProgressColor(limit.percentUsed)} />
            {:else}
              <div class="text-muted-foreground text-xs">Unlimited usage</div>
            {/if}
          </div>
        {/each}
      </div>
    {:else}
      <div class="text-muted-foreground text-sm">No usage limits for this feature.</div>
    {/if}
  </CardContent>
  <CardFooter>
    <div class="flex w-full justify-end">
      <ResetButton {onReset} featureId={feature.id} featureName={feature.name} />
    </div>
  </CardFooter>
</Card>
