<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { goto } from '$app/navigation';
  import { Lock, AlertTriangle } from 'lucide-svelte';
  import { createFeatureAccess } from '$lib/models/features';
  import { onMount } from 'svelte';

  // Props
  export let userData: any;
  export let featureId: string;
  export let limitId: string | undefined = undefined;
  export let showUpgradeButton: boolean = true;
  export let upgradeButtonText: string = 'Upgrade Plan';
  export let upgradeButtonLink: string = '/dashboard/settings/billing';
  export let limitReachedMessage: string = 'You have reached the limit for this feature.';
  export let notIncludedMessage: string = 'This feature is not included in your current plan.';

  // Feature access logic
  let featureAccess: any;
  let hasAccess = false;
  let hasReachedLimit = false;
  let message = '';

  // Initialize feature access
  onMount(() => {
    updateFeatureAccess();
  });

  // Update when props change
  $: userData && featureId && updateFeatureAccess();
  $: limitId && updateFeatureAccess();

  function updateFeatureAccess() {
    try {
      if (userData) {
        featureAccess = createFeatureAccess(userData);
        hasAccess = featureAccess.hasAccess(featureId);

        if (hasAccess && limitId) {
          hasReachedLimit = featureAccess.hasReachedLimit(featureId, limitId);

          if (hasReachedLimit) {
            const limitValue = featureAccess.getLimitValue(featureId, limitId);
            message = `${limitReachedMessage} (Limit: ${limitValue})`;
          } else {
            message = '';
          }
        } else if (!hasAccess) {
          message = notIncludedMessage;
        } else {
          message = '';
        }
      }
    } catch (error) {
      console.error('Error in FeatureGuard:', error);
      hasAccess = false;
      hasReachedLimit = false;
      message = 'Error checking feature access.';
    }
  }
</script>

{#if hasAccess && !hasReachedLimit}
  <slot />
{:else}
  <div
    class="flex flex-col items-center justify-center rounded-md border border-dashed p-8 text-center">
    <div class="bg-muted mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full">
      {#if hasReachedLimit}
        <AlertTriangle class="text-warning h-6 w-6" />
      {:else}
        <Lock class="text-muted-foreground h-6 w-6" />
      {/if}
    </div>
    <h3 class="mb-2 text-lg font-medium">
      {#if hasReachedLimit}
        Limit Reached
      {:else}
        Feature Not Available
      {/if}
    </h3>
    <p class="text-muted-foreground mb-4 max-w-md">
      {message}
    </p>
    {#if showUpgradeButton}
      <Button variant="outline" onclick={() => goto(upgradeButtonLink)}>
        {upgradeButtonText}
      </Button>
    {/if}
  </div>
{/if}
