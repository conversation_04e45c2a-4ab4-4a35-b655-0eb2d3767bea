<script context="module">
  // Helper function to get category name from slug
  function getCategoryName(slug: string): string {
    const categoryMap = {
      'getting-started': 'Getting Started',
      'auto-apply': 'Using Auto Apply',
      'account-billing': 'Account & Billing',
      troubleshooting: 'Troubleshooting',
      'privacy-security': 'Privacy & Security',
    };

    return categoryMap[slug as keyof typeof categoryMap] || slug;
  }
</script>

<!-- src/routes/help/[slug]/+page.svelte -->
<script lang="ts">
  import SEO from '$components/shared/SEO.svelte';
  import HelpSidebar from '$components/help/HelpSidebar.svelte';
  import HelpArticleCard from '$components/help/HelpArticleCard.svelte';
  import PortableText from '$lib/components/sanity/PortableText.svelte';
  import { ArrowLeft, Calendar, Eye } from 'lucide-svelte';
  import { formatDistanceToNow } from 'date-fns';

  // Get data from server
  export let data;

  // Format the updated date
  let updatedDate = '';
  $: updatedDate = data.article?.updatedAt
    ? formatDistanceToNow(new Date(data.article.updatedAt), { addSuffix: true })
    : 'Recently';
</script>

<SEO
  title="{data.article.title} | Help Center"
  description={data.article.description}
  keywords="help center, {data.article.title.toLowerCase()}, {data.article
    .category}, support, guides" />

<div class="container mx-auto px-4 py-12">
  <div class="grid gap-8 lg:grid-cols-4">
    <!-- Sidebar -->
    <div class="lg:col-span-1">
      <HelpSidebar categories={data.categories} />
    </div>

    <!-- Main Content -->
    <div class="lg:col-span-3">
      <div class="mb-6">
        <a href="/help" class="text-primary inline-flex items-center text-sm hover:underline">
          <ArrowLeft class="mr-1 h-4 w-4" />
          Back to Help Center
        </a>
      </div>

      <article>
        <header class="mb-8">
          <h1 class="mb-4 text-3xl font-bold">{data.article.title}</h1>
          <div class="text-muted-foreground flex flex-wrap items-center gap-4 text-sm">
            <a
              href="/help/category/{data.article.category}"
              class="bg-primary/10 text-primary rounded-full px-3 py-1">
              {data.article.category}
            </a>
            <div class="flex items-center gap-1">
              <Calendar class="h-4 w-4" />
              <span>Updated {updatedDate}</span>
            </div>
            <div class="flex items-center gap-1">
              <Eye class="h-4 w-4" />
              <span>{data.article.viewCount} view{data.article.viewCount !== 1 ? 's' : ''}</span>
            </div>
          </div>
        </header>

        <!-- Article Content -->
        <div class="prose prose-lg max-w-none">
          {#if data.article.content}
            <PortableText value={data.article.content} />
          {:else}
            <p class="bg-muted rounded-lg border p-4">
              This article doesn't have any content yet. Please check back later.
            </p>
          {/if}
        </div>

        <!-- Tags -->
        {#if data.article.tags && data.article.tags.length > 0}
          <div class="mt-8">
            <h2 class="mb-2 text-lg font-semibold">Tags</h2>
            <div class="flex flex-wrap gap-2">
              {#each data.article.tags as tag}
                <span class="bg-muted rounded-full px-3 py-1 text-sm">
                  {tag}
                </span>
              {/each}
            </div>
          </div>
        {/if}

        <!-- Related Articles -->
        {#if data.article.relatedArticles && data.article.relatedArticles.length > 0}
          <div class="mt-12">
            <h2 class="mb-6 text-2xl font-semibold">Related Articles</h2>
            <div class="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
              {#each data.article.relatedArticles as article}
                <HelpArticleCard {article} />
              {/each}
            </div>
          </div>
        {/if}

        <!-- More Articles in this Category -->
        {#if data.categoryArticles && data.categoryArticles.length > 0}
          <div class="mt-12">
            <h2 class="mb-6 text-2xl font-semibold">
              More in {getCategoryName(data.article.category)}
            </h2>
            <div class="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
              {#each data.categoryArticles as article}
                <HelpArticleCard {article} />
              {/each}
            </div>
          </div>
        {/if}
      </article>
    </div>
  </div>
</div>
