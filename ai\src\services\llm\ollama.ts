import fetch from "node-fetch";
import { logger } from "../../utils/logger.js";
import { config } from "../../config.js";
import { isCircuitClosed } from "../health/circuit-breaker.js";
import { CircuitOpenError } from "../../utils/errors.js";
import type { CompletionOptions, CompletionResult } from "./index.js";

let ollamaRunning = false;
let modelLoaded = false;

export async function init() {
  try {
    // Check if Ollama is running
    const response = await fetch(`${config.llm.ollamaUrl}/api/tags`);

    if (response.ok) {
      ollamaRunning = true;
      logger.info("✅ Ollama service is running");

      // Check if model is available
      const tags = await response.json();
      let modelExists = false;

      if (
        typeof tags === "object" &&
        tags !== null &&
        Array.isArray((tags as any).models)
      ) {
        modelExists = (tags as { models: any[] }).models.some(
          (model: any) => model.name === config.llm.model
        );
      } else {
        logger.warn("Unexpected response format from Ollama /api/tags");
      }

      // Only pull the model if it doesn't exist
      if (modelExists) {
        logger.info(`✅ Model ${config.llm.model} is available`);
        modelLoaded = true;
      } else {
        logger.info(`⏳ Model ${config.llm.model} not found, pulling...`);
        await pullModel(config.llm.model);
        modelLoaded = true;
      }
    } else {
      logger.warn("Ollama service not running, attempting to start...");
      await startOllama();
    }
  } catch (error) {
    logger.error("Failed to initialize Ollama:", error);
    logger.warn("Attempting to start Ollama service...");
    await startOllama();
  }
}

/**
 * Attempts to connect to Ollama service and load the model
 */
async function startOllama() {
  try {
    logger.info("Waiting for Ollama service to be available...");

    // Wait and retry connecting to Ollama
    let retries = 0;
    const maxRetries = 10;

    while (retries < maxRetries) {
      try {
        logger.info(
          `Attempt ${retries + 1}/${maxRetries} to connect to Ollama...`
        );
        const response = await fetch(`${config.llm.ollamaUrl}/api/tags`);

        if (response.ok) {
          ollamaRunning = true;
          logger.info("Successfully connected to Ollama service");

          // Check if model is already available
          const tags = await response.json();
          let modelExists = false;

          if (
            typeof tags === "object" &&
            tags !== null &&
            Array.isArray((tags as any).models)
          ) {
            modelExists = (tags as { models: any[] }).models.some(
              (model: any) => model.name === config.llm.model
            );
          }

          // Only pull the model if it doesn't exist
          if (modelExists) {
            logger.info(`Model ${config.llm.model} is already available`);
            modelLoaded = true;
          } else {
            logger.info(`Model ${config.llm.model} not found, pulling...`);
            await pullModel(config.llm.model);
            modelLoaded = true;
          }

          return;
        }
      } catch (error) {
        logger.warn(`Attempt ${retries + 1} failed, retrying in 5 seconds...`);
        logger.error("Error while trying to connect to Ollama:", error);
      }

      retries++;
      await new Promise((resolve) => setTimeout(resolve, 5000));
    }

    // If we've exhausted all retries
    logger.error(`Failed to connect to Ollama after ${maxRetries} attempts.`);
    throw new Error(
      `Failed to connect to Ollama after ${maxRetries} attempts. Ollama is required for the AI service to function.`
    );
  } catch (error) {
    logger.error("Failed to connect to Ollama:", error);
    throw new Error("Failed to connect to Ollama service");
  }
}

async function pullModel(modelName: string) {
  try {
    logger.info(`⏳ Pulling model: ${modelName}`);

    const response = await fetch(`${config.llm.ollamaUrl}/api/pull`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        name: modelName,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to pull model: ${response.statusText}`);
    }

    logger.info(`✅ Model ${modelName} pulled successfully`);
    modelLoaded = true;
  } catch (error) {
    logger.error(`Failed to pull model ${modelName}:`, error);
    throw new Error(`Failed to pull model ${modelName}`);
  }
}

/**
 * Check if Ollama is running and the model is loaded
 * @returns An object with status information
 */
export function getOllamaStatus() {
  return {
    ollamaRunning,
    modelLoaded,
    modelName: config.llm.model,
    ready: ollamaRunning && modelLoaded,
  };
}

export async function generateCompletion(
  prompt: string,
  options: CompletionOptions = {}
): Promise<CompletionResult> {
  // Check if circuit breaker is closed
  if (!isCircuitClosed()) {
    throw new CircuitOpenError();
  }

  try {
    const response = await fetch(`${config.llm.ollamaUrl}/api/generate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: config.llm.model,
        prompt,
        stream: false,
        options: {
          temperature: options.temperature ?? 0.7,
          num_predict: options.maxTokens,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.statusText}`);
    }

    const result = (await response.json()) as {
      response: string;
      prompt_eval_count?: number;
      eval_count?: number;
    };

    return {
      text: result.response,
      usage: {
        promptTokens: result.prompt_eval_count ?? 0,
        completionTokens: result.eval_count ?? 0,
        totalTokens: (result.prompt_eval_count ?? 0) + (result.eval_count ?? 0),
      },
    };
  } catch (error) {
    logger.error("Error generating completion:", error);
    throw new Error(
      "Failed to generate completion: Ollama service is required"
    );
  }
}
