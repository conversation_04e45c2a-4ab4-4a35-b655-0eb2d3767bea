<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
  import { MoreHorizontal, Trash2, Eye, EyeOff, ArrowLeft } from 'lucide-svelte';
  import SEO from '$components/shared/SEO.svelte';
  import { goto } from '$app/navigation';
  import { toast } from 'svelte-sonner';
  import { onMount } from 'svelte';
  import type { ProfileData } from '$lib/types/profile';
  import { parseProfileData, migrateProfileData } from '$lib/utils/profileHelpers';
  import type { CompleteProfileSchema } from '$lib/validators/profile';

  // Import components
  import ProfileTabs from './components/ProfileTabs.svelte';

  // Get data from props
  const { data } = $props();

  // Loading state
  let loading = $state(true);
  let isResumeBeingParsed = $state(false);

  // Profile data with default values for missing properties
  let profile = $state({
    ...data.profile,
    // Add properties with default values if they don't exist
    published: false,
    publishedAt: null,
  });
  let profileData = $state<ProfileData>({});

  // Initialize profile data
  // Parse and migrate profile data
  console.log('Profile data:', data.profile);

  // Function to fetch profile data
  async function fetchProfileData() {
    try {
      const response = await fetch(`/api/profile/${data.profile.id}/data`);
      const profileDataResponse = await response.json();
      console.log('Profile data from API:', profileDataResponse);

      // Check if we have valid data
      if (profileDataResponse && profileDataResponse.data) {
        const parsedData = parseProfileData(profileDataResponse.data);
        profileData = migrateProfileData(parsedData);
      } else if (profileDataResponse && typeof profileDataResponse === 'object') {
        // If the response itself contains the data (not nested in a data property)
        // This handles the case where the API directly returns the profile data
        profileData = migrateProfileData(profileDataResponse);
      } else {
        // Initialize with empty data if no data is returned
        profileData = migrateProfileData({});
      }

      // Check for resume parsing status
      const typedProfileData = profileDataResponse as any;
      const resumeId =
        typedProfileData.resumeId ||
        (typedProfileData.resumeData && typedProfileData.resumeData.id) ||
        typedProfileData.resume?.resumeId;

      if (resumeId) {
        // Check the parsing status using the new API endpoint
        try {
          const statusResponse = await fetch(`/api/resume/${resumeId}/parsing-status`);
          if (statusResponse.ok) {
            const statusData = await statusResponse.json();

            // Update the parsing status
            isResumeBeingParsed = statusData.isParsing;

            // If a resume is being parsed, poll for updates
            if (statusData.isParsing) {
              setTimeout(checkParsingStatus, 5000); // Check again in 5 seconds
            }
          }
        } catch (statusError) {
          console.error('Error checking resume parsing status:', statusError);
        }
      } else if (profileDataResponse && 'isParsing' in profileDataResponse) {
        // Fallback to the old method if available
        isResumeBeingParsed = profileDataResponse.isParsing;

        // If a resume is being parsed, poll for updates
        if (profileDataResponse.isParsing) {
          setTimeout(checkParsingStatus, 5000); // Check again in 5 seconds
        }
      }

      // Check if we have parsed resume data
      if (profileDataResponse && profileDataResponse.parsedResumeData) {
        console.log('Parsed resume data available:', profileDataResponse.parsedResumeData);

        // Update the profile data with the parsed resume data
        updateProfileWithParsedResumeData(profileDataResponse.parsedResumeData);
      }

      // Update the complete profile with the new data
      completeProfile = createCompleteProfile();

      return profileDataResponse;
    } catch (error) {
      console.error('Error fetching profile data:', error);
      return null;
    }
  }

  // Function to update profile data with parsed resume data
  async function updateProfileWithParsedResumeData(parsedResumeData: any) {
    try {
      console.log('Updating profile with parsed resume data');

      // Extract data from the parsed resume
      const resumeData = parsedResumeData.legacy ? parsedResumeData.data : parsedResumeData;

      // Create updated profile data, only filling in fields that don't already have data
      const updatedProfileData = {
        ...profileData,

        // Personal info
        personalInfo: {
          ...profileData.personalInfo,
          fullName: profileData.personalInfo?.fullName || resumeData.profile?.name,
          email: profileData.personalInfo?.email || resumeData.profile?.email,
          phone: profileData.personalInfo?.phone || resumeData.profile?.phone,
          address: profileData.personalInfo?.address || resumeData.profile?.location,
          jobTitle: profileData.personalInfo?.jobTitle || resumeData.profile?.title,
        },

        // Only use parsed data for fields that don't exist in profileData
        workExperience:
          profileData.workExperience?.length > 0
            ? profileData.workExperience
            : resumeData.experience || resumeData.workExperiences || [],

        education:
          profileData.education?.length > 0
            ? profileData.education
            : resumeData.education || resumeData.educations || [],

        skills:
          profileData.skills?.length > 0
            ? profileData.skills
            : Array.isArray(resumeData.skills)
              ? resumeData.skills.map((skill: any) =>
                  typeof skill === 'string' ? skill : skill.name || skill.skill
                )
              : [],

        projects:
          profileData.projects?.length > 0 ? profileData.projects : resumeData.projects || [],

        languages:
          profileData.languages?.length > 0 ? profileData.languages : resumeData.languages || [],

        certifications:
          profileData.certifications?.length > 0
            ? profileData.certifications
            : resumeData.certifications || [],

        summary: profileData.summary || resumeData.profile?.summary,

        // Store the resume ID to associate it with this profile
        resumeId: resumeData.resumeId,

        // Flag to indicate that we've already applied the parsed resume data
        parsedResumeApplied: true,
      };

      // Only update the profile data if we haven't already applied parsed resume data
      // or if the profile data is mostly empty
      const typedProfileData = profileData as any;
      if (!typedProfileData.parsedResumeApplied || Object.keys(profileData).length < 5) {
        // Save the updated profile data to the API
        const response = await fetch(`/api/profile/${profile.id}/data`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedProfileData),
        });

        if (!response.ok) {
          throw new Error('Failed to save profile data with parsed resume data');
        }

        // Update local state
        profileData = updatedProfileData;
        console.log('Profile data updated with parsed resume data:', profileData);

        // Update the complete profile
        completeProfile = createCompleteProfile();

        // Show success message
        toast.success('Profile updated with resume data');
      } else {
        console.log('Parsed resume data already applied to this profile, skipping update');
      }
    } catch (error) {
      console.error('Error updating profile with parsed resume data:', error);
      toast.error('Failed to update profile with parsed resume data');
    }
  }

  // Function to check parsing status
  async function checkParsingStatus() {
    try {
      // Get the resume ID from the profile data
      // Use type assertion to access custom fields that might not be in the ProfileData type
      const typedProfileData = profileData as any;
      const resumeId =
        typedProfileData.resumeId ||
        (typedProfileData.resumeData && typedProfileData.resumeData.id) ||
        typedProfileData.resume?.resumeId;

      if (!resumeId) {
        console.log('No resume ID found in profile data, skipping parsing status check');
        isResumeBeingParsed = false;
        return;
      }

      // Check the parsing status using the new API endpoint
      const response = await fetch(`/api/resume/${resumeId}/parsing-status`);
      if (!response.ok) {
        throw new Error('Failed to check parsing status');
      }

      const statusData = await response.json();

      // Update the parsing status
      isResumeBeingParsed = statusData.isParsing;

      // If still parsing, check again in 5 seconds
      if (statusData.isParsing) {
        setTimeout(checkParsingStatus, 5000);
      } else {
        // If parsing is complete, refresh the profile data
        const updatedProfileData = await fetchProfileData();

        // If we have updated data, update the complete profile
        if (updatedProfileData) {
          completeProfile = createCompleteProfile();
        }
      }
    } catch (error) {
      console.error('Error checking parsing status:', error);
      // Reset parsing status on error
      isResumeBeingParsed = false;
    }
  }

  // Initialize data when component mounts
  onMount(async () => {
    try {
      await fetchProfileData();

      loading = false;
    } catch (error) {
      console.error('Error initializing profile data:', error);
      profileData = migrateProfileData({});
      loading = false;
    }
  });

  // Note: Loading state is now handled in the onMount function

  // Initialize complete profile data
  function createCompleteProfile() {
    // Use type assertion to access custom fields that might not be in the ProfileData type
    const typedProfileData = profileData as any;

    // Check for resumeData which might be nested differently
    const resumeData = typedProfileData.resumeData || {};

    // Get job search status from jobPreferences or default to 'actively_looking'
    const jobSearchStatus = typedProfileData.jobPreferences?.jobSearchStatus || 'actively_looking';

    return {
      header: {
        profileName: profile.name || '',
        fullName:
          typedProfileData.fullName ||
          typedProfileData.personalInfo?.fullName ||
          typedProfileData.header?.fullName ||
          '',
        jobTitle: typedProfileData.jobType || typedProfileData.personalInfo?.jobTitle || '',
        jobSearchStatus: jobSearchStatus,
      },
      visibility: {
        showToRecruiters: typedProfileData.visibility?.showToRecruiters || false,
        getDiscovered: typedProfileData.visibility?.getDiscovered || true,
        hideFromCurrentEmployer: typedProfileData.visibility?.hideFromCurrentEmployer || false,
      },
      personalInfo: {
        email: typedProfileData.email || typedProfileData.personalInfo?.email || '',
        phone: typedProfileData.phone || typedProfileData.personalInfo?.phone || '',
        address: typedProfileData.personalInfo?.address || '',
        city: typedProfileData.personalInfo?.city || '',
        state: typedProfileData.personalInfo?.state || '',
        zip: typedProfileData.personalInfo?.zip || '',
        country: typedProfileData.personalInfo?.country || 'USA',
      },
      resume:
        resumeData.id || typedProfileData.resumeId
          ? {
              resumeId: resumeData.id || typedProfileData.resumeId,
              fileName: resumeData.name || typedProfileData.resumeName || 'resume.pdf',
              uploadedAt: resumeData.updatedAt || typedProfileData.resumeUpdatedAt,
              isDefault: true,
            }
          : null,
      workExperiences: typedProfileData.workExperience || typedProfileData.workExperiences || [],
      educations: typedProfileData.education || typedProfileData.educations || [],
      projects: typedProfileData.projects || [],
      portfolioLinks: {
        linkedinUrl:
          typedProfileData.portfolioLinks?.linkedin ||
          (typedProfileData.personalInfo as any)?.linkedin ||
          '',
        githubUrl:
          typedProfileData.portfolioLinks?.github ||
          (typedProfileData.personalInfo as any)?.github ||
          '',
        portfolioUrl:
          typedProfileData.website || (typedProfileData.personalInfo as any)?.website || '',
        otherUrl: typedProfileData.portfolioLinks?.other || '',
      },
      skills: {
        skills: Array.isArray(typedProfileData.skills)
          ? typedProfileData.skills
          : typedProfileData.skillsData?.list || typedProfileData.skillsData?.technical || [],
      },
      languages: typedProfileData.languages
        ? typedProfileData.languages.map((lang: any) => ({
            ...lang,
            proficiency: lang.proficiency || 'intermediate',
          }))
        : [],
    } as CompleteProfileSchema;
  }

  // Create reactive state for complete profile
  let completeProfile = $state(createCompleteProfile());

  // Format date - used in the profile page
  function formatDate(dateString: string | Date): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  // Last updated date - using the formatDate function
  const lastUpdated = profile?.updatedAt ? formatDate(profile.updatedAt) : 'Recently';

  // Save profile data
  async function saveProfileData(updatedData: Partial<ProfileData>) {
    try {
      // Merge with existing data
      const mergedData = {
        ...profileData,
        ...updatedData,
      };

      console.log('Saving profile data:', mergedData);

      // Save to API using the data endpoint
      const response = await fetch(`/api/profile/${profile.id}/data`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mergedData),
      });

      if (!response.ok) {
        throw new Error('Failed to save profile data');
      }

      // Update local state
      profileData = mergedData;

      // Update the complete profile to reflect the changes
      completeProfile = createCompleteProfile();

      toast.success('Profile updated successfully');
      return true;
    } catch (error) {
      console.error('Error saving profile data:', error);
      toast.error('Failed to save profile data');
      return false;
    }
  }

  // Save profile header
  async function saveProfileHeader(data: any) {
    // Update the profile name in the database
    try {
      const response = await fetch(`/api/profile/${profile.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: data.profileName,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update profile name');
      }

      // Update local profile name
      profile.name = data.profileName;
    } catch (error) {
      console.error('Error updating profile name:', error);
      toast.error('Failed to update profile name');
      return false;
    }

    const updatedData: Partial<ProfileData> = {
      fullName: data.fullName,
      jobType: data.jobTitle,
      personalInfo: {
        ...profileData.personalInfo,
        fullName: data.fullName,
        jobTitle: data.jobTitle,
      },
      jobPreferences: {
        ...profileData.jobPreferences,
        jobSearchStatus: data.jobSearchStatus,
      },
    };

    completeProfile.header = data;
    return saveProfileData(updatedData);
  }

  // Save profile visibility
  async function saveProfileVisibility(data: any) {
    // Store visibility settings directly in the profile data JSON
    // This works because ProfileData is just a type - the actual data can contain any fields
    const updatedData = {
      visibility: data,
    };

    completeProfile.visibility = data;
    return saveProfileData(updatedData as any);
  }

  // Save resume
  async function saveResume(data: any) {
    // Validate that resumeId exists
    if (!data.resumeId) {
      console.error('Cannot save resume: resumeId is missing');
      toast.error('Cannot save resume: Resume ID is missing');
      return false;
    }

    console.log('[snapshot] Saving resume data:', data);
    console.log('Saving resume data:', data);

    // Store resume data directly in the profile data JSON
    const updatedData = {
      resumeId: data.resumeId, // Store at the top level for easier access
      resumeData: {
        id: data.resumeId,
        name: data.fileName,
        updatedAt: data.uploadedAt,
      },
      // Reset the parsedResumeApplied flag when a new resume is selected
      // This will allow the system to apply the parsed data from the new resume
      parsedResumeApplied: false,
    };

    // If parseIntoProfile is true, trigger resume parsing
    if (data.parseIntoProfile && data.resumeId) {
      try {
        console.log('Triggering resume parsing for resumeId:', data.resumeId);

        // Set parsing status immediately to ensure UI is updated
        isResumeBeingParsed = true;

        // Call the API to trigger resume parsing
        const parseResponse = await fetch(`/api/resume/${data.resumeId}/parse`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            profileId: profile.id,
          }),
        });

        if (parseResponse.ok) {
          // Keep parsing status and start polling
          toast.success('Resume parsing started');
          setTimeout(checkParsingStatus, 5000);
        } else {
          const errorData = await parseResponse.json();
          console.error('Resume parsing error:', errorData);
          toast.error(`Failed to start resume parsing: ${errorData.error || 'Unknown error'}`);
          // Reset parsing status on error
          isResumeBeingParsed = false;
        }
      } catch (error) {
        console.error('Error triggering resume parsing:', error);
        toast.error('Failed to start resume parsing');
        // Reset parsing status on error
        isResumeBeingParsed = false;
      }
    } else {
      // If not parsing, check if the resume is already parsed
      try {
        console.log('Checking if resume is already parsed:', data.resumeId);

        // Fetch the profile data which already includes parsed resume data
        const profileDataResponse = await fetchProfileData();

        if (profileDataResponse && profileDataResponse.data) {
          console.log('Profile data already includes parsed resume data');

          // The profile data is already updated with parsed resume data in the fetchProfileData function
          // No need to do anything else here
        }
      } catch (error) {
        console.error('Error checking if resume is already parsed:', error);
      }
    }

    // Update the complete profile
    completeProfile.resume = data;

    // Save the profile data
    try {
      const success = await saveProfileData(updatedData as any);

      // Update the complete profile again after saving
      if (success) {
        completeProfile = createCompleteProfile();
      }

      return success;
    } catch (error) {
      console.error('Error saving profile data:', error);
      toast.error('Failed to save profile data');
      // Reset parsing status on error
      if (data.parseIntoProfile) {
        isResumeBeingParsed = false;
      }
      return false;
    }
  }

  // Save work experiences
  async function saveWorkExperiences(data: any) {
    const updatedData: Partial<ProfileData> = {
      workExperience: data,
    };

    completeProfile.workExperiences = data;
    return saveProfileData(updatedData);
  }

  // Save educations
  async function saveEducations(data: any) {
    const updatedData: Partial<ProfileData> = {
      education: data,
    };

    completeProfile.educations = data;
    return saveProfileData(updatedData);
  }

  // Save projects
  async function saveProjects(data: any) {
    const updatedData: Partial<ProfileData> = {
      projects: data,
    };

    completeProfile.projects = data;
    return saveProfileData(updatedData);
  }

  // Save portfolio links
  async function savePortfolioLinks(data: any) {
    const updatedData = {
      website: data.portfolioUrl,
      personalInfo: {
        ...profileData.personalInfo,
        website: data.portfolioUrl,
      },
      // Store additional links directly in the profile data JSON
      portfolioLinks: {
        linkedin: data.linkedinUrl,
        github: data.githubUrl,
        other: data.otherUrl,
      },
    };

    completeProfile.portfolioLinks = data;
    return saveProfileData(updatedData as any);
  }

  // Save skills
  async function saveSkills(data: any) {
    const updatedData: Partial<ProfileData> = {
      skills: data.skills,
      skillsData: {
        ...profileData.skillsData,
        list: data.skills,
      },
    };

    completeProfile.skills = data;
    return saveProfileData(updatedData);
  }

  // Save languages
  async function saveLanguages(data: any) {
    const updatedData: Partial<ProfileData> = {
      languages: data,
    };

    completeProfile.languages = data;
    return saveProfileData(updatedData);
  }

  // Save job preferences
  async function saveJobPreferences(data: any) {
    console.log('Saving job preferences:', data);

    // Map the data to the correct format
    const updatedData: Partial<ProfileData> = {
      jobPreferences: {
        ...profileData.jobPreferences,
        valueInRole: data.valueInRole || [],
        interestedRoles: data.interestedRoles || [],
        preferredLocations: data.preferredLocations || [],
        remotePreference: data.remotePreference || 'hybrid',
        minimumSalary: data.salary || '', // Map salary to minimumSalary which is in the JobPreferences type
        desiredIndustries: data.industries || [],
        avoidIndustries: data.avoidIndustries || [],
        avoidSkills: data.avoidSkills || [],
        companySize: data.idealCompanySize || 'medium',
        jobSearchStatus: data.jobSearchStatus || 'actively_looking',
        // Store additional fields as custom properties
        // These will be stored in the JSON but not typed in the JobPreferences interface
      } as any, // Use type assertion to allow additional properties
    };

    // Add additional properties that aren't in the JobPreferences type
    if (data.jobTypes) {
      (updatedData.jobPreferences as any).jobTypes = data.jobTypes;
    }

    if (data.experienceLevels) {
      (updatedData.jobPreferences as any).experienceLevels = data.experienceLevels;
    }

    if (data.securityClearance !== undefined) {
      (updatedData.jobPreferences as any).securityClearance = data.securityClearance;
    }

    const success = await saveProfileData(updatedData);

    // Update the complete profile if save was successful
    if (success) {
      completeProfile = createCompleteProfile();
    }

    return success;
  }

  // Save employment information
  async function saveEmploymentInfo(data: any) {
    const updatedData: Partial<ProfileData> = {
      employmentInfo: {
        ...profileData.employmentInfo,
        ...data,
      },
    };

    return saveProfileData(updatedData);
  }

  // Save personal info
  async function savePersonalInfo(data: any) {
    console.log('Saving personal info:', data);

    const updatedData: Partial<ProfileData> = {
      email: data.email,
      phone: data.phone,
      personalInfo: {
        ...profileData.personalInfo,
        email: data.email,
        phone: data.phone,
        address: data.address,
        city: data.city,
        state: data.state,
        zip: data.zip,
        country: data.country || 'USA',
      },
    };

    const success = await saveProfileData(updatedData);

    // Update the complete profile if save was successful
    if (success) {
      completeProfile.personalInfo = data;
      // Also update the complete profile to reflect the changes
      completeProfile = createCompleteProfile();
    }

    return success;
  }
</script>

<SEO
  title={`${profile?.name || 'Profile'} - Hirli`}
  description="Edit your professional profile for job applications."
  keywords="profile, resume, job search, career profile, professional information"
  url={`https://hirli.com/dashboard/settings/profile/${profile?.id}`} />

<div class="border-border flex items-center justify-between border-b p-6">
  <div>
    <h1 class="text-xl font-semibold">{profile?.name || 'Profile'}</h1>
    <p class="text-muted-foreground text-sm">Last updated: {lastUpdated}</p>
  </div>
  <div>
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>
        <Button variant="ghost" size="icon" class="h-8 w-8 rounded-full">
          <MoreHorizontal class="h-4 w-4" />
          <span class="sr-only">Open menu</span>
        </Button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content align="end" class="w-48">
        <DropdownMenu.Label>Profile Actions</DropdownMenu.Label>
        <DropdownMenu.Separator />

        <button
          class="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
          onclick={async () => {
            const currentVisibility = completeProfile.visibility.showToRecruiters;
            const updatedVisibility = {
              ...completeProfile.visibility,
              showToRecruiters: !currentVisibility,
            };

            const success = await saveProfileVisibility(updatedVisibility);
            if (success) {
              toast.success(
                `Profile is now ${!currentVisibility ? 'visible' : 'hidden'} to recruiters`
              );
            }
          }}>
          {#if completeProfile.visibility.showToRecruiters}
            <EyeOff class="mr-2 h-4 w-4" />
            <span>Hide from Recruiters</span>
          {:else}
            <Eye class="mr-2 h-4 w-4" />
            <span>Show to Recruiters</span>
          {/if}
        </button>

        <DropdownMenu.Separator />

        <button
          class="text-destructive hover:bg-destructive hover:text-destructive-foreground focus:bg-destructive focus:text-destructive-foreground relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors"
          onclick={() => {
            if (
              confirm('Are you sure you want to delete this profile? This action cannot be undone.')
            ) {
              fetch(`/api/profile/${profile.id}`, {
                method: 'DELETE',
              })
                .then(async (res) => {
                  if (res.ok) {
                    toast.success('Profile deleted successfully');
                    goto('/dashboard/settings/profile');
                  } else {
                    const data = await res.json();
                    toast.error(data.error || 'Failed to delete profile');
                  }
                })
                .catch((err) => {
                  console.error('Error deleting profile:', err);
                  toast.error('Failed to delete profile');
                });
            }
          }}>
          <Trash2 class="mr-2 h-4 w-4" />
          <span>Delete Profile</span>
        </button>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  </div>
</div>

<div class="container mx-auto p-6">
  {#if loading}
    <div class="flex flex-col items-center justify-center py-12">
      <div class="border-primary h-12 w-12 animate-spin rounded-full border-4 border-t-transparent">
      </div>
      <p class="mt-4 text-lg">Loading profile...</p>
    </div>
  {:else}
    <!-- Main content with tabs -->
    <div class="grid grid-cols-1 gap-6">
      <!-- Profile Tabs -->
      <div class="mb-4 flex items-center justify-between">
        <Button
          variant="outline"
          size="sm"
          onclick={() => goto('/dashboard/settings/profile')}
          class="flex items-center gap-2">
          <ArrowLeft class="h-4 w-4" />
          Back to Profiles
        </Button>

        {#if isResumeBeingParsed}
          <div class="flex items-center rounded-md bg-amber-50 px-3 py-1 text-sm text-amber-600">
            <span class="mr-2 animate-pulse">●</span>
            Resume parsing in progress...
          </div>
        {/if}
      </div>

      <ProfileTabs
        {completeProfile}
        {profileData}
        {isResumeBeingParsed}
        onResumeParsingChange={(isParsing) => {
          isResumeBeingParsed = isParsing;
        }}
        saveHandlers={{
          saveProfileHeader,
          saveProfileVisibility,
          savePersonalInfo,
          saveResume,
          saveWorkExperiences,
          saveEducations,
          saveProjects,
          savePortfolioLinks,
          saveSkills,
          saveLanguages,
          saveJobPreferences,
          saveEmploymentInfo,
        }} />
    </div>
  {/if}
</div>
