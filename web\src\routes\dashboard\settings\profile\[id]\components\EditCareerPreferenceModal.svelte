<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { X, Save } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';

  // Props
  const { open, sectionType, sectionTitle, sectionData, options, onClose, onSave } = $props<{
    open: boolean;
    sectionType: 'roles' | 'locations' | 'remote' | 'industries';
    sectionTitle: string;
    sectionData: string[] | string;
    options?: { value: string; label: string }[];
    onClose: () => void;
    onSave: (data: string[] | string) => Promise<boolean>;
  }>();

  // Local state for editing
  let editedRoles = $state<string[]>([]);
  let editedLocations = $state<string[]>([]);
  let editedRemotePreference = $state('');
  let editedIndustries = $state<string[]>([]);

  // New item inputs
  let newRole = $state('');
  let newLocation = $state('');
  let newIndustry = $state('');

  // Initialize local state when modal opens
  $effect(() => {
    if (open) {
      if (sectionType === 'roles') {
        editedRoles = Array.isArray(sectionData) ? [...sectionData] : [];
      } else if (sectionType === 'locations') {
        editedLocations = Array.isArray(sectionData) ? [...sectionData] : [];
      } else if (sectionType === 'remote') {
        editedRemotePreference = typeof sectionData === 'string' ? sectionData : '';
      } else if (sectionType === 'industries') {
        editedIndustries = Array.isArray(sectionData) ? [...sectionData] : [];
      }

      // Reset new item inputs
      newRole = '';
      newLocation = '';
      newIndustry = '';
    }
  });

  // Add new role
  function addRole() {
    if (newRole && !editedRoles.includes(newRole)) {
      editedRoles = [...editedRoles, newRole];
      newRole = '';
    }
  }

  // Remove role
  function removeRole(role: string) {
    editedRoles = editedRoles.filter((r) => r !== role);
  }

  // Add new location
  function addLocation() {
    if (newLocation && !editedLocations.includes(newLocation)) {
      editedLocations = [...editedLocations, newLocation];
      newLocation = '';
    }
  }

  // Remove location
  function removeLocation(location: string) {
    editedLocations = editedLocations.filter((l) => l !== location);
  }

  // Add new industry
  function addIndustry() {
    if (newIndustry && !editedIndustries.includes(newIndustry)) {
      editedIndustries = [...editedIndustries, newIndustry];
      newIndustry = '';
    }
  }

  // Remove industry
  function removeIndustry(industry: string) {
    editedIndustries = editedIndustries.filter((i) => i !== industry);
  }

  // Handle save
  async function handleSave() {
    try {
      let dataToSave: string[] | string;

      if (sectionType === 'roles') {
        dataToSave = editedRoles;
      } else if (sectionType === 'locations') {
        dataToSave = editedLocations;
      } else if (sectionType === 'remote') {
        dataToSave = editedRemotePreference;
      } else if (sectionType === 'industries') {
        dataToSave = editedIndustries;
      } else {
        throw new Error('Invalid section type');
      }

      const success = await onSave(dataToSave);

      if (success) {
        toast.success(`${sectionTitle} updated successfully`);
        onClose();
      }
    } catch (error) {
      console.error(`Error saving ${sectionType}:`, error);
      toast.error(`Failed to save ${sectionTitle}`);
    }
  }
</script>

<Dialog.Root {open} onOpenChange={onClose}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="sm:max-w-[500px]">
      <Dialog.Header>
        <Dialog.Title>Edit {sectionTitle}</Dialog.Title>
        <Dialog.Description>
          Make changes to your {sectionTitle.toLowerCase()}.
        </Dialog.Description>
      </Dialog.Header>

      <div class="py-4">
        {#if sectionType === 'roles'}
          <div class="space-y-4">
            <div class="space-y-2">
              <Label>Desired Roles</Label>
              <div class="flex items-center space-x-2">
                <Input
                  bind:value={newRole}
                  placeholder="Enter a role"
                  onkeydown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addRole();
                    }
                  }} />
                <Button type="button" variant="outline" onclick={addRole}>Add</Button>
              </div>
              {#if editedRoles.length > 0}
                <div class="mt-2 flex flex-wrap gap-2">
                  {#each editedRoles as role}
                    <Badge variant="secondary" class="flex items-center gap-1">
                      {role}
                      <button
                        type="button"
                        class="hover:bg-primary/20 ml-1 rounded-full p-0.5"
                        onclick={() => removeRole(role)}>
                        <X class="h-3 w-3" />
                      </button>
                    </Badge>
                  {/each}
                </div>
              {:else}
                <p class="text-muted-foreground text-sm">No roles added yet</p>
              {/if}
            </div>
          </div>
        {:else if sectionType === 'locations'}
          <div class="space-y-4">
            <div class="space-y-2">
              <Label>Preferred Locations</Label>
              <div class="flex items-center space-x-2">
                <Input
                  bind:value={newLocation}
                  placeholder="Enter a location"
                  onkeydown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addLocation();
                    }
                  }} />
                <Button type="button" variant="outline" onclick={addLocation}>Add</Button>
              </div>
              {#if editedLocations.length > 0}
                <div class="mt-2 flex flex-wrap gap-2">
                  {#each editedLocations as location}
                    <Badge variant="secondary" class="flex items-center gap-1">
                      {location}
                      <button
                        type="button"
                        class="hover:bg-primary/20 ml-1 rounded-full p-0.5"
                        onclick={() => removeLocation(location)}>
                        <X class="h-3 w-3" />
                      </button>
                    </Badge>
                  {/each}
                </div>
              {:else}
                <p class="text-muted-foreground text-sm">No locations added yet</p>
              {/if}
            </div>
          </div>
        {:else if sectionType === 'remote'}
          <div class="space-y-4">
            <div class="space-y-2">
              <Label for="remotePreference">Remote Preference</Label>
              <Select.Root
                value={editedRemotePreference}
                onValueChange={(value) => (editedRemotePreference = value)}>
                <Select.Trigger id="remotePreference" class="w-full">
                  <Select.Value placeholder="Select remote preference" />
                </Select.Trigger>
                <Select.Content>
                  <Select.Group>
                    {#each options || [] as option}
                      <Select.Item value={option.value}>{option.label}</Select.Item>
                    {/each}
                  </Select.Group>
                </Select.Content>
              </Select.Root>
            </div>
          </div>
        {:else if sectionType === 'industries'}
          <div class="space-y-4">
            <div class="space-y-2">
              <Label>Desired Industries</Label>
              <Select.Root value={newIndustry} onValueChange={(value) => (newIndustry = value)}>
                <Select.Trigger class="w-full">
                  <Select.Value placeholder="Select an industry" />
                </Select.Trigger>
                <Select.Content>
                  <Select.Group>
                    {#each options || [] as option}
                      <Select.Item value={option.value}>{option.label}</Select.Item>
                    {/each}
                  </Select.Group>
                </Select.Content>
              </Select.Root>
              <Button type="button" variant="outline" class="mt-2" onclick={addIndustry}>
                Add Industry
              </Button>
              {#if editedIndustries.length > 0}
                <div class="mt-2 flex flex-wrap gap-2">
                  {#each editedIndustries as industry}
                    <Badge variant="secondary" class="flex items-center gap-1">
                      {industry}
                      <button
                        type="button"
                        class="hover:bg-primary/20 ml-1 rounded-full p-0.5"
                        onclick={() => removeIndustry(industry)}>
                        <X class="h-3 w-3" />
                      </button>
                    </Badge>
                  {/each}
                </div>
              {:else}
                <p class="text-muted-foreground text-sm">No industries added yet</p>
              {/if}
            </div>
          </div>
        {/if}
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={onClose}>Cancel</Button>
        <Button onclick={handleSave} class="ml-2">
          <Save class="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
