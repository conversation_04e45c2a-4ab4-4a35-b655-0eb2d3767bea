<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Accordion from '$lib/components/ui/accordion/index.js';
  import * as AlertDialog from '$lib/components/ui/alert-dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { Trash, Edit, Save, X, RefreshCw, Plus, Loader2 } from 'lucide-svelte';
  import { FeatureCategory, LimitType } from '$lib/models/features/features';
  import { toast } from 'svelte-sonner';
  import { onMount } from 'svelte';

  // State for features from database
  let features = [];
  let loading = true;
  let error = null;
  let syncing = false;

  // State for delete confirmation dialog
  let deleteDialogOpen = false;
  let featureToDelete = null;
  let isDeleting = false;

  // State for editing a feature
  let editingFeature: string | null = null;
  let editFeatureData: FeatureType = {
    id: '',
    name: '',
    description: '',
    category: FeatureCategory.Core,
    icon: '',
    beta: false,
    limits: [],
  };

  // State for adding a new limit
  let newLimit = {
    id: '',
    name: '',
    description: '',
    defaultValue: 10,
    type: LimitType.Monthly,
    unit: '',
    resetDay: 1,
  };

  // Default expanded accordion items
  let expandedCategories = [];

  // Load features from the database
  async function loadFeatures() {
    try {
      loading = true;
      error = null;

      const response = await fetch('/api/admin/features', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to load features: ${response.status}`);
      }

      const result = await response.json();
      console.log('Loaded features:', result);

      // Check if the result has a features property, otherwise use the result itself
      if (result && result.features) {
        features = result.features;
      } else if (Array.isArray(result)) {
        features = result;
      } else {
        features = [];
      }

      // Set the first category as expanded when features are loaded
      if (Object.keys(featuresByCategory).length > 0 && expandedCategories.length === 0) {
        expandedCategories = [Object.keys(featuresByCategory)[0]];
      }
    } catch (err) {
      console.error('Error loading features:', err);
      error = err.message;
    } finally {
      loading = false;
    }
  }

  // Open delete confirmation dialog
  function openDeleteDialog(featureId: string) {
    featureToDelete = featureId;
    deleteDialogOpen = true;
  }

  // Remove a feature from the database
  async function removeFeature() {
    if (!featureToDelete) return;

    try {
      isDeleting = true;

      const response = await fetch('/api/admin/features', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'remove_feature',
          featureId: featureToDelete,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `Failed to remove feature: ${response.status}`);
      }

      // Parse response
      await response.json();

      toast.success('Feature removed', {
        description: `Feature ${featureToDelete} removed successfully`,
        duration: 3000,
      });

      // Close dialog and reset state
      deleteDialogOpen = false;
      featureToDelete = null;

      // Reload features
      await loadFeatures();
    } catch (err) {
      console.error('Error removing feature:', err);
      toast.error('Failed to remove feature', {
        description: err.message,
        duration: 5000,
      });
    } finally {
      isDeleting = false;
    }
  }

  // Start editing a feature
  function startEditFeature(feature: FeatureType) {
    editingFeature = feature.id;
    editFeatureData = { ...feature };
  }

  // Cancel editing a feature
  function cancelEditFeature() {
    editingFeature = null;
    editFeatureData = {
      id: '',
      name: '',
      description: '',
      category: FeatureCategory.Core,
      icon: '',
      beta: false,
      limits: [],
    };
  }

  // Add a new limit to the feature being edited
  function addLimitToFeature() {
    if (!newLimit.id || !newLimit.name) {
      toast.error('Limit ID and name are required');
      return;
    }

    // Add the new limit to the feature
    editFeatureData.limits = [...editFeatureData.limits, { ...newLimit }];

    // Reset the new limit form
    newLimit = {
      id: '',
      name: '',
      description: '',
      defaultValue: 10,
      type: LimitType.Monthly,
      unit: '',
      resetDay: 1,
    };
  }

  // Remove a limit from the feature being edited
  function removeLimitFromFeature(limitId: string) {
    editFeatureData.limits = editFeatureData.limits.filter((limit) => limit.id !== limitId);
  }

  // Save edited feature
  async function saveEditFeature() {
    try {
      if (!editFeatureData.id || !editFeatureData.name) {
        toast.error('Feature ID and name are required');
        return;
      }

      const response = await fetch('/api/admin/features', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'update_feature',
          feature: editFeatureData,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `Failed to update feature: ${response.status}`);
      }

      // Parse response
      await response.json();

      toast.success('Feature updated', {
        description: `Feature ${editFeatureData.name} updated successfully`,
        duration: 3000,
      });

      // Reset editing state
      editingFeature = null;
      editFeatureData = {
        id: '',
        name: '',
        description: '',
        category: FeatureCategory.Core,
        icon: '',
        beta: false,
        limits: [],
      };

      // Reload features
      await loadFeatures();
    } catch (err) {
      console.error('Error updating feature:', err);
      toast.error('Failed to update feature', {
        description: err.message,
        duration: 5000,
      });
    }
  }

  // Define feature type
  interface FeatureType {
    id: string;
    name: string;
    description: string;
    category: FeatureCategory;
    icon?: string;
    beta: boolean;
    limits: any[];
  }

  // Group features by category
  $: featuresByCategory = Array.isArray(features)
    ? features.reduce(
        (acc: Record<string, FeatureType[]>, feature: any) => {
          // Ensure feature has a category
          const category = feature.category || 'uncategorized';

          if (!acc[category]) {
            acc[category] = [];
          }

          // Ensure feature has a limits array
          if (!feature.limits) {
            feature.limits = [];
          }

          acc[category].push(feature as FeatureType);
          return acc;
        },
        {} as Record<string, FeatureType[]>
      )
    : ({} as Record<string, FeatureType[]>);

  // Ensure each category has an array of features
  $: Object.keys(featuresByCategory).forEach((category) => {
    if (!Array.isArray(featuresByCategory[category])) {
      featuresByCategory[category] = [];
    }
  });

  // Sync all features with all plans
  async function syncAllFeaturesWithPlans() {
    try {
      syncing = true;

      const response = await fetch('/api/admin/features/sync-all', {
        method: 'POST',
        credentials: 'include',
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `Failed to sync features: ${response.status}`);
      }

      const result = await response.json();

      toast.success('Features synced with plans', {
        description: `Successfully synchronized ${result.count} feature-plan relationships`,
        duration: 3000,
      });

      // Reload features to show any changes
      await loadFeatures();
    } catch (err) {
      console.error('Error syncing features with plans:', err);
      toast.error('Failed to sync features with plans', {
        description: err.message,
        duration: 5000,
      });
    } finally {
      syncing = false;
    }
  }

  // Listen for feature added event
  function handleFeatureAdded() {
    loadFeatures();
  }

  // Load features on component mount
  onMount(() => {
    loadFeatures();
    window.addEventListener('featureAdded', handleFeatureAdded);
    return () => {
      window.removeEventListener('featureAdded', handleFeatureAdded);
    };
  });
</script>

{#if loading}
  <div class="flex h-64 items-center justify-center">
    <div class="text-center">
      <div
        class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]">
      </div>
      <p class="mt-4 text-lg">Loading features...</p>
    </div>
  </div>
{:else if error}
  <div class="mb-4 rounded-lg bg-red-100 p-4 text-sm text-red-700">
    <p>Error loading features: {error}</p>
  </div>
{:else}
  <Accordion.Root
    class="border-border w-full divide-x border"
    type="multiple"
    value={expandedCategories}>
    {#each Object.entries(featuresByCategory) as [category, categoryFeatures]}
      <Accordion.Item value={category}>
        <Accordion.Trigger class="px-4 text-base"
          >{category.charAt(0).toUpperCase() + category.slice(1)} Features</Accordion.Trigger>
        <Accordion.Content class="border-border border-t">
          {#each categoryFeatures as feature}
            {@const typedFeature = feature as FeatureType}
            <div
              class="bover:border-primary/30 hover:bg-primary/5 rounded-md p-4 transition-colors">
              {#if editingFeature === typedFeature.id}
                <!-- Edit Feature Form -->
                <div class="grid grid-cols-2 gap-4">
                  <div class="space-y-2">
                    <Label for="edit-feature-id">Feature ID</Label>
                    <input
                      id="edit-feature-id"
                      value={editFeatureData.id}
                      disabled
                      class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
                  </div>
                  <div class="space-y-2">
                    <Label for="edit-feature-name">Feature Name</Label>
                    <input
                      id="edit-feature-name"
                      bind:value={editFeatureData.name}
                      class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
                  </div>
                  <div class="col-span-2 space-y-2">
                    <Label for="edit-feature-description">Description</Label>
                    <textarea
                      id="edit-feature-description"
                      bind:value={editFeatureData.description}
                      class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[80px] w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"
                    ></textarea>
                  </div>
                  <div class="space-y-2">
                    <Label for="edit-feature-category">Category</Label>
                    <select
                      id="edit-feature-category"
                      bind:value={editFeatureData.category}
                      class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50">
                      {#each Object.values(FeatureCategory) as category}
                        <option value={category}>{category}</option>
                      {/each}
                    </select>
                  </div>
                  <div class="space-y-2">
                    <Label for="edit-feature-icon">Icon (optional)</Label>
                    <input
                      id="edit-feature-icon"
                      bind:value={editFeatureData.icon}
                      class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
                  </div>
                  <div class="col-span-2 flex items-center space-y-2">
                    <input
                      type="checkbox"
                      id="edit-feature-beta"
                      bind:checked={editFeatureData.beta}
                      class="mr-2" />
                    <Label for="edit-feature-beta">Beta Feature</Label>
                  </div>

                  <!-- Feature Limits Section -->
                  <div class="col-span-2 mt-4 border-t pt-4">
                    <h4 class="mb-2 text-base font-semibold">Feature Limits</h4>

                    <!-- Existing Limits -->
                    {#if editFeatureData.limits && editFeatureData.limits.length > 0}
                      <div class="mb-4 space-y-2">
                        {#each editFeatureData.limits as limit}
                          <div class="flex items-center justify-between rounded-md border p-2">
                            <div>
                              <div class="font-medium">{limit.name}</div>
                              <div class="text-muted-foreground text-xs">
                                ID: {limit.id} | Default: {limit.defaultValue}
                                {limit.unit || ''}
                              </div>
                              <div class="text-muted-foreground text-xs">
                                {limit.description}
                              </div>
                            </div>
                            <button
                              class="text-destructive hover:text-destructive/80 hover:bg-destructive/10 rounded-full p-1"
                              onclick={() => removeLimitFromFeature(limit.id)}>
                              <Trash class="h-4 w-4" />
                            </button>
                          </div>
                        {/each}
                      </div>
                    {:else}
                      <div
                        class="text-muted-foreground mb-4 rounded-md border border-dashed p-4 text-center">
                        No limits defined for this feature.
                      </div>
                    {/if}

                    <!-- Add New Limit Form -->
                    <div class="rounded-md border p-3">
                      <h5 class="mb-2 text-sm font-semibold">Add New Limit</h5>
                      <div class="grid grid-cols-2 gap-2">
                        <div>
                          <Label for="new-limit-id" class="text-xs">Limit ID</Label>
                          <input
                            id="new-limit-id"
                            bind:value={newLimit.id}
                            placeholder="e.g. monthly_usage"
                            class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
                        </div>
                        <div>
                          <Label for="new-limit-name" class="text-xs">Name</Label>
                          <input
                            id="new-limit-name"
                            bind:value={newLimit.name}
                            placeholder="e.g. Monthly Usage"
                            class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
                        </div>
                        <div class="col-span-2">
                          <Label for="new-limit-description" class="text-xs">Description</Label>
                          <input
                            id="new-limit-description"
                            bind:value={newLimit.description}
                            placeholder="e.g. Maximum usage per month"
                            class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
                        </div>
                        <div>
                          <Label for="new-limit-default" class="text-xs">Default Value</Label>
                          <input
                            id="new-limit-default"
                            type="number"
                            bind:value={newLimit.defaultValue}
                            class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
                        </div>
                        <div>
                          <Label for="new-limit-unit" class="text-xs">Unit (optional)</Label>
                          <input
                            id="new-limit-unit"
                            bind:value={newLimit.unit}
                            placeholder="e.g. uses, items, GB"
                            class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
                        </div>
                        <div>
                          <Label for="new-limit-type" class="text-xs">Limit Type</Label>
                          <select
                            id="new-limit-type"
                            bind:value={newLimit.type}
                            class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50">
                            <option value={LimitType.Monthly}>Monthly</option>
                            <option value={LimitType.Total}>Total</option>
                            <option value={LimitType.Concurrent}>Concurrent</option>
                            <option value={LimitType.Unlimited}>Unlimited</option>
                          </select>
                        </div>
                        <div>
                          <Label for="new-limit-reset" class="text-xs"
                            >Reset Day (Monthly only)</Label>
                          <input
                            id="new-limit-reset"
                            type="number"
                            min="1"
                            max="31"
                            bind:value={newLimit.resetDay}
                            disabled={newLimit.type !== LimitType.Monthly}
                            class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
                        </div>
                        <div class="col-span-2 mt-2">
                          <button
                            class="border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-8 items-center justify-center rounded-md border px-3 py-1 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"
                            onclick={() => addLimitToFeature()}>
                            <Plus class="mr-1.5 h-4 w-4" />
                            Add Limit
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-span-2 mt-4 flex justify-end space-x-2">
                    <button
                      class="border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-9 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"
                      onclick={() => cancelEditFeature()}>
                      <X class="mr-2 h-4 w-4" />
                      Cancel
                    </button>
                    <button
                      class="border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-9 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"
                      onclick={() => saveEditFeature()}>
                      <Save class="mr-2 h-4 w-4" />
                      Save Changes
                    </button>
                  </div>
                </div>
              {:else}
                <!-- Feature Display -->
                <div class="flex items-center justify-between">
                  <div>
                    <h4 class="text-base font-semibold">{typedFeature.name}</h4>
                    <p class="text-muted-foreground text-sm">
                      ID: {typedFeature.id}
                    </p>
                    <p class="text-muted-foreground text-sm">
                      {typedFeature.description || 'No description'}
                    </p>

                    <!-- Display feature limits -->
                    {#if typedFeature.limits && typedFeature.limits.length > 0}
                      <div class="mt-2">
                        <p class="text-xs font-semibold">Limits:</p>
                        <div class="mt-1 flex flex-wrap gap-1.5">
                          {#each typedFeature.limits as limit}
                            <Badge variant="outline" class="bg-primary/5 border-primary/20 text-xs">
                              {limit.name}: {limit.defaultValue}
                              {limit.unit || ''}
                            </Badge>
                          {/each}
                        </div>
                      </div>
                    {/if}
                  </div>
                  <div class="flex items-center gap-2">
                    {#if typedFeature.beta}
                      <Badge variant="outline">Beta</Badge>
                    {/if}
                    <button
                      class="border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-8 items-center justify-center rounded-md border px-3 py-1 text-xs font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"
                      onclick={() => startEditFeature(typedFeature)}>
                      <Edit class="mr-1 h-4 w-4" /> Edit
                    </button>
                    <button
                      class="border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-8 items-center justify-center rounded-md border px-3 py-1 text-xs font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"
                      onclick={() => openDeleteDialog(typedFeature.id)}>
                      <Trash class="mr-1 h-4 w-4" /> Delete
                    </button>
                  </div>
                </div>
              {/if}
            </div>
          {/each}
        </Accordion.Content>
      </Accordion.Item>
    {/each}
  </Accordion.Root>
{/if}

<!-- Delete Confirmation Dialog -->
<AlertDialog.Root bind:open={deleteDialogOpen}>
  <AlertDialog.Content>
    <AlertDialog.Header>
      <AlertDialog.Title>Delete Feature</AlertDialog.Title>
      <AlertDialog.Description>
        Are you sure you want to remove feature "{featureToDelete}"? This will also remove it from
        all plans. This action cannot be undone.
      </AlertDialog.Description>
    </AlertDialog.Header>
    <AlertDialog.Footer>
      <AlertDialog.Cancel onclick={() => (deleteDialogOpen = false)}>Cancel</AlertDialog.Cancel>
      <AlertDialog.Action
        onclick={removeFeature}
        disabled={isDeleting}
        class={isDeleting
          ? 'cursor-not-allowed opacity-70'
          : 'bg-destructive text-destructive-foreground hover:bg-destructive/90'}>
        {#if isDeleting}
          <Loader2 class="mr-2 h-4 w-4 animate-spin" />
          Deleting...
        {:else}
          Delete
        {/if}
      </AlertDialog.Action>
    </AlertDialog.Footer>
  </AlertDialog.Content>
</AlertDialog.Root>
