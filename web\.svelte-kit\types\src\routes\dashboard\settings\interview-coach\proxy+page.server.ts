// @ts-nocheck
import type { PageServerLoad } from './$types';
import { prisma } from '$lib/server/prisma';
import { error } from '@sveltejs/kit';

export const load = async ({ locals }: Parameters<PageServerLoad>[0]) => {
  // Check if user is authenticated
  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  try {
    // Check if user has access to the ai_interview_coach feature
    const user = await prisma.user.findUnique({
      where: { id: locals.user.id },
      include: {
        subscriptions: {
          where: { status: 'active' },
          include: {
            plan: {
              include: {
                features: {
                  where: { featureId: 'ai_interview_coach' },
                  include: {
                    limits: true,
                  },
                },
              },
            },
          },
        },
        featureUsage: {
          where: {
            featureId: 'ai_interview_coach',
            limitId: 'ai_interview_sessions_monthly',
          },
        },
      },
    });

    // Default values if user is not found
    if (!user) {
      return {
        hasAccess: process.env.NODE_ENV !== 'production',
        usageLimit: null,
        currentUsage: 0,
        remainingSessions: null,
      };
    }

    const hasAccess =
      user.subscriptions?.some((sub) =>
        sub.plan?.features?.some((feature) => feature?.featureId === 'ai_interview_coach')
      ) || false;

    if (!hasAccess && process.env.NODE_ENV === 'production') {
      throw error(403, 'Feature not available in your plan');
    }

    // Get usage limits
    const usageLimit = user.subscriptions
      ?.flatMap((sub) => sub.plan?.features ?? [])
      .find((feature) => feature?.featureId === 'ai_interview_coach')
      ?.limits?.find((limit) => limit?.limitId === 'ai_interview_sessions_monthly')?.value;

    const currentUsage =
      user.featureUsage?.find(
        (usage) =>
          usage?.featureId === 'ai_interview_coach' &&
          usage?.limitId === 'ai_interview_sessions_monthly'
      )?.used ?? 0;

    // Return data
    return {
      hasAccess,
      usageLimit: usageLimit ? parseInt(usageLimit) : null,
      currentUsage,
      remainingSessions: usageLimit ? Math.max(0, parseInt(usageLimit) - currentUsage) : null,
    };
  } catch (e) {
    console.error('Error loading interview coach data:', e);
    throw error(500, 'Failed to load data');
  }
};
