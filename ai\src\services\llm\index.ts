import { logger } from "../../utils/logger.js";
import { config } from "../../config.js";
import * as ollamaService from "./ollama.js";

export interface CompletionOptions {
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

export interface CompletionResult {
  text: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export async function initLLM() {
  logger.info(`🧠 Initializing LLM with provider: ${config.llm.provider}`);

  switch (config.llm.provider) {
    case "ollama":
      await ollamaService.init();
      break;
    default:
      throw new Error(`Unsupported LLM provider: ${config.llm.provider}`);
  }
}

export async function generateCompletion(
  prompt: string,
  options: CompletionOptions = {}
): Promise<CompletionResult> {
  logger.debug(`Generating completion with provider: ${config.llm.provider}`);

  switch (config.llm.provider) {
    case "ollama":
      return ollamaService.generateCompletion(prompt, options);
    default:
      throw new Error(`Unsupported LLM provider: ${config.llm.provider}`);
  }
}
