// @ts-nocheck
import type { PageServerLoad } from './$types';
import { prisma } from '$lib/server/prisma';
import { determineDocumentSource } from '$lib/utils/documentSource';
import { error } from '@sveltejs/kit';

export const load = async ({ params, locals }: Parameters<PageServerLoad>[0]) => {
  const { id } = params;

  // Check if user is authenticated
  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  try {
    // Fetch the document
    const document = await prisma.document.findUnique({
      where: { id },
      include: {
        resume: true,
      },
    });

    // Check if document exists and belongs to the user
    if (!document || document.userId !== locals.user.id) {
      throw error(404, 'Document not found');
    }

    // Check if document is a resume
    if (document.type !== 'resume') {
      throw error(400, 'ATS analysis is only available for resumes');
    }

    // Determine the source based on document properties
    const source = determineDocumentSource(document);

    // Return the document with the source information
    return {
      id,
      document: {
        ...document,
        source,
      },
    };
  } catch (e) {
    console.error('Error loading document for ATS analysis:', e);
    throw error(500, 'Failed to load document');
  }
};
