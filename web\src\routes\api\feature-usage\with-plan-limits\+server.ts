// src/routes/api/feature-usage/with-plan-limits/+server.ts
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { verifySessionToken } from '$lib/server/auth';
import { getFeatureUsage } from '$lib/server/feature-usage';

/**
 * Get feature usage for the current user
 * This provides a comprehensive view of all features and their usage
 */
export const GET: RequestHandler = async ({ cookies }) => {
  const token = cookies.get('auth_token');

  if (!token) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userData = verifySessionToken(token);

  if (!userData?.id) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Get user feature usage without plan limits
    const usageData = await getFeatureUsage(userData.id);

    // Group usage by feature for easier consumption by the client
    const groupedUsage = usageData.reduce((acc, usage) => {
      if (!acc[usage.featureId]) {
        acc[usage.featureId] = {
          id: usage.featureId,
          name: usage.featureName,
          usage: [],
        };
      }

      acc[usage.featureId].usage.push({
        limitId: usage.limitId,
        limitName: usage.limitName,
        used: usage.used,
        limit: usage.limit,
        remaining: usage.remaining,
        percentUsed: usage.percentUsed,
        period: usage.period,
        updatedAt: usage.updatedAt,
      });

      return acc;
    }, {});

    // Convert to array
    const features = Object.values(groupedUsage);

    return json({ features });
  } catch (error) {
    console.error('Error in feature usage API:', error);
    return json(
      {
        features: [],
        error: error.message || 'An error occurred while fetching feature usage data',
      },
      { status: 500 }
    );
  }
};
