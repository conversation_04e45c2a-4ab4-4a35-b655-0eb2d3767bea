<script lang="ts">
  import { invalidateAll } from '$app/navigation';
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { toast } from 'svelte-sonner';
  import {
    Plus,
    Edit,
    Trash2,
    AlertTriangle,
    Clock,
    CheckCircle,
    XCircle,
    History,
  } from 'lucide-svelte';
  import MaintenanceHistoryDialog from '$components/system-status/MaintenanceHistoryDialog.svelte';
  import MaintenanceCreateDialog from '$components/system-status/MaintenanceCreateDialog.svelte';
  import MaintenanceEditDialog from '$components/system-status/MaintenanceEditDialog.svelte';
  import MaintenanceDeleteDialog from '$components/system-status/MaintenanceDeleteDialog.svelte';
  import MaintenanceCommentDialog from '$components/system-status/MaintenanceCommentDialog.svelte';
  import MaintenanceUpdateDialog from '$components/system-status/MaintenanceUpdateDialog.svelte';
  import type { MaintenanceEvent } from '$lib/types';
  import SEO from '$components/shared/SEO.svelte';
  import { superForm } from 'sveltekit-superforms/client';

  // Import page data
  export let data: any;

  // Define active tab
  let activeTab = 'upcoming';

  // Define service options array
  const serviceOptions = [
    { value: 'Matches', label: 'Matches (Job matching and recommendations)' },
    { value: 'Jobs', label: 'Jobs (Job search and listings)' },
    { value: 'Tracker', label: 'Tracker (Application tracking)' },
    { value: 'Documents', label: 'Documents (Resume and document management)' },
    { value: 'Automation', label: 'Automation (Automated job application tools)' },
    { value: 'System', label: 'System (Core system services)' },
    { value: 'Website', label: 'Website (Website and user interface)' },
  ];

  // Setup SuperForms
  const {
    form: createForm,
    reset: resetCreateForm,
    errors: createErrors,
  } = superForm(data.createForm, {
    resetForm: true,
    validationMethod: 'submit-only',
    onResult({ result }) {
      if (result.type === 'success') {
        dialogState.isCreateDialogOpen = false;
        toast.success('Maintenance event created successfully');
        invalidateAll();
      } else if (result.type === 'failure') {
        toast.error(
          typeof result.data === 'string' ? result.data : 'Failed to create maintenance event'
        );
      }
    },
    dataType: 'json',
  });

  const {
    form: editForm,
    reset: resetEditForm,
    errors: editErrors,
  } = superForm(data.editForm, {
    resetForm: true,
    validationMethod: 'submit-only',
    onResult: ({ result }) => {
      if (result.type === 'success') {
        dialogState.isEditDialogOpen = false;
        toast.success('Maintenance event updated successfully');
        invalidateAll();
      } else if (result.type === 'failure') {
        toast.error(
          typeof result.data === 'string' ? result.data : 'Failed to update maintenance event'
        );
      }
    },
    dataType: 'json',
  });

  const { form: deleteForm } = superForm(data.deleteForm, {
    resetForm: true,
    onResult: ({ result }) => {
      if (result.type === 'success') {
        dialogState.isDeleteDialogOpen = false;
        toast.success('Maintenance event deleted successfully');
        invalidateAll();
      } else if (result.type === 'failure') {
        toast.error(
          typeof result.data === 'string' ? result.data : 'Failed to delete maintenance event'
        );
      }
    },
  });

  // State objects
  const dialogState = {
    isCreateDialogOpen: false,
    isEditDialogOpen: false,
    isDeleteDialogOpen: false,
    isHistoryDialogOpen: false,
    isAddUpdateDialogOpen: false,
    isCommentDialogOpen: false,
  };

  // Comment state
  const commentState = {
    commentAction: 'start' as 'start' | 'complete',
    commentEvent: null as MaintenanceEvent | null,
    commentText: '',
    sendNotification: false,
  };

  // Event state
  const eventState = {
    selectedEvent: null as MaintenanceEvent | null,
    selectedEventId: '',
    eventHistory: [] as any[],
  };

  // Initialize forms with default values
  $createForm = {
    title: '',
    description: '',
    startTime: '',
    endTime: '',
    status: 'scheduled',
    severity: 'maintenance',
    affectedServices: [serviceOptions[0].value],
    sendNotification: false,
  };

  $editForm = {
    id: '',
    title: '',
    description: '',
    startTime: '',
    endTime: '',
    status: 'scheduled',
    severity: 'maintenance',
    affectedServices: [serviceOptions[0].value],
    sendNotification: false,
    comment: '',
    commentStatus: 'investigating',
  };

  // Format date for display
  function formatDate(dateStr: string): string {
    const date = new Date(dateStr);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  // Get status badge variant
  function getStatusBadgeVariant(
    status: string
  ): 'default' | 'outline' | 'destructive' | 'secondary' | 'success' | 'warning' {
    switch (status) {
      case 'scheduled':
        return 'secondary';
      case 'in-progress':
        return 'warning';
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  }

  // Get severity badge variant
  function getSeverityBadgeVariant(
    severity: string
  ): 'default' | 'outline' | 'destructive' | 'secondary' | 'success' | 'warning' {
    switch (severity) {
      case 'info':
        return 'secondary';
      case 'warning':
        return 'warning';
      case 'critical':
        return 'destructive';
      default:
        return 'outline';
    }
  }

  // Get status icon
  function getStatusIcon(status: string) {
    switch (status) {
      case 'scheduled':
        return Clock;
      case 'in-progress':
        return AlertTriangle;
      case 'completed':
        return CheckCircle;
      case 'cancelled':
        return XCircle;
      default:
        return AlertTriangle;
    }
  }

  // Open edit dialog
  function openEditDialog(event: MaintenanceEvent) {
    eventState.selectedEvent = event;
    eventState.selectedEventId = event.id;

    // Set up the edit form with the event data
    $editForm = {
      id: event.id,
      title: event.title,
      description: event.description,
      startTime: new Date(event.startTime).toISOString().slice(0, 16),
      endTime: new Date(event.endTime).toISOString().slice(0, 16),
      status: event.status,
      severity: event.severity,
      // Ensure we're passing the full array of affected services
      affectedServices:
        Array.isArray(event.affectedServices) && event.affectedServices.length > 0
          ? event.affectedServices
          : [serviceOptions[0].value], // Default to first service if none provided
      sendNotification: false,
      comment: '',
      commentStatus: 'investigating', // Default comment status
    };

    // Also fetch existing comments/history for this event
    fetchEventHistory(event.id);
    dialogState.isEditDialogOpen = true;
  }

  // Open delete dialog
  function openDeleteDialog(event: MaintenanceEvent) {
    eventState.selectedEvent = event;
    $deleteForm = { id: event.id };
    dialogState.isDeleteDialogOpen = true;
  }

  // Open history dialog
  function openHistoryDialog(event: MaintenanceEvent) {
    eventState.selectedEventId = event.id;
    dialogState.isHistoryDialogOpen = true;
    fetchEventHistory(event.id);
  }

  // Fetch event history
  async function fetchEventHistory(eventId: string) {
    try {
      const response = await fetch(`/api/maintenance/${eventId}/history`);

      if (!response.ok) {
        throw new Error(`Failed to fetch history: ${response.status}`);
      }

      const data = await response.json();
      eventState.eventHistory = data;
    } catch (error) {
      console.error('Error fetching event history:', error);
      toast.error('Failed to load event history');
    }
  }

  // Open comment dialog
  function openCommentDialog(action: 'start' | 'complete', event: MaintenanceEvent) {
    commentState.commentAction = action;
    commentState.commentEvent = event;
    commentState.commentText = '';
    dialogState.isCommentDialogOpen = true;
  }

  // Start maintenance
  function startMaintenance(event: MaintenanceEvent) {
    openCommentDialog('start', event);
  }

  // Submit start maintenance with comment
  async function submitStartMaintenance() {
    try {
      if (!commentState.commentEvent) return;

      // Include all required fields from the original event
      const response = await fetch('/api/maintenance', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'same-origin',
        body: JSON.stringify({
          id: commentState.commentEvent.id,
          title: commentState.commentEvent.title,
          description: commentState.commentEvent.description,
          startTime: commentState.commentEvent.startTime,
          endTime: commentState.commentEvent.endTime,
          status: 'in-progress',
          severity: commentState.commentEvent.severity,
          affectedServices: commentState.commentEvent.affectedServices,
          sendNotification: commentState.sendNotification,
          comment: commentState.commentText || undefined,
          commentStatus: 'in-progress', // Use the swimlane status
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success('Maintenance started successfully');
        dialogState.isCommentDialogOpen = false;
        invalidateAll();
      } else {
        toast.error(result.error || 'Failed to start maintenance');
      }
    } catch (error) {
      toast.error('An error occurred while starting maintenance');
      console.error('Error starting maintenance:', error);
    }
  }

  // Complete maintenance
  function completeMaintenance(event: MaintenanceEvent) {
    openCommentDialog('complete', event);
  }

  // Submit complete maintenance with comment
  async function submitCompleteMaintenance() {
    try {
      if (!commentState.commentEvent) return;

      // Include all required fields from the original event
      const response = await fetch('/api/maintenance', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'same-origin',
        body: JSON.stringify({
          id: commentState.commentEvent.id,
          title: commentState.commentEvent.title,
          description: commentState.commentEvent.description,
          startTime: commentState.commentEvent.startTime,
          endTime: commentState.commentEvent.endTime,
          status: 'completed',
          severity: commentState.commentEvent.severity,
          affectedServices: commentState.commentEvent.affectedServices,
          sendNotification: commentState.sendNotification,
          comment: commentState.commentText || undefined,
          commentStatus: 'resolved', // Use the swimlane status
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success('Maintenance completed successfully');
        dialogState.isCommentDialogOpen = false;
        invalidateAll();
      } else {
        toast.error(result.error || 'Failed to complete maintenance');
      }
    } catch (error) {
      toast.error('An error occurred while completing maintenance');
      console.error('Error completing maintenance:', error);
    }
  }

  // Handle comment text change
  function handleCommentTextChange(text: string) {
    commentState.commentText = text;
    // Update sendNotification from the checkbox in the dialog
    const checkbox = document.getElementById('comment-sendNotification') as HTMLInputElement;
    commentState.sendNotification = checkbox?.checked || false;
  }

  // Handle add update submit
  function handleAddUpdateSubmit() {
    // Submit the form to update the event
    const form = document.getElementById('edit-maintenance-form') as HTMLFormElement;
    if (form) {
      // Use a try-catch block to handle any potential errors
      try {
        form.dispatchEvent(new Event('submit', { cancelable: true }));
        dialogState.isAddUpdateDialogOpen = false;
      } catch (error) {
        console.error('Error submitting form:', error);
      }
    }
  }

  // Handle comment submit
  function handleCommentSubmit() {
    if (commentState.commentAction === 'start') {
      submitStartMaintenance();
    } else if (commentState.commentAction === 'complete') {
      submitCompleteMaintenance();
    }
  }
</script>

<SEO title="Maintenance Management - Hirli" />
<div class="border-border flex flex-col gap-1 border-b p-4">
  <div class="flex items-center justify-between">
    <h1 class="text-2xl font-bold">Maintenance Management</h1>
    <Button
      variant="outline"
      onclick={() => {
        dialogState.isCreateDialogOpen = true;
      }}>
      <Plus class="mr-2 h-4 w-4" />
      Schedule Maintenance
    </Button>
  </div>
</div>
<Tabs.Root bind:value={activeTab}>
  <div class="border-border border-b p-0">
    <Tabs.List class="flex flex-row gap-2 divide-x">
      <Tabs.Trigger value="upcoming" class="flex-1 border-none">Upcoming</Tabs.Trigger>
      <Tabs.Trigger value="past" class="flex-1 border-none">Past</Tabs.Trigger>
      <Tabs.Trigger value="all" class="flex-1 border-none">All Events</Tabs.Trigger>
    </Tabs.List>
  </div>
  <Tabs.Content value="upcoming">
    <Card.Root>
      <Card.Header>
        <Card.Title>Upcoming Maintenance</Card.Title>
        <Card.Description>Scheduled and in-progress maintenance events</Card.Description>
      </Card.Header>
      <Card.Content>
        {#if data.upcomingEvents.length === 0}
          <div class="rounded-lg border p-6 text-center">
            <p class="text-muted-foreground">No upcoming maintenance events</p>
          </div>
        {:else}
          <div class="space-y-4">
            {#each data.upcomingEvents as event}
              <div class="flex items-start justify-between rounded-lg border p-4">
                <div class="flex-1">
                  <div class="mb-2 flex items-center gap-2">
                    <h3 class="font-medium">{event.title}</h3>
                    <Badge variant={getStatusBadgeVariant(event.status)}>
                      {#key event.status}
                        <svelte:component this={getStatusIcon(event.status)} class="mr-1 h-3 w-3" />
                      {/key}
                      {event.status.charAt(0).toUpperCase() + event.status.slice(1)}
                    </Badge>
                    <Badge variant={getSeverityBadgeVariant(event.severity)}>
                      {event.severity.charAt(0).toUpperCase() + event.severity.slice(1)}
                    </Badge>
                  </div>
                  <p class="text-muted-foreground mb-2 text-sm">{event.description}</p>
                  <div class="grid grid-cols-1 gap-2 sm:grid-cols-2">
                    <div>
                      <p class="text-xs font-medium">Start Time</p>
                      <p class="text-muted-foreground text-xs">{formatDate(event.startTime)}</p>
                    </div>
                    <div>
                      <p class="text-xs font-medium">End Time</p>
                      <p class="text-muted-foreground text-xs">{formatDate(event.endTime)}</p>
                    </div>
                  </div>
                  {#if event.affectedServices && event.affectedServices.length > 0}
                    <div class="mt-2">
                      <p class="text-xs font-medium">Affected Services</p>
                      <div class="mt-1 flex flex-wrap gap-1">
                        {#each event.affectedServices as service}
                          <Badge variant="outline" class="text-xs">{service}</Badge>
                        {/each}
                      </div>
                    </div>
                  {/if}
                </div>
                <div class="ml-4 flex flex-col gap-2">
                  {#if event.status === 'scheduled'}
                    <Button variant="outline" size="sm" onclick={() => startMaintenance(event)}>
                      Start
                    </Button>
                  {/if}
                  {#if event.status === 'in-progress'}
                    <Button variant="outline" size="sm" onclick={() => completeMaintenance(event)}>
                      Complete
                    </Button>
                  {/if}
                  <Button variant="ghost" size="icon" onclick={() => openEditDialog(event)}>
                    <Edit class="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" onclick={() => openDeleteDialog(event)}>
                    <Trash2 class="h-4 w-4" />
                  </Button>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </Card.Content>
    </Card.Root>
  </Tabs.Content>

  <Tabs.Content value="past">
    <Card.Root>
      <Card.Header>
        <Card.Title>Past Maintenance</Card.Title>
        <Card.Description>Completed and cancelled maintenance events</Card.Description>
      </Card.Header>
      <Card.Content>
        {#if data.pastEvents.length === 0}
          <div class="rounded-lg border p-6 text-center">
            <p class="text-muted-foreground">No past maintenance events</p>
          </div>
        {:else}
          <div class="space-y-4">
            {#each data.pastEvents as event}
              <div class="flex items-start justify-between rounded-lg border p-4">
                <div class="flex-1">
                  <div class="mb-2 flex items-center gap-2">
                    <h3 class="font-medium">{event.title}</h3>
                    <Badge variant={getStatusBadgeVariant(event.status)}>
                      {#key event.status}
                        <svelte:component this={getStatusIcon(event.status)} class="mr-1 h-3 w-3" />
                      {/key}
                      {event.status.charAt(0).toUpperCase() + event.status.slice(1)}
                    </Badge>
                  </div>
                  <p class="text-muted-foreground mb-2 text-sm">{event.description}</p>
                  <div class="grid grid-cols-1 gap-2 sm:grid-cols-2">
                    <div>
                      <p class="text-xs font-medium">Start Time</p>
                      <p class="text-muted-foreground text-xs">{formatDate(event.startTime)}</p>
                    </div>
                    <div>
                      <p class="text-xs font-medium">End Time</p>
                      <p class="text-muted-foreground text-xs">{formatDate(event.endTime)}</p>
                    </div>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </Card.Content>
    </Card.Root>
  </Tabs.Content>

  <Tabs.Content value="all">
    <Card.Root>
      <Card.Header>
        <Card.Title>All Maintenance Events</Card.Title>
        <Card.Description>Complete history of maintenance events</Card.Description>
      </Card.Header>
      <Card.Content>
        {#if data.maintenanceEvents.length === 0}
          <div class="rounded-lg border p-6 text-center">
            <p class="text-muted-foreground">No maintenance events found</p>
          </div>
        {:else}
          <div class="space-y-4">
            {#each data.maintenanceEvents as event}
              <div class="flex items-start justify-between rounded-lg border p-4">
                <div class="flex-1">
                  <div class="mb-2 flex items-center gap-2">
                    <h3 class="font-medium">{event.title}</h3>
                    <Badge variant={getStatusBadgeVariant(event.status)}>
                      {#key event.status}
                        <svelte:component this={getStatusIcon(event.status)} class="mr-1 h-3 w-3" />
                      {/key}
                      {event.status.charAt(0).toUpperCase() + event.status.slice(1)}
                    </Badge>
                  </div>
                  <p class="text-muted-foreground mb-2 text-sm">{event.description}</p>
                  <div class="grid grid-cols-1 gap-2 sm:grid-cols-2">
                    <div>
                      <p class="text-xs font-medium">Start Time</p>
                      <p class="text-muted-foreground text-xs">{formatDate(event.startTime)}</p>
                    </div>
                    <div>
                      <p class="text-xs font-medium">End Time</p>
                      <p class="text-muted-foreground text-xs">{formatDate(event.endTime)}</p>
                    </div>
                  </div>
                </div>
                <div class="ml-4 flex flex-col gap-2">
                  {#if event.status === 'scheduled'}
                    <Button variant="outline" size="sm" onclick={() => startMaintenance(event)}>
                      Start
                    </Button>
                  {/if}
                  {#if event.status === 'in-progress'}
                    <Button variant="outline" size="sm" onclick={() => completeMaintenance(event)}>
                      Complete
                    </Button>
                  {/if}
                  <Button variant="ghost" size="icon" onclick={() => openEditDialog(event)}>
                    <Edit class="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" onclick={() => openHistoryDialog(event)}>
                    <History class="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" onclick={() => openDeleteDialog(event)}>
                    <Trash2 class="h-4 w-4" />
                  </Button>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </Card.Content>
    </Card.Root>
  </Tabs.Content>
</Tabs.Root>

<!-- Create Maintenance Dialog -->
<MaintenanceCreateDialog
  open={dialogState.isCreateDialogOpen}
  {createForm}
  {createErrors}
  {serviceOptions}
  onClose={() => (dialogState.isCreateDialogOpen = false)}
  onSubmit={() => {
    const form = document.getElementById('create-maintenance-form') as HTMLFormElement;
    if (form) {
      // Use a try-catch block to handle any potential errors
      try {
        form.dispatchEvent(new Event('submit', { cancelable: true }));
      } catch (error) {
        console.error('Error submitting form:', error);
      }
    }
  }}
  resetForm={resetCreateForm} />

<!-- Edit Maintenance Dialog -->
<MaintenanceEditDialog
  open={dialogState.isEditDialogOpen}
  {editForm}
  {editErrors}
  {serviceOptions}
  eventHistory={eventState.eventHistory}
  onClose={() => (dialogState.isEditDialogOpen = false)}
  onSubmit={() => {
    const form = document.getElementById('edit-maintenance-form') as HTMLFormElement;
    if (form) {
      // Use a try-catch block to handle any potential errors
      try {
        form.dispatchEvent(new Event('submit', { cancelable: true }));
      } catch (error) {
        console.error('Error submitting form:', error);
      }
    }
  }}
  resetForm={resetEditForm}
  onOpenHistory={() => {
    dialogState.isHistoryDialogOpen = true;
    dialogState.isEditDialogOpen = false;
  }}
  onOpenAddUpdate={() => (dialogState.isAddUpdateDialogOpen = true)} />

<!-- Delete Maintenance Dialog -->
<MaintenanceDeleteDialog
  open={dialogState.isDeleteDialogOpen}
  {deleteForm}
  selectedEvent={eventState.selectedEvent}
  onClose={() => (dialogState.isDeleteDialogOpen = false)}
  onSubmit={() => {
    const form = document.getElementById('delete-maintenance-form') as HTMLFormElement;
    if (form) {
      // Use a try-catch block to handle any potential errors
      try {
        form.dispatchEvent(new Event('submit', { cancelable: true }));
      } catch (error) {
        console.error('Error submitting form:', error);
      }
    }
  }} />

<!-- Maintenance History Dialog -->
<MaintenanceHistoryDialog
  bind:open={dialogState.isHistoryDialogOpen}
  eventId={eventState.selectedEventId}
  onClose={() => (dialogState.isHistoryDialogOpen = false)} />

<!-- Add Update Dialog -->
<MaintenanceUpdateDialog
  open={dialogState.isAddUpdateDialogOpen}
  {editForm}
  onClose={() => (dialogState.isAddUpdateDialogOpen = false)}
  onSubmit={handleAddUpdateSubmit} />

<!-- Comment Dialog -->
<MaintenanceCommentDialog
  open={dialogState.isCommentDialogOpen}
  commentAction={commentState.commentAction}
  commentEvent={commentState.commentEvent}
  commentText={commentState.commentText}
  onClose={() => (dialogState.isCommentDialogOpen = false)}
  onSubmit={handleCommentSubmit}
  onCommentTextChange={handleCommentTextChange} />
