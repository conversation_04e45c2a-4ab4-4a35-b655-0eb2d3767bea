/**
 * Work Experience Extractor
 *
 * This module provides functions for extracting work experience from resume text.
 * It focuses on accurately identifying job titles, companies, dates, and descriptions.
 */

const fs = require("fs");
const path = require("path");
const mammoth = require("mammoth");

/**
 * Extract text from a DOCX file
 * @param {string} filePath - Path to the DOCX file
 * @returns {Promise<string>} - Extracted text
 */
async function extractTextFromDocx(filePath) {
  try {
    console.log(`Reading DOCX file: ${filePath}`);
    const result = await mammoth.extractRawText({ path: filePath });
    return result.value;
  } catch (error) {
    console.error(`Error extracting text: ${error.message}`);
    throw error;
  }
}

/**
 * Extract skills from work experience descriptions
 * @param {string[]} descriptions - Array of work experience descriptions
 * @returns {string[]} - Array of skills
 */
function extractSkillsFromDescriptions(descriptions) {
  const skills = [];

  // Common patterns for skills in descriptions
  const skillsPatterns = [
    /using\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /with\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /skills:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /technologies:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
    /tools:\s+([A-Za-z0-9,\s]+(?:and\s+[A-Za-z0-9]+)?)$/i,
  ];

  // Process each description
  for (const desc of descriptions) {
    if (!desc) continue;

    // Check each pattern
    for (const pattern of skillsPatterns) {
      const match = desc.match(pattern);
      if (match && match[1]) {
        // Split the skills by commas and "and"
        const extractedSkills = match[1]
          .split(/,|\sand\s/)
          .map((s) => s.trim())
          .filter((s) => s.length > 0);

        // Add to the skills array
        skills.push(...extractedSkills);
      }
    }

    // Also check for common skills directly in the text
    const commonSkills = [
      "JavaScript",
      "TypeScript",
      "Python",
      "Java",
      "C#",
      "C++",
      "Ruby",
      "PHP",
      "React",
      "Angular",
      "Vue",
      "Node.js",
      "Express",
      "Django",
      "Flask",
      "Spring",
      "AWS",
      "Azure",
      "GCP",
      "Docker",
      "Kubernetes",
      "Git",
      "SQL",
      "MongoDB",
      "PostgreSQL",
      "MySQL",
      "Redis",
      "GraphQL",
      "REST",
      "API",
      "HTML",
      "CSS",
      "SASS",
      "LESS",
      "Bootstrap",
      "Tailwind",
      "Material UI",
      "Redux",
      "MobX",
      "jQuery",
      "WebSockets",
      "Microservices",
      "Serverless",
      "CI/CD",
      "Jenkins",
      "GitHub Actions",
      "Travis CI",
      "CircleCI",
      "Agile",
      "Scrum",
      "Kanban",
      "TDD",
      "BDD",
      "Jest",
      "Mocha",
      "Chai",
      "Cypress",
      "Selenium",
      "Webpack",
    ];

    for (const skill of commonSkills) {
      if (desc.includes(skill)) {
        skills.push(skill);
      }
    }
  }

  // Remove duplicates and return
  return Array.from(new Set(skills));
}

/**
 * Parse date string into a structured format
 * @param {string} dateStr - Date string (e.g., "01/2020 - Present" or "January 2020 - December 2021")
 * @returns {Object} - Structured date object with startDate and endDate
 */
function parseDate(dateStr) {
  if (!dateStr) {
    return { startDate: null, endDate: null };
  }

  // Handle various date formats
  const dateFormats = [
    // MM/YYYY - MM/YYYY or Present
    /(\d{1,2}\/\d{4})\s*[-–]\s*(\d{1,2}\/\d{4}|Present|Current)/i,
    // Month YYYY - Month YYYY or Present
    /(\w+ \d{4})\s*[-–]\s*(\w+ \d{4}|Present|Current)/i,
    // YYYY - YYYY or Present
    /(\d{4})\s*[-–]\s*(\d{4}|Present|Current)/i,
  ];

  for (const regex of dateFormats) {
    const match = dateStr.match(regex);
    if (match) {
      return {
        startDate: match[1].trim(),
        endDate: match[2].trim(),
      };
    }
  }

  // If no match, return the original string as startDate
  return {
    startDate: dateStr.trim(),
    endDate: null,
  };
}

/**
 * Extract work experience from resume text
 * @param {string} text - Resume text
 * @returns {Array<Object>} - Array of work experience objects
 */
function extractWorkExperience(text) {
  if (!text) return [];

  const lines = text
    .split("\n")
    .filter((line) => line && line.trim().length > 0);
  const workExperience = [];

  // Regular expressions for job titles, companies, and dates
  const jobTitleRegex =
    /(?:senior|lead|principal|junior|staff)?\s*(?:software|frontend|backend|fullstack|full stack|web|mobile|cloud|devops|data|machine learning|ml|ai|qa|test|security|network|systems|database|ui|ux|product|project|program|technical|solutions|application|systems|infrastructure|network|cloud|database|security|support|help desk|customer|client|sales|marketing|business|finance|hr|human resources|legal|administrative|executive|c-level|ceo|cto|cio|cfo|coo|vp|director|manager|supervisor|team lead|tech lead|architect|engineer|developer|programmer|analyst|specialist|consultant|administrator|technician|designer|researcher|scientist|strategist)\s*(?:engineer|developer|programmer|analyst|specialist|consultant|administrator|technician|designer|researcher|scientist|strategist|manager|director|lead|architect|officer)?/i;

  const dateRegex =
    /(?:\d{1,2}\/\d{4}|\w+ \d{4})\s*[-–]\s*(?:\d{1,2}\/\d{4}|\w+ \d{4}|Present|Current)/i;
  const shortDateRegex =
    /\d{2}\/\d{4}\s*[-–]\s*(?:\d{2}\/\d{4}|Present|Current)/i;
  const yearRangeRegex = /\d{4}\s*[-–]\s*(?:\d{4}|Present|Current)/i;
  const companyLocationRegex = /([^|]+)\s*\|\s*([^|]+)/;
  const technologiesRegex = /Technologies:\s*(.*)/i;

  // Find the "Work Experience" or "Professional Experience" section
  let experienceStartIndex = -1;
  let educationStartIndex = -1;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim().toLowerCase();
    if (
      line === "work experience" ||
      line === "professional experience" ||
      line === "experience"
    ) {
      experienceStartIndex = i + 1;
    } else if (line === "education" || line === "academic background") {
      educationStartIndex = i;
      break;
    }
  }

  // If we couldn't find the sections, use some heuristics
  if (experienceStartIndex === -1) {
    // Look for the first job title after the summary
    for (let i = 10; i < lines.length; i++) {
      if (
        companyLocationRegex.test(lines[i]) &&
        jobTitleRegex.test(lines[i - 1])
      ) {
        experienceStartIndex = i - 1;
        break;
      }
    }
  }

  if (educationStartIndex === -1) {
    // Look for education keywords
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim().toLowerCase();
      if (
        line.includes("university") ||
        line.includes("college") ||
        line.includes("bachelor") ||
        line.includes("master") ||
        line.includes("degree") ||
        line.includes("education")
      ) {
        educationStartIndex = i;
        break;
      }
    }
  }

  // If we still couldn't find the sections, use default values
  if (experienceStartIndex === -1) experienceStartIndex = 10; // Skip header
  if (educationStartIndex === -1) educationStartIndex = lines.length;

  // Process only the work experience section
  const experienceLines = lines.slice(
    experienceStartIndex,
    educationStartIndex
  );

  // First pass: identify job blocks by looking for company|location patterns and date ranges
  const jobBlocks = [];
  let currentBlock = [];
  let inJobBlock = false;

  // Debug: Print the experience section
  console.log("\nExperience Section Lines:");
  for (let i = 0; i < Math.min(experienceLines.length, 20); i++) {
    console.log(`${i}: ${experienceLines[i]}`);
  }

  // Look for specific patterns in the resume
  // For this specific resume format, we know the structure is:
  // Job Title
  // Company | Location
  // Date Range
  // Description bullet points

  for (let i = 0; i < experienceLines.length; i++) {
    const line = experienceLines[i].trim();

    // Check if this line contains a company|location pattern
    const companyLocationMatch = line.match(companyLocationRegex);

    // Check if this line contains a date range
    const hasDateRange =
      dateRegex.test(line) ||
      shortDateRegex.test(line) ||
      yearRangeRegex.test(line);

    // If we find a job title pattern and it's not already in a block, start a new block
    if (jobTitleRegex.test(line) && line.length < 100 && !inJobBlock) {
      // If we were already in a job block, save it
      if (currentBlock.length > 0) {
        jobBlocks.push(currentBlock);
        currentBlock = [];
      }

      // Start a new job block
      inJobBlock = true;
      currentBlock.push(line);

      // Look ahead for company|location and date
      if (
        i + 1 < experienceLines.length &&
        companyLocationRegex.test(experienceLines[i + 1])
      ) {
        currentBlock.push(experienceLines[i + 1]);
        i++;

        if (
          i + 1 < experienceLines.length &&
          (dateRegex.test(experienceLines[i + 1]) ||
            shortDateRegex.test(experienceLines[i + 1]) ||
            yearRangeRegex.test(experienceLines[i + 1]))
        ) {
          currentBlock.push(experienceLines[i + 1]);
          i++;
        }
      }
    }
    // If we find a company|location pattern or a date range, it's likely part of a job block
    else if ((companyLocationMatch || hasDateRange) && !inJobBlock) {
      // Start a new job block
      inJobBlock = true;
      currentBlock.push(line);
    }
    // If we're in a job block, add the line to it
    else if (inJobBlock) {
      // If we encounter a new job title, end the current block and start a new one
      if (
        jobTitleRegex.test(line) &&
        line.length < 100 &&
        // Make sure it's not just a description that happens to match the job title pattern
        i + 1 < experienceLines.length &&
        (companyLocationRegex.test(experienceLines[i + 1]) ||
          dateRegex.test(experienceLines[i + 1]) ||
          shortDateRegex.test(experienceLines[i + 1]) ||
          yearRangeRegex.test(experienceLines[i + 1]))
      ) {
        jobBlocks.push(currentBlock);
        currentBlock = [line];

        // Look ahead for company|location and date
        if (
          i + 1 < experienceLines.length &&
          companyLocationRegex.test(experienceLines[i + 1])
        ) {
          currentBlock.push(experienceLines[i + 1]);
          i++;

          if (
            i + 1 < experienceLines.length &&
            (dateRegex.test(experienceLines[i + 1]) ||
              shortDateRegex.test(experienceLines[i + 1]) ||
              yearRangeRegex.test(experienceLines[i + 1]))
          ) {
            currentBlock.push(experienceLines[i + 1]);
            i++;
          }
        }
      } else {
        currentBlock.push(line);
      }
    }
  }

  // Add the last block if there is one
  if (currentBlock.length > 0) {
    jobBlocks.push(currentBlock);
  }

  // Debug: Print the job blocks
  console.log("\nJob Blocks:");
  for (let i = 0; i < jobBlocks.length; i++) {
    console.log(`\nBlock ${i}:`);
    for (let j = 0; j < jobBlocks[i].length; j++) {
      console.log(`  ${j}: ${jobBlocks[i][j]}`);
    }
  }

  // Second pass: process each job block to extract structured information
  for (const block of jobBlocks) {
    // Skip blocks that are too short
    if (block.length < 2) continue;

    let title = "";
    let company = "";
    let location = "";
    let startDate = null;
    let endDate = null;
    let descriptions = [];
    let technologies = [];

    // Look for job title, company, location, and date
    for (let i = 0; i < block.length; i++) {
      const line = block[i].trim();

      // Check if this line contains a company|location pattern
      const companyLocationMatch = line.match(companyLocationRegex);

      // Check if this line contains a date range
      const hasDateRange =
        dateRegex.test(line) ||
        shortDateRegex.test(line) ||
        yearRangeRegex.test(line);

      // Check if this line contains technologies
      const technologiesMatch = line.match(technologiesRegex);

      if (companyLocationMatch) {
        company = companyLocationMatch[1].trim();
        location = companyLocationMatch[2].trim();

        // If the previous line looks like a job title, use it
        if (
          i > 0 &&
          jobTitleRegex.test(block[i - 1]) &&
          block[i - 1].length < 100
        ) {
          title = block[i - 1].trim();
        }
        // If the next line looks like a date range, extract it
        if (
          i + 1 < block.length &&
          (dateRegex.test(block[i + 1]) ||
            shortDateRegex.test(block[i + 1]) ||
            yearRangeRegex.test(block[i + 1]))
        ) {
          const dateInfo = parseDate(block[i + 1].trim());
          startDate = dateInfo.startDate;
          endDate = dateInfo.endDate;
          i++; // Skip the date line
        }
      } else if (hasDateRange) {
        const dateInfo = parseDate(line);
        startDate = dateInfo.startDate;
        endDate = dateInfo.endDate;

        // If the previous line looks like a job title, use it
        if (
          i > 0 &&
          jobTitleRegex.test(block[i - 1]) &&
          block[i - 1].length < 100
        ) {
          title = block[i - 1].trim();
        }
        // If the previous line contains company|location, extract it
        if (i > 0 && companyLocationRegex.test(block[i - 1])) {
          const match = block[i - 1].match(companyLocationRegex);
          company = match[1].trim();
          location = match[2].trim();
        }
      } else if (technologiesMatch) {
        technologies = technologiesMatch[1].split(/,\s*/).map((t) => t.trim());
      } else if (jobTitleRegex.test(line) && line.length < 100 && !title) {
        title = line;
      } else if (
        line.startsWith("•") ||
        line.startsWith("-") ||
        line.startsWith("*") ||
        /^\d+\./.test(line)
      ) {
        descriptions.push(line.replace(/^[•\-*\d\.]+\s*/, ""));
      } else if (
        line.length > 30 &&
        !jobTitleRegex.test(line) &&
        !hasDateRange &&
        !companyLocationMatch
      ) {
        descriptions.push(line);
      }
    }

    // If we have a title or company, create a job entry
    if (title || company) {
      // Extract skills from descriptions
      const skills = extractSkillsFromDescriptions(descriptions);

      // Add skills from technologies
      if (technologies.length > 0) {
        skills.push(...technologies);
      }

      workExperience.push({
        title,
        company,
        location,
        startDate,
        endDate,
        descriptions,
        skills: Array.from(new Set(skills)),
      });
    }
  }

  // Filter out entries that don't look like real jobs
  const filteredExperience = workExperience.filter((job) => {
    // Must have either a title or company
    if (!job.title && !job.company) return false;

    // Skip entries that are just technologies
    if (job.title.toLowerCase().startsWith("technologies:")) return false;

    // Skip entries that are just references
    if (job.title.toLowerCase().includes("reference")) return false;

    return true;
  });

  // Post-process the filtered experience to clean up and consolidate entries
  const processedExperience = [];
  const jobMap = new Map();

  // Group jobs by company
  for (const job of filteredExperience) {
    const company = job.company || "";

    if (!jobMap.has(company)) {
      jobMap.set(company, []);
    }

    jobMap.get(company).push(job);
  }

  // Process each company's jobs
  for (const [company, jobs] of jobMap.entries()) {
    if (company === "") continue; // Skip empty companies

    // Sort jobs by date (if available)
    jobs.sort((a, b) => {
      if (a.startDate && b.startDate) {
        return a.startDate.localeCompare(b.startDate);
      }
      return 0;
    });

    // Find the most complete job entry
    let bestJob = jobs[0];
    for (const job of jobs) {
      // Prefer jobs with more information
      if (
        (job.title && !bestJob.title) ||
        (job.location && !bestJob.location) ||
        (job.startDate && !bestJob.startDate) ||
        (job.endDate && !bestJob.endDate) ||
        job.descriptions.length > bestJob.descriptions.length
      ) {
        bestJob = job;
      }
    }

    // Combine information from all jobs for this company
    const combinedJob = {
      title: bestJob.title || "",
      company: company,
      location: bestJob.location || "",
      startDate: bestJob.startDate || "",
      endDate: bestJob.endDate || "",
      descriptions: [],
      skills: [],
    };

    // Collect all descriptions and skills
    for (const job of jobs) {
      if (job.descriptions && job.descriptions.length > 0) {
        combinedJob.descriptions.push(...job.descriptions);
      }

      if (job.skills && job.skills.length > 0) {
        combinedJob.skills.push(...job.skills);
      }
    }

    // Remove duplicates
    combinedJob.descriptions = Array.from(new Set(combinedJob.descriptions));
    combinedJob.skills = Array.from(new Set(combinedJob.skills));

    processedExperience.push(combinedJob);
  }

  // Add any jobs with empty company that have good information
  for (const job of filteredExperience) {
    if (
      !job.company &&
      job.title &&
      (job.startDate || job.endDate || job.descriptions.length > 0)
    ) {
      processedExperience.push(job);
    }
  }

  // Sort by date (most recent first)
  processedExperience.sort((a, b) => {
    if (a.startDate && b.startDate) {
      return b.startDate.localeCompare(a.startDate);
    }
    return 0;
  });

  return processedExperience;
}

// Main function to test the extractor
async function main() {
  try {
    // Get the file path from command line arguments
    const filePath =
      process.argv[2] || "../web/static/uploads/resumes/Resume.docx";

    // Make sure the file exists
    try {
      await fs.promises.access(filePath, fs.constants.F_OK);
      console.log(`File exists: ${filePath}`);
    } catch (error) {
      console.error(`File does not exist: ${filePath}`);
      process.exit(1);
    }

    // Extract text from the DOCX file
    const text = await extractTextFromDocx(filePath);

    // Extract work experience
    const workExperience = extractWorkExperience(text);

    // Print the results
    console.log("\nExtracted Work Experience:");
    console.log(JSON.stringify(workExperience, null, 2));

    console.log(`\nFound ${workExperience.length} work experiences`);
  } catch (error) {
    console.error("Error:", error);
  }
}

// Run the main function if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  extractWorkExperience,
  parseDate,
  extractSkillsFromDescriptions,
};
