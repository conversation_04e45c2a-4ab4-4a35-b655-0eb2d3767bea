<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import { Checkbox } from '$lib/components/ui/checkbox/index.js';
  import * as Select from '$lib/components/ui/select/index.js';

  // Props
  export let open: boolean;
  export let editForm: any;
  export let onClose: () => void;
  export let onSubmit: () => void;

  // Computed values
  $: currentDate = new Date().toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
</script>

<Dialog.Root bind:open>
  <Dialog.Overlay />
  <Dialog.Content class="sm:max-w-[500px]">
    <Dialog.Header>
      <Dialog.Title>Add Status Update</Dialog.Title>
      <Dialog.Description>
        Update the maintenance status and add a comment. This will be recorded in the history.
      </Dialog.Description>
    </Dialog.Header>

    <div class="py-4">
      <div class="grid gap-4">
        <div class="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div class="grid gap-2">
            <Label for="update-status">Update Status</Label>
            <Select.Root type="single" bind:value={$editForm.commentStatus}>
              <Select.SelectTrigger class="w-full">
                <Select.SelectValue placeholder="Select status update" />
              </Select.SelectTrigger>
              <Select.SelectContent class="w-full">
                <Select.SelectItem value="investigating">Investigating</Select.SelectItem>
                <Select.SelectItem value="identified">Issue Identified</Select.SelectItem>
                <Select.SelectItem value="in-progress">In Progress</Select.SelectItem>
                <Select.SelectItem value="monitoring">Monitoring</Select.SelectItem>
                <Select.SelectItem value="resolved">Resolved</Select.SelectItem>
              </Select.SelectContent>
            </Select.Root>
          </div>

          <div class="grid gap-2">
            <Label for="update-date">Comment Date</Label>
            <div class="bg-muted flex h-9 items-center rounded-md border px-3 py-1">
              {currentDate}
            </div>
          </div>
        </div>

        <div class="grid gap-2">
          <Label for="update-comment">Comment</Label>
          <Textarea bind:value={$editForm.comment} />
          <p class="text-muted-foreground text-xs">
            This comment will be recorded in the maintenance history along with the status update
          </p>
        </div>

        <div class="mt-4 flex items-center space-x-2">
          <Checkbox id="update-sendNotification" bind:checked={$editForm.sendNotification} />
          <Label for="update-sendNotification" class="text-sm font-normal">
            Send notification to all users about this update
          </Label>
        </div>
      </div>
    </div>

    <Dialog.Footer>
      <Button type="button" variant="outline" onclick={onClose}>Cancel</Button>
      <Button type="button" onclick={onSubmit}>Add Update</Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
