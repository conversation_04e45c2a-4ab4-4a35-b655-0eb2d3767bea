// File: web/src/routes/api/resume/[id]/parsing-status/+server.ts
import { prisma } from '$lib/server/prisma';
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types.js';

/**
 * GET handler to check if a resume is currently being parsed
 *
 * This endpoint checks if a resume is currently being parsed by looking for
 * active worker processes for the resume.
 */
export const GET: RequestHandler = async ({ params, locals }) => {
  const user = locals.user;
  if (!user) return new Response('Unauthorized', { status: 401 });

  const resumeId = params.id;
  if (!resumeId) {
    return json({ error: 'Resume ID is required' }, { status: 400 });
  }

  try {
    // Get the resume with its document
    const resume = await prisma.resume.findUnique({
      where: { id: resumeId },
      include: {
        document: true,
      },
    });

    if (!resume) {
      return json({ error: 'Resume not found' }, { status: 404 });
    }

    // Check if the document belongs to the user
    if (resume.document.userId !== user.id) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if there are any active worker processes for this resume
    const activeWorkerProcess = await prisma.workerProcess.findFirst({
      where: {
        type: 'resume-parsing',
        status: {
          in: ['pending', 'processing'],
        },
        data: {
          path: ['resumeId'],
          equals: resumeId,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Return the parsing status based on whether there's an active worker process
    return json({
      isParsing: !!activeWorkerProcess,
      workerProcess: activeWorkerProcess
        ? {
            id: activeWorkerProcess.id,
            status: activeWorkerProcess.status,
            createdAt: activeWorkerProcess.createdAt,
            updatedAt: activeWorkerProcess.updatedAt,
          }
        : null,
    });
  } catch (error) {
    console.error('Error checking resume parsing status:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
