declare module 'node-os-utils' {
  interface MemInfo {
    totalMemMb: number;
    usedMemMb: number;
    freeMemMb: number;
    freeMemPercentage: number;
    usedMemPercentage: number;
  }

  interface CpuInfo {
    manufacturer: string;
    brand: string;
    vendor: string;
    family: string;
    model: string;
    stepping: string;
    revision: string;
    voltage: string;
    speed: string;
    speedMin: string;
    speedMax: string;
    governor: string;
    cores: number;
    physicalCores: number;
    processors: number;
    socket: string;
    cache: {
      l1d: string;
      l1i: string;
      l2: string;
      l3: string;
    };
  }

  interface OsInfo {
    platform: string;
    distro: string;
    release: string;
    codename: string;
    kernel: string;
    arch: string;
    hostname: string;
    logofile: string;
  }

  interface NetStat {
    iface: string;
    operstate: string;
    rx: number;
    tx: number;
    rx_sec: number;
    tx_sec: number;
    ms: number;
  }

  interface NetCheckOptions {
    timeout?: number;
    host?: string;
  }

  interface NetCheck {
    (options?: NetCheckOptions): Promise<boolean>;
  }

  interface Mem {
    info(): Promise<MemInfo>;
    free(): Promise<number>;
    used(): Promise<number>;
    totalMem(): number;
  }

  interface CPU {
    usage(interval?: number): Promise<number>;
    free(interval?: number): Promise<number>;
    count(): number;
    model(): string;
    loadavg(): number[];
    loadavgTime(time: number): number;
    info(): Promise<CpuInfo>;
  }

  interface Drive {
    info(drive?: string): Promise<{
      totalGb: number;
      usedGb: number;
      freeGb: number;
      usedPercentage: number;
      freePercentage: number;
    }>;
    free(drive?: string): Promise<{
      totalGb: number;
      freeGb: number;
      freePercentage: number;
    }>;
    used(drive?: string): Promise<{
      totalGb: number;
      usedGb: number;
      usedPercentage: number;
    }>;
  }

  interface OS {
    oos(): Promise<OsInfo>;
    platform(): string;
    uptime(): number;
    ip(): string;
    hostname(): string;
    type(): string;
    arch(): string;
  }

  interface NetStat {
    stats(iface?: string): Promise<NetStat>;
    inOut(iface?: string): Promise<{
      total: {
        inputMb: number;
        outputMb: number;
      };
      ms: number;
    }>;
  }

  interface Proc {
    totalProcesses(): Promise<number>;
    zombieProcesses(): Promise<number>;
  }

  interface Users {
    openedCount(): Promise<number>;
  }

  const osu: {
    mem: Mem;
    cpu: CPU;
    drive: Drive;
    os: OS;
    netstat: NetStat;
    proc: Proc;
    users: Users;
    isOnline: NetCheck;
  };

  export = osu;
}
