<script lang="ts">
  import * as Card from '$lib/components/ui/card';
  import * as Progress from '$lib/components/ui/progress';
  import { Badge } from '$lib/components/ui/badge';
  import {
    FileText,
    AlertTriangle,
    CheckCircle,
    RefreshCw,
    Download,
    ChevronDown,
    Sparkles,
  } from 'lucide-svelte';
  import * as Accordion from '$lib/components/ui/accordion';
  import type { ATSAnalysisResult, JobSpecificATSAnalysis } from '$lib/services/ai-service';
  import { getATSAnalysis, getJobSpecificATSAnalysis } from '$lib/services/ai-service';
  import { toast } from 'svelte-sonner';

  // Props
  const { resumeId, jobId } = $props<{
    resumeId: string;
    jobId?: string;
  }>();

  // State
  let analysis = $state<ATSAnalysisResult | JobSpecificATSAnalysis | null>(null);
  let isLoading = $state(false);
  let error = $state<string | null>(null);
  let isJobSpecific = $state(false);

  // Load analysis when props change
  $effect(() => {
    if (resumeId) {
      loadAnalysis();
    }
  });

  // Load analysis
  async function loadAnalysis() {
    isLoading = true;
    error = null;

    try {
      if (jobId) {
        // Job-specific analysis
        analysis = await getJobSpecificATSAnalysis(resumeId, jobId);
        isJobSpecific = true;
      } else {
        // General analysis
        analysis = await getATSAnalysis(resumeId);
        isJobSpecific = false;
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load ATS analysis';
      console.error('Error loading ATS analysis:', err);
    } finally {
      isLoading = false;
    }
  }

  // Refresh analysis
  async function refreshAnalysis() {
    await loadAnalysis();
    toast.success('ATS analysis refreshed');
  }

  // Get score color
  function getScoreColor(score: number): string {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-amber-600';
    return 'text-red-600';
  }

  // Format score for display
  function formatScore(score: number): string {
    return Math.round(score).toString();
  }

  // Get score label
  function getScoreLabel(score: number): string {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Poor';
  }

  // Get job-specific analysis
  function getJobSpecificAnalysis(): JobSpecificATSAnalysis | null {
    return isJobSpecific ? (analysis as JobSpecificATSAnalysis) : null;
  }
</script>

<Card.Root class="w-full">
  <Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
    <Card.Title class="text-md font-medium">
      <div class="flex items-center">
        <FileText class="mr-2 h-4 w-4 text-blue-500" />
        {isJobSpecific ? 'Job-Specific ATS Analysis' : 'ATS Analysis'}
      </div>
    </Card.Title>
    <button
      class="inline-flex h-8 w-8 items-center justify-center rounded-md p-0 text-gray-700 hover:bg-gray-100"
      onclick={refreshAnalysis}>
      <RefreshCw class="h-4 w-4" />
      <span class="sr-only">Refresh</span>
    </button>
  </Card.Header>

  <Card.Content>
    {#if isLoading}
      <div class="flex items-center justify-center py-6">
        <div class="h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
        <span class="ml-2">Analyzing resume...</span>
      </div>
    {:else if error}
      <div class="rounded-md bg-red-50 p-4 text-sm text-red-500">
        <p>Error: {error}</p>
        <button
          class="mt-2 inline-flex h-9 items-center justify-center rounded-md border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100"
          onclick={loadAnalysis}>
          Try Again
        </button>
      </div>
    {:else if !analysis}
      <div class="py-4 text-center text-sm text-gray-500">
        <p>No analysis available for this resume.</p>
        <button
          class="mt-2 inline-flex h-9 items-center justify-center rounded-md border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100"
          onclick={refreshAnalysis}>
          Run Analysis
        </button>
      </div>
    {:else}
      <!-- Overall Score -->
      <div class="mb-6 rounded-lg bg-gray-50 p-4">
        <div class="mb-2 text-center">
          <h3 class="text-lg font-medium">Overall ATS Score</h3>
          <div class="mt-2 text-4xl font-bold {getScoreColor(analysis.overallScore)}">
            {formatScore(analysis.overallScore)}%
          </div>
          <div class="mt-1 text-sm text-gray-500">
            {getScoreLabel(analysis.overallScore)}
          </div>
        </div>

        <Progress.Root value={analysis.overallScore} class="mt-2 h-2" />

        {#if isJobSpecific && getJobSpecificAnalysis()}
          <div class="mt-4 text-center">
            <h4 class="text-sm font-medium">Match Percentage</h4>
            <div
              class="mt-1 text-2xl font-bold {getScoreColor(
                getJobSpecificAnalysis()!.matchPercentage
              )}">
              {formatScore(getJobSpecificAnalysis()!.matchPercentage)}%
            </div>
          </div>
        {/if}
      </div>

      <!-- Score Breakdown -->
      <div class="mb-4">
        <h3 class="mb-2 text-sm font-medium">Score Breakdown</h3>
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <span class="text-sm">Keywords</span>
            <div class="flex items-center">
              <Progress.Root value={analysis.keywordScore} class="mr-2 h-2 w-24" />
              <span class="text-sm font-medium {getScoreColor(analysis.keywordScore)}">
                {formatScore(analysis.keywordScore)}%
              </span>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm">Format</span>
            <div class="flex items-center">
              <Progress.Root value={analysis.formatScore} class="mr-2 h-2 w-24" />
              <span class="text-sm font-medium {getScoreColor(analysis.formatScore)}">
                {formatScore(analysis.formatScore)}%
              </span>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm">Content</span>
            <div class="flex items-center">
              <Progress.Root value={analysis.contentScore} class="mr-2 h-2 w-24" />
              <span class="text-sm font-medium {getScoreColor(analysis.contentScore)}">
                {formatScore(analysis.contentScore)}%
              </span>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm">Readability</span>
            <div class="flex items-center">
              <Progress.Root value={analysis.readabilityScore} class="mr-2 h-2 w-24" />
              <span class="text-sm font-medium {getScoreColor(analysis.readabilityScore)}">
                {formatScore(analysis.readabilityScore)}%
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Issues and Recommendations -->
      <Accordion.Root class="w-full" type="single">
        <!-- Detected Issues -->
        <Accordion.Item value="issues">
          <Accordion.Trigger
            class="flex w-full items-center justify-between py-2 text-sm font-medium">
            <div class="flex items-center">
              <AlertTriangle class="mr-2 h-4 w-4 text-amber-500" />
              Detected Issues ({analysis.detectedIssues.length})
            </div>
            <ChevronDown class="h-4 w-4 transition-transform duration-200" />
          </Accordion.Trigger>
          <Accordion.Content class="pb-2 pt-1">
            {#if analysis.detectedIssues.length === 0}
              <p class="text-sm text-gray-500">No issues detected.</p>
            {:else}
              <ul class="space-y-1 pl-6 text-sm">
                {#each analysis.detectedIssues as issue}
                  <li class="list-disc text-gray-700">{issue.message}</li>
                {/each}
              </ul>
            {/if}
          </Accordion.Content>
        </Accordion.Item>

        <!-- Suggested Keywords -->
        <Accordion.Item value="keywords">
          <Accordion.Trigger
            class="flex w-full items-center justify-between py-2 text-sm font-medium">
            <div class="flex items-center">
              <Sparkles class="mr-2 h-4 w-4 text-blue-500" />
              Suggested Keywords ({analysis.suggestedKeywords.length})
            </div>
            <ChevronDown class="h-4 w-4 transition-transform duration-200" />
          </Accordion.Trigger>
          <Accordion.Content class="pb-2 pt-1">
            {#if analysis.suggestedKeywords.length === 0}
              <p class="text-sm text-gray-500">No keywords suggested.</p>
            {:else}
              <div class="flex flex-wrap gap-1">
                {#each analysis.suggestedKeywords as keyword}
                  <Badge variant="outline" class="text-xs">{keyword}</Badge>
                {/each}
              </div>
            {/if}
          </Accordion.Content>
        </Accordion.Item>

        <!-- Job-Specific Recommendations -->
        {#if isJobSpecific && getJobSpecificAnalysis()?.recommendations}
          <Accordion.Item value="recommendations">
            <Accordion.Trigger
              class="flex w-full items-center justify-between py-2 text-sm font-medium">
              <div class="flex items-center">
                <CheckCircle class="mr-2 h-4 w-4 text-green-500" />
                Recommendations
              </div>
              <ChevronDown class="h-4 w-4 transition-transform duration-200" />
            </Accordion.Trigger>
            <Accordion.Content class="pb-2 pt-1">
              {#if getJobSpecificAnalysis()?.recommendations?.length === 0}
                <p class="text-sm text-gray-500">No recommendations available.</p>
              {:else}
                <ul class="space-y-1 pl-6 text-sm">
                  {#each getJobSpecificAnalysis()?.recommendations || [] as recommendation}
                    <li class="list-disc text-gray-700">{recommendation}</li>
                  {/each}
                </ul>
              {/if}
            </Accordion.Content>
          </Accordion.Item>
        {/if}
      </Accordion.Root>

      <!-- Actions -->
      <div class="mt-4 flex justify-end space-x-2">
        <button
          class="inline-flex h-8 items-center justify-center rounded-md border border-gray-200 bg-white px-3 py-1 text-xs font-medium text-gray-900 hover:bg-gray-100">
          <Download class="mr-1 h-3 w-3" />
          Export Report
        </button>
      </div>
    {/if}
  </Card.Content>
</Card.Root>
