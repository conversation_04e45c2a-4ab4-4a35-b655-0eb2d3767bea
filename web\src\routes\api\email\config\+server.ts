import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { env } from '$env/dynamic/private';

/**
 * GET handler to check if Resend API is configured
 */
export const GET: RequestHandler = async ({ request }) => {
  try {
    // Check if Resend API key is configured
    const resendApiKey = env.RESEND_API_KEY;
    const resendConfigured = !!resendApiKey;

    return json({
      resendConfigured,
      message: resendConfigured 
        ? 'Resend API is configured' 
        : 'Resend API key is not configured'
    });
  } catch (error) {
    console.error('Error checking Resend API configuration:', error);
    return json(
      {
        resendConfigured: false,
        error: 'Failed to check Resend API configuration'
      },
      { status: 500 }
    );
  }
};
