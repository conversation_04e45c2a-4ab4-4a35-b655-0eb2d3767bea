import { PrismaClient } from "@prisma/client";
import path from "path";
import { logger } from "../utils/logger";
import fs from "fs/promises";

const prisma = new PrismaClient();
const FILE_PATH = path.resolve("./utils/json/schools.json");

/** Treat blank or literal “N/A” (any case) as missing */
const isNA = (v?: string) =>
  !v || v.trim() === "" || v.trim().toLowerCase() === "n/a";

/** Try to map a 3‑letter ISO code (e.g. “PRI”) to a 2‑letter code (“PR”). */
const ISO3_TO_ISO2: Record<string, string> = {
  USA: "US",
  PRI: "PR",
  VIR: "VI",
  GUM: "GU",
  ASM: "AS",
  MNP: "MP",
  CAN: "CA",
  MEX: "MX",
  // Add more if needed
};

const US_TERRITORIES = new Set(["PR", "VI", "GU", "AS", "MP", "DC"]);

type IpedsRow = {
  name: string;
  city?: string;
  state?: string;
  country?: string;
  website?: string;
};

export async function loadSchools() {
  logger.info("📥 Loading school list (NCES/IPEDS)…");

  const raw = await fs.readFile(FILE_PATH, "utf-8");
  const schools: IpedsRow[] = JSON.parse(raw);

  const seen = new Set<string>();
  let inserted = 0;

  for (const row of schools) {
    const institution = row.name?.trim();
    if (isNA(institution)) continue;

    /* ─────────────── Country ─────────────── */
    let countryId: string | undefined;
    let countryKey = "";

    if (!isNA(row.country)) {
      const iso3 = row.country!.trim().toUpperCase();
      const iso2 = ISO3_TO_ISO2[iso3] ?? iso3;

      // If it's a U.S. territory, normalize to "US"
      const normalizedIso = US_TERRITORIES.has(iso2) ? "US" : iso2;

      const country = await prisma.country.upsert({
        where: { isoCode: normalizedIso },
        update: {},
        create: {
          name: normalizedIso,
          isoCode: normalizedIso,
        },
      });

      countryId = country.id;
      countryKey = country.isoCode ?? "";
    }

    /* ─────────────── State / Territory ─────────────── */
    let stateId: string | undefined;
    let stateKey = "";

    if (!isNA(row.state) && countryId) {
      const sCode = row.state!.trim().toUpperCase();

      const state = await prisma.state.findFirst({
        where: {
          countryId,
          code: sCode,
        },
        select: { id: true },
      });

      if (state) {
        stateId = state.id;
        stateKey = sCode;
      } else {
        logger.warn(
          `⚠️ Missing state ${sCode} in country ${countryKey}, skipping association`
        );
      }
    }

    /* ─────────────── Skip duplicates ─────────────── */
    const dedupKey = `${institution}::${stateKey}::${countryKey}`;
    if (seen.has(dedupKey)) continue;

    const exists = await prisma.school.findFirst({
      where: { institution, stateId, countryId },
      select: { id: true },
    });
    if (exists) continue;

    /* ─────────────── Insert ─────────────── */
    await prisma.school.create({
      data: {
        institution,
        countryId,
        stateId,
      },
    });

    seen.add(dedupKey);
    inserted++;
    logger.debug(
      `🏫 Added: ${institution}` +
        (stateKey ? `, ${stateKey}` : "") +
        (countryKey ? `, ${countryKey}` : "")
    );
  }

  logger.info(`✅ Loaded ${inserted} new schools`);
}
