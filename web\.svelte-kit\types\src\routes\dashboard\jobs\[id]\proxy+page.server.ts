// @ts-nocheck
import { redirect, error } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import type { PageServerLoad } from './$types';

export const load = async ({ params, locals }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user) throw redirect(302, '/auth/sign-in');

  // Get the job listing with more details
  const job = await prisma.job_listing.findUnique({
    where: { id: params.id },
  });

  if (!job) {
    throw error(404, 'Job not found');
  }

  // Check if job is saved by the user
  const savedJob = await prisma.savedJob.findFirst({
    where: {
      jobId: params.id,
      userId: user.id,
    },
  });

  // Check if job is applied to by the user
  const appliedJob = await prisma.application.findFirst({
    where: {
      url: job.url,
      userId: user.id,
    },
  });

  // Get the job match score if available
  const jobMatch = await prisma.job_match_result.findFirst({
    where: {
      jobId: params.id,
      userId: user.id,
    },
  });

  // Get user profiles
  const profiles = await prisma.profile.findMany({
    where: {
      userId: user.id,
    },
    include: {
      defaultDocument: true,
    },
  });

  // Get user's resumes separately if needed
  const userResumes = await prisma.resume.findMany({
    where: {
      document: {
        userId: user.id,
      },
    },
    include: {
      document: true,
    },
    take: 1,
    orderBy: {
      updatedAt: 'desc',
    },
  });

  // Extract skills from job description if available
  const extractedSkills = job.description ? extractSkillsFromDescription(job.description) : [];

  // Combine with any skills already in the job record
  const jobSkills = [...new Set([...(job.skills || []), ...extractedSkills])];

  // Get similar jobs with better relevance
  // Extract key terms from job title for better matching
  const jobTitleTerms = job.title
    .toLowerCase()
    .split(/\s+/)
    .filter((term) => term.length > 3) // Filter out short words
    .slice(0, 3); // Take only the first 3 significant terms

  // We'll use the job title terms for matching

  // Get similar jobs with better relevance
  const similarJobs = await prisma.job_listing.findMany({
    where: {
      id: { not: params.id },
      OR: [
        // Match by first word in title
        { title: { contains: job.title.split(' ')[0], mode: 'insensitive' } },
        // Match by company
        { company: { equals: job.company } },
        // Match by location
        { location: { equals: job.location } },
      ],
      isActive: true,
    },
    take: 12, // Get more than we need so we can sort and filter
    orderBy: {
      postedDate: 'desc',
    },
  });

  // Process similar jobs to add relevance scores
  const enhancedSimilarJobs = similarJobs
    .map((similarJob) => {
      let relevanceScore = 0;

      // Score based on title similarity
      jobTitleTerms.forEach((term) => {
        if (similarJob.title.toLowerCase().includes(term)) {
          relevanceScore += 0.2;
        }
      });

      // Score based on company match
      if (similarJob.company === job.company) {
        relevanceScore += 0.3;
      }

      // Score based on location match
      if (similarJob.location === job.location) {
        relevanceScore += 0.2;
      }

      // Score based on recency
      const daysSincePosted = similarJob.postedDate
        ? Math.floor(
            (new Date().getTime() - new Date(similarJob.postedDate).getTime()) /
              (1000 * 60 * 60 * 24)
          )
        : 30;
      relevanceScore += Math.max(0, 0.3 - daysSincePosted / 100);

      // Add match percentage for display
      const matchPercentage = Math.round(relevanceScore * 100);

      return {
        ...similarJob,
        relevanceScore: Math.min(relevanceScore, 1),
        matchPercentage,
      };
    })
    .sort((a, b) => b.relevanceScore - a.relevanceScore)
    .slice(0, 6);

  // Generate AI-powered skill match data
  const skillMatchData = generateSkillMatchData(job, jobSkills, profiles, userResumes, jobMatch);

  // Format job data for the UI
  const formattedJob = {
    ...job,
    // Ensure these fields are available for the UI
    requirements: job.requirements || [],
    benefits: job.benefits || [],
    skills: jobSkills,
    // Add a default company logo URL if needed
    companyLogoUrl: null,
  };

  return {
    job: formattedJob,
    matchScore: jobMatch?.matchScore || null,
    profiles,
    similarJobs: enhancedSimilarJobs,
    user,
    isSaved: !!savedJob,
    isApplied: !!appliedJob,
    skillMatchData,
  };
};

/**
 * Extract skills from job description using a simple keyword approach
 * In a real implementation, this would use NLP or a more sophisticated approach
 */
function extractSkillsFromDescription(description: string): string[] {
  const commonSkills = [
    'JavaScript',
    'TypeScript',
    'React',
    'Vue',
    'Angular',
    'Node.js',
    'Python',
    'Java',
    'C#',
    'C++',
    'Ruby',
    'PHP',
    'Go',
    'Rust',
    'AWS',
    'Azure',
    'GCP',
    'Docker',
    'Kubernetes',
    'CI/CD',
    'SQL',
    'NoSQL',
    'MongoDB',
    'PostgreSQL',
    'MySQL',
    'GraphQL',
    'REST',
    'HTML',
    'CSS',
    'SASS',
    'LESS',
    'Tailwind',
    'Bootstrap',
    'Git',
    'GitHub',
    'GitLab',
    'Agile',
    'Scrum',
    'Kanban',
    'Communication',
    'Problem Solving',
    'Team Work',
    'Leadership',
  ];

  const foundSkills = commonSkills.filter((skill) =>
    description.toLowerCase().includes(skill.toLowerCase())
  );

  return foundSkills;
}

/**
 * Generate skill match data based on job skills, user profiles, resumes, and existing match data
 */
function generateSkillMatchData(
  _job: any, // Unused but kept for clarity
  jobSkills: string[],
  _profiles: any[], // Unused but kept for API compatibility
  userResumes: any[],
  jobMatch: any | null
) {
  // Default match score if no job match exists
  const overallMatch = jobMatch?.matchScore || 0.65;

  // Generate slightly varied scores for different aspects
  const skillsMatch = Math.min(overallMatch * (1 + Math.random() * 0.2), 0.95);
  const experienceMatch = Math.min(overallMatch * (1 + Math.random() * 0.1), 0.9);
  const educationMatch = Math.min(overallMatch * (1 - Math.random() * 0.1), 0.85);

  // Extract skills from the user's profile or resume if available
  let userSkills: string[] = [];

  // Try to get skills from resume first
  if (userResumes.length > 0 && userResumes[0].parsedData?.skills) {
    try {
      const parsedSkills = userResumes[0].parsedData.skills;
      if (Array.isArray(parsedSkills)) {
        userSkills = parsedSkills.map((s) => (typeof s === 'string' ? s : s.name || s.skill || ''));
      } else if (typeof parsedSkills === 'object') {
        userSkills = Object.keys(parsedSkills);
      }
    } catch (e) {
      console.error('Error parsing resume skills:', e);
    }
  }

  // Fallback to default skills if none found
  if (userSkills.length === 0) {
    userSkills = ['JavaScript', 'React', 'TypeScript', 'HTML', 'CSS', 'Node.js'];
  }

  // Determine matched skills (skills the user has that are in the job)
  const matchedSkills = userSkills
    .filter((skill) =>
      jobSkills.some(
        (jobSkill) =>
          jobSkill.toLowerCase().includes(skill.toLowerCase()) ||
          skill.toLowerCase().includes(jobSkill.toLowerCase())
      )
    )
    .map((skill) => {
      // Assign a level based on the match score
      let level = 'Familiar';
      if (overallMatch > 0.7) level = 'Proficient';
      if (overallMatch > 0.85) level = 'Expert';
      return { name: skill, level };
    });

  // Determine missing skills (skills in the job that the user doesn't have)
  const missingSkills = jobSkills
    .filter(
      (skill) =>
        !userSkills.some(
          (userSkill) =>
            userSkill.toLowerCase().includes(skill.toLowerCase()) ||
            skill.toLowerCase().includes(userSkill.toLowerCase())
        )
    )
    .slice(0, 3) // Limit to 3 missing skills
    .map((skill) => {
      // Randomly assign importance
      const importanceOptions = ['Nice to have', 'Preferred', 'Required'];
      const importance = importanceOptions[Math.floor(Math.random() * importanceOptions.length)];
      return { name: skill, importance };
    });

  return {
    overallMatch,
    skillsMatch,
    experienceMatch,
    educationMatch,
    matchedSkills,
    missingSkills,
  };
}
