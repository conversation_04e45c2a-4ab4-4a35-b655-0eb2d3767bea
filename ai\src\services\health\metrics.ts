import fs from "fs/promises";
import path from "path";
import { fileURLToPath } from "url";
import { logger } from "../../utils/logger.js";
import type { SystemMetrics } from "./monitor.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Keep a rolling window of metrics
const metricsHistory: SystemMetrics[] = [];
const MAX_HISTORY_LENGTH = 1440; // Store 24 hours of metrics at 1-minute intervals

export function getMetricsHistory(): SystemMetrics[] {
  return metricsHistory;
}

export async function recordMetrics(metrics: SystemMetrics) {
  // Add to in-memory history
  metricsHistory.push(metrics);

  // Trim history if needed
  if (metricsHistory.length > MAX_HISTORY_LENGTH) {
    metricsHistory.shift();
  }

  // Log metrics in the same format as other services
  logger.debug(
    `Metrics recorded - CPU: ${metrics.cpuUsage.toFixed(
      1
    )}%, Memory: ${metrics.memoryUsage.toFixed(1)}%, Uptime: ${formatUptime(
      metrics.uptime
    )}`
  );

  // Optionally write to disk for persistence
  try {
    const metricsDir = path.join(process.cwd(), "metrics");
    await fs.mkdir(metricsDir, { recursive: true });

    const date = new Date().toISOString().split("T")[0]; // YYYY-MM-DD
    const metricsFile = path.join(metricsDir, `metrics-${date}.json`);

    // Check if file exists
    let existingMetrics: SystemMetrics[] = [];
    try {
      // Check if file exists first
      await fs.access(metricsFile);

      // If we get here, file exists, so read it
      const data = await fs.readFile(metricsFile, "utf-8");
      existingMetrics = JSON.parse(data);
    } catch (error: any) {
      if (error.code === "ENOENT") {
        // File doesn't exist, which is fine for a new day
        logger.debug(`Creating new metrics file: ${metricsFile}`);
      } else {
        // Log other errors but continue with empty array
        logger.warn(`Error reading metrics file: ${error.message}`);
      }
    }

    // Add new metrics and save
    existingMetrics.push(metrics);
    await fs.writeFile(metricsFile, JSON.stringify(existingMetrics), "utf-8");
  } catch (error) {
    logger.error("Failed to save metrics to disk:", error);
  }
}

function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  return `${days}d ${hours}h ${minutes}m`;
}
