<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label';
  import * as Dialog from '$lib/components/ui/dialog';
  import * as Select from '$lib/components/ui/select';
  import { Checkbox } from '$lib/components/ui/checkbox';
  import { Badge } from '$lib/components/ui/badge';
  import * as Tabs from '$lib/components/ui/tabs';
  import { Slider } from '$lib/components/ui/slider';
  import { Plus, X, Settings, Target, Building, Filter } from 'lucide-svelte';

  const { 
    open, 
    basicConfig,
    onClose, 
    onSubmit 
  } = $props<{
    open: boolean;
    basicConfig: any;
    onClose: () => void;
    onSubmit: (config: any) => void;
  }>();

  // Advanced settings state
  let maxJobsToApply = $state(basicConfig?.maxJobsToApply || 10);
  let minMatchScore = $state(basicConfig?.minMatchScore || 70);
  let autoApplyEnabled = $state(basicConfig?.autoApplyEnabled || false);
  let salaryMin = $state(basicConfig?.salaryMin || '');
  let salaryMax = $state(basicConfig?.salaryMax || '');
  let experienceLevelMin = $state(basicConfig?.experienceLevelMin || '');
  let experienceLevelMax = $state(basicConfig?.experienceLevelMax || '');
  let jobTypes = $state<string[]>(basicConfig?.jobTypes || []);
  let remotePreference = $state(basicConfig?.remotePreference || 'any');
  let companySizePreference = $state<string[]>(basicConfig?.companySizePreference || []);
  let excludeCompanies = $state<string[]>(basicConfig?.excludeCompanies || []);
  let preferredCompanies = $state<string[]>(basicConfig?.preferredCompanies || []);

  // UI state
  let isSubmitting = $state(false);
  let newExcludeCompany = $state('');
  let newPreferredCompany = $state('');

  // Options
  const jobTypeOptions = [
    { value: 'full-time', label: 'Full-time' },
    { value: 'part-time', label: 'Part-time' },
    { value: 'contract', label: 'Contract' },
    { value: 'temporary', label: 'Temporary' },
    { value: 'internship', label: 'Internship' }
  ];

  const remoteOptions = [
    { value: 'any', label: 'Any' },
    { value: 'remote', label: 'Remote Only' },
    { value: 'hybrid', label: 'Hybrid' },
    { value: 'onsite', label: 'On-site Only' }
  ];

  const companySizeOptions = [
    { value: 'startup', label: 'Startup (1-10)' },
    { value: 'small', label: 'Small (11-50)' },
    { value: 'medium', label: 'Medium (51-200)' },
    { value: 'large', label: 'Large (201-1000)' },
    { value: 'enterprise', label: 'Enterprise (1000+)' }
  ];

  // Handle form submission
  async function handleSubmit() {
    isSubmitting = true;

    try {
      const fullConfig = {
        ...basicConfig,
        maxJobsToApply,
        minMatchScore,
        autoApplyEnabled,
        salaryMin: salaryMin ? parseInt(salaryMin) : null,
        salaryMax: salaryMax ? parseInt(salaryMax) : null,
        experienceLevelMin: experienceLevelMin ? parseInt(experienceLevelMin) : null,
        experienceLevelMax: experienceLevelMax ? parseInt(experienceLevelMax) : null,
        jobTypes,
        remotePreference,
        companySizePreference,
        excludeCompanies,
        preferredCompanies
      };

      await onSubmit(fullConfig);
      onClose();
    } catch (error) {
      console.error('Error submitting automation config:', error);
    } finally {
      isSubmitting = false;
    }
  }

  // Company management functions
  function addExcludeCompany() {
    if (newExcludeCompany.trim()) {
      excludeCompanies = [...excludeCompanies, newExcludeCompany.trim()];
      newExcludeCompany = '';
    }
  }

  function addPreferredCompany() {
    if (newPreferredCompany.trim()) {
      preferredCompanies = [...preferredCompanies, newPreferredCompany.trim()];
      newPreferredCompany = '';
    }
  }

  function removeExcludeCompany(index: number) {
    excludeCompanies = excludeCompanies.filter((_, i) => i !== index);
  }

  function removePreferredCompany(index: number) {
    preferredCompanies = preferredCompanies.filter((_, i) => i !== index);
  }

  // Selection toggle functions
  function toggleJobType(jobType: string) {
    if (jobTypes.includes(jobType)) {
      jobTypes = jobTypes.filter(t => t !== jobType);
    } else {
      jobTypes = [...jobTypes, jobType];
    }
  }

  function toggleCompanySize(size: string) {
    if (companySizePreference.includes(size)) {
      companySizePreference = companySizePreference.filter(s => s !== size);
    } else {
      companySizePreference = [...companySizePreference, size];
    }
  }
</script>

<Dialog.Root bind:open>
  <Dialog.Overlay />
  <Dialog.Content class="max-w-5xl max-h-[90vh] overflow-y-auto">
    <Dialog.Header>
      <Dialog.Title>Advanced Automation Settings</Dialog.Title>
      <Dialog.Description>
        Configure detailed job matching criteria and automation behavior.
      </Dialog.Description>
    </Dialog.Header>

    <Tabs.Root value="execution" class="w-full">
      <Tabs.List class="grid w-full grid-cols-4">
        <Tabs.Trigger value="execution" class="flex items-center gap-2">
          <Settings class="h-4 w-4" />
          Execution
        </Tabs.Trigger>
        <Tabs.Trigger value="matching" class="flex items-center gap-2">
          <Target class="h-4 w-4" />
          Matching
        </Tabs.Trigger>
        <Tabs.Trigger value="filters" class="flex items-center gap-2">
          <Filter class="h-4 w-4" />
          Filters
        </Tabs.Trigger>
        <Tabs.Trigger value="companies" class="flex items-center gap-2">
          <Building class="h-4 w-4" />
          Companies
        </Tabs.Trigger>
      </Tabs.List>

      <!-- Execution Settings -->
      <Tabs.Content value="execution" class="space-y-6 mt-6">
        <div class="grid gap-4">
          <div class="grid gap-2">
            <Label>Maximum Jobs to Apply: {maxJobsToApply}</Label>
            <Slider
              bind:value={maxJobsToApply}
              min={1}
              max={50}
              step={1}
              class="w-full" />
            <p class="text-sm text-gray-500">
              Limit the number of jobs to automatically apply to in this run.
            </p>
          </div>

          <div class="grid gap-2">
            <Label>Minimum Match Score: {minMatchScore}%</Label>
            <Slider
              bind:value={minMatchScore}
              min={50}
              max={95}
              step={5}
              class="w-full" />
            <p class="text-sm text-gray-500">
              Only apply to jobs with a match score above this threshold.
            </p>
          </div>

          <div class="flex items-center space-x-2">
            <Checkbox
              id="auto-apply"
              bind:checked={autoApplyEnabled} />
            <Label for="auto-apply">Enable automatic job applications</Label>
          </div>
          <p class="text-sm text-gray-500">
            When enabled, the system will automatically submit applications. 
            When disabled, it will only collect and score jobs for manual review.
          </p>
        </div>
      </Tabs.Content>

      <!-- Matching Criteria -->
      <Tabs.Content value="matching" class="space-y-6 mt-6">
        <div class="grid grid-cols-2 gap-4">
          <div class="grid gap-2">
            <Label for="salary-min">Minimum Salary ($)</Label>
            <Input
              id="salary-min"
              type="number"
              bind:value={salaryMin}
              placeholder="e.g. 80000" />
          </div>
          <div class="grid gap-2">
            <Label for="salary-max">Maximum Salary ($)</Label>
            <Input
              id="salary-max"
              type="number"
              bind:value={salaryMax}
              placeholder="e.g. 150000" />
          </div>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div class="grid gap-2">
            <Label for="exp-min">Minimum Experience (years)</Label>
            <Input
              id="exp-min"
              type="number"
              bind:value={experienceLevelMin}
              placeholder="e.g. 2" />
          </div>
          <div class="grid gap-2">
            <Label for="exp-max">Maximum Experience (years)</Label>
            <Input
              id="exp-max"
              type="number"
              bind:value={experienceLevelMax}
              placeholder="e.g. 10" />
          </div>
        </div>

        <div class="grid gap-2">
          <Label>Remote Work Preference</Label>
          <Select.Root
            type="single"
            value={remotePreference}
            onValueChange={(value) => {
              remotePreference = value || 'any';
            }}>
            <Select.Trigger>
              <Select.Value />
            </Select.Trigger>
            <Select.Content>
              {#each remoteOptions as option (option.value)}
                <Select.Item value={option.value}>
                  {option.label}
                </Select.Item>
              {/each}
            </Select.Content>
          </Select.Root>
        </div>
      </Tabs.Content>

      <!-- Job Filters -->
      <Tabs.Content value="filters" class="space-y-6 mt-6">
        <div class="grid gap-4">
          <div class="grid gap-2">
            <Label>Job Types</Label>
            <div class="flex flex-wrap gap-2">
              {#each jobTypeOptions as option (option.value)}
                <Button
                  variant={jobTypes.includes(option.value) ? 'default' : 'outline'}
                  size="sm"
                  onclick={() => toggleJobType(option.value)}>
                  {option.label}
                </Button>
              {/each}
            </div>
          </div>

          <div class="grid gap-2">
            <Label>Company Size Preference</Label>
            <div class="flex flex-wrap gap-2">
              {#each companySizeOptions as option (option.value)}
                <Button
                  variant={companySizePreference.includes(option.value) ? 'default' : 'outline'}
                  size="sm"
                  onclick={() => toggleCompanySize(option.value)}>
                  {option.label}
                </Button>
              {/each}
            </div>
          </div>
        </div>
      </Tabs.Content>

      <!-- Company Preferences -->
      <Tabs.Content value="companies" class="space-y-6 mt-6">
        <div class="grid gap-6">
          <!-- Preferred Companies -->
          <div class="grid gap-2">
            <Label>Preferred Companies</Label>
            <div class="flex gap-2">
              <Input
                bind:value={newPreferredCompany}
                placeholder="Add preferred company"
                onkeydown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addPreferredCompany();
                  }
                }} />
              <Button onclick={addPreferredCompany} size="sm">
                <Plus class="h-4 w-4" />
              </Button>
            </div>
            <div class="flex flex-wrap gap-2 mt-2">
              {#each preferredCompanies as company, index (index)}
                <Badge variant="secondary" class="flex items-center gap-1">
                  {company}
                  <Button
                    variant="ghost"
                    size="sm"
                    onclick={() => removePreferredCompany(index)}
                    class="h-4 w-4 p-0 hover:bg-transparent">
                    <X class="h-3 w-3" />
                  </Button>
                </Badge>
              {/each}
            </div>
          </div>

          <!-- Excluded Companies -->
          <div class="grid gap-2">
            <Label>Companies to Avoid</Label>
            <div class="flex gap-2">
              <Input
                bind:value={newExcludeCompany}
                placeholder="Add company to exclude"
                onkeydown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addExcludeCompany();
                  }
                }} />
              <Button onclick={addExcludeCompany} size="sm">
                <Plus class="h-4 w-4" />
              </Button>
            </div>
            <div class="flex flex-wrap gap-2 mt-2">
              {#each excludeCompanies as company, index (index)}
                <Badge variant="destructive" class="flex items-center gap-1">
                  {company}
                  <Button
                    variant="ghost"
                    size="sm"
                    onclick={() => removeExcludeCompany(index)}
                    class="h-4 w-4 p-0 hover:bg-transparent">
                    <X class="h-3 w-3" />
                  </Button>
                </Badge>
              {/each}
            </div>
          </div>
        </div>
      </Tabs.Content>
    </Tabs.Root>

    <Dialog.Footer class="mt-6">
      <Button variant="outline" onclick={onClose}>Cancel</Button>
      <Button
        variant="default"
        onclick={handleSubmit}
        disabled={isSubmitting}>
        {#if isSubmitting}
          Starting Automation...
        {:else}
          Start Automation Run
        {/if}
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
