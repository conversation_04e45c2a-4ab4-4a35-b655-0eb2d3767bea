import { logger } from '../../utils/logger.js';

interface ReadabilityAnalysisResult {
  score: number;
  suggestions: string[];
}

export function analyzeReadability(text: string): ReadabilityAnalysisResult {
  try {
    const suggestions: string[] = [];
    let score = 100; // Start with perfect score and deduct for issues
    
    // Calculate basic metrics
    const sentences = getSentences(text);
    const words = getWords(text);
    const sentenceCount = sentences.length;
    const wordCount = words.length;
    const avgWordsPerSentence = wordCount / Math.max(1, sentenceCount);
    
    // Check sentence length
    if (avgWordsPerSentence > 25) {
      suggestions.push('Sentences are too long (average > 25 words). Use shorter, clearer sentences.');
      score -= 10;
    }
    
    // Check for passive voice
    const passiveVoiceCount = countPassiveVoice(sentences);
    const passiveVoicePercentage = (passiveVoiceCount / Math.max(1, sentenceCount)) * 100;
    
    if (passiveVoicePercentage > 20) {
      suggestions.push('Too many sentences use passive voice. Use active voice for stronger impact.');
      score -= 10;
    }
    
    // Check for weak verbs
    const weakVerbCount = countWeakVerbs(words);
    const weakVerbPercentage = (weakVerbCount / Math.max(1, wordCount)) * 100;
    
    if (weakVerbPercentage > 5) {
      suggestions.push('Too many weak verbs. Replace with stronger, more specific action verbs.');
      score -= 10;
    }
    
    // Check for filler words
    const fillerWordCount = countFillerWords(words);
    const fillerWordPercentage = (fillerWordCount / Math.max(1, wordCount)) * 100;
    
    if (fillerWordPercentage > 3) {
      suggestions.push('Too many filler words. Remove unnecessary words to make your resume more concise.');
      score -= 10;
    }
    
    // Check for jargon and complex words
    const complexWordCount = countComplexWords(words);
    const complexWordPercentage = (complexWordCount / Math.max(1, wordCount)) * 100;
    
    if (complexWordPercentage > 15) {
      suggestions.push('Too many complex words or jargon. Use simpler language unless they are industry-specific terms.');
      score -= 10;
    }
    
    // Check for bullet point consistency
    if (!hasBulletPointConsistency(text)) {
      suggestions.push('Inconsistent bullet point structure. Ensure all bullet points follow similar grammatical structure.');
      score -= 10;
    }
    
    // Check for redundancy
    if (hasRedundancy(text)) {
      suggestions.push('Contains redundant phrases or repetition. Remove redundancy to improve clarity.');
      score -= 10;
    }
    
    // Ensure score is between 0 and 100
    score = Math.max(0, Math.min(100, score));
    
    return {
      score,
      suggestions,
    };
  } catch (error) {
    logger.error('Error analyzing readability:', error);
    return {
      score: 70, // Default score
      suggestions: ['Unable to fully analyze resume readability'],
    };
  }
}

function getSentences(text: string): string[] {
  // Simple sentence splitting - not perfect but good enough for this purpose
  return text
    .replace(/([.!?])\s*(?=[A-Z])/g, '$1|')
    .split('|')
    .filter(s => s.trim().length > 0);
}

function getWords(text: string): string[] {
  return text
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(w => w.length > 0);
}

function countPassiveVoice(sentences: string[]): number {
  // Simple passive voice detection - not perfect but catches common cases
  const passivePatterns = [
    /\b(?:is|are|was|were|be|been|being)\s+\w+ed\b/i,
    /\b(?:is|are|was|were|be|been|being)\s+\w+en\b/i,
  ];
  
  let count = 0;
  for (const sentence of sentences) {
    if (passivePatterns.some(pattern => pattern.test(sentence))) {
      count++;
    }
  }
  
  return count;
}

function countWeakVerbs(words: string[]): number {
  const weakVerbs = [
    'is', 'are', 'was', 'were', 'be', 'been', 'being',
    'have', 'has', 'had', 'do', 'does', 'did',
    'go', 'went', 'gone', 'get', 'got', 'gotten',
    'make', 'made', 'put', 'set', 'came', 'take', 'took',
  ];
  
  return words.filter(word => weakVerbs.includes(word)).length;
}

function countFillerWords(words: string[]): number {
  const fillerWords = [
    'very', 'really', 'quite', 'basically', 'actually', 'literally',
    'just', 'simply', 'that', 'totally', 'definitely', 'certainly',
    'kind of', 'sort of', 'type of', 'perhaps', 'maybe', 'probably',
  ];
  
  return words.filter(word => fillerWords.includes(word)).length;
}

function countComplexWords(words: string[]): number {
  // Count words with more than 3 syllables as complex
  let count = 0;
  
  for (const word of words) {
    if (countSyllables(word) > 3 && !isCommonTechnicalTerm(word)) {
      count++;
    }
  }
  
  return count;
}

function countSyllables(word: string): number {
  // Simple syllable counting - not perfect but good enough for this purpose
  word = word.toLowerCase();
  if (word.length <= 3) return 1;
  
  // Remove es, ed at the end
  word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '');
  word = word.replace(/^y/, '');
  
  // Count vowel groups
  const syllables = word.match(/[aeiouy]{1,2}/g);
  return syllables ? syllables.length : 1;
}

function isCommonTechnicalTerm(word: string): boolean {
  const technicalTerms = [
    'technology', 'development', 'programming', 'javascript', 'typescript',
    'python', 'analysis', 'engineering', 'management', 'professional',
    'experience', 'education', 'university', 'technical', 'application',
    'implementation', 'infrastructure', 'architecture', 'organization',
  ];
  
  return technicalTerms.includes(word.toLowerCase());
}

function hasBulletPointConsistency(text: string): boolean {
  const lines = text.split('\n');
  const bulletLines = lines.filter(line => 
    /^[ \t]*[•\-\*\+]/.test(line) || /^[ \t]*\d+[\.\)]/.test(line)
  );
  
  if (bulletLines.length < 3) return true; // Not enough bullet points to check
  
  // Check if bullet points start with the same part of speech (verb, noun, etc.)
  const startsWithVerb = bulletLines.filter(line => {
    const firstWord = line.replace(/^[ \t]*[•\-\*\+\d\.\)]+\s*/, '').split(' ')[0].toLowerCase();
    return isVerb(firstWord);
  }).length;
  
  // If more than 70% of bullet points follow the same pattern, consider it consistent
  return startsWithVerb > bulletLines.length * 0.7 || startsWithVerb < bulletLines.length * 0.3;
}

function isVerb(word: string): boolean {
  const commonVerbs = [
    'managed', 'developed', 'created', 'implemented', 'led', 'achieved',
    'improved', 'increased', 'reduced', 'negotiated', 'coordinated',
    'designed', 'launched', 'built', 'delivered', 'organized', 'planned',
    'produced', 'provided', 'resolved', 'supervised', 'trained', 'wrote',
    'analyzed', 'conducted', 'established', 'generated', 'maintained',
    'performed', 'prepared', 'presented', 'processed', 'recommended',
    'researched', 'reviewed', 'supported', 'tested', 'won', 'worked',
  ];
  
  return commonVerbs.includes(word.toLowerCase());
}

function hasRedundancy(text: string): boolean {
  const redundantPhrases = [
    'each and every',
    'first and foremost',
    'various different',
    'basic fundamentals',
    'true facts',
    'past experience',
    'future plans',
    'unexpected surprise',
    'end result',
    'final outcome',
    'collaborate together',
    'repeat again',
    'completely eliminate',
    'advance planning',
    'continue on',
  ];
  
  for (const phrase of redundantPhrases) {
    if (text.toLowerCase().includes(phrase)) {
      return true;
    }
  }
  
  // Check for word repetition in close proximity
  const words = getWords(text);
  for (let i = 0; i < words.length - 5; i++) {
    const currentWord = words[i];
    if (currentWord.length < 4) continue; // Skip short words
    
    // Check next 5 words for repetition
    for (let j = i + 1; j < i + 6 && j < words.length; j++) {
      if (words[j] === currentWord) {
        return true;
      }
    }
  }
  
  return false;
}
