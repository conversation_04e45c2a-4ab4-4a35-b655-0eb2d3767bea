import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types.js';

export const GET: RequestHandler = async ({ params, locals }) => {
  const user = locals.user;
  if (!user) return new Response('Unauthorized', { status: 401 });

  const resumeId = params.id;
  if (!resumeId) {
    return new Response('Resume ID is required', { status: 400 });
  }

  try {
    // Get the resume with its document
    const resume = await prisma.resume.findUnique({
      where: { id: resumeId },
      include: {
        document: true,
      },
    });

    if (!resume) {
      return new Response('Resume not found', { status: 404 });
    }

    // Check if the document belongs to the user
    if (resume.document.userId !== user.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Return the parsing status
    return new Response(
      JSON.stringify({
        isParsed: resume.isParsed,
        parsedAt: resume.parsedAt,
        parsedData: resume.parsedData,
        error: resume.isParsed === false && resume.parsedAt !== null ? 'Failed to parse resume' : null,
      }),
      {
        headers: { 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    console.error('Error checking resume parsing status:', error);
    return new Response('Internal server error', { status: 500 });
  }
};
