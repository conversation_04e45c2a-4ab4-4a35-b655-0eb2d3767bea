/**
 * Logger Utility for AI Service
 *
 * Standardized logger implementation that matches the format used in other services
 */

import { config } from "../config.js";

// Log levels
export enum LogLevel {
  DEBUG = "debug",
  INFO = "info",
  WARN = "warn",
  ERROR = "error",
}

// Get current log level from environment
const currentLogLevel = (
  config.logging.level || "info"
).toLowerCase() as LogLevel;

// Log level priorities
const logLevelPriority: Record<LogLevel, number> = {
  [LogLevel.DEBUG]: 0,
  [LogLevel.INFO]: 1,
  [LogLevel.WARN]: 2,
  [LogLevel.ERROR]: 3,
};

// Helper function to check if we should log at this level
function shouldLog(level: LogLevel): boolean {
  return logLevelPriority[level] >= logLevelPriority[currentLogLevel];
}

// Logger implementation
export const logger = {
  debug(message: string, ...args: any[]): void {
    if (shouldLog(LogLevel.DEBUG)) {
      console.debug(`[DEBUG] ${message}`, ...args);
    }
  },

  info(message: string, ...args: any[]): void {
    if (shouldLog(LogLevel.INFO)) {
      console.info(`[INFO] ${message}`, ...args);
    }
  },

  warn(message: string, ...args: any[]): void {
    if (shouldLog(LogLevel.WARN)) {
      console.warn(`[WARN] ${message}`, ...args);
    }
  },

  error(message: string, ...args: any[]): void {
    if (shouldLog(LogLevel.ERROR)) {
      console.error(`[ERROR] ${message}`, ...args);
    }
  },
};

// Handle uncaught exceptions and unhandled rejections
process.on("unhandledRejection", (reason) => {
  logger.error(`Unhandled Promise Rejection: ${reason}`);
});

process.on("uncaughtException", (error) => {
  logger.error(`Uncaught Exception: ${error}`);
});

export default logger;
