-- AlterTable
ALTER TABLE "web"."AutomationRun" ADD COLUMN     "specifications" JSONB,
ADD COLUMN     "maxJobsToApply" INTEGER DEFAULT 10,
ADD COLUMN     "minMatchScore" DOUBLE PRECISION DEFAULT 70,
ADD COLUMN     "autoApplyEnabled" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "salaryMin" INTEGER,
ADD COLUMN     "salaryMax" INTEGER,
ADD COLUMN     "experienceLevelMin" INTEGER,
ADD COLUMN     "experienceLevelMax" INTEGER,
ADD COLUMN     "jobTypes" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "remotePreference" TEXT,
ADD COLUMN     "companySizePreference" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "excludeCompanies" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "preferredCompanies" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "jobsFound" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "jobsApplied" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "jobsSkipped" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "avgMatchScore" DOUBLE PRECISION;

-- AlterTable
ALTER TABLE "web"."JobListing" ADD COLUMN     "matchScore" DOUBLE PRECISION,
ADD COLUMN     "skillsMatch" JSONB,
ADD COLUMN     "experienceMatch" JSONB,
ADD COLUMN     "salaryMatch" BOOLEAN,
ADD COLUMN     "locationMatch" BOOLEAN,
ADD COLUMN     "applicationStatus" TEXT,
ADD COLUMN     "appliedAt" TIMESTAMP(3),
ADD COLUMN     "applicationId" TEXT;

-- CreateTable
CREATE TABLE "web"."AutomationApplication" (
    "id" TEXT NOT NULL,
    "automationRunId" TEXT NOT NULL,
    "jobListingId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "profileId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "applicationData" JSONB,
    "externalId" TEXT,
    "matchScore" DOUBLE PRECISION NOT NULL,
    "matchReasons" JSONB,
    "submittedAt" TIMESTAMP(3),
    "responseAt" TIMESTAMP(3),
    "responseStatus" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AutomationApplication_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "AutomationApplication_automationRunId_jobListingId_key" ON "web"."AutomationApplication"("automationRunId", "jobListingId");

-- AddForeignKey
ALTER TABLE "web"."AutomationApplication" ADD CONSTRAINT "AutomationApplication_automationRunId_fkey" FOREIGN KEY ("automationRunId") REFERENCES "web"."AutomationRun"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "web"."AutomationApplication" ADD CONSTRAINT "AutomationApplication_jobListingId_fkey" FOREIGN KEY ("jobListingId") REFERENCES "web"."JobListing"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "web"."AutomationApplication" ADD CONSTRAINT "AutomationApplication_userId_fkey" FOREIGN KEY ("userId") REFERENCES "web"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "web"."AutomationApplication" ADD CONSTRAINT "AutomationApplication_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "web"."Profile"("id") ON UPDATE CASCADE;

-- AlterTable for job_listing enhancements
ALTER TABLE "cron"."job_listing" ADD COLUMN     "normalizedTitle" TEXT,
ADD COLUMN     "seniorityLevel" TEXT,
ADD COLUMN     "techStack" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "industryTags" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "companySize" TEXT,
ADD COLUMN     "isRemoteFriendly" BOOLEAN,
ADD COLUMN     "requirementScore" INTEGER DEFAULT 0,
ADD COLUMN     "popularityScore" DOUBLE PRECISION DEFAULT 0,
ADD COLUMN     "lastMatchedAt" TIMESTAMP(3),
ADD COLUMN     "matchCount" INTEGER NOT NULL DEFAULT 0;

-- CreateIndex for faster job matching
CREATE INDEX "job_listing_normalizedTitle_seniorityLevel_idx" ON "cron"."job_listing"("normalizedTitle", "seniorityLevel");
CREATE INDEX "job_listing_techStack_idx" ON "cron"."job_listing"("techStack");
CREATE INDEX "job_listing_salaryMin_salaryMax_idx" ON "cron"."job_listing"("salaryMin", "salaryMax");
CREATE INDEX "job_listing_yearsOfExperience_seniorityLevel_idx" ON "cron"."job_listing"("yearsOfExperience", "seniorityLevel");
CREATE INDEX "job_listing_isRemoteFriendly_remoteType_idx" ON "cron"."job_listing"("isRemoteFriendly", "remoteType");
CREATE INDEX "job_listing_companySize_idx" ON "cron"."job_listing"("companySize");
CREATE INDEX "job_listing_lastMatchedAt_idx" ON "cron"."job_listing"("lastMatchedAt");
