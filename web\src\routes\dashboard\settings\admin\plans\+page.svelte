<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { ArrowRight, Save, RefreshCw, ExternalLink, ChevronDown } from 'lucide-svelte';
  import SEO from '$components/shared/SEO.svelte';
  import { FeatureAccessLevel } from '$lib/models/features';
  import { onMount } from 'svelte';
  import { toast } from 'svelte-sonner';

  // Import tab content components
  import FeaturesTabContent from './components/FeaturesTabContent.svelte';
  import PlanDetailsTabContent from './components/PlanDetailsTabContent.svelte';
  import PreviewTabContent from './components/PreviewTabContent.svelte';

  // Plans data
  let editablePlans = [];
  let loading = true;
  let error = null;
  let syncingWithStripe = false;
  let isSyncingFeatures = false;
  let stripeMessage = '';

  // Selected plan for editing
  let selectedPlanId = '';
  $: selectedPlan = editablePlans.find((plan) => plan.id === selectedPlanId);

  // Load plans from the database
  async function loadPlans() {
    try {
      loading = true;
      error = null;

      // First, initialize the database with plans if they don't exist
      try {
        const initResponse = await fetch('/api/admin/plans/initialize', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        });

        if (!initResponse.ok) {
          console.warn(`Failed to initialize plans: ${initResponse.status}`);
          // Continue anyway - we'll try to load plans
        }
      } catch (initError) {
        console.warn('Error initializing plans:', initError);
        // Continue anyway - we'll try to load plans
      }

      // Fetch plans from the API
      const response = await fetch('/api/admin/plans', {
        credentials: 'include',
      });

      let responseText;
      try {
        responseText = await response.text();
      } catch (e) {
        responseText = '';
      }

      if (!response.ok) {
        // Try to get error details
        let errorMessage = 'Failed to load plans';

        if (responseText) {
          try {
            const errorData = JSON.parse(responseText);
            errorMessage = errorData.error || errorMessage;
          } catch (parseError) {
            // If we can't parse as JSON, use the text as the error message
            errorMessage = responseText;
          }
        }

        throw new Error(errorMessage);
      }

      // Parse the response text as JSON
      try {
        editablePlans = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing plans JSON:', parseError);
        throw new Error('Invalid response format from server');
      }

      // Remove support features from all plans
      const supportFeatureIds = ['email_support', 'priority_support', 'dedicated_support'];
      editablePlans.forEach((plan) => {
        plan.features = plan.features.filter(
          (feature) => !supportFeatureIds.includes(feature.featureId)
        );
      });

      if (editablePlans.length > 0) {
        selectedPlanId = editablePlans[0].id;
        // No automatic syncing - user must use the Sync Features button
      }
    } catch (err) {
      console.error('Error loading plans:', err);
      error = err.message;
      // Create default plans as fallback
      editablePlans = [
        {
          id: 'free',
          name: 'Free',
          description: 'Basic features for personal use',
          section: 'pro',
          monthlyPrice: 0,
          annualPrice: 0,
          features: [],
        },
        {
          id: 'casual',
          name: 'Casual',
          description: 'For occasional job seekers',
          section: 'pro',
          monthlyPrice: 999,
          annualPrice: 9990,
          features: [],
        },
      ];

      // Remove support features from all plans
      const supportFeatureIds = ['email_support', 'priority_support', 'dedicated_support'];
      editablePlans.forEach((plan) => {
        plan.features = plan.features.filter(
          (feature) => !supportFeatureIds.includes(feature.featureId)
        );
      });

      selectedPlanId = editablePlans[0].id;
    } finally {
      loading = false;
    }
  }

  // Load plans and features on component mount
  onMount(() => {
    loadPlans();
    loadFeatures();
  });

  // Features data
  let allFeatures = [];
  let featuresByCategory = {};
  let expandedCategories: string[] = [];

  // Load features from the database
  async function loadFeatures() {
    try {
      const response = await fetch('/api/admin/features', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to load features');
      }

      const data = await response.json();
      allFeatures = data.features || [];

      // Group features by category
      featuresByCategory = allFeatures.reduce((acc, feature) => {
        if (!acc[feature.category]) {
          acc[feature.category] = [];
        }
        acc[feature.category].push(feature);
        return acc;
      }, {});

      // Set only the first category as expanded
      const categoryKeys = Object.keys(featuresByCategory);
      if (categoryKeys.length > 0) {
        expandedCategories = [categoryKeys[0]];
      } else {
        expandedCategories = [];
      }

      console.log(
        `Loaded ${allFeatures.length} features in ${expandedCategories.length} categories`
      );

      // No automatic syncing - user must use the Sync Features button
    } catch (err) {
      console.error('Error loading features:', err);
      // Create empty categories as fallback
      featuresByCategory = {};
      expandedCategories = [];

      // Show error to user
      toast.error('Failed to load features', {
        description: 'Please try refreshing the page or contact support if the issue persists.',
        duration: 5000,
      });
    }
  }

  // Get feature access level
  function getFeatureAccessLevel(featureId: string): FeatureAccessLevel {
    if (!selectedPlan) return FeatureAccessLevel.NotIncluded;

    const planFeature = selectedPlan.features.find((f: any) => f.featureId === featureId);
    return planFeature?.accessLevel || FeatureAccessLevel.NotIncluded;
  }

  // Get feature limit value
  function getFeatureLimitValue(
    featureId: string,
    limitId: string
  ): number | 'unlimited' | undefined {
    if (!selectedPlan) return undefined;

    const planFeature = selectedPlan.features.find((f: any) => f.featureId === featureId);
    if (!planFeature || planFeature.accessLevel !== FeatureAccessLevel.Limited) return undefined;

    const limitValue = planFeature.limits?.find((l: any) => l.limitId === limitId);
    return limitValue?.value;
  }

  // Update feature access level
  async function updateFeatureAccessLevel(featureId: string, accessLevel: FeatureAccessLevel) {
    if (!selectedPlan) return;

    // Find the feature in the plan
    const featureIndex = selectedPlan.features.findIndex((f: any) => f.featureId === featureId);
    let limits: { limitId: string; value: number | 'unlimited' }[] | undefined;

    if (featureIndex >= 0) {
      // Update existing feature in local state
      selectedPlan.features[featureIndex].accessLevel = accessLevel;

      // If the feature is not limited, remove limits
      if (accessLevel !== FeatureAccessLevel.Limited) {
        selectedPlan.features[featureIndex].limits = undefined;
      } else if (!selectedPlan.features[featureIndex].limits) {
        // If the feature is limited but has no limits, add default limits
        const feature = allFeatures.find((f: any) => f.id === featureId);
        if (feature?.limits && feature.limits.length > 0) {
          // Get limits from the database feature
          limits = feature.limits.map((limit: any) => ({
            limitId: limit.id,
            value: limit.defaultValue || 10,
          }));
          selectedPlan.features[featureIndex].limits = limits;
        } else {
          // If no limits defined in the feature, create some default ones
          limits = [
            { limitId: 'monthly_usage', value: 10 },
            { limitId: 'max_items', value: 5 },
          ];
          selectedPlan.features[featureIndex].limits = limits;
        }
      } else {
        // Use existing limits
        limits = selectedPlan.features[featureIndex].limits;
      }
    } else {
      // Add new feature to local state
      const newFeature: {
        featureId: string;
        accessLevel: FeatureAccessLevel;
        limits?: { limitId: string; value: number | 'unlimited' }[];
      } = {
        featureId,
        accessLevel,
      };

      // If the feature is limited, add default limits
      if (accessLevel === FeatureAccessLevel.Limited) {
        const feature = allFeatures.find((f: any) => f.id === featureId);
        if (feature?.limits && feature.limits.length > 0) {
          // Get limits from the database feature
          limits = feature.limits.map((limit: any) => ({
            limitId: limit.id,
            value: limit.defaultValue || 10,
          }));
          newFeature.limits = limits;
        } else {
          // If no limits defined in the feature, create some default ones
          limits = [
            { limitId: 'monthly_usage', value: 10 },
            { limitId: 'max_items', value: 5 },
          ];
          newFeature.limits = limits;
        }
      }

      selectedPlan.features.push(newFeature);
    }

    // Force reactivity update
    selectedPlan = { ...selectedPlan };

    // Save the change to the database
    try {
      const response = await fetch('/api/admin/plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'update_feature',
          planId: selectedPlan.id,
          featureId,
          accessLevel,
          limits,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error updating feature:', errorText);
        toast.error('Failed to update feature', {
          description: errorText || 'An error occurred while updating the feature',
          duration: 5000,
        });
        // Reload plans to get the current state
        await loadPlans();
      } else {
        const result = await response.json();
        toast.success('Feature updated', {
          description: result.message || `Feature ${featureId} updated successfully`,
          duration: 3000,
        });
      }
    } catch (error) {
      console.error('Error updating feature:', error);
      toast.error('Failed to update feature', {
        description: error.message || 'An error occurred while updating the feature',
        duration: 5000,
      });
      // Reload plans to get the current state
      await loadPlans();
    }
  }

  // Update feature limit value
  async function updateFeatureLimitValue(
    featureId: string,
    limitId: string,
    value: number | 'unlimited'
  ) {
    if (!selectedPlan) return;

    // Find the feature in the plan
    const featureIndex = selectedPlan.features.findIndex((f: any) => f.featureId === featureId);

    if (
      featureIndex >= 0 &&
      selectedPlan.features[featureIndex].accessLevel === FeatureAccessLevel.Limited
    ) {
      // Ensure limits array exists
      if (!selectedPlan.features[featureIndex].limits) {
        selectedPlan.features[featureIndex].limits = [];
      }

      // Find the limit
      const limitIndex = selectedPlan.features[featureIndex].limits.findIndex(
        (l: any) => l.limitId === limitId
      );

      if (limitIndex >= 0) {
        // Update existing limit
        selectedPlan.features[featureIndex].limits[limitIndex].value = value;
      } else {
        // Add new limit
        selectedPlan.features[featureIndex].limits.push({
          limitId,
          value,
        });
      }

      // Force reactivity update
      selectedPlan = { ...selectedPlan };

      // Save the change to the database
      try {
        const response = await fetch('/api/admin/plans', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            action: 'update_feature',
            planId: selectedPlan.id,
            featureId,
            accessLevel: FeatureAccessLevel.Limited,
            limits: selectedPlan.features[featureIndex].limits,
          }),
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error updating feature limit:', errorText);
          toast.error('Failed to update limit', {
            description: errorText || 'An error occurred while updating the limit',
            duration: 5000,
          });
          // Reload plans to get the current state
          await loadPlans();
        } else {
          const result = await response.json();
          toast.success('Limit updated', {
            description: result.message || `Limit ${limitId} updated successfully`,
            duration: 3000,
          });
        }
      } catch (error) {
        console.error('Error updating feature limit:', error);
        toast.error('Failed to update limit', {
          description: error.message || 'An error occurred while updating the limit',
          duration: 5000,
        });
        // Reload plans to get the current state
        await loadPlans();
      }
    }
  }

  // Save changes
  async function saveChanges() {
    try {
      // Save plans to the database
      const response = await fetch('/api/admin/plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ plans: editablePlans }),
      });

      let responseText;
      try {
        // Try to get the response as text first
        responseText = await response.text();
      } catch (e) {
        responseText = '';
      }

      if (response.ok) {
        try {
          const result = JSON.parse(responseText);
          if (result.success) {
            toast.success('Changes saved successfully!', {
              description: 'All plan changes have been saved to the database',
              duration: 3000,
            });
          } else {
            toast.error('Failed to save plans', {
              description: result.error || 'An error occurred while saving plans',
              duration: 5000,
            });
          }
        } catch (parseError) {
          // If we can't parse the response as JSON but it's still a success
          toast.success('Changes saved successfully!', {
            description: 'All plan changes have been saved to the database',
            duration: 3000,
          });
        }
      } else {
        // Handle error response
        let errorMessage = 'Failed to save plans';

        if (responseText) {
          try {
            // Try to parse the error text as JSON
            const errorResult = JSON.parse(responseText);
            errorMessage = errorResult.error || errorMessage;
          } catch (e) {
            // If it's not valid JSON, use the text as the error message
            errorMessage = responseText;
          }
        }

        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Error saving plans:', error);
      toast.error('Failed to save plans', {
        description: error.message || 'An unexpected error occurred',
        duration: 5000,
      });
    }
  }

  // Reset changes
  async function resetChanges() {
    try {
      loading = true;

      // Fetch plans from the API
      const response = await fetch('/api/admin/plans');
      if (!response.ok) {
        throw new Error('Failed to load plans');
      }

      editablePlans = await response.json();

      // Remove support features from all plans
      const supportFeatureIds = ['email_support', 'priority_support', 'dedicated_support'];
      editablePlans.forEach((plan) => {
        plan.features = plan.features.filter(
          (feature: any) => !supportFeatureIds.includes(feature.featureId)
        );
      });

      selectedPlan = editablePlans.find((plan) => plan.id === selectedPlanId);
    } catch (err) {
      console.error('Error resetting plans:', err);
      // Create default plans as fallback
      editablePlans = [
        {
          id: 'free',
          name: 'Free',
          description: 'Basic features for personal use',
          section: 'pro',
          monthlyPrice: 0,
          annualPrice: 0,
          features: [],
        },
        {
          id: 'casual',
          name: 'Casual',
          description: 'For occasional job seekers',
          section: 'pro',
          monthlyPrice: 999,
          annualPrice: 9990,
          features: [],
        },
      ];
      selectedPlan = editablePlans.find((plan) => plan.id === selectedPlanId);
    } finally {
      loading = false;
    }
  }

  // Format price
  function formatPrice(cents: number): string {
    return (cents / 100).toFixed(2);
  }

  // Confirm before saving changes
  function confirmSaveChanges() {
    if (
      confirm(
        'Are you sure you want to save these changes? This will update the subscription plans for all users.'
      )
    ) {
      saveChanges();
    }
  }

  // Sync plan with Stripe
  async function syncPlanWithStripe() {
    if (!selectedPlan) return;

    try {
      syncingWithStripe = true;
      stripeMessage = '';

      const response = await fetch('/api/admin/plans/sync-stripe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          planId: selectedPlan.id,
        }),
      });

      let responseText;
      try {
        // Try to get the response as text first
        responseText = await response.text();
      } catch (e) {
        responseText = '';
      }

      // Check if response is ok
      if (response.ok) {
        let result;
        try {
          // Try to parse the text as JSON
          result = JSON.parse(responseText);

          if (result.success && result.plan) {
            // Update the plan with the new Stripe price IDs
            selectedPlan.stripePriceMonthlyId = result.plan.stripePriceMonthlyId;
            selectedPlan.stripePriceYearlyId = result.plan.stripePriceYearlyId;
            selectedPlan = { ...selectedPlan };

            stripeMessage = result.message || 'Plan synced with Stripe successfully';
            toast.success('Plan synced with Stripe', {
              description: stripeMessage,
              duration: 3000,
            });
          } else {
            stripeMessage = result.error || 'Failed to sync plan with Stripe';
            toast.error('Failed to sync plan with Stripe', {
              description: stripeMessage,
              duration: 5000,
            });
          }
        } catch (parseError) {
          // If it's not valid JSON, use a default success message
          stripeMessage = 'Plan synced with Stripe successfully';
          toast.success('Plan synced with Stripe', {
            description: stripeMessage,
            duration: 3000,
          });
        }
      } else {
        // Handle error response
        let errorMessage = 'Failed to sync plan with Stripe';

        if (responseText) {
          try {
            // Try to parse the error text as JSON
            const errorResult = JSON.parse(responseText);
            errorMessage = errorResult.error || errorMessage;
          } catch (e) {
            // If it's not valid JSON, use the text as the error message
            errorMessage = responseText;
          }
        }

        stripeMessage = errorMessage;
        alert(`Error: ${stripeMessage} (Status: ${response.status})`);
      }
    } catch (error) {
      console.error('Error syncing plan with Stripe:', error);
      stripeMessage = error.message;
      toast.error('Failed to sync plan with Stripe', {
        description: error.message || 'An unexpected error occurred',
        duration: 5000,
      });
    } finally {
      syncingWithStripe = false;
    }
  }

  // Sync all plans with Stripe
  async function syncAllPlansWithStripe() {
    try {
      syncingWithStripe = true;
      stripeMessage = '';

      const response = await fetch('/api/admin/plans/sync-stripe', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      let responseText;
      try {
        // Try to get the response as text first
        responseText = await response.text();
      } catch (e) {
        responseText = '';
      }

      // Check if response is ok
      if (response.ok) {
        let result;
        try {
          // Try to parse the text as JSON
          result = JSON.parse(responseText);
        } catch (e) {
          // If it's not valid JSON, use a default success message
          result = { message: 'Plans synced with Stripe successfully' };
        }

        // Reload plans to get the updated data
        await loadPlans();

        stripeMessage = result.message || 'Plans synced with Stripe successfully';
        toast.success('Plans synced with Stripe', {
          description: stripeMessage,
          duration: 3000,
        });
      } else {
        // Handle error response
        let errorMessage = 'Failed to sync plans with Stripe';

        if (responseText) {
          try {
            // Try to parse the error text as JSON
            const errorResult = JSON.parse(responseText);
            errorMessage = errorResult.error || errorMessage;
          } catch (e) {
            // If it's not valid JSON, use the text as the error message
            errorMessage = responseText;
          }
        }

        stripeMessage = errorMessage;
        alert(`Error: ${stripeMessage} (Status: ${response.status})`);
      }
    } catch (error) {
      console.error('Error syncing plans with Stripe:', error);
      stripeMessage = error.message;
      toast.error('Failed to sync plans with Stripe', {
        description: error.message || 'An unexpected error occurred',
        duration: 5000,
      });
    } finally {
      syncingWithStripe = false;
    }
  }

  // No local syncing function - removed to avoid overwriting existing features

  // Sync features with server
  async function syncFeatures() {
    try {
      // Confirm with the user before proceeding
      if (!confirm('This will sync features with plans in the database. Continue?')) {
        return;
      }

      isSyncingFeatures = true;
      toast.loading('Syncing features with plans...');

      const response = await fetch('/api/admin/features', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'sync_features',
          // Add a flag to indicate this is a manual sync
          manual: true,
        }),
      });

      console.log('Sync features response status:', response.status);

      if (!response.ok) {
        console.error('Sync features response not OK:', response.statusText);
        toast.dismiss();
        toast.error('Failed to sync features', {
          description: `Error: ${response.status} ${response.statusText}`,
          duration: 5000,
        });
        return;
      }

      const result = await response.json();
      console.log('Sync features result:', result);

      if (result.success) {
        toast.dismiss();
        toast.success('Features synced successfully', {
          description: result.message || 'All features have been synced across plans',
          duration: 3000,
        });

        // Reload plans to get the updated data
        await loadPlans();
      } else {
        toast.dismiss();
        toast.error('Failed to sync features', {
          description: result.error || 'An error occurred while syncing features',
          duration: 5000,
        });
      }
    } catch (error) {
      console.error('Error syncing features:', error);
      toast.dismiss();
      toast.error('Failed to sync features', {
        description: error.message || 'An unexpected error occurred',
        duration: 5000,
      });
    } finally {
      isSyncingFeatures = false;
    }
  }

  // Load plans from Stripe
  async function loadPlansFromStripe() {
    try {
      loading = true;
      error = null;

      const response = await fetch('/api/admin/plans/load-from-stripe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      let responseText;
      try {
        // Try to get the response as text first
        responseText = await response.text();
      } catch (e) {
        responseText = '';
      }

      // Check if response is ok
      if (response.ok) {
        let result;
        try {
          // Try to parse the text as JSON
          result = JSON.parse(responseText);
        } catch (e) {
          // If it's not valid JSON, use a default success message
          result = { message: 'Successfully loaded plans from Stripe' };
        }

        // Reload plans to get the updated data
        await loadPlans();

        toast.success('Plans loaded from Stripe', {
          description: result.message || `Successfully loaded ${result.count} plans from Stripe`,
          duration: 3000,
        });
      } else {
        // Handle error response
        let errorMessage = 'Failed to load plans from Stripe';

        if (responseText) {
          try {
            // Try to parse the error text as JSON
            const errorResult = JSON.parse(responseText);
            errorMessage = errorResult.error || errorMessage;
          } catch (e) {
            // If it's not valid JSON, use the text as the error message
            errorMessage = responseText;
          }
        }

        error = errorMessage;
        alert(`Error: ${error} (Status: ${response.status})`);
      }
    } catch (error) {
      console.error('Error loading plans from Stripe:', error);
      alert(`Error loading plans from Stripe: ${error.message}`);
    } finally {
      loading = false;
    }
  }
</script>

<SEO title="Plan Management" />

<div class="flex items-center justify-between gap-1 border-b px-4 py-2">
  <h2 class="text-lg font-semibold">Plan Management</h2>
  <div class="space-y-4">
    <div class="flex gap-2">
      <DropdownMenu.Root>
        <DropdownMenu.Trigger>
          <Button variant="outline">
            Actions
            <ChevronDown class="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content align="end">
          <DropdownMenu.Item
            onclick={() => (window.location.href = '/dashboard/settings/admin/plans/view')}>
            View Plans
          </DropdownMenu.Item>
          <DropdownMenu.Item
            onclick={() => (window.location.href = '/dashboard/settings/admin/plans/edit')}>
            Edit Plans
          </DropdownMenu.Item>
          <DropdownMenu.Item onclick={resetChanges}>Reset Changes</DropdownMenu.Item>
          <DropdownMenu.Separator class="border-border my-2 border-b" />
          <DropdownMenu.Item onclick={loading ? undefined : loadPlansFromStripe}>
            <ExternalLink class="mr-2 h-4 w-4" />
            Load from Stripe
            {#if loading}
              <span class="text-muted-foreground ml-2">(Loading...)</span>
            {/if}
          </DropdownMenu.Item>
          <DropdownMenu.Item onclick={syncingWithStripe ? undefined : syncAllPlansWithStripe}>
            <RefreshCw class={`mr-2 h-4 w-4 ${syncingWithStripe ? 'animate-spin' : ''}`} />
            Sync All with Stripe
            {#if syncingWithStripe}
              <span class="text-muted-foreground ml-2">(Syncing...)</span>
            {/if}
          </DropdownMenu.Item>
          <DropdownMenu.Item onclick={isSyncingFeatures ? undefined : syncFeatures}>
            <RefreshCw class={`mr-2 h-4 w-4 ${isSyncingFeatures ? 'animate-spin' : ''}`} />
            Sync Features
            {#if isSyncingFeatures}
              <span class="text-muted-foreground ml-2">(Syncing...)</span>
            {/if}
          </DropdownMenu.Item>
          <DropdownMenu.Separator />
          <DropdownMenu.Item onclick={confirmSaveChanges}>
            <Save class="mr-2 h-4 w-4" />
            Save Changes
          </DropdownMenu.Item>
        </DropdownMenu.Content>
      </DropdownMenu.Root>
    </div>
  </div>
</div>

{#if loading}
  <div class="flex h-64 items-center justify-center">
    <div class="text-center">
      <div
        class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]">
      </div>
      <p class="mt-4 text-lg">Loading plans...</p>
    </div>
  </div>
{:else if error}
  <div class="mb-4 rounded-lg bg-red-100 p-4 text-sm text-red-700">
    <p>Error loading plans: {error}</p>
    <p class="mt-2">Using fallback data. Changes may not be saved correctly.</p>
  </div>
{/if}

<div class="grid grid-cols-12">
  {#if selectedPlan && !loading}
    <!-- Plan Selection -->
    <div class="border-border col-span-2 flex flex-col gap-2 border-r p-2">
      {#each editablePlans as plan}
        <Button
          variant="outline"
          class="w-full justify-start {selectedPlanId === plan.id ? 'background-primary/80' : ''}"
          onclick={() => (selectedPlanId = plan.id)}>
          {plan.name}
          {#if plan.popular}
            <Badge variant="secondary" class="bg-primary/10 text-primary ml-2">Popular</Badge>
          {/if}
        </Button>
      {/each}
    </div>

    <!-- Plan Details -->
    <div class="col-span-10 flex flex-col">
      <div class="border-border flex items-center justify-between border-b p-4">
        <div class="flex flex-col gap-1">
          <h5>{selectedPlan.name} Plan</h5>
          <div class="text-muted-foreground text-sm">{selectedPlan.description}</div>
        </div>
        <div class="flex items-center gap-4">
          <div class="text-right">
            <div class="text-muted-foreground text-sm">Monthly</div>
            <div class="text-xl font-bold">${formatPrice(selectedPlan.monthlyPrice)}</div>
          </div>
          <ArrowRight class="text-muted-foreground h-4 w-4" />
          <div class="text-right">
            <div class="text-muted-foreground text-sm">Annual</div>
            <div class="text-xl font-bold">
              ${formatPrice(selectedPlan.annualPrice / 12)}/mo
            </div>
          </div>
        </div>
      </div>

      <Tabs.Root value="features" class="w-full">
        <Card.Content class="border-border border-b p-0">
          <Tabs.List class="flex flex-row gap-2 divide-x">
            <Tabs.Trigger value="features" class="flex-1 border-none">Features</Tabs.Trigger>
            <Tabs.Trigger value="details" class="flex-1 border-none">Plan Details</Tabs.Trigger>
            <Tabs.Trigger value="preview" class="flex-1 border-none">Preview</Tabs.Trigger>
          </Tabs.List>
        </Card.Content>

        <!-- Features Tab -->
        <Tabs.Content value="features">
          <FeaturesTabContent
            {featuresByCategory}
            {expandedCategories}
            {getFeatureAccessLevel}
            {getFeatureLimitValue}
            {updateFeatureAccessLevel}
            {updateFeatureLimitValue} />
        </Tabs.Content>

        <!-- Plan Details Tab -->
        <Tabs.Content value="details" class="h-full w-full p-4">
          <PlanDetailsTabContent
            {selectedPlan}
            {syncPlanWithStripe}
            {syncingWithStripe}
            {stripeMessage} />
        </Tabs.Content>

        <!-- Preview Tab -->
        <Tabs.Content value="preview" class="p-4">
          <PreviewTabContent {selectedPlan} {formatPrice} features={allFeatures} />
        </Tabs.Content>
      </Tabs.Root>
    </div>
  {/if}
</div>
