import { error, json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { prisma } from '$lib/server/prisma';
import fs from 'fs';
import path from 'path';

export const GET: RequestHandler = async ({ params, locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  try {
    // Get the document ID from the params
    const { id } = params;

    if (!id) {
      throw error(400, 'Document ID is required');
    }

    // Fetch the document from the database
    const document = await prisma.document.findUnique({
      where: { id },
    });

    // Check if document exists and belongs to the user
    if (!document || document.userId !== locals.user.id) {
      throw error(404, 'Document not found');
    }

    // Get the file path from the document
    let filePath = '';

    if (document.fileUrl) {
      // Remove leading slash if present
      const fileUrl = document.fileUrl.startsWith('/')
        ? document.fileUrl.substring(1)
        : document.fileUrl;

      // Check if it's a relative path or full URL
      if (fileUrl.startsWith('http://') || fileUrl.startsWith('https://')) {
        // Return the URL directly
        return json({
          success: true,
          document: {
            ...document,
            directUrl: document.fileUrl,
            isExternal: true,
          },
        });
      } else {
        // Construct the file path
        filePath = path.join(process.cwd(), 'static', fileUrl);
      }
    } else if (document.filePath) {
      filePath = document.filePath;
    } else {
      // Create a placeholder path for testing
      filePath = path.join(process.cwd(), 'static', 'placeholder.pdf');
    }

    // Check if the file exists
    let fileExists = false;
    try {
      fileExists = fs.existsSync(filePath);
    } catch (err) {
      console.error('Error checking file existence:', err);
    }

    // Create a direct URL for the file
    let directUrl = '';
    if (fileExists) {
      // Use a relative URL for the static directory
      const relativePath = path.relative(path.join(process.cwd(), 'static'), filePath);
      directUrl = '/' + relativePath.replace(/\\/g, '/');
    } else {
      // Use a placeholder URL
      directUrl = '/placeholder.pdf';
    }

    // Return the document with the direct URL
    return json({
      success: true,
      document: {
        ...document,
        directUrl,
        fileExists,
      },
    });
  } catch (err) {
    console.error('Error serving document:', err);
    throw error(500, 'Failed to serve document');
  }
};
