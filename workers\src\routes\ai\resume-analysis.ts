import { Router } from 'express';
import fetch from 'node-fetch';
import { z } from 'zod';
import { logger } from '../../utils/logger';

const router = Router();

// Environment variables
const OLLAMA_URL = process.env.OLLAMA_URL || 'http://localhost:11434';
const LLM_MODEL = process.env.LLM_MODEL || 'mistral';

// Schema for resume analysis request
const ResumeAnalysisRequestSchema = z.object({
  resumeText: z.string().min(1, 'Resume text is required'),
  jobDescription: z.string().optional(),
});

// Schema for resume analysis response
const ResumeAnalysisResponseSchema = z.object({
  overallScore: z.number(),
  keywordScore: z.number(),
  formatScore: z.number(),
  contentScore: z.number(),
  readabilityScore: z.number(),
  keywordMatches: z.array(z.string()),
  missingKeywords: z.array(z.string()),
  formatIssues: z.array(z.string()),
  contentSuggestions: z.array(z.string()),
  readabilitySuggestions: z.array(z.string()),
});

/**
 * Analyze a resume for ATS compatibility
 * POST /api/ai/resume-analysis
 */
router.post('/resume-analysis', async (req, res) => {
  try {
    // Validate request
    const validationResult = ResumeAnalysisRequestSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: validationResult.error.message,
      });
    }
    
    const { resumeText, jobDescription } = validationResult.data;
    
    // Create prompt for Ollama
    let prompt = `Analyze this resume for ATS compatibility:

Resume Text:
${resumeText}

`;

    // Add job description if provided
    if (jobDescription) {
      prompt += `
Job Description:
${jobDescription}

Please analyze how well this resume matches the job description.
`;
    }

    prompt += `
Provide a detailed analysis with the following scores (0-100):
1. Overall Score
2. Keyword Score
3. Format Score
4. Content Score
5. Readability Score

Also provide:
- Keyword matches found
- Missing important keywords
- Format issues
- Content suggestions
- Readability suggestions

Return the analysis as a JSON object with these fields.
`;

    // Call Ollama API
    const response = await fetch(`${OLLAMA_URL}/api/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: LLM_MODEL,
        prompt,
        stream: false,
      }),
    });

    if (!response.ok) {
      throw new Error(`Ollama API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    // Extract JSON from the response
    const jsonMatch = data.response.match(/\{[\s\S]*\}/);
    let analysisResult;
    
    if (jsonMatch) {
      try {
        analysisResult = JSON.parse(jsonMatch[0]);
      } catch (error) {
        logger.error('Failed to parse JSON from Ollama response:', error);
        analysisResult = extractStructuredData(data.response);
      }
    } else {
      analysisResult = extractStructuredData(data.response);
    }
    
    // Validate and normalize the analysis result
    const validatedResult = {
      overallScore: analysisResult.overallScore || 70,
      keywordScore: analysisResult.keywordScore || 65,
      formatScore: analysisResult.formatScore || 75,
      contentScore: analysisResult.contentScore || 70,
      readabilityScore: analysisResult.readabilityScore || 80,
      keywordMatches: analysisResult.keywordMatches || ['resume', 'experience', 'skills'],
      missingKeywords: analysisResult.missingKeywords || ['specific technical skills', 'certifications'],
      formatIssues: analysisResult.formatIssues || ['Consider using a more ATS-friendly format'],
      contentSuggestions: analysisResult.contentSuggestions || ['Add more quantifiable achievements'],
      readabilitySuggestions: analysisResult.readabilitySuggestions || ['Use more bullet points for better readability'],
    };
    
    return res.json({
      success: true,
      analysis: validatedResult,
    });
  } catch (error) {
    logger.error('Error analyzing resume:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to analyze resume',
    });
  }
});

/**
 * Extract structured data from unstructured text response
 */
function extractStructuredData(text: string) {
  const result = {
    overallScore: extractScore(text, 'overall'),
    keywordScore: extractScore(text, 'keyword'),
    formatScore: extractScore(text, 'format'),
    contentScore: extractScore(text, 'content'),
    readabilityScore: extractScore(text, 'readability'),
    keywordMatches: extractList(text, 'keyword matches'),
    missingKeywords: extractList(text, 'missing keywords'),
    formatIssues: extractList(text, 'format issues'),
    contentSuggestions: extractList(text, 'content suggestions'),
    readabilitySuggestions: extractList(text, 'readability suggestions'),
  };
  
  return result;
}

/**
 * Extract a score from text
 */
function extractScore(text: string, scoreType: string): number {
  const regex = new RegExp(`${scoreType}\\s*score\\s*:?\\s*(\\d+)`, 'i');
  const match = text.match(regex);
  return match ? parseInt(match[1], 10) : 70;
}

/**
 * Extract a list from text
 */
function extractList(text: string, listType: string): string[] {
  const regex = new RegExp(`${listType}\\s*:?\\s*([\\s\\S]*?)(?=\\n\\n|$)`, 'i');
  const match = text.match(regex);
  if (!match) return [];
  
  return match[1]
    .split(/\n-|\n\*|\n\d+\./)
    .map(item => item.trim())
    .filter(item => item.length > 0);
}

export default router;
