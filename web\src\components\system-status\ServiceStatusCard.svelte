<script lang="ts">
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { CheckCircle, AlertTriangle, XCircle, Clock } from 'lucide-svelte';
  import ServiceTrendChart from './ServiceTrendChart.svelte';

  // Props
  export let name: string;
  export let status: 'operational' | 'degraded' | 'outage' | 'maintenance' | 'unknown';
  export let description: string = '';
  export let historyData: any[] = [];
  export let metrics: Record<string, any> = {};

  // Helper function to get status icon
  function getStatusIcon(status: string) {
    switch (status) {
      case 'operational':
        return CheckCircle;
      case 'degraded':
        return AlertTriangle;
      case 'outage':
        return XCircle;
      case 'maintenance':
        return Clock;
      default:
        return AlertTriangle;
    }
  }

  // Helper function to get status color
  function getStatusColor(status: string): string {
    switch (status) {
      case 'operational':
        return '#4CAF50'; // Green
      case 'degraded':
        return '#FFC107'; // Yellow
      case 'outage':
        return '#F44336'; // Red
      case 'maintenance':
        return '#2196F3'; // Blue
      default:
        return '#9E9E9E'; // Gray
    }
  }

  // Helper function to get badge variant
  function getStatusVariant(
    status: string
  ): 'default' | 'outline' | 'destructive' | 'secondary' | 'warning' | 'success' {
    switch (status) {
      case 'operational':
        return 'success';
      case 'degraded':
        return 'warning';
      case 'outage':
        return 'destructive';
      case 'maintenance':
        return 'secondary';
      default:
        return 'outline';
    }
  }

  // Format number with commas
  function formatNumber(num: number): string {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  // Format time in ms or seconds
  function formatTime(ms: number): string {
    return ms >= 1000 ? `${(ms / 1000).toFixed(1)}s` : `${ms}ms`;
  }

  // Get color for chart
  $: chartColor = getStatusColor(status);
</script>

<div class="rounded-lg border p-4">
  <div class="mb-3 flex items-center justify-between">
    <div class="flex items-center gap-3">
      <div class="h-3 w-3 rounded-full" style="background-color: {chartColor}"></div>
      <h3 class="font-medium">{name}</h3>
    </div>
    <Badge variant={getStatusVariant(status)}>
      <svelte:component this={getStatusIcon(status)} class="mr-1 h-3 w-3" />
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  </div>

  {#if description}
    <p class="text-muted-foreground mb-3 text-sm">{description}</p>
  {/if}

  <div class="mb-4">
    <ServiceTrendChart {historyData} metric="successRate" title="Last 30 Days" height={80} />
  </div>

  {#if metrics && Object.keys(metrics).length > 0}
    <div class="grid grid-cols-2 gap-2 text-xs">
      {#if metrics.responseTime !== undefined}
        <div class="flex justify-between">
          <span class="text-muted-foreground">Response Time:</span>
          <span class="font-medium">{formatTime(metrics.responseTime)}</span>
        </div>
      {/if}

      {#if metrics.successRate !== undefined}
        <div class="flex justify-between">
          <span class="text-muted-foreground">Success Rate:</span>
          <span class="font-medium">{metrics.successRate.toFixed(1)}%</span>
        </div>
      {/if}

      {#if metrics.errorRate !== undefined}
        <div class="flex justify-between">
          <span class="text-muted-foreground">Error Rate:</span>
          <span class="font-medium">{metrics.errorRate.toFixed(1)}%</span>
        </div>
      {/if}

      {#if metrics.requestCount !== undefined}
        <div class="flex justify-between">
          <span class="text-muted-foreground">Requests:</span>
          <span class="font-medium">{formatNumber(metrics.requestCount)}</span>
        </div>
      {/if}

      {#if metrics.queueSize !== undefined}
        <div class="flex justify-between">
          <span class="text-muted-foreground">Queue Size:</span>
          <span class="font-medium">{formatNumber(metrics.queueSize)}</span>
        </div>
      {/if}

      {#if metrics.processingCount !== undefined}
        <div class="flex justify-between">
          <span class="text-muted-foreground">Processing:</span>
          <span class="font-medium">{formatNumber(metrics.processingCount)}</span>
        </div>
      {/if}
    </div>
  {/if}
</div>
