<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import { Button } from '$lib/components/ui/button';
  import { Download, ExternalLink, ZoomIn, ZoomOut, RotateCw } from 'lucide-svelte';
  import { Skeleton } from '$lib/components/ui/skeleton';

  // We'll dynamically import these libraries only on the client side
  let docx: any;
  let pdfjsLib: any;

  const { document } = $props<{
    document: any;
  }>();

  let container: HTMLDivElement;
  let loading = $state(true);
  let error = $state(false);
  let errorMessage = $state('');
  let scale = $state(1);
  let rotation = $state(0);
  let pdfDocument = $state<any>(null);
  let currentPage = $state(1);
  let totalPages = $state(0);

  // Determine document type
  function getDocumentType() {
    if (!document?.fileUrl) return 'unknown';

    const url = document.fileUrl.toLowerCase();
    const fileName = document.fileName?.toLowerCase() || '';

    // Check for images
    if (
      url.match(/\.(jpeg|jpg|gif|png|webp|svg|bmp|tiff)$/) ||
      fileName.match(/\.(jpeg|jpg|gif|png|webp|svg|bmp|tiff)$/) ||
      document.type?.includes('image')
    ) {
      return 'image';
    }

    // Check for PDFs
    if (url.match(/\.(pdf)$/) || fileName.match(/\.(pdf)$/) || document.type?.includes('pdf')) {
      return 'pdf';
    }

    // Check for docx files
    if (url.match(/\.(docx)$/) || fileName.match(/\.(docx)$/) || document.type?.includes('docx')) {
      return 'docx';
    }

    // Check for doc files
    if (url.match(/\.(doc)$/) || fileName.match(/\.(doc)$/) || document.type?.includes('doc')) {
      return 'doc';
    }

    // Check for text files
    if (
      url.match(/\.(txt|md|rtf)$/) ||
      fileName.match(/\.(txt|md|rtf)$/) ||
      document.type?.includes('text')
    ) {
      return 'text';
    }

    // Check for Office documents
    if (
      url.match(/\.(xls|xlsx|ppt|pptx)$/) ||
      fileName.match(/\.(xls|xlsx|ppt|pptx)$/) ||
      document.type?.includes('office')
    ) {
      return 'office';
    }

    // Check for code files
    if (
      url.match(/\.(js|ts|html|css|json|xml|py|java|c|cpp|cs|go|rb|php)$/) ||
      fileName.match(/\.(js|ts|html|css|json|xml|py|java|c|cpp|cs|go|rb|php)$/) ||
      document.type?.includes('code')
    ) {
      return 'code';
    }

    return 'generic';
  }

  // Download the document
  function downloadDocument() {
    if (document?.fileUrl) {
      window.open(document.fileUrl, '_blank');
    }
  }

  // Open in new tab
  function openInNewTab() {
    if (document?.fileUrl) {
      window.open(document.fileUrl, '_blank');
    }
  }

  // Zoom in
  function zoomIn() {
    scale += 0.2;
    if (getDocumentType() === 'pdf' && pdfDocument) {
      renderPdfPage(currentPage);
    }
  }

  // Zoom out
  function zoomOut() {
    if (scale <= 0.4) return;
    scale -= 0.2;
    if (getDocumentType() === 'pdf' && pdfDocument) {
      renderPdfPage(currentPage);
    }
  }

  // Rotate
  function rotate() {
    rotation = (rotation + 90) % 360;
  }

  // Render PDF document
  async function renderPdf() {
    if (!browser || !container || !document?.fileUrl || !pdfjsLib) return;

    try {
      loading = true;

      // Load the PDF document
      const loadingTask = pdfjsLib.getDocument(document.fileUrl);
      pdfDocument = await loadingTask.promise;
      totalPages = pdfDocument.numPages;

      // Render the first page
      await renderPdfPage(1);

      loading = false;
    } catch (err) {
      console.error('Error rendering PDF:', err);
      error = true;
      errorMessage = err instanceof Error ? err.message : 'Failed to render PDF';
      loading = false;
    }
  }

  // Render a specific PDF page
  async function renderPdfPage(pageNumber: number) {
    if (!browser || !container || !pdfDocument) return;

    try {
      // Get the page
      const page = await pdfDocument.getPage(pageNumber);
      currentPage = pageNumber;

      // Create or get canvas element
      let canvas = container.querySelector('canvas');
      if (!canvas) {
        canvas = document.createElement('canvas');
        container.innerHTML = '';
        container.appendChild(canvas);
      }

      const context = canvas.getContext('2d');
      if (!context) throw new Error('Could not get canvas context');

      // Calculate scale to fit the container
      const viewport = page.getViewport({ scale: scale });

      // Set canvas dimensions
      canvas.height = viewport.height;
      canvas.width = viewport.width;
      canvas.style.width = '100%';
      canvas.style.height = 'auto';

      // Render PDF page to canvas
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };

      await page.render(renderContext).promise;
    } catch (err) {
      console.error('Error rendering PDF page:', err);
      error = true;
      errorMessage = err instanceof Error ? err.message : 'Failed to render PDF page';
    }
  }

  // Render DOCX document
  async function renderDocx() {
    if (!browser || !container || !document?.fileUrl || !docx) return;

    try {
      loading = true;

      // Fetch the DOCX file
      const response = await fetch(document.fileUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();

      // Clear container
      container.innerHTML = '';

      // Render DOCX using docx-preview
      await docx.renderAsync(blob, container, null, {
        className: 'docx-viewer',
        inWrapper: true,
        ignoreWidth: false,
        ignoreHeight: false,
        useBase64URL: true,
      });

      loading = false;
    } catch (err) {
      console.error('Error rendering DOCX:', err);
      error = true;
      errorMessage = err instanceof Error ? err.message : 'Failed to render DOCX';
      loading = false;
    }
  }

  // Render document based on type
  async function renderDocument() {
    if (!browser) {
      loading = false;
      return;
    }

    // If libraries aren't loaded yet, load them first
    if (!pdfjsLib || !docx) {
      try {
        // Dynamically import the libraries
        const [docxModule, pdfModule] = await Promise.all([
          import('docx-preview'),
          import('pdfjs-dist'),
        ]);

        docx = docxModule;
        pdfjsLib = pdfModule;

        // Set PDF.js worker source
        pdfjsLib.GlobalWorkerOptions.workerSrc =
          'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js';
      } catch (err) {
        console.error('Error loading document libraries:', err);
        error = true;
        errorMessage = 'Failed to load document viewer libraries';
        loading = false;
        return;
      }
    }

    const type = getDocumentType();

    switch (type) {
      case 'pdf':
        await renderPdf();
        break;
      case 'docx':
        await renderDocx();
        break;
      case 'image':
        // Image rendering is handled in the template
        loading = false;
        break;
      default:
        // For other types, we'll use the iframe approach
        loading = false;
        break;
    }
  }

  onMount(() => {
    if (!browser) {
      loading = false;
      return;
    }

    console.log('Universal Document Viewer mounted with document:', document);
    console.log('Document type:', getDocumentType());
    console.log('Document URL:', document?.fileUrl);

    if (document?.fileUrl) {
      renderDocument();
    } else {
      error = true;
      errorMessage = 'No document URL provided';
      loading = false;
    }
  });
</script>

<div class="document-viewer flex h-full w-full flex-col overflow-hidden rounded-md border">
  <!-- Document Viewer Container -->
  <div
    bind:this={container}
    class="flex flex-1 items-center justify-center overflow-auto bg-gray-100">
    {#if loading}
      <div class="flex flex-col items-center justify-center p-4">
        <Skeleton class="h-[400px] w-[300px] rounded-md" />
        <p class="mt-4 text-sm text-gray-500">Loading document...</p>
      </div>
    {:else if error}
      <div class="flex flex-col items-center justify-center p-4 text-center">
        <p class="mb-2 font-medium text-red-500">Failed to load document</p>
        <p class="mb-4 text-gray-500">{errorMessage}</p>
        <div class="flex gap-2">
          <Button
            variant="outline"
            onclick={() => {
              error = false;
              loading = true;
              renderDocument();
            }}>
            Try Again
          </Button>
          <Button variant="outline" onclick={openInNewTab}>
            <ExternalLink class="mr-2 h-4 w-4" />
            Open in Browser
          </Button>
        </div>
      </div>
    {:else if getDocumentType() === 'image'}
      <div class="flex h-full items-center justify-center p-4">
        <img
          src={document.fileUrl}
          alt={document.label || 'Image'}
          style="transform: scale({scale}) rotate({rotation}deg);"
          class="max-h-full max-w-full transition-transform duration-200 ease-in-out"
          onload={() => {
            loading = false;
          }}
          onerror={() => {
            error = true;
            errorMessage = 'Failed to load image';
          }} />
      </div>
    {:else if getDocumentType() === 'pdf'}
      <!-- PDF is rendered via renderPdf() function -->
    {:else if getDocumentType() === 'text'}
      <iframe
        src={document.fileUrl}
        title={document.label || 'Text Document'}
        class="h-full w-full"
        onload={() => {
          loading = false;
        }}
        onerror={() => {
          error = true;
          errorMessage = 'Failed to load text document';
        }}></iframe>
    {:else if getDocumentType() === 'docx' || getDocumentType() === 'doc'}
      <!-- DOCX is rendered via renderDocx() function -->
    {:else if getDocumentType() === 'office' || getDocumentType() === 'code'}
      <iframe
        src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(document.fileUrl)}`}
        title={document.label || 'Office Document'}
        class="h-full w-full"
        onload={() => {
          loading = false;
        }}
        onerror={() => {
          error = true;
          errorMessage = 'Failed to load document';
        }}></iframe>
    {:else}
      <iframe
        src={document.fileUrl}
        title={document.label || 'Document'}
        class="h-full w-full"
        sandbox="allow-same-origin allow-scripts"
        onload={() => {
          loading = false;
        }}
        onerror={() => {
          error = true;
          errorMessage = 'Failed to load document';
        }}></iframe>
    {/if}
  </div>

  <!-- Controls -->
  <div class="flex items-center justify-between border-t bg-gray-50 p-2">
    <div class="flex items-center space-x-2">
      <span class="text-sm font-medium">
        {document?.label || 'Document'}
      </span>
      <span class="text-xs text-gray-500">
        {document?.fileName || ''}
      </span>
      {#if pdfDocument && totalPages > 0}
        <span class="ml-2 text-xs text-gray-500">
          Page {currentPage} of {totalPages}
        </span>
      {/if}
    </div>

    <div class="flex items-center space-x-2">
      {#if getDocumentType() === 'image' || getDocumentType() === 'pdf'}
        <Button variant="outline" size="sm" onclick={zoomOut} disabled={loading || error}>
          <ZoomOut class="h-4 w-4" />
        </Button>

        <Button variant="outline" size="sm" onclick={zoomIn} disabled={loading || error}>
          <ZoomIn class="h-4 w-4" />
        </Button>
      {/if}

      {#if getDocumentType() === 'image'}
        <Button variant="outline" size="sm" onclick={rotate} disabled={loading || error}>
          <RotateCw class="h-4 w-4" />
        </Button>
      {/if}

      {#if pdfDocument && totalPages > 1}
        <Button
          variant="outline"
          size="sm"
          onclick={() => currentPage > 1 && renderPdfPage(currentPage - 1)}
          disabled={loading || error || currentPage <= 1}>
          Previous
        </Button>

        <Button
          variant="outline"
          size="sm"
          onclick={() => currentPage < totalPages && renderPdfPage(currentPage + 1)}
          disabled={loading || error || currentPage >= totalPages}>
          Next
        </Button>
      {/if}

      <Button variant="outline" size="sm" onclick={openInNewTab} disabled={loading}>
        <ExternalLink class="mr-1 h-4 w-4" />
        Open
      </Button>

      <Button variant="outline" size="sm" onclick={downloadDocument} disabled={loading}>
        <Download class="mr-1 h-4 w-4" />
        Download
      </Button>
    </div>
  </div>
</div>
