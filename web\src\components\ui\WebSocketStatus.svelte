<script lang="ts">
  import websocket from '$lib/websocket/websocket-singleton';
  import { Wifi, WifiOff } from 'lucide-svelte';

  // Props
  export let showLogs = false;
  export let showIcon = true;
  export let showText = true;
  export let showIndicator = true;

  // Initialize with current values
  let status = websocket.getStatus();

  // Subscribe to status changes
  websocket.status.subscribe((value) => {
    status = value;
  });

  // Get status color
  $: statusColor =
    status === 'connected'
      ? 'bg-green-500'
      : status === 'connecting'
        ? 'bg-yellow-500'
        : status === 'error'
          ? 'bg-red-500'
          : 'bg-gray-500';

  // Get status text
  $: statusText =
    status === 'connected'
      ? 'Connected'
      : status === 'connecting'
        ? 'Connecting'
        : status === 'error'
          ? 'Error'
          : 'Disconnected';
</script>

<div class="websocket-status flex items-center gap-2">
  {#if showIcon}
    <div class="icon">
      {#if status === 'connected'}
        <Wifi class="h-4 w-4 text-green-500" />
      {:else}
        <WifiOff class="h-4 w-4 text-gray-500" />
      {/if}
    </div>
  {/if}

  {#if showIndicator}
    <div class="indicator">
      <div class="h-2 w-2 rounded-full {statusColor}"></div>
    </div>
  {/if}

  {#if showText}
    <div class="status-text text-xs">
      {statusText}
    </div>
  {/if}
</div>

{#if showLogs}
  <div
    class="logs mt-2 max-h-40 overflow-y-auto rounded border border-gray-200 bg-gray-50 p-2 text-xs">
    <div class="log-entry">Logs disabled in simplified WebSocket implementation</div>
  </div>
{/if}
