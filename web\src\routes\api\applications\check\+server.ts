import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';

export const GET: RequestHandler = async ({ locals }) => {
  try {
    // First check if we have any applications in the system
    const applicationCount = await prisma.application.count();

    if (applicationCount > 0) {
      // Get the first application
      const applications = await prisma.application.findMany({
        take: 1,
        orderBy: {
          createdAt: 'desc',
        },
      });

      if (applications.length > 0) {
        const application = applications[0];
        return json({
          exists: true,
          application: {
            id: application.id,
            company: application.company,
            position: application.position,
          },
        });
      }
    }

    // If we get here, we need to create a test application
    // First, find a valid user to associate with the application
    const users = await prisma.user.findMany({ take: 1 });

    if (users.length === 0) {
      return json(
        {
          error: 'No users found in the system to create a test application',
        },
        { status: 500 }
      );
    }

    // Create a test application with a generated UUID
    const newApplication = await prisma.application.create({
      data: {
        company: 'Test Company',
        position: 'Test Position',
        location: 'Remote',
        appliedDate: new Date(),
        status: 'Applied',
        userId: users[0].id,
      },
    });

    return json({
      exists: false,
      created: true,
      application: {
        id: newApplication.id,
        company: newApplication.company,
        position: newApplication.position,
      },
    });
  } catch (error) {
    console.error('Error checking application:', error);
    return json(
      {
        error: 'Failed to check application',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
};
