<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';

  import {
    Calendar,
    Clock,
    Plus,
    CheckCircle,
    XCircle,
    AlertCircle,
    FileQuestion,
  } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import AddInterviewModal from './AddInterviewModal.svelte';
  import AddQuestionModal from './AddQuestionModal.svelte';
  import InterviewModal from './InterviewModal.svelte';

  // Props
  let { applicationId } = $props<{
    applicationId: string;
  }>();

  // State
  let interviewStages = $state([]);
  let isLoading = $state(true);
  let error = $state(null);
  let showAddInterviewModal = $state(false);
  let showAddQuestionModal = $state(false);
  let showInterviewModal = $state(false);
  let selectedInterviewId = $state(null);
  let selectedInterview = $state(null);

  // Fetch interview stages on mount
  $effect(() => {
    if (applicationId) {
      fetchInterviewStages();
    }
  });

  // Fetch interview stages from the API
  async function fetchInterviewStages() {
    isLoading = true;
    error = null;

    try {
      console.log('Fetching interviews for application ID:', applicationId);

      // First check if the application exists
      const checkResponse = await fetch('/api/applications/check');
      console.log(
        'Application check response:',
        checkResponse.ok ? 'OK' : 'Failed',
        checkResponse.status
      );

      if (checkResponse.ok) {
        const checkData = await checkResponse.json();
        console.log('Application check data:', checkData);
      }

      // Now fetch the interviews
      const response = await fetch(`/api/applications/${applicationId}/interviews`);
      console.log('Interview API response status:', response.status, response.statusText);

      if (!response.ok) {
        throw new Error(`Failed to fetch interview stages: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Interview data received:', data);
      interviewStages = data.interviewStages || [];

      // If no interviews, show a message but don't treat it as an error
      if (interviewStages.length === 0) {
        console.log('No interview stages found for this application');
      }
    } catch (err) {
      console.error('Error fetching interview stages:', err);
      error = err.message;
      toast.error('Failed to load interview data');
    } finally {
      isLoading = false;
    }
  }

  // Format date for display
  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  // Get status badge for interview outcome
  function getOutcomeBadge(outcome: string) {
    if (!outcome) return null;

    type BadgeVariant = 'success' | 'destructive' | 'outline' | 'secondary' | 'default' | 'warning';

    const outcomeMap: Record<string, { variant: BadgeVariant; icon: any }> = {
      Passed: { variant: 'success', icon: CheckCircle },
      Failed: { variant: 'destructive', icon: XCircle },
      Pending: { variant: 'outline', icon: Clock },
      Scheduled: { variant: 'secondary', icon: Calendar },
    };

    return outcomeMap[outcome] || { variant: 'outline' as BadgeVariant, icon: AlertCircle };
  }

  // Handle adding a new interview
  function handleAddInterview() {
    showAddInterviewModal = true;
  }

  // Handle adding a new question to an interview
  function handleAddQuestion(interviewId: string): void {
    selectedInterviewId = interviewId;
    showAddQuestionModal = true;
  }

  // Handle successful interview creation
  function handleInterviewCreated() {
    fetchInterviewStages();
    showAddInterviewModal = false;
    toast.success('Interview added successfully');
  }

  // Handle successful question creation
  function handleQuestionCreated() {
    fetchInterviewStages();
    showAddQuestionModal = false;
    toast.success('Question added successfully');
  }

  // Open interview modal
  function openInterviewModal(interview) {
    selectedInterview = interview;
    showInterviewModal = true;
  }

  // Handle adding a question from the modal
  function handleAddQuestionFromModal(interviewId) {
    selectedInterviewId = interviewId;
    showAddQuestionModal = true;
    showInterviewModal = false;
  }

  // Handle interview modal close
  function handleInterviewModalClose() {
    showInterviewModal = false;
    // Refresh data to get any updates
    fetchInterviewStages();
  }
</script>

<div class="mt-6 flex flex-col gap-5">
  <div class="flex flex-row items-center justify-between">
    <div class="flex flex-col">
      <h3 class="text-lg font-medium">Interviews</h3>
      <p class="text-muted-foreground mt-1 text-sm">Track your interview stages and questions</p>
    </div>
    {#if interviewStages.length > 0 || isLoading || error}
      <Button
        variant="outline"
        size="sm"
        onclick={handleAddInterview}
        class="flex items-center gap-2 shadow-sm hover:shadow">
        <Plus class="h-4 w-4" />
        <span>Add Interview</span>
      </Button>
    {/if}
  </div>

  {#if isLoading}
    <div class="flex flex-col items-center justify-center py-12">
      <div class="border-primary h-10 w-10 animate-spin rounded-full border-b-2 border-t-2"></div>
      <p class="text-muted-foreground mt-4 text-sm">Loading interview data...</p>
    </div>
  {:else if error}
    <div class="bg-destructive/10 text-destructive rounded-lg p-6 text-center shadow-sm">
      <AlertCircle class="mx-auto mb-2 h-8 w-8" />
      <p class="font-medium">{error}</p>
      <p class="text-muted-foreground mt-1 text-sm">
        There was a problem loading your interview data.
      </p>
      <Button variant="outline" size="sm" class="mt-4 shadow-sm" onclick={fetchInterviewStages}>
        Try Again
      </Button>
    </div>
  {:else if interviewStages.length === 0}
    <div class="bg-muted/20 rounded-lg border border-dashed p-10 text-center">
      <FileQuestion class="text-muted-foreground mx-auto mb-3 h-10 w-10" />
      <h4 class="text-base font-medium">No interviews yet</h4>
      <p class="text-muted-foreground mx-auto mt-2 max-w-md">
        Track your interview process by adding interview stages and questions to keep a record of
        your progress.
      </p>
      <Button
        variant="outline"
        size="sm"
        onclick={handleAddInterview}
        class="mt-5 shadow-sm hover:shadow">
        <Plus class="mr-1.5 h-4 w-4" />
        Add Your First Interview
      </Button>
    </div>
  {:else}
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {#each interviewStages as interview}
        <button
          type="button"
          class="hover:border-primary/30 w-full cursor-pointer rounded-lg border p-5 text-left shadow-sm transition-all hover:shadow-md"
          onclick={() => openInterviewModal(interview)}
          onkeydown={(e) => e.key === 'Enter' && openInterviewModal(interview)}>
          <div class="flex items-start justify-between">
            <div class="flex flex-col gap-2">
              <h3 class="text-lg font-semibold">{interview.stageName}</h3>
              <div class="text-muted-foreground flex items-center gap-1.5 text-sm">
                <Calendar class="h-4 w-4" />
                <span>{formatDate(interview.stageDate)}</span>
              </div>
            </div>

            {#if interview.outcome}
              {@const badge = getOutcomeBadge(interview.outcome)}
              <Badge variant={badge.variant} class="flex items-center gap-1.5 px-2.5 py-1">
                {#if badge.icon}
                  <badge.icon class="h-3.5 w-3.5" />
                {/if}
                <span>{interview.outcome}</span>
              </Badge>
            {/if}
          </div>

          {#if interview.nextAction}
            <div class="mt-4 border-t pt-3">
              <p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">
                Next Action
              </p>
              <p class="mt-1 line-clamp-2 text-sm">{interview.nextAction}</p>
            </div>
          {/if}

          <div class="mt-4 flex items-center justify-between">
            {#if interview.questions?.length}
              <div class="text-muted-foreground flex items-center gap-1.5 text-sm">
                <FileQuestion class="h-4 w-4" />
                <span
                  >{interview.questions.length}
                  {interview.questions.length === 1 ? 'question' : 'questions'}</span>
              </div>
            {:else}
              <div class="text-muted-foreground text-sm">No questions</div>
            {/if}

            <Button
              variant="ghost"
              size="sm"
              class="h-7 px-2"
              onclick={(e) => {
                e.stopPropagation();
                handleAddQuestion(interview.id);
              }}>
              <Plus class="mr-1 h-3.5 w-3.5" />
              Add Question
            </Button>
          </div>
        </button>
      {/each}
    </div>
  {/if}
</div>

<!-- Add Interview Modal -->
<AddInterviewModal
  {applicationId}
  open={showAddInterviewModal}
  onClose={() => (showAddInterviewModal = false)}
  onSuccess={handleInterviewCreated} />

<!-- Add Question Modal -->
<AddQuestionModal
  {applicationId}
  interviewId={selectedInterviewId}
  open={showAddQuestionModal}
  onClose={() => (showAddQuestionModal = false)}
  onSuccess={handleQuestionCreated} />

<!-- Interview Details Modal -->
<InterviewModal
  open={showInterviewModal}
  interview={selectedInterview}
  onClose={handleInterviewModalClose}
  onAddQuestion={handleAddQuestionFromModal} />
