// @ts-nocheck
import { error, redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { prisma } from '$lib/server/prisma';

export const load = async ({ params, locals }: Parameters<PageServerLoad>[0]) => {
  // Check if user is authenticated
  const user = locals.user;
  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  const { id } = params;

  if (!id) {
    throw error(400, 'Profile ID is required');
  }

  try {
    console.log('Loading profile with ID:', id);
    console.log('User ID:', user.id);

    // Get profile data
    const profile = await prisma.profile.findUnique({
      where: {
        id,
        userId: user.id, // Ensure the profile belongs to the user
      },
      include: {
        data: true,
        defaultDocument: true,
      },
    });

    console.log('Profile found:', profile ? 'Yes' : 'No');

    if (!profile) {
      throw error(404, 'Profile not found');
    }

    // Get user documents (resumes)
    console.log('Fetching documents for user:', user.id);

    const documents = await prisma.document.findMany({
      where: {
        userId: user.id,
        // Filter for documents that have a resume relation
        resume: {
          isNot: null,
        },
      },
      select: {
        id: true,
        label: true,
        resume: true,
        updatedAt: true,
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    console.log('Documents found:', documents.length);

    return {
      profile,
      documents,
    };
  } catch (err) {
    console.error('Error loading profile data:', err);
    console.error('Error details:', JSON.stringify(err, null, 2));
    throw error(
      500,
      `Failed to load profile data: ${err instanceof Error ? err.message : 'Unknown error'}`
    );
  }
};
