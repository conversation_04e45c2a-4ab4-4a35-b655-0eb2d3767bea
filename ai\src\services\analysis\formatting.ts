import { logger } from '../../utils/logger.js';

interface FormatAnalysisResult {
  score: number;
  issues: string[];
}

export function analyzeFormat(resumeText: string): FormatAnalysisResult {
  try {
    const issues: string[] = [];
    let score = 100; // Start with perfect score and deduct for issues
    
    // Check for contact information
    if (!hasEmail(resumeText)) {
      issues.push('Missing email address');
      score -= 10;
    }
    
    if (!hasPhone(resumeText)) {
      issues.push('Missing phone number');
      score -= 5;
    }
    
    // Check for section headings
    if (!hasSection(resumeText, ['experience', 'work', 'employment', 'history'])) {
      issues.push('No clear work experience section heading');
      score -= 10;
    }
    
    if (!hasSection(resumeText, ['education', 'academic', 'qualification', 'degree'])) {
      issues.push('No clear education section heading');
      score -= 10;
    }
    
    if (!hasSection(resumeText, ['skill', 'proficiency', 'competency', 'expertise'])) {
      issues.push('No clear skills section heading');
      score -= 10;
    }
    
    // Check for bullet points
    if (!hasBulletPoints(resumeText)) {
      issues.push('No bullet points found - use bullet points for better readability');
      score -= 5;
    }
    
    // Check for excessive length
    const lineCount = resumeText.split('\n').length;
    if (lineCount > 70) {
      issues.push('Resume may be too long - consider condensing to 1-2 pages');
      score -= 5;
    }
    
    // Check for inconsistent formatting
    if (hasInconsistentFormatting(resumeText)) {
      issues.push('Inconsistent formatting detected - ensure consistent date formats and bullet styles');
      score -= 5;
    }
    
    // Check for tables (which ATS systems often struggle with)
    if (hasTables(resumeText)) {
      issues.push('Possible table structure detected - tables may not parse correctly in ATS systems');
      score -= 10;
    }
    
    // Ensure score is between 0 and 100
    score = Math.max(0, Math.min(100, score));
    
    return {
      score,
      issues,
    };
  } catch (error) {
    logger.error('Error analyzing format:', error);
    return {
      score: 70, // Default score
      issues: ['Unable to fully analyze resume format'],
    };
  }
}

function hasEmail(text: string): boolean {
  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;
  return emailRegex.test(text);
}

function hasPhone(text: string): boolean {
  const phoneRegex = /(\+\d{1,3}[ -]?)?\(?\d{3}\)?[ -]?\d{3}[ -]?\d{4}/;
  return phoneRegex.test(text);
}

function hasSection(text: string, sectionKeywords: string[]): boolean {
  const lines = text.split('\n');
  
  for (const line of lines) {
    const cleanLine = line.trim().toLowerCase();
    if (sectionKeywords.some(keyword => cleanLine.includes(keyword))) {
      // Check if it looks like a heading (short, possibly followed by colon)
      if (cleanLine.length < 30 || cleanLine.includes(':')) {
        return true;
      }
    }
  }
  
  return false;
}

function hasBulletPoints(text: string): boolean {
  // Check for common bullet point characters
  const bulletRegex = /^[ \t]*[•\-\*\+\>\◦\◆\◇\◈\◉\◊\○\◌\◍\◎\●\◐\◑\◒\◓\◔\◕\◖\◗\◘\◙\◚\◛\◜\◝\◞\◟\◠\◡\◢\◣\◤\◥\◦\◧\◨\◩\◪\◫\◬\◭\◮\◯\◰\◱\◲\◳\◴\◵\◶\◷\◸\◹\◺\◻\◼\◽\◾\◿]/m;
  
  // Check for numbered lists
  const numberedRegex = /^[ \t]*\d+[\.\)]/m;
  
  return bulletRegex.test(text) || numberedRegex.test(text);
}

function hasInconsistentFormatting(text: string): boolean {
  const lines = text.split('\n');
  const dateFormats = [];
  
  // Check date formats
  const dateRegex = /\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December|0?[1-9]|1[0-2])[\/\-\. ](20\d{2}|19\d{2}|\d{2})\b/gi;
  
  for (const line of lines) {
    const matches = line.match(dateRegex);
    if (matches) {
      for (const match of matches) {
        dateFormats.push(match);
      }
    }
  }
  
  // Check if there are multiple date formats
  if (dateFormats.length >= 2) {
    const formatTypes = new Set();
    
    for (const date of dateFormats) {
      // Determine format type (MM/YYYY, MM-YYYY, Month YYYY, etc.)
      if (/\d{1,2}\/\d{2,4}/.test(date)) {
        formatTypes.add('numeric/slash');
      } else if (/\d{1,2}-\d{2,4}/.test(date)) {
        formatTypes.add('numeric/dash');
      } else if (/\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\b/i.test(date)) {
        formatTypes.add('abbreviated month');
      } else if (/\b(January|February|March|April|May|June|July|August|September|October|November|December)\b/i.test(date)) {
        formatTypes.add('full month');
      }
    }
    
    return formatTypes.size > 1;
  }
  
  return false;
}

function hasTables(text: string): boolean {
  const lines = text.split('\n');
  let consecutiveSpacedLines = 0;
  
  for (const line of lines) {
    // Check for multiple spaces or tabs in a row (potential table structure)
    if (/\s{2,}/.test(line) || /\t/.test(line)) {
      consecutiveSpacedLines++;
      if (consecutiveSpacedLines >= 3) {
        return true;
      }
    } else {
      consecutiveSpacedLines = 0;
    }
  }
  
  return false;
}
