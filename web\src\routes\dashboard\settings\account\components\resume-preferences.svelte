<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Form from '$lib/components/ui/form/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import { Switch } from '$lib/components/ui/switch/index.js';

  const { form, formData } = $props<{ form: any; formData: any }>();

  const privacyOptions = [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
  ];

  // Handle setting change
  function handleSettingChange(setting: string, value: any) {
    formData.update((f: any) => ({ ...f, [setting]: value }));
    
    // Submit the form to save changes to the database
    setTimeout(() => {
      const submitButton = document.getElementById('submit-button') as HTMLButtonElement;
      submitButton?.click();
    }, 100);
  }
</script>

<Card.Root>
  <Card.Header class="p-6">
    <Card.Title>Resume Preferences</Card.Title>
    <Card.Description>Configure how your resumes are handled and processed.</Card.Description>
  </Card.Header>
  <Card.Content class="space-y-6 p-6 pt-0">
    <Form.Field {form} name="defaultResumeParsingEnabled">
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <div class="font-medium">Default Resume Parsing</div>
          <Form.Description>Enable resume parsing by default when uploading new resumes</Form.Description>
        </div>
        <Form.Control>
          <Switch
            checked={Boolean($formData.defaultResumeParsingEnabled)}
            onCheckedChange={(checked) => handleSettingChange('defaultResumeParsingEnabled', checked)} />
        </Form.Control>
      </div>
      <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="autoUpdateProfileFromResume">
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <div class="font-medium">Auto-Update Profile</div>
          <Form.Description>Automatically update your profile with parsed resume data</Form.Description>
        </div>
        <Form.Control>
          <Switch
            checked={Boolean($formData.autoUpdateProfileFromResume)}
            onCheckedChange={(checked) => handleSettingChange('autoUpdateProfileFromResume', checked)} />
        </Form.Control>
      </div>
      <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="resumePrivacyLevel">
      <Form.Control>
        {#snippet children({ props })}
          <div class="space-y-0.5">
            <Form.Label>Resume Privacy Level</Form.Label>
            <Form.Description>Control who can access your resume information</Form.Description>
          </div>
          <Select.Root
            {...props}
            type="single"
            value={$formData.resumePrivacyLevel || 'private'}
            onValueChange={(value) => handleSettingChange('resumePrivacyLevel', value)}>
            <Select.Trigger class="w-full">
              <Select.Value placeholder="Select privacy level" />
            </Select.Trigger>
            <Select.Content class="max-h-60">
              <Select.Group>
                {#each privacyOptions as option (option.value)}
                  <Select.Item value={option.value} label={option.label}
                    >{option.label}</Select.Item>
                {/each}
              </Select.Group>
            </Select.Content>
          </Select.Root>
        {/snippet}
      </Form.Control>
      <Form.FieldErrors />
    </Form.Field>
  </Card.Content>
</Card.Root>
