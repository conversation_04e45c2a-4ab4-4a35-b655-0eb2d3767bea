<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { Edit } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import type { PersonalInfoSchema } from '$lib/validators/profile';
  import PersonalInfoModal from './PersonalInfoModal.svelte';

  // Props
  const { data, onSave } = $props<{
    data: PersonalInfoSchema;
    onSave: (data: PersonalInfoSchema) => Promise<boolean>;
  }>();

  // Modal state
  let modalOpen = $state(false);

  // Open edit modal
  function openEditModal() {
    modalOpen = true;
  }

  // Handle modal save
  async function handleSave(updatedData: PersonalInfoSchema): Promise<boolean> {
    try {
      const success = await onSave(updatedData);
      return success;
    } catch (error) {
      console.error('Error saving personal information:', error);
      toast.error('Failed to save personal information');
      return false;
    }
  }
</script>

<div class="rounded-lg border p-6">
  <div class="flex items-center justify-between">
    <h2 class="text-xl font-semibold">Personal Information</h2>
    <Button variant="ghost" size="sm" onclick={openEditModal}>
      <Edit class="mr-2 h-4 w-4" />
      Edit
    </Button>
  </div>
  <p class="text-muted-foreground mt-2 text-sm">
    This information helps you fill out job applications faster and more accurately.
  </p>

  <div class="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">Email</h3>
      <p>{data.email || 'Not specified'}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">Phone</h3>
      <p>{data.phone || 'Not specified'}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">Address</h3>
      <p>{data.address || 'Not specified'}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">City</h3>
      <p>{data.city || 'Not specified'}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">State</h3>
      <p>{data.state || 'Not specified'}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">ZIP</h3>
      <p>{data.zip || 'Not specified'}</p>
    </div>
    <div>
      <h3 class="text-muted-foreground text-sm font-medium">Country</h3>
      <p>{data.country || 'USA'}</p>
    </div>
  </div>
</div>

<!-- Personal Info Modal -->
<PersonalInfoModal
  open={modalOpen}
  {data}
  onClose={() => (modalOpen = false)}
  onSave={handleSave} />
