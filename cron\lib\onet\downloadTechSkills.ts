// cron/scripts/downloadOnetTechSkills.ts

import { chromium } from "playwright";
import fs from "fs/promises";
import path from "path";
import { logger } from "../../utils/logger";

const FILE_URL =
  "https://www.onetcenter.org/dl_files/database/db_29_2_text/Technology%20Skills.txt";
const OUTPUT_DIR = path.resolve("data");
const OUTPUT_FILE = path.join(OUTPUT_DIR, "Technology%20Skills.txt");

export async function downloadOnetTechSkills() {
  logger.info("🌐 Launching browser to download Technology Skills file...");

  // Always use headless mode for this function
  const browser = await chromium.launch({
    headless: true,
    args: ["--no-sandbox"], // Required for Docker environments
  });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    logger.info(`📥 Downloading from: ${FILE_URL}`);

    const response = await page.goto(FILE_URL);
    const content = await response?.text();

    if (!content || content.includes("<html")) {
      throw new Error(
        "Received HTML response instead of file content. Possibly blocked."
      );
    }

    await fs.mkdir(OUTPUT_DIR, { recursive: true });
    await fs.writeFile(OUTPUT_FILE, content);

    logger.info(`✅ File saved to: ${OUTPUT_FILE}`);
  } catch (err) {
    logger.error("❌ Failed to download file:", err);
  } finally {
    await browser.close();
  }
}

await downloadOnetTechSkills();
