#!/usr/bin/env node

/**
 * This script runs a specified command with a memory limit.
 * If the process exceeds the memory limit, it will be terminated.
 * Usage: tsx run-with-memory-limit.ts <command>
 * Example: tsx run-with-memory-limit.ts jobs/parallelJobScraper.ts
 */

import { spawn } from "child_process";
import path from "path";
import os from "os";

// Get the command to run from command line arguments
const args = process.argv.slice(2);

if (args.length === 0) {
  console.error("Error: No command specified");
  console.error("Usage: tsx run-with-memory-limit.ts <command>");
  console.error("Example: tsx run-with-memory-limit.ts jobs/parallelJobScraper.ts");
  process.exit(1);
}

// Memory limit in MB (default: 80% of total memory)
const totalMemoryMB = Math.floor(os.totalmem() / 1024 / 1024);
const defaultMemoryLimitMB = Math.floor(totalMemoryMB * 0.8);
const memoryLimitMB = parseInt(process.env.MEMORY_LIMIT_MB || defaultMemoryLimitMB.toString(), 10);

console.log(`Total system memory: ${totalMemoryMB} MB`);
console.log(`Setting memory limit to: ${memoryLimitMB} MB (${Math.round((memoryLimitMB / totalMemoryMB) * 100)}% of total)`);

// Determine if we're in production
const isProduction = process.env.NODE_ENV === "production";

// In production, don't try to use --expose-gc since it's not allowed on Render.com
const command = "tsx";
const commandArgs = isProduction ? [...args] : ["--expose-gc", ...args];

console.log(`Running: ${command} ${commandArgs.join(" ")} (GC flag: ${!isProduction})`);

// Memory monitoring interval in milliseconds
const MEMORY_CHECK_INTERVAL = 30000; // 30 seconds

// Spawn the process
const child = spawn(command, commandArgs, {
  stdio: "inherit",
  shell: true,
});

// Set up memory monitoring
const memoryMonitorInterval = setInterval(() => {
  const memoryUsage = process.memoryUsage();
  const usedMemoryMB = Math.round(memoryUsage.rss / 1024 / 1024);
  const memoryUsagePercent = Math.round((usedMemoryMB / totalMemoryMB) * 100);
  
  console.log(`Memory usage: ${usedMemoryMB} MB / ${memoryLimitMB} MB (${memoryUsagePercent}% of total system memory)`);
  
  if (usedMemoryMB > memoryLimitMB) {
    console.error(`Memory limit exceeded: ${usedMemoryMB} MB > ${memoryLimitMB} MB`);
    console.error("Terminating process...");
    
    // Kill the child process
    child.kill("SIGTERM");
    
    // Give it a moment to clean up, then force kill if needed
    setTimeout(() => {
      if (!child.killed) {
        console.error("Process did not terminate gracefully, force killing...");
        child.kill("SIGKILL");
      }
      
      // Exit with a special code to indicate memory limit exceeded
      process.exit(143);
    }, 5000);
    
    // Clear the interval
    clearInterval(memoryMonitorInterval);
  }
}, MEMORY_CHECK_INTERVAL);

// Handle process exit
child.on("close", (code) => {
  clearInterval(memoryMonitorInterval);
  console.log(`Process exited with code ${code}`);
  process.exit(code || 0);
});

// Handle process errors
child.on("error", (err) => {
  clearInterval(memoryMonitorInterval);
  console.error("Failed to start process:", err);
  process.exit(1);
});
