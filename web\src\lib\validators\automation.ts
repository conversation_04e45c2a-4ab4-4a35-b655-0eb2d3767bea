import { z } from 'zod';

// Automation form schema
export const automationFormSchema = z
  .object({
    profileId: z.string().min(1, 'Please select a profile'),
    keywords: z.array(z.string()).min(0),
    locations: z.array(z.string()).min(0),
    maxJobsToApply: z.number().min(1).max(50).default(10),
    minMatchScore: z.number().min(60).max(95).default(70),
    autoApplyEnabled: z.boolean().default(false),
    salaryRange: z.array(z.number()).length(2).default([50, 120]),
    experienceRange: z.array(z.number()).length(2).default([2, 8]),
    jobTypes: z.array(z.string()).default([]),
    remotePreference: z.string().default('any'),
    companySizePreference: z.array(z.string()).default([]),
    excludeCompanies: z.array(z.string()).default([]),
    preferredCompanies: z.array(z.string()).default([]),
  })
  .refine((data) => data.keywords.length > 0 || data.locations.length > 0, {
    message: 'Please select at least one keyword or location',
    path: ['keywords'],
  })
  .refine((data) => data.salaryRange[0] <= data.salaryRange[1], {
    message: 'Minimum salary cannot be greater than maximum salary',
    path: ['salaryRange'],
  })
  .refine((data) => data.experienceRange[0] <= data.experienceRange[1], {
    message: 'Minimum experience cannot be greater than maximum experience',
    path: ['experienceRange'],
  });

export type AutomationFormSchema = z.infer<typeof automationFormSchema>;
