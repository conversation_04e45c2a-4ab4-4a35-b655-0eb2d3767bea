<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Form from '$lib/components/ui/form/index.js';
  import { Switch } from '$lib/components/ui/switch/index.js';

  const { form, formData } = $props<{ form: any; formData: any }>();

  // Handle setting change
  function handleSettingChange(setting: string, value: boolean) {
    formData.update((f: any) => ({ ...f, [setting]: value }));
    
    // Submit the form to save changes to the database
    setTimeout(() => {
      const submitButton = document.getElementById('submit-button') as HTMLButtonElement;
      submitButton?.click();
    }, 100);
  }
</script>

<Card.Root>
  <Card.Header class="p-6">
    <Card.Title>Application Preferences</Card.Title>
    <Card.Description>Configure how the application handles your job applications and resumes.</Card.Description>
  </Card.Header>
  <Card.Content class="space-y-6 p-6 pt-0">
    <Form.Field {form} name="autoParseResumes">
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <div class="font-medium">Auto-Parse Resumes</div>
          <Form.Description>Automatically parse resume data when uploading new resumes</Form.Description>
        </div>
        <Form.Control>
          <Switch
            checked={Boolean($formData.autoParseResumes)}
            onCheckedChange={(checked) => handleSettingChange('autoParseResumes', checked)} />
        </Form.Control>
      </div>
      <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="autoSaveApplications">
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <div class="font-medium">Auto-Save Applications</div>
          <Form.Description>Automatically save job applications as you fill them out</Form.Description>
        </div>
        <Form.Control>
          <Switch
            checked={Boolean($formData.autoSaveApplications)}
            onCheckedChange={(checked) => handleSettingChange('autoSaveApplications', checked)} />
        </Form.Control>
      </div>
      <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="applicationReminders">
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <div class="font-medium">Application Reminders</div>
          <Form.Description>Receive reminders about pending applications and follow-ups</Form.Description>
        </div>
        <Form.Control>
          <Switch
            checked={Boolean($formData.applicationReminders)}
            onCheckedChange={(checked) => handleSettingChange('applicationReminders', checked)} />
        </Form.Control>
      </div>
      <Form.FieldErrors />
    </Form.Field>
  </Card.Content>
</Card.Root>
