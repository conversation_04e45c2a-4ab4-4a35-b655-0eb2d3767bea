// web/src/routes/api/health/report/+server.ts
// API endpoint for services to report their health status directly to the database

import { json } from '@sveltejs/kit';
import { logger } from '$lib/server/logger';
import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types';

interface HealthReport {
  service: string;
  status: 'operational' | 'degraded' | 'outage' | 'maintenance';
  responseTime?: number;
  details?: {
    cpuUsage?: number;
    memoryUsage?: number;
    activeWorkers?: number;
    queueSize?: number;
    errorRate?: number;
    uptime?: number;
    [key: string]: any;
  };
  timestamp?: string;
}

// Service name mapping to consumer-facing categories
const SERVICE_MAPPING: Record<string, string> = {
  // Worker service maps to Automation
  'auto-apply-worker': 'Automation',
  worker: 'Automation',
  automation: 'Automation',

  // AI service maps to System (internal AI processing)
  'auto-apply-ai': 'System',
  ai: 'System',

  // Web service maps to Website
  'auto-apply-web': 'Website',
  web: 'Website',
  website: 'Website',

  // Direct mappings for consumer-facing services
  matches: 'Matches',
  jobs: 'Jobs',
  tracker: 'Tracker',
  documents: 'Documents',
  system: 'System',

  // Resume services map to Documents
  'resume-builder': 'Documents',
  'resume-scanner': 'Documents',
  resume: 'Documents',

  // Job services map to Jobs
  'job-search': 'Jobs',
  'job-scraper': 'Jobs',
  scraper: 'Jobs',

  // Application services map to Tracker
  'application-system': 'Tracker',
  applications: 'Tracker',

  // Account services map to System
  'account-services': 'System',
  accounts: 'System',
  auth: 'System',

  // Database and infrastructure map to System
  database: 'System',
  redis: 'System',
  api: 'System',
};

export const POST: RequestHandler = async ({ request }) => {
  try {
    const healthReport: HealthReport = await request.json();

    // Validate required fields
    if (!healthReport.service || !healthReport.status) {
      return json(
        {
          error: 'Missing required fields: service and status are required',
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Map service name to consumer-facing category
    const serviceName = healthReport.service.toLowerCase();
    const mappedServiceName = SERVICE_MAPPING[serviceName] || 'System';

    logger.info(
      `Health report received: ${healthReport.service} -> ${mappedServiceName} (${healthReport.status})`
    );

    // Find or create the service status record
    let serviceStatus = await prisma.serviceStatus.findUnique({
      where: { name: mappedServiceName },
    });

    if (!serviceStatus) {
      // Create the service if it doesn't exist
      const serviceDescriptions: Record<string, string> = {
        Matches: 'Job matching and recommendations',
        Jobs: 'Job search and listings',
        Tracker: 'Application tracking',
        Documents: 'Resume and document management',
        Automation: 'Automated job application tools',
        System: 'Core system services',
        Website: 'Website and user interface',
      };

      serviceStatus = await prisma.serviceStatus.create({
        data: {
          name: mappedServiceName,
          status: healthReport.status,
          description: serviceDescriptions[mappedServiceName] || 'Service',
          lastCheckedAt: new Date(),
        },
      });

      logger.info(`Created new service status record for ${mappedServiceName}`);
    } else {
      // Update existing service status
      const statusChanged = serviceStatus.status !== healthReport.status;

      await prisma.serviceStatus.update({
        where: { id: serviceStatus.id },
        data: {
          status: healthReport.status,
          lastCheckedAt: new Date(),
        },
      });

      // Record status change in history if status changed
      if (statusChanged) {
        await prisma.serviceStatusHistory.create({
          data: {
            serviceId: serviceStatus.id,
            status: healthReport.status,
          },
        });

        logger.info(
          `Status changed for ${mappedServiceName}: ${serviceStatus.status} -> ${healthReport.status}`
        );
      }
    }

    return json({
      success: true,
      message: `Health status updated for ${mappedServiceName}`,
      mappedService: mappedServiceName,
      originalService: healthReport.service,
      status: healthReport.status,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Error processing health report:', error);

    return json(
      {
        success: false,
        error: 'Failed to process health report',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
};

// GET endpoint to retrieve current service health from database
export const GET: RequestHandler = async ({ url }) => {
  try {
    const service = url.searchParams.get('service');

    if (service) {
      // Get specific service status
      const serviceStatus = await prisma.serviceStatus.findUnique({
        where: { name: service },
        include: {
          statusHistory: {
            orderBy: { recordedAt: 'desc' },
            take: 10, // Last 10 status changes
          },
        },
      });

      if (!serviceStatus) {
        return json({ error: `Service '${service}' not found` }, { status: 404 });
      }

      return json({
        service: serviceStatus.name,
        status: serviceStatus.status,
        description: serviceStatus.description,
        lastCheckedAt: serviceStatus.lastCheckedAt.toISOString(),
        history: serviceStatus.statusHistory.map((h) => ({
          status: h.status,
          recordedAt: h.recordedAt.toISOString(),
        })),
        timestamp: new Date().toISOString(),
      });
    } else {
      // Get all service statuses
      const services = await prisma.serviceStatus.findMany({
        orderBy: { name: 'asc' },
      });

      return json({
        services: services.map((s) => ({
          name: s.name,
          status: s.status,
          description: s.description,
          lastCheckedAt: s.lastCheckedAt.toISOString(),
        })),
        totalServices: services.length,
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    logger.error('Error retrieving service health:', error);

    return json(
      {
        error: 'Failed to retrieve service health',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
};

// OPTIONS handler for CORS
export const OPTIONS: RequestHandler = async () => {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
};
