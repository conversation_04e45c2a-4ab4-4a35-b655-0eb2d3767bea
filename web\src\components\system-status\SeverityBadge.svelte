<script lang="ts">
  import { cn } from '$lib/utils.js';
  import { AlertTriangle, AlertCircle, Info } from 'lucide-svelte';
  import type { SeverityLevelType } from '../../routes/system-status/types';

  // Props
  const { severity, className = '' } = $props<{
    severity: SeverityLevelType;
    className?: string;
  }>();

  // Get badge color based on severity
  function getBadgeColor(severity: SeverityLevelType): string {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'major':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'minor':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'maintenance':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'info':
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }

  // Get icon based on severity
  function getSeverityIcon(severity: SeverityLevelType) {
    switch (severity) {
      case 'critical':
        return AlertCircle;
      case 'major':
      case 'minor':
        return AlertTriangle;
      case 'maintenance':
      case 'info':
      default:
        return Info;
    }
  }

  // Format severity text
  function formatSeverityText(severity: SeverityLevelType): string {
    switch (severity) {
      case 'critical':
        return 'Critical Outage';
      case 'major':
        return 'Major Outage';
      case 'minor':
        return 'Minor Outage';
      case 'maintenance':
        return 'Maintenance';
      case 'info':
      default:
        return 'Information';
    }
  }

  const badgeColor = $derived(getBadgeColor(severity));
  const SeverityIcon = $derived(getSeverityIcon(severity));
  const severityText = $derived(formatSeverityText(severity));
</script>

<div
  class={cn(
    'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold',
    badgeColor,
    className
  )}>
  <SeverityIcon class="mr-1 h-3 w-3" />
  {severityText}
</div>
