<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import * as Badge from '$lib/components/ui/badge/index.js';
  import * as Checkbox from '$lib/components/ui/checkbox/index.js';
  import { FileText, Upload, Save, Check } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import { goto } from '$app/navigation';
  import type { ResumeSchema } from '$lib/validators/profile';

  // Props
  const {
    open,
    data,
    onClose,
    onSave,
    disabled = false,
  } = $props<{
    open: boolean;
    data: ResumeSchema | null;
    onClose: () => void;
    onSave: (data: ResumeSchema) => Promise<boolean>;
    disabled?: boolean;
  }>();

  // Create a local copy of the data for editing
  let formData = $state({
    resumeId: data?.resumeId || '',
    fileName: data?.fileName || '',
    uploadedAt: data?.uploadedAt || '',
    isDefault: data?.isDefault || false,
    parseIntoProfile: true, // Default to true
  });

  // Form submission state
  let submitting = $state(false);

  // Existing resumes state
  let existingResumes = $state<any[]>([]);
  let loadingResumes = $state(false);
  let showUpgradeMessage = $state(false);

  // Resume parsing state
  let isAlreadyParsed = $state(false);
  let checkingParseStatus = $state(false);
  let isParsing = $state(false);

  // Import WebSocket handlers
  import {
    registerParsingListener,
    unregisterParsingListener,
  } from '$lib/websocket/resume-parsing-handler';
  import { onDestroy } from 'svelte';

  // Reset form when modal opens
  $effect(() => {
    if (open) {
      // Reset parsing state
      isAlreadyParsed = false;
      checkingParseStatus = false;

      formData = {
        resumeId: data?.resumeId || '',
        fileName: data?.fileName || '',
        uploadedAt: data?.uploadedAt || '',
        isDefault: data?.isDefault || false,
        parseIntoProfile: true, // Default to true
      };

      // Load existing resumes when modal opens
      loadExistingResumes();

      // Check if the resume is already parsed
      if (data?.resumeId) {
        checkResumeParseStatus(data.resumeId);
      }
    }
  });

  // Load existing resumes
  async function loadExistingResumes() {
    loadingResumes = true;
    try {
      const response = await fetch('/api/documents?type=resume');
      if (!response.ok) {
        throw new Error('Failed to load existing resumes');
      }

      const data = await response.json();
      existingResumes = data.documents || [];
    } catch (error) {
      console.error('Error loading existing resumes:', error);
    } finally {
      loadingResumes = false;
    }
  }

  // Check if resume is already parsed
  async function checkResumeParseStatus(resumeId: string) {
    if (!resumeId) return;

    checkingParseStatus = true;
    isAlreadyParsed = false;

    try {
      const response = await fetch(`/api/resume/${resumeId}/status`);
      if (!response.ok) {
        throw new Error('Failed to check resume parse status');
      }

      const statusData = await response.json();
      console.log('Resume parse status check (one-time):', statusData);

      // If the resume has already been parsed, set isAlreadyParsed to true
      if (statusData.isParsed && statusData.parsedData) {
        isAlreadyParsed = true;
        // We'll show that it's already parsed, but we won't automatically
        // disable parsing - let the user decide if they want to re-parse
        // Default to false to avoid unnecessary re-parsing
        formData.parseIntoProfile = false;
      } else {
        // If not parsed, default parseIntoProfile to true
        isAlreadyParsed = false;
        formData.parseIntoProfile = true;
      }

      // Only check parsing status if the resume is not already parsed
      // This prevents unnecessary API calls for resumes that are already parsed
      if (!isAlreadyParsed) {
        await checkParsingStatus(resumeId);
      }
    } catch (error) {
      console.error('Error checking resume parse status:', error);
    } finally {
      checkingParseStatus = false;
    }
  }

  // Check if resume is currently being parsed
  async function checkParsingStatus(resumeId: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/resume/${resumeId}/parsing-status`);
      if (!response.ok) {
        throw new Error('Failed to check parsing status');
      }

      const statusData = await response.json();

      if (statusData.isParsing) {
        isParsing = true;
        setupParsingListener(resumeId);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking parsing status:', error);
      return false;
    }
  }

  // Setup WebSocket listener for resume parsing status
  function setupParsingListener(resumeId: string) {
    // Unregister any existing listener first
    unregisterParsingListener(resumeId);

    // Register new listener
    registerParsingListener(
      resumeId,
      (resumeId, parsedData) => {
        console.log(`Resume parsing completed for ${resumeId}:`, parsedData);
        isParsing = false;
        isAlreadyParsed = true;
        formData.parseIntoProfile = false;

        // Show success toast notification
        toast.success('Resume parsing completed successfully', {
          description: 'Your resume has been parsed and the data has been added to your profile.',
          duration: 5000,
        });

        // Close the modal
        onClose();
      },
      (resumeId, error) => {
        console.error(`Resume parsing failed for ${resumeId}:`, error);
        isParsing = false;

        // Show error toast notification
        toast.error(`Resume parsing failed`, {
          description: error ? `Error: ${error}` : 'Please try again later.',
          duration: 5000,
        });
      }
    );

    // Also listen for the custom resume-parsing-completed event
    const handleParsingCompleted = (event: any) => {
      if (event.detail?.resumeId === resumeId) {
        console.log(`Received resume-parsing-completed event for ${resumeId} in modal`);
        isParsing = false;
        isAlreadyParsed = true;
        formData.parseIntoProfile = false;

        // Close the modal
        onClose();
      }
    };

    window.addEventListener('resume-parsing-completed', handleParsingCompleted);

    // Store the event handler for cleanup
    parsingCompletedHandler = handleParsingCompleted;
  }

  // Store the event handler for cleanup
  let parsingCompletedHandler: ((event: any) => void) | null = null;

  // Cleanup listener on component destroy
  onDestroy(() => {
    if (formData.resumeId) {
      unregisterParsingListener(formData.resumeId);
    }

    // Also remove the custom event listener
    if (parsingCompletedHandler) {
      window.removeEventListener('resume-parsing-completed', parsingCompletedHandler);
      parsingCompletedHandler = null;
    }
  });

  // Select existing resume
  async function selectExistingResume(document: any) {
    console.log('Selected document:', document);
    const resumeId = document.resume?.id || '';

    formData = {
      ...formData,
      resumeId,
      fileName: document.label || document.fileName || '',
      uploadedAt: document.createdAt || new Date().toISOString(),
      isDefault: true,
    };

    // Check if the resume is already parsed
    if (resumeId) {
      await checkResumeParseStatus(resumeId);

      // If the resume is already parsed, we should disable the parse checkbox
      // since we don't need to parse it again
      if (isAlreadyParsed) {
        console.log('Resume is already parsed, disabling parse checkbox');
        formData.parseIntoProfile = false;
      }
    }
  }

  // Format date for display
  function formatDate(dateString: string | undefined): string {
    if (!dateString) return 'Not available';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  // Handle form submission
  async function handleSubmit() {
    // Basic validation
    if (!formData.fileName) {
      toast.error('Please upload a resume file');
      return;
    }

    // If parsing is requested, set isParsing to true immediately
    if (formData.parseIntoProfile) {
      isParsing = true;
    }

    // Submit the form
    submitting = true;
    try {
      const success = await onSave(formData);
      if (success) {
        toast.success('Resume updated successfully');
        onClose();
      } else {
        // Reset parsing status if save failed
        isParsing = false;
      }
    } catch (error) {
      console.error('Error saving resume:', error);
      toast.error('Failed to save resume');
      // Reset parsing status on error
      isParsing = false;
    } finally {
      submitting = false;
    }
  }

  // Handle file upload
  async function handleFileUpload(event: Event) {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;

    const file = input.files[0];

    // Check file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size exceeds 5MB limit');
      return;
    }

    // Check file type
    const fileExt = file.name.split('.').pop()?.toLowerCase();
    if (!['pdf', 'doc', 'docx'].includes(fileExt || '')) {
      toast.error('Only PDF, DOC, and DOCX files are supported');
      return;
    }

    submitting = true;

    try {
      // Create FormData for upload
      const uploadFormData = new FormData();
      uploadFormData.append('file', file);

      // Get profile ID from URL
      const profileId = window.location.pathname.split('/').pop();
      if (profileId) {
        uploadFormData.append('profileId', profileId);
      }

      uploadFormData.append('label', file.name);
      uploadFormData.append('type', 'resume');
      uploadFormData.append('parseIntoProfile', formData.parseIntoProfile.toString());

      // Upload the file
      try {
        const response = await fetch('/api/resume/upload', {
          method: 'POST',
          body: uploadFormData,
        });

        if (!response.ok) {
          const errorData = await response.json();

          // Check for plan limit error
          if (
            response.status === 403 &&
            (errorData.limitReached || errorData.message?.includes('limit'))
          ) {
            // Show upgrade message
            showUpgradeMessage = true;
            throw new Error(
              errorData.message ||
                'Document limit reached. Please upgrade your plan to upload more documents.'
            );
          } else {
            throw new Error(errorData.message || 'Failed to upload resume');
          }
        }

        // Process successful response
        const data = await response.json();
        console.log('Resume upload response:', data);

        // Update form data with response
        const resumeId = data.resume.id;
        formData = {
          ...formData,
          resumeId,
          fileName: data.document.label,
          uploadedAt: data.document.createdAt || new Date().toISOString(),
          isDefault: true,
        };

        // Check if the resume was already parsed from the response
        if (data.alreadyParsed) {
          isAlreadyParsed = true;
          formData.parseIntoProfile = false;
          toast.success('Resume uploaded successfully. It was already parsed previously.');
        } else {
          // If not already parsed from the response, check the status
          if (resumeId) {
            await checkResumeParseStatus(resumeId);
          }

          toast.success(data.message || 'Resume uploaded successfully');
        }
      } catch (error) {
        console.error('Error in fetch operation:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error uploading resume:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to upload resume');
    } finally {
      submitting = false;
    }
  }
</script>

<Dialog.Root {open} onOpenChange={(isOpen) => !isOpen && onClose()}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="sm:max-w-[500px]">
      <Dialog.Header>
        <Dialog.Title>{data?.resumeId ? 'Replace Resume' : 'Upload Resume'}</Dialog.Title>
        <Dialog.Description>Upload your resume to use with job applications.</Dialog.Description>
      </Dialog.Header>

      <div class="grid gap-4 py-4">
        {#if showUpgradeMessage}
          <div
            class="rounded-md border border-yellow-300 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-900/20">
            <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Document Limit Reached
            </h3>
            <p class="text-muted-foreground mt-2 text-xs">
              You have reached your document upload limit. Please upgrade your plan to upload more
              documents.
            </p>
            <Button
              variant="outline"
              class="mt-2"
              size="sm"
              onclick={() => {
                goto('/pricing?plan=pro&billing=monthly');
                onClose();
              }}>
              Upgrade Plan
            </Button>
          </div>
        {/if}

        {#if checkingParseStatus}
          <div
            class="flex items-center rounded-md bg-amber-50 p-2 text-sm text-amber-700 dark:bg-amber-900/20 dark:text-amber-200">
            <span>Checking resume parse status...</span>
          </div>
        {:else if isParsing}
          <div
            class="flex items-center rounded-md bg-amber-50 p-2 text-amber-700 dark:bg-amber-900/20 dark:text-amber-200">
            <span class="mr-2 animate-pulse">●</span>
            <span class="text-sm">Resume parsing in progress...</span>
          </div>
        {:else if isAlreadyParsed}
          <div
            class="flex items-center rounded-md bg-green-50 p-2 text-green-700 dark:bg-green-900/20 dark:text-green-200">
            <Check class="mr-2 h-4 w-4" />
            <span class="text-sm">This resume has already been parsed.</span>
          </div>
        {/if}

        <!-- Currently Selected Resume -->
        {#if formData.fileName}
          <div class="rounded-md border p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <FileText class="mr-2 h-5 w-5 text-blue-500" />
                <div>
                  <p class="font-medium">{formData.fileName}</p>
                  <p class="text-muted-foreground text-xs">
                    Uploaded: {formatDate(formData.uploadedAt)}
                  </p>
                </div>
              </div>
              <Badge.Badge variant="outline">Selected</Badge.Badge>
            </div>
          </div>
        {/if}

        <!-- Options Section -->
        <div class="rounded-md border p-4">
          <h3 class="mb-3 text-sm font-medium">Resume Options</h3>

          <!-- Upload New Resume -->
          <div class="mb-4">
            <h4 class="text-muted-foreground mb-2 text-xs font-medium">Upload New Resume</h4>
            <div class="flex items-center space-x-2">
              <input
                type="file"
                id="resumeUpload"
                accept=".pdf,.doc,.docx"
                class="hidden"
                onchange={handleFileUpload} />
              <Button
                type="button"
                variant="outline"
                class="w-full"
                disabled={disabled || submitting}
                onclick={() => document.getElementById('resumeUpload')?.click()}>
                <Upload class="mr-2 h-4 w-4" />
                Choose File
              </Button>
            </div>
            <p class="text-muted-foreground mt-1 text-xs">
              Accepted file formats: PDF, DOC, DOCX. Maximum file size: 5MB.
            </p>
          </div>

          <!-- Existing Resumes -->
          {#if existingResumes.length > 0}
            <div class="">
              <h4 class="text-muted-foreground mb-2 text-center text-xs font-medium">
                Or Select Existing Resume
              </h4>
              <div class="max-h-40 overflow-y-auto rounded-md border">
                {#each existingResumes as document}
                  <button
                    type="button"
                    class="hover:bg-muted/50 flex w-full items-center border-b p-3 text-left last:border-0 {formData.resumeId ===
                    document.resume?.id
                      ? 'bg-muted/30'
                      : ''}"
                    onclick={() => selectExistingResume(document)}>
                    <FileText class="mr-2 h-4 w-4 text-blue-500" />
                    <div>
                      <p class="text-sm font-medium">{document.label || 'Unnamed Resume'}</p>
                      <p class="text-muted-foreground text-xs">
                        Uploaded: {formatDate(document.createdAt)}
                      </p>
                    </div>
                    {#if formData.resumeId === document.resume?.id}
                      <Badge.Badge variant="outline" class="ml-auto">Selected</Badge.Badge>
                    {/if}
                  </button>
                {/each}
              </div>
            </div>
          {:else if loadingResumes}
            <div class="text-muted-foreground text-center text-sm">Loading your resumes...</div>
          {/if}
        </div>

        <!-- Parse Option -->
        <div class="space-y-2">
          <div class="flex items-center space-x-2">
            <Checkbox.Root
              id="parseIntoProfile"
              checked={formData.parseIntoProfile}
              disabled={isParsing}
              onCheckedChange={(checked) => {
                formData.parseIntoProfile = checked === true;

                // If user checks the box and the resume is already parsed,
                // we need to check if it's currently being parsed
                if (checked && isAlreadyParsed && formData.resumeId) {
                  checkParsingStatus(formData.resumeId);
                }
              }} />
            <Label
              for="parseIntoProfile"
              class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              Parse resume data into profile
              {#if isParsing}
                <span class="text-muted-foreground">(parsing in progress)</span>
              {:else if isAlreadyParsed}
                <span class="text-muted-foreground">(already parsed)</span>
              {/if}
            </Label>
          </div>
          <p class="text-muted-foreground ml-6 text-xs">
            {#if isParsing}
              Resume parsing is currently in progress. Please wait until it completes.
            {:else if isAlreadyParsed}
              This resume has already been parsed. You can use it as is, or check the box to
              re-parse it if you need to update your profile data.
            {:else}
              When enabled, we'll extract information from your resume and update your profile with
              it.
            {/if}
          </p>
        </div>
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={onClose} disabled={submitting || isParsing}
          >Cancel</Button>
        <Button
          onclick={handleSubmit}
          disabled={disabled || submitting || !formData.fileName || isParsing}>
          {#if submitting}
            <span class="mr-2">Saving...</span>
          {:else if isParsing}
            <span class="mr-2">Parsing in progress...</span>
          {:else}
            <Save class="mr-2 h-4 w-4" />
            Save Changes
          {/if}
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
