// cron/utils/stealthUserAgent.ts
import { logger } from "./logger";
import StealthPlugin from "puppeteer-extra-plugin-stealth";
import UserAgentPlugin from "puppeteer-extra-plugin-stealth/evasions/user-agent-override";

/**
 * Get a random user agent string using the stealth plugin
 * Focused on USA/English browsers
 */
export function getRandomUserAgent(): string {
  // USA/English-focused modern user agents
  const usaUserAgents = [
    // Chrome on Windows (USA)
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    // Firefox on Windows (USA)
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
    // Edge on Windows (USA)
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
    // Chrome on Windows (USA) - newer version
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
    // Chrome on Windows (USA) - slightly older version
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
  ];

  // Return a random user agent from the USA-focused list
  return usaUserAgents[Math.floor(Math.random() * usaUserAgents.length)];
}

/**
 * Get random browser headers to help with fingerprinting evasion
 */
export function getRandomHeaders() {
  const languages = [
    "en-US,en;q=0.9",
    "en-US,en;q=0.8",
    "en-GB,en;q=0.9,en-US;q=0.8",
    "en-CA,en;q=0.9,fr-CA;q=0.8",
  ];

  const acceptHeaders = [
    "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
  ];

  const cacheControl = Math.random() > 0.5 ? "max-age=0" : "no-cache";

  return {
    "Accept-Language": languages[Math.floor(Math.random() * languages.length)],
    Accept: acceptHeaders[Math.floor(Math.random() * acceptHeaders.length)],
    "Cache-Control": cacheControl,
    "Sec-Ch-Ua": '"Google Chrome";v="120", "Chromium";v="120"',
    "Sec-Ch-Ua-Mobile": "?0",
    "Sec-Ch-Ua-Platform": '"Windows"',
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "none",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
  };
}

/**
 * Create a stealth user agent plugin with custom settings
 */
export function createStealthUserAgent(
  options: { userAgent?: string; locale?: string } = {}
) {
  const userAgent = options.userAgent || getRandomUserAgent();
  const locale = options.locale || "en-US,en";

  return UserAgentPlugin({
    userAgent,
    locale,
    maskLinux: true,
  });
}
