<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import * as Alert from '$lib/components/ui/alert';
  import { AlertCircle, Terminal, Lock, AlertTriangle } from 'lucide-svelte';
  import { createFeatureAccess } from '$lib/models/features';
  import { openPricingModal } from '$lib/utils/pricing';
  import { isFeatureEnabled, shouldBypassLimits, getFeatureConfig } from '$lib/config/feature-flags';

  export let userData: {
    id: string;
    role?: string;
    subscription?: {
      planId?: string;
      status?: string;
      startDate?: Date;
      endDate?: Date;
      cancelAtPeriodEnd?: boolean;
      trialEndDate?: Date;
    };
    usage?: Record<string, number>;
  };

  export let featureId: string;
  export let limitId: string | undefined = undefined;
  export let showUpgradePrompt = true;
  export let fallbackMessage: string | undefined = undefined;
  export let debugMode = false;

  // Check if feature is enabled at the configuration level
  $: featureEnabled = isFeatureEnabled(featureId);
  $: bypassLimits = shouldBypassLimits(featureId);
  $: featureConfig = getFeatureConfig(featureId);

  // Create the feature access instance only if feature is enabled
  $: featureAccess = featureEnabled ? createFeatureAccess(userData) : null;

  // Check if the user can access the feature
  $: canAccess = (() => {
    // Feature disabled at config level
    if (!featureEnabled) {
      return false;
    }

    // Bypass all limits in development/testing
    if (bypassLimits) {
      return true;
    }

    // Check subscription-based access
    if (!featureAccess) {
      return false;
    }

    return limitId
      ? featureAccess.canPerformAction(featureId, limitId)
      : featureAccess.hasAccess(featureId);
  })();

  // Get the block reason if the user can't access the feature
  $: blockReason = (() => {
    if (!featureEnabled) {
      return fallbackMessage || `The ${featureConfig?.description || featureId} feature is currently disabled.`;
    }

    if (bypassLimits) {
      return '';
    }

    if (!featureAccess) {
      return 'Unable to check feature access.';
    }

    return limitId
      ? featureAccess.getBlockReason(featureId, limitId)
      : featureAccess.getBlockReason(featureId);
  })();

  // Handle upgrade
  function handleUpgrade() {
    openPricingModal({
      section: 'pro',
      currentPlanId: userData.role || userData.subscription?.planId || 'free',
    });
  }

  // Debug information
  $: debugInfo = debugMode ? {
    featureId,
    featureEnabled,
    bypassLimits,
    canAccess,
    blockReason,
    limitId,
    userRole: userData.role,
    planId: userData.subscription?.planId
  } : null;
</script>

{#if debugMode && debugInfo}
  <div class="mb-4 rounded border border-yellow-500 bg-yellow-50 p-2 text-xs">
    <strong>FeatureGuard Debug:</strong>
    <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
  </div>
{/if}

{#if canAccess}
  <slot />
{:else}
  <div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8 text-center">
    <div class="bg-muted mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full">
      {#if !featureEnabled}
        <AlertTriangle class="text-warning h-6 w-6" />
      {:else}
        <Lock class="text-muted-foreground h-6 w-6" />
      {/if}
    </div>
    
    <h3 class="mb-2 text-lg font-medium">
      {#if !featureEnabled}
        Feature Unavailable
      {:else}
        Access Restricted
      {/if}
    </h3>
    
    <p class="text-muted-foreground mb-4 max-w-md">
      {blockReason}
    </p>
    
    {#if showUpgradePrompt && featureEnabled}
      <Button variant="outline" onclick={handleUpgrade}>
        Upgrade Plan
      </Button>
    {/if}
  </div>
{/if}
