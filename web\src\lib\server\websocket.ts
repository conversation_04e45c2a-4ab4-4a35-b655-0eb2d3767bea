/**
 * WebSocket Integration
 *
 * This module provides integration with the WebSocket server running in ws-server.js.
 * It exports functions for broadcasting messages to connected WebSocket clients via Redis.
 */

import { getRedisClient } from '$lib/server/redis';

// Define the message interface
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp?: string;
}

/**
 * Broadcast a message to all connected WebSocket clients via Redis
 */
export async function broadcastMessage(message: WebSocketMessage): Promise<void> {
  try {
    // Add timestamp if not provided
    if (!message.timestamp) {
      message.timestamp = new Date().toISOString();
    }

    // Get Redis client
    const redis = await getRedisClient();
    if (!redis) {
      console.error('[WebSocket] Redis client not available for broadcasting');
      return;
    }

    // Publish to the websocket::broadcast channel
    await redis.publish('websocket::broadcast', JSON.stringify(message));

    console.log(`[WebSocket] Published message to Redis: ${message.type}`);
  } catch (error) {
    console.error('[WebSocket] Error broadcasting message:', error);
  }
}
