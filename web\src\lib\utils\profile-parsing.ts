/**
 * Utility functions for profile parsing state
 */
import { prisma } from '$lib/server/prisma';

/**
 * Check if a profile is currently being parsed
 * @param profileId The ID of the profile to check
 * @returns An object with the parsing state and resume ID if available
 */
export async function getProfileParsingState(profileId: string) {
  try {
    // Find any WorkerProcess records for this profile that are in pending or processing state
    const parsingProcess = await prisma.workerProcess.findFirst({
      where: {
        id: { startsWith: `profile-parsing-${profileId}-` },
        status: { in: ['pending', 'processing'] },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    if (!parsingProcess) {
      return { isParsing: false };
    }

    // Extract the resume ID from the process ID
    const resumeId = parsingProcess.id.split(`profile-parsing-${profileId}-`)[1];

    return {
      isParsing: true,
      resumeId,
      status: parsingProcess.status,
      startedAt: parsingProcess.startedAt,
      createdAt: parsingProcess.createdAt,
    };
  } catch (error) {
    console.error('Error checking profile parsing state:', error);
    return { isParsing: false, error: String(error) };
  }
}

/**
 * Check if a profile has a parsed resume
 * @param profileId The ID of the profile to check
 * @returns An object with the parsed state and parsed resume ID if available
 */
export async function getProfileParsedState(profileId: string) {
  try {
    // Find any ParsedResume records for this profile
    const parsedResume = await prisma.parsedResume.findFirst({
      where: {
        profileId,
      },
      orderBy: {
        parsedAt: 'desc',
      },
    });

    if (!parsedResume) {
      return { isParsed: false };
    }

    return {
      isParsed: true,
      parsedResumeId: parsedResume.id,
      resumeId: parsedResume.resumeId,
      parsedAt: parsedResume.parsedAt,
    };
  } catch (error) {
    console.error('Error checking profile parsed state:', error);
    return { isParsed: false, error: String(error) };
  }
}

/**
 * Get the profile parsing status
 * @param profileId The ID of the profile to check
 * @returns An object with the profile parsing status
 */
export async function getProfileParsingStatus(profileId: string) {
  try {
    // Check if the profile is currently being parsed
    const parsingState = await getProfileParsingState(profileId);
    
    // If the profile is being parsed, return the parsing state
    if (parsingState.isParsing) {
      return {
        status: 'parsing',
        ...parsingState,
      };
    }
    
    // Check if the profile has a parsed resume
    const parsedState = await getProfileParsedState(profileId);
    
    // If the profile has a parsed resume, return the parsed state
    if (parsedState.isParsed) {
      return {
        status: 'parsed',
        ...parsedState,
      };
    }
    
    // If the profile is not being parsed and doesn't have a parsed resume, return not_parsed
    return {
      status: 'not_parsed',
    };
  } catch (error) {
    console.error('Error getting profile parsing status:', error);
    return { status: 'error', error: String(error) };
  }
}
