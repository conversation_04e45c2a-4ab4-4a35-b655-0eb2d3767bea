// @ts-nocheck
import { prisma } from '$lib/server/prisma';
import type { PageServerLoad } from './$types';

export const load = async () => {
  try {
    const jobCollections = await prisma.$queryRaw`
      SELECT * FROM cron.job_collections ORDER BY name ASC
    `;

    return {
      // No need to return user here as it's already available from layout.server
      jobCollections,
    };
  } catch (error) {
    console.error('Error fetching job collections:', error);
    // Return empty array as fallback
    return {
      jobCollections: [],
    };
  }
};
;null as any as PageServerLoad;