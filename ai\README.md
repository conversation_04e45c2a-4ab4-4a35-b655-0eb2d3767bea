# AI Service for Resume Analysis

This service provides AI-powered resume analysis and optimization using locally hosted LLMs.

## Features

- Resume ATS compatibility analysis
- Keyword extraction and matching
- Format analysis
- Readability analysis
- Health monitoring with circuit breaker
- Resource usage metrics

## Prerequisites

- Node.js 18+
- [Ollama](https://github.com/ollama/ollama) for running local LLMs

## Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Copy the example environment file:

```bash
cp .env.example .env
```

4. Install Ollama following the instructions at [ollama.ai](https://ollama.ai)

## Configuration

Edit the `.env` file to configure the service:

- `PORT`: The port to run the server on (default: 3100)
- `LLM_PROVIDER`: The LLM provider to use (default: ollama)
- `LLM_MODEL`: The model to use (default: mistral)
- `MEMORY_THRESHOLD`: Memory usage threshold for circuit breaker (default: 85%)
- `CPU_THRESHOLD`: CPU usage threshold for circuit breaker (default: 85%)

## Ollama Models

This service is configured to use the Mistral model by default, but you can change it to any model available in the [Ollama Library](https://ollama.com/library):

- **mistral**: Excellent performance for text analysis with moderate resource requirements
- **llama2**: Good all-around performer with balanced capabilities
- **phi**: Microsoft's smaller but efficient model, good for lower-resource environments
- **gemma**: Google's recently released open model with good structured output capabilities
- **neural-chat**: Optimized for conversational tasks
- **codegemma**: Specialized for code-related tasks

## Running the Service

### Local Development Mode

```bash
npm run dev
```

### Production Mode

```bash
npm run build
npm start
```

### Docker Deployment (Recommended for Production)

We provide Docker and Docker Compose configurations for easy deployment:

```bash
# Build the TypeScript code first
npm run build

# Start the services with Docker Compose
docker-compose up -d
```

This will start both Ollama and the AI service in containers. The Ollama container will automatically download the specified model.

### Server Deployment Options

1. **Docker on VPS/Cloud Provider**:

   - Deploy using the provided Docker Compose file on any VPS or cloud provider
   - Ensure at least 4GB RAM for running the Mistral model

2. **Render.com**:

   - Create a Docker service pointing to this repository
   - Set up environment variables as specified in the .env file
   - Add a persistent volume for Ollama's model storage

3. **AWS/GCP/Azure**:
   - Deploy using Docker on EC2/Compute Engine/VM
   - Or use container services like ECS, GKE, or ACI

## API Endpoints

### Resume Analysis

```
POST /api/resume/analyze
```

Request body:

```json
{
  "resumeText": "Full text of the resume",
  "jobDescription": "Optional job description for targeted analysis"
}
```

### Keyword Extraction

```
POST /api/resume/keywords
```

Request body:

```json
{
  "text": "Text to extract keywords from",
  "jobDescription": "Optional job description for comparison"
}
```

### Health Check

```
GET /api/health
GET /api/health/details
```

### Metrics

```
GET /api/metrics
GET /api/metrics/last/:minutes
```

## Health Monitoring

The service includes a health monitoring system that:

1. Collects system metrics (CPU, memory)
2. Implements a circuit breaker to prevent overload
3. Records metrics history for monitoring

## License

MIT
