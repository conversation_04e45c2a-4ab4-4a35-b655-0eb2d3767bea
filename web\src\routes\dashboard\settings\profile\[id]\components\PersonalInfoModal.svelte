<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Save } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import type { PersonalInfoSchema } from '$lib/validators/profile';
  import LocationSelect from '$components/ui/location-select.svelte';

  // Props
  const { open, data, onClose, onSave } = $props<{
    open: boolean;
    data: PersonalInfoSchema;
    onClose: () => void;
    onSave: (data: PersonalInfoSchema) => Promise<boolean>;
  }>();

  // US cities for dropdown
  const cities = [
    { value: 'New York', label: 'New York' },
    { value: 'Los Angeles', label: 'Los Angeles' },
    { value: 'Chicago', label: 'Chicago' },
    { value: 'Houston', label: 'Houston' },
    { value: 'Phoenix', label: 'Phoenix' },
    { value: 'Philadelphia', label: 'Philadelphia' },
    { value: 'San Antonio', label: 'San Antonio' },
    { value: 'San Diego', label: 'San Diego' },
    { value: 'Dallas', label: 'Dallas' },
    { value: 'San Jose', label: 'San Jose' },
    { value: 'Austin', label: 'Austin' },
    { value: 'Jacksonville', label: 'Jacksonville' },
    { value: 'Fort Worth', label: 'Fort Worth' },
    { value: 'Columbus', label: 'Columbus' },
    { value: 'Indianapolis', label: 'Indianapolis' },
  ];

  // US states for dropdown
  const states = [
    { value: 'AL', label: 'Alabama' },
    { value: 'AK', label: 'Alaska' },
    { value: 'AZ', label: 'Arizona' },
    { value: 'AR', label: 'Arkansas' },
    { value: 'CA', label: 'California' },
    { value: 'CO', label: 'Colorado' },
    { value: 'CT', label: 'Connecticut' },
    { value: 'DE', label: 'Delaware' },
    { value: 'FL', label: 'Florida' },
    { value: 'GA', label: 'Georgia' },
    { value: 'HI', label: 'Hawaii' },
    { value: 'ID', label: 'Idaho' },
    { value: 'IL', label: 'Illinois' },
    { value: 'IN', label: 'Indiana' },
    { value: 'IA', label: 'Iowa' },
    { value: 'KS', label: 'Kansas' },
    { value: 'KY', label: 'Kentucky' },
    { value: 'LA', label: 'Louisiana' },
    { value: 'ME', label: 'Maine' },
    { value: 'MD', label: 'Maryland' },
    { value: 'MA', label: 'Massachusetts' },
    { value: 'MI', label: 'Michigan' },
    { value: 'MN', label: 'Minnesota' },
    { value: 'MS', label: 'Mississippi' },
    { value: 'MO', label: 'Missouri' },
    { value: 'MT', label: 'Montana' },
    { value: 'NE', label: 'Nebraska' },
    { value: 'NV', label: 'Nevada' },
    { value: 'NH', label: 'New Hampshire' },
    { value: 'NJ', label: 'New Jersey' },
    { value: 'NM', label: 'New Mexico' },
    { value: 'NY', label: 'New York' },
    { value: 'NC', label: 'North Carolina' },
    { value: 'ND', label: 'North Dakota' },
    { value: 'OH', label: 'Ohio' },
    { value: 'OK', label: 'Oklahoma' },
    { value: 'OR', label: 'Oregon' },
    { value: 'PA', label: 'Pennsylvania' },
    { value: 'RI', label: 'Rhode Island' },
    { value: 'SC', label: 'South Carolina' },
    { value: 'SD', label: 'South Dakota' },
    { value: 'TN', label: 'Tennessee' },
    { value: 'TX', label: 'Texas' },
    { value: 'UT', label: 'Utah' },
    { value: 'VT', label: 'Vermont' },
    { value: 'VA', label: 'Virginia' },
    { value: 'WA', label: 'Washington' },
    { value: 'WV', label: 'West Virginia' },
    { value: 'WI', label: 'Wisconsin' },
    { value: 'WY', label: 'Wyoming' },
    { value: 'DC', label: 'District of Columbia' },
  ];

  // Local state for editing
  let formData = $state<PersonalInfoSchema>({
    email: data?.email || '',
    phone: data?.phone || '',
    address: data?.address || '',
    city: data?.city || '',
    state: data?.state || '',
    zip: data?.zip || '',
    country: data?.country || 'USA',
  });

  let submitting = $state(false);
  let errors = $state<Record<string, string>>({});

  // Validate form
  function validateForm(): boolean {
    errors = {};
    let isValid = true;

    // Email validation
    if (formData.email) {
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        errors.email = 'Please enter a valid email address';
        isValid = false;
      }
    }

    // Phone validation
    if (formData.phone) {
      // Remove all non-numeric characters for validation
      const phoneDigits = formData.phone.replace(/\D/g, '');
      if (phoneDigits.length < 10) {
        errors.phone = 'Phone number must have at least 10 digits';
        isValid = false;
      }
    }

    return isValid;
  }

  // Format phone number as user types
  function formatPhoneNumber(input: string): string {
    // Remove all non-numeric characters
    const phoneDigits = input.replace(/\D/g, '');

    // Format the phone number
    if (phoneDigits.length <= 3) {
      return phoneDigits;
    } else if (phoneDigits.length <= 6) {
      return `(${phoneDigits.slice(0, 3)}) ${phoneDigits.slice(3)}`;
    } else {
      return `(${phoneDigits.slice(0, 3)}) ${phoneDigits.slice(3, 6)}-${phoneDigits.slice(6, 10)}`;
    }
  }

  // Handle phone input
  function handlePhoneInput(e: Event) {
    const input = (e.target as HTMLInputElement).value;
    formData.phone = formatPhoneNumber(input);
  }

  // Handle save
  async function handleSave() {
    if (!validateForm()) {
      return;
    }

    submitting = true;
    try {
      const success = await onSave(formData);
      if (success) {
        toast.success('Personal information updated successfully');
        onClose();
      }
    } catch (error) {
      console.error('Error saving personal information:', error);
      toast.error('Failed to save personal information');
    } finally {
      submitting = false;
    }
  }
</script>

<Dialog.Root {open} onOpenChange={(isOpen) => !isOpen && onClose()}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="sm:max-w-[600px]">
      <Dialog.Header>
        <Dialog.Title>Edit Personal Information</Dialog.Title>
        <Dialog.Description>
          Update your personal information to help fill out job applications faster.
        </Dialog.Description>
      </Dialog.Header>

      <div class="py-4">
        <form
          class="space-y-6"
          onsubmit={(e) => {
            e.preventDefault();
            handleSave();
          }}>
          <!-- Contact Information -->
          <div class="space-y-4">
            <h3 class="text-base font-medium">Contact Information</h3>
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div class="space-y-2">
                <Label for="email">Email Address</Label>
                <input
                  id="email"
                  type="email"
                  class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  bind:value={formData.email} />
                {#if errors.email}
                  <p class="text-destructive text-sm">{errors.email}</p>
                {/if}
              </div>
              <div class="space-y-2">
                <Label for="phone">Phone Number</Label>
                <input
                  id="phone"
                  type="tel"
                  class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  bind:value={formData.phone}
                  oninput={handlePhoneInput}
                  placeholder="(*************" />
                {#if errors.phone}
                  <p class="text-destructive text-sm">{errors.phone}</p>
                {/if}
              </div>
            </div>
          </div>

          <!-- Location Information -->
          <div class="space-y-4">
            <h3 class="text-base font-medium">Location Information</h3>
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div class="space-y-2">
                <Label for="address">Street Address</Label>
                <input
                  id="address"
                  type="text"
                  class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  bind:value={formData.address} />
              </div>

              <div class="space-y-2">
                <Label for="city">City</Label>
                <LocationSelect
                  id="city"
                  name="city"
                  options={cities}
                  bind:value={formData.city}
                  placeholder="Select city..."
                  searchPlaceholder="Search cities..."
                  onChange={(value) => (formData.city = value)} />
              </div>

              <div class="space-y-2">
                <Label for="state">State</Label>
                <LocationSelect
                  id="state"
                  name="state"
                  options={states}
                  bind:value={formData.state}
                  placeholder="Select state..."
                  searchPlaceholder="Search states..."
                  onChange={(value) => (formData.state = value)} />
              </div>

              <div class="space-y-2">
                <Label for="zip">ZIP/Postal Code</Label>
                <input
                  id="zip"
                  type="text"
                  class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  bind:value={formData.zip} />
              </div>

              <div class="space-y-2">
                <Label for="country">Country</Label>
                <input
                  id="country"
                  value="USA"
                  disabled
                  class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
              </div>
            </div>
          </div>
        </form>
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={onClose}>Cancel</Button>
        <Button onclick={handleSave} disabled={submitting} class="ml-2">
          <Save class="mr-2 h-4 w-4" />
          {submitting ? 'Saving...' : 'Save Changes'}
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
