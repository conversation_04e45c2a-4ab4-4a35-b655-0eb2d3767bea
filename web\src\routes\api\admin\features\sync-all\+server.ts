import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';
import type { RequestHandler } from './$types';
import { FeatureAccessLevel } from '$lib/models/features/features';

/**
 * Synchronizes all features with all plans
 * This ensures that every feature is added to every plan with appropriate access levels
 */
async function syncAllFeaturesWithPlans() {
  try {
    // Get all features
    const features = await prisma.feature.findMany();

    if (features.length === 0) {
      console.log('No features found to synchronize.');
      return { count: 0, errors: 0 };
    }

    console.log(`Found ${features.length} features to synchronize.`);

    // Get all plans
    const plans = await prisma.plan.findMany({
      include: {
        features: {
          include: {
            limits: true,
          },
        },
      },
    });

    if (plans.length === 0) {
      console.log('No plans found to synchronize with.');
      return { count: 0, errors: 0 };
    }

    console.log(`Found ${plans.length} plans to synchronize.`);

    // Define default access levels based on plan tier
    const getDefaultAccessLevel = (planName: string, featureId: string) => {
      const planNameLower = planName.toLowerCase();

      // Core features - available to all plans
      if (featureId.startsWith('career_') ||
          featureId === 'privacy_settings' ||
          featureId === 'notification_preferences') {
        return FeatureAccessLevel.Included;
      }

      // Free plan gets basic features
      if (planNameLower.includes('free')) {
        // Only give free users access to very basic features
        if (featureId === 'job_alerts' ||
            featureId === 'resume_builder' ||
            featureId === 'application_tracking') {
          return FeatureAccessLevel.Limited;
        }
        return FeatureAccessLevel.NotIncluded;
      }

      // Starter/Basic plans get limited access to most features
      if (planNameLower.includes('starter') || planNameLower.includes('basic') || planNameLower.includes('casual')) {
        return FeatureAccessLevel.Limited;
      }

      // Pro/Premium plans get full access to most features
      if (planNameLower.includes('pro') || planNameLower.includes('premium')) {
        return FeatureAccessLevel.Included;
      }

      // Enterprise/Business plans get everything unlimited
      if (planNameLower.includes('enterprise') || planNameLower.includes('business')) {
        return FeatureAccessLevel.Unlimited;
      }

      // Default for other plans - limited access
      return FeatureAccessLevel.Limited;
    };

    let syncCount = 0;
    let errorCount = 0;

    // For each plan, ensure all features are added
    for (const plan of plans) {
      console.log(`\nSynchronizing plan: ${plan.name}`);

      // Get existing feature IDs for this plan
      const existingFeatureIds = plan.features.map(f => f.featureId);

      // Find features that need to be added to this plan
      const featuresToAdd = features.filter(feature => !existingFeatureIds.includes(feature.id));

      if (featuresToAdd.length === 0) {
        console.log(`  No new features to add to plan ${plan.name}`);
      } else {
        console.log(`  Adding ${featuresToAdd.length} features to plan ${plan.name}`);

        // Add each feature to the plan
        for (const feature of featuresToAdd) {
          try {
            const accessLevel = getDefaultAccessLevel(plan.name, feature.id);

            await prisma.planFeature.create({
              data: {
                planId: plan.id,
                featureId: feature.id,
                accessLevel: accessLevel,
              },
            });

            console.log(`  Added feature ${feature.id} to plan ${plan.name} with access level: ${accessLevel}`);
            syncCount++;
          } catch (error) {
            console.error(`  Error adding feature ${feature.id} to plan ${plan.name}:`, error);
            errorCount++;
          }
        }
      }
    }

    console.log('\nPlan synchronization complete!');
    console.log(`Synchronized: ${syncCount} feature-plan relationships`);
    console.log(`Errors: ${errorCount} during synchronization`);

    return { count: syncCount, errors: errorCount };
  } catch (error) {
    console.error('Error synchronizing features with plans:', error);
    throw error;
  }
}

/**
 * POST /api/admin/features/sync-all
 * Synchronize all features with all plans
 */
export const POST: RequestHandler = async ({ cookies }) => {
  // Check if user is authenticated
  const token = cookies.get('auth_token');
  if (!token) return new Response('Unauthorized', { status: 401 });

  const userData = await verifySessionToken(token);
  if (!userData?.id) return new Response('Unauthorized', { status: 401 });

  // Check if user is admin
  if (!userData.isAdmin) {
    return new Response('Forbidden - Admin access required', { status: 403 });
  }

  try {
    // Sync all features with all plans
    const result = await syncAllFeaturesWithPlans();

    return json({
      success: true,
      message: `Successfully synchronized ${result.count} feature-plan relationships`,
      count: result.count,
      errors: result.errors
    });
  } catch (error) {
    console.error('Error in feature sync API:', error);
    return json({
      success: false,
      message: `Error synchronizing features: ${error.message}`,
    }, { status: 500 });
  }
};
