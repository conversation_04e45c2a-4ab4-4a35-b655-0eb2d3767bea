<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import { toast } from 'svelte-sonner';
  import type { ProfileData } from '$lib/types/profile';

  // Props
  const { profileData, onSave, onCancel } = $props<{
    profileData: ProfileData;
    onSave: (data: Partial<ProfileData>) => Promise<boolean>;
    onCancel: () => void;
  }>();

  // Form data
  let fullName = $state(profileData.fullName || profileData.personalInfo?.fullName || '');
  let email = $state(profileData.email || profileData.personalInfo?.email || '');
  let jobTitle = $state(profileData.jobType || profileData.personalInfo?.jobTitle || '');
  let industry = $state(profileData.industry || '');

  // Industries list
  const industries = [
    { value: 'technology', label: 'Technology' },
    { value: 'healthcare', label: 'Healthcare' },
    { value: 'finance', label: 'Finance' },
    { value: 'education', label: 'Education' },
    { value: 'retail', label: 'Retail' },
    { value: 'manufacturing', label: 'Manufacturing' },
    { value: 'government', label: 'Government' },
    { value: 'nonprofit', label: 'Non-Profit' },
    { value: 'other', label: 'Other' },
  ];

  // Handle form submission
  async function handleSubmit() {
    try {
      // Validate form
      if (!fullName) {
        toast.error('Full name is required');
        return;
      }

      // Prepare data
      const updatedData: Partial<ProfileData> = {
        fullName,
        email,
        jobType: jobTitle,
        industry,
        personalInfo: {
          ...profileData.personalInfo,
          fullName,
          email,
          jobTitle,
        },
      };

      // Save data
      const success = await onSave(updatedData);
      if (success) {
        toast.success('Basic information updated successfully');
      }
    } catch (error) {
      console.error('Error saving basic info:', error);
      toast.error('Failed to save basic information');
    }
  }
</script>

<form on:submit|preventDefault={handleSubmit} class="space-y-4">
  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
    <div class="space-y-2">
      <Label for="fullName">Full Name</Label>
      <Input id="fullName" bind:value={fullName} placeholder="Enter your full name" />
    </div>
    <div class="space-y-2">
      <Label for="email">Email</Label>
      <Input id="email" type="email" bind:value={email} placeholder="Enter your email" />
    </div>
    <div class="space-y-2">
      <Label for="jobTitle">Job Title</Label>
      <Input id="jobTitle" bind:value={jobTitle} placeholder="Enter your job title" />
    </div>
    <div class="space-y-2">
      <Label for="industry">Industry</Label>
      <Select.Root value={industry} onValueChange={(value) => (industry = value)}>
        <Select.Trigger id="industry" class="w-full">
          <Select.Value placeholder="Select an industry" />
        </Select.Trigger>
        <Select.Content>
          <Select.Group>
            {#each industries as industry}
              <Select.Item value={industry.value}>{industry.label}</Select.Item>
            {/each}
          </Select.Group>
        </Select.Content>
      </Select.Root>
    </div>
  </div>

  <div class="flex justify-end space-x-2 pt-4">
    <Button variant="outline" type="button" on:click={onCancel}>Cancel</Button>
    <Button type="submit">Save Changes</Button>
  </div>
</form>
