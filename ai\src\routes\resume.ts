import express from 'express';
import { analyzeResume } from '../services/analysis/resume.js';
import { extractKeywords } from '../services/analysis/keywords.js';
import { allowRequest } from '../services/health/circuit-breaker.js';
import { CircuitOpenError, BadRequestError } from '../utils/errors.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// Analyze resume
router.post('/analyze', async (req, res, next) => {
  try {
    // Check circuit breaker
    if (!allowRequest()) {
      throw new CircuitOpenError();
    }
    
    const { resumeText, jobDescription } = req.body;
    
    if (!resumeText) {
      throw new BadRequestError('Resume text is required');
    }
    
    logger.info('Received resume analysis request');
    
    const analysis = await analyzeResume(resumeText, jobDescription);
    
    res.json({
      success: true,
      analysis,
    });
  } catch (error) {
    next(error);
  }
});

// Extract keywords
router.post('/keywords', async (req, res, next) => {
  try {
    // Check circuit breaker
    if (!allowRequest()) {
      throw new CircuitOpenError();
    }
    
    const { text, jobDescription } = req.body;
    
    if (!text) {
      throw new BadRequestError('Text is required');
    }
    
    logger.info('Received keyword extraction request');
    
    const keywords = await extractKeywords(text, jobDescription);
    
    res.json({
      success: true,
      keywords,
    });
  } catch (error) {
    next(error);
  }
});

export default router;
