<!-- src/components/help/HelpCategoryCard.svelte -->
<script lang="ts">
  import * as Card from '$lib/components/ui/card';
  import { ArrowRight } from 'lucide-svelte';
  import * as lucideIcons from 'lucide-svelte';

  // Props
  let { category, className = '' } = $props<{
    category: {
      id: string;
      name: string;
      slug: string;
      description?: string;
      icon?: string;
      articleCount?: number;
    };
    className?: string;
  }>();

  // We don't need the iconComponent variable anymore since we're using direct component references
</script>

<Card.Root class="h-full {className}">
  <Card.Header>
    <div class="mb-2 flex items-center gap-3">
      <div class="bg-primary/10 text-primary rounded-full p-2">
        {#if category.icon === 'BookOpen'}
          <lucideIcons.BookOpen class="h-5 w-5" />
        {:else if category.icon === 'FileText'}
          <lucideIcons.FileText class="h-5 w-5" />
        {:else if category.icon === 'CreditCard'}
          <lucideIcons.CreditCard class="h-5 w-5" />
        {:else if category.icon === 'Shield'}
          <lucideIcons.Shield class="h-5 w-5" />
        {:else}
          <lucideIcons.HelpCircle class="h-5 w-5" />
        {/if}
      </div>
      <Card.Title>{category.name}</Card.Title>
    </div>
    {#if category.description}
      <Card.Description>{category.description}</Card.Description>
    {/if}
  </Card.Header>
  <Card.Footer class="flex justify-between">
    <div class="text-muted-foreground text-sm">
      {category.articleCount ?? 0} article{category.articleCount !== 1 ? 's' : ''}
    </div>
    <a
      href="/help/category/{category.slug}"
      class="text-primary inline-flex items-center text-sm font-medium hover:underline">
      Browse <ArrowRight class="ml-1 h-4 w-4" />
    </a>
  </Card.Footer>
</Card.Root>
