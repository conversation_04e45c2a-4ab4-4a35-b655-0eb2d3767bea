// cron/jobs/dailySummary.ts

import { logger, globalJobStats } from "../utils/logger.js";
import {
  sendEmailNotification,
  EmailNotificationType,
} from "../utils/emailService.js";
import { PrismaClient } from "@prisma/client";

/**
 * Generate and send a daily summary of all job executions
 * This script is run by the scheduler at the end of each day
 */
async function generateDailySummary() {
  logger.info("📊 Generating daily job summary");

  const startTime = Date.now();

  try {
    // Get formatted stats from the global job stats tracker
    const stats = globalJobStats.getFormattedStats();

    // Calculate success rate
    const successRate =
      stats.totalProcessed > 0
        ? ((stats.totalSucceeded / stats.totalProcessed) * 100).toFixed(1)
        : "0.0";

    // Format duration
    const totalDurationMs = stats.totalDuration;
    const totalDurationMinutes = Math.floor(totalDurationMs / (60 * 1000));
    const totalDurationHours = Math.floor(totalDurationMinutes / 60);

    let durationText = "";
    if (totalDurationHours > 0) {
      durationText = `${totalDurationHours} hours, ${totalDurationMinutes % 60} minutes`;
    } else {
      durationText = `${totalDurationMinutes} minutes`;
    }

    // Get database stats
    const dbStats = await getDatabaseStats();

    // Format the report date
    const reportDate = new Date().toLocaleDateString();

    // Log summary for debugging
    logger.info(
      `📊 Daily Summary: ${stats.totalProcessed} jobs processed, ${stats.totalSucceeded} succeeded, ${stats.totalFailed} failed (${successRate}% success rate)`
    );
    logger.info(`⏱️ Total processing time: ${durationText}`);

    // Log job type breakdown
    logger.info("📊 Job Type Breakdown:");
    stats.jobTypes.forEach((job) => {
      logger.info(
        `  • ${job.type}: ${job.count} runs, ${job.success} succeeded, ${job.failure} failed`
      );
    });

    // Log failure reasons if any
    if (stats.failureReasons.length > 0) {
      logger.info("❌ Failure Reasons:");
      stats.failureReasons.forEach((failure) => {
        logger.info(`  • ${failure.reason}: ${failure.count} occurrences`);
      });
    }

    // Log database stats
    logger.info("💾 Database Stats:");
    logger.info(`  • Total Jobs: ${dbStats.totalJobs}`);
    logger.info(`  • Jobs Added Today: ${dbStats.jobsAddedToday}`);
    logger.info(`  • Companies: ${dbStats.totalCompanies}`);
    logger.info(`  • Users: ${dbStats.totalUsers}`);

    // Send email notification with the summary
    await sendEmailNotification(EmailNotificationType.JOB_SUMMARY, {
      reportTitle: "Daily Job Processing Summary",
      reportDate: reportDate,
      jobsProcessed: stats.totalProcessed,
      jobsSucceeded: stats.totalSucceeded,
      jobsFailed: stats.totalFailed,
      processingTime: durationText,
      successRate: `${successRate}%`,

      // Add job types for the email template
      jobTypes: stats.jobTypes,

      // Add failure reasons for the email template
      failureReasons: stats.failureReasons,

      // Add database stats
      databaseStats: dbStats,

      // Add system info
      systemInfo: {
        nodeVersion: process.version,
        platform: process.platform,
        memory: process.memoryUsage(),
        uptime: process.uptime(),
      },

      // Add timestamp
      timestamp: new Date().toISOString(),

      // Add details for better reporting
      details: {
        totalProcessed: stats.totalProcessed,
        totalSucceeded: stats.totalSucceeded,
        totalFailed: stats.totalFailed,
        successRate: `${successRate}%`,
        totalDuration: durationText,
        lastReset: new Date().toISOString(),
      },
    });

    logger.info("✅ Daily summary generated and sent successfully");

    // Calculate execution time
    const endTime = Date.now();
    const executionTime = endTime - startTime;
    logger.info(`⏱️ Daily summary generation took ${executionTime}ms`);

    return {
      success: true,
      executionTime,
      stats,
    };
  } catch (error) {
    logger.error("❌ Error generating daily summary:", error);

    // Try to send an error notification
    try {
      await sendEmailNotification(EmailNotificationType.CRON_ERROR, {
        jobType: "dailySummary",
        status: "Failed",
        errorTitle: "Daily Summary Generation Failed",
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
      });
    } catch (emailError) {
      logger.error("❌ Failed to send error notification:", emailError);
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Get database statistics for the daily report
 */
async function getDatabaseStats() {
  const prisma = new PrismaClient();

  try {
    // Get today's date at midnight
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Query database for stats
    const [totalJobs, jobsAddedToday, totalCompanies, totalUsers] =
      await Promise.all([
        prisma.jobListing.count(),
        prisma.jobListing.count({
          where: {
            createdAt: {
              gte: today,
            },
          },
        }),
        prisma.company.count(),
        prisma.user.count(),
      ]);

    return {
      totalJobs,
      jobsAddedToday,
      totalCompanies,
      totalUsers,
    };
  } catch (error) {
    logger.error("❌ Error getting database stats:", error);
    return {
      totalJobs: 0,
      jobsAddedToday: 0,
      totalCompanies: 0,
      totalUsers: 0,
      error: error instanceof Error ? error.message : String(error),
    };
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function if this script is executed directly
if (require.main === module) {
  generateDailySummary()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      logger.error("❌ Unhandled error in daily summary:", error);
      process.exit(1);
    });
}

// Export for use in scheduledJobs.ts
export default generateDailySummary;
