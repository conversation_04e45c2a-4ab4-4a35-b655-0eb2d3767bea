import { Page } from "playwright";
import { logger } from "../utils/logger";
import { delay } from "../utils/humanBehavior";
import { handleCaptchaIfPresent } from "../scripts/improvedCaptchaSolver";
import { detectCaptcha } from "../utils/captchaDetector";

export class CaptchaService {
  /**
   * Checks for CAPTCHA and handles it if detected
   */
  async checkAndHandleCaptcha(
    page: Page,
    workerInfo: { id: number; captchaCount: number }
  ): Promise<{
    captchaDetected: boolean;
    handled: boolean;
    backoffTime: number;
    reason?: string;
  }> {
    try {
      // Check if CAPTC<PERSON> is present
      const { detected, reason } = await detectCaptcha(page);

      if (!detected) {
        return {
          captchaDetected: false,
          handled: false,
          backoffTime: 0,
        };
      }

      logger.warn(
        `⚠️ CAPTCHA detected for worker #${workerInfo.id}. Reason: ${reason}`
      );

      // Try to solve the CAPTCHA
      const solved = await handleCaptchaIfPresent(page);

      if (solved) {
        logger.info(
          `✅ Successfully solved CAPTCHA for worker #${workerInfo.id}`
        );
        return {
          captchaDetected: true,
          handled: true,
          backoffTime: 5000, // Short delay after successful solving
        };
      }

      // Calculate backoff time based on captcha count
      const baseDelay = 60 * 1000; // 1 minute
      const maxDelay = 15 * 60 * 1000; // 15 minutes
      const backoffTime = Math.min(
        baseDelay * Math.pow(2, workerInfo.captchaCount),
        maxDelay
      );

      logger.warn(
        `⚠️ Failed to solve CAPTCHA for worker #${workerInfo.id}, backing off for ${Math.round(backoffTime / 1000)} seconds`
      );

      return {
        captchaDetected: true,
        handled: false,
        backoffTime,
        reason,
      };
    } catch (error) {
      logger.error(`Error in checkAndHandleCaptcha:`, error);
      return {
        captchaDetected: true, // Assume CAPTCHA to be safe
        handled: false,
        backoffTime: 60000, // 1 minute default backoff
        reason: `Error checking CAPTCHA: ${error}`,
      };
    }
  }
}
