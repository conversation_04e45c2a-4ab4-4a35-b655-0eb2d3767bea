// cron/scripts/fix-job-locations.ts
//
// This script fixes problematic job locations in the database.
// It can be run periodically to ensure location data quality.
//
// Usage:
//   npx tsx scripts/fix-job-locations.ts [--all] [--id=JOB_ID]
//
// Options:
//   --all: Fix all problematic locations
//   --id=JOB_ID: Fix a specific job by ID

import { PrismaClient } from "@prisma/client";
import { JobService } from "../services/jobService";

const prisma = new PrismaClient();
const jobService = new JobService();

// Parse command line arguments
const args = process.argv.slice(2);
const fixAll = args.includes("--all");
const jobIdArg = args.find(arg => arg.startsWith("--id="));
const jobId = jobIdArg ? jobIdArg.split("=")[1] : null;

async function main() {
  if (jobId) {
    // Fix a specific job
    await fixJobById(jobId);
  } else if (fixAll) {
    // Fix all problematic locations
    await fixAllProblematicLocations();
  } else {
    // Show usage
    console.log("Usage:");
    console.log("  npx tsx scripts/fix-job-locations.ts [--all] [--id=JOB_ID]");
    console.log("");
    console.log("Options:");
    console.log("  --all: Fix all problematic locations");
    console.log("  --id=JOB_ID: Fix a specific job by ID");
  }
  
  await prisma.$disconnect();
}

async function fixJobById(id: string) {
  console.log(`Fixing job with ID: ${id}`);
  
  // Get the job from the database
  const job = await prisma.jobListing.findUnique({
    where: { id }
  });
  
  if (!job) {
    console.error(`Job with ID ${id} not found`);
    return;
  }
  
  console.log(`Title: ${job.title}`);
  console.log(`Company: ${job.company}`);
  console.log(`Original location: "${job.location}"`);
  
  // Clean the job data using our improved logic
  const cleanedJob = await jobService.cleanJobData({
    title: job.title,
    company: job.company,
    location: job.location,
    url: job.url,
    description: job.description,
    platform: job.platform || "bing", // Default to "bing" if platform is not specified
    stateId: job.stateId
  });
  
  // Only update if the location has changed
  if (cleanedJob.location !== job.location) {
    console.log(`Cleaned location: "${cleanedJob.location}"`);
    
    // Update the job
    await prisma.jobListing.update({
      where: { id: job.id },
      data: { 
        location: cleanedJob.location,
        remoteType: cleanedJob.remoteType || job.remoteType
      }
    });
    
    console.log("Job updated successfully");
  } else {
    console.log("Location is already clean, no update needed");
  }
}

async function fixAllProblematicLocations() {
  console.log("Fixing all problematic locations...");
  
  // Define problematic location patterns
  const problematicPatterns = [
    // Empty locations
    "",
    
    // Single words that aren't locations
    "this",
    "enable",
    "AD",
    "current",
    
    // LinkedIn indicators
    "benefits",
    "retirement",
    ", LI",
    
    // Non-location text
    "company",
    "unknown",
    
    // Multiple spaces
    "  "
  ];
  
  // Build the WHERE clause for the query
  const whereClause = {
    OR: [
      ...problematicPatterns.map(pattern => ({
        location: {
          contains: pattern,
          mode: 'insensitive'
        }
      })),
      {
        location: {
          equals: ""
        }
      }
    ]
  };
  
  // Get jobs with problematic locations
  const problematicJobs = await prisma.jobListing.findMany({
    where: whereClause
  });
  
  console.log(`Found ${problematicJobs.length} jobs with problematic locations`);
  
  // Process each job
  let fixedCount = 0;
  for (const job of problematicJobs) {
    console.log(`\nProcessing job: ${job.id}`);
    console.log(`Title: ${job.title}`);
    console.log(`Company: ${job.company}`);
    console.log(`Original location: "${job.location}"`);
    
    // Clean the job data using our improved logic
    const cleanedJob = await jobService.cleanJobData({
      title: job.title,
      company: job.company,
      location: job.location,
      url: job.url,
      description: job.description,
      platform: job.platform || "bing", // Default to "bing" if platform is not specified
      stateId: job.stateId
    });
    
    // Only update if the location has changed
    if (cleanedJob.location !== job.location) {
      console.log(`Cleaned location: "${cleanedJob.location}"`);
      
      // Update the job
      await prisma.jobListing.update({
        where: { id: job.id },
        data: { 
          location: cleanedJob.location,
          remoteType: cleanedJob.remoteType || job.remoteType
        }
      });
      
      fixedCount++;
    } else {
      console.log("Location is already clean, no update needed");
    }
  }
  
  console.log(`\nFixed ${fixedCount} jobs with problematic locations`);
}

main()
  .then(() => {
    console.log("Fix complete");
    process.exit(0);
  })
  .catch(error => {
    console.error(`Error: ${error}`);
    process.exit(1);
  });
