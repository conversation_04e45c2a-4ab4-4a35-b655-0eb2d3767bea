// workers/utils/system-resources.ts
// Utility for getting system resource information using container metrics

import os from "os";
import { getContainerMetrics } from "./containerMetrics.js";

/**
 * Format bytes to a human-readable string
 */
export function formatBytes(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

/**
 * Get detailed system resource information using container metrics
 * This is the standardized way to get system resource information across all services
 */
export async function getSystemResourceInfo() {
  try {
    // Get container metrics
    const containerMetrics = await getContainerMetrics();
    
    let memoryUsagePercent = 0;
    let cpuUsagePercent = 0;
    
    if (containerMetrics) {
      // Use container metrics
      memoryUsagePercent = containerMetrics.memoryUsagePercent;
      cpuUsagePercent = containerMetrics.cpuUsagePercent;
      
      console.log(`📊 Using container metrics - CPU: ${cpuUsagePercent.toFixed(1)}%, Memory: ${memoryUsagePercent.toFixed(1)}%`);
    } else {
      // Not in a container, log a warning
      console.warn(`⚠️ Not running in a container, using default values for metrics`);
    }
    
    // Ensure we don't have NaN values
    if (isNaN(memoryUsagePercent)) {
      console.warn(`⚠️ Memory usage is NaN, using fallback value of 0`);
      memoryUsagePercent = 0;
    }
    
    if (isNaN(cpuUsagePercent)) {
      console.warn(`⚠️ CPU usage is NaN, using fallback value of 0`);
      cpuUsagePercent = 0;
    }
    
    // Get basic system information for reference
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const cpuCount = os.cpus().length;
    const loadAvg = os.loadavg()[0]; // 1 minute load average
    
    return {
      memory: {
        total: formatBytes(totalMemory),
        free: formatBytes(freeMemory),
        used: formatBytes(usedMemory),
        usagePercent: memoryUsagePercent.toFixed(2) + "%",
        usagePercentRaw: memoryUsagePercent,
        totalBytes: totalMemory,
        freeBytes: freeMemory,
        usedBytes: usedMemory
      },
      cpu: {
        count: cpuCount,
        loadAverage: loadAvg.toFixed(2),
        usagePercent: cpuUsagePercent.toFixed(2) + "%",
        usagePercentRaw: cpuUsagePercent
      }
    };
  } catch (error) {
    console.error(`❌ Error getting system resource info: ${error}`);
    
    // Fallback to basic OS information
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsagePercent = (usedMemory / totalMemory) * 100;
    
    const cpuCount = os.cpus().length;
    const loadAvg = os.loadavg()[0]; // 1 minute load average
    const cpuUsagePercent = 0; // We can't reliably get CPU usage in this fallback
    
    return {
      memory: {
        total: formatBytes(totalMemory),
        free: formatBytes(freeMemory),
        used: formatBytes(usedMemory),
        usagePercent: memoryUsagePercent.toFixed(2) + "%",
        usagePercentRaw: memoryUsagePercent,
        totalBytes: totalMemory,
        freeBytes: freeMemory,
        usedBytes: usedMemory
      },
      cpu: {
        count: cpuCount,
        loadAverage: loadAvg.toFixed(2),
        usagePercent: cpuUsagePercent.toFixed(2) + "%",
        usagePercentRaw: cpuUsagePercent
      }
    };
  }
}
