<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import { toast } from 'svelte-sonner';
  import ServiceStatusCard from '$components/system-status/ServiceStatusCard.svelte';
  import SystemMetrics from './SystemMetrics.svelte';
  import MaintenanceEvents from './MaintenanceEvents.svelte';
  import type {
    ServiceStatus,
    SystemMetrics as SystemMetricsType,
    MaintenanceEvent,
    Incident,
  } from './types';

  // Props
  const { pageData } = $props<{
    pageData: any;
  }>();

  // State
  let services = $state([] as ServiceStatus[]);
  let metrics = $state({
    uptime: 99.9,
    emailDeliveryRate: 0,
    apiResponseTime: 250,
    jobSuccessRate: 0,
  } as SystemMetricsType);
  let lastUpdated = $state(new Date());
  let isLoading = $state(false);
  let recentIncidents = $state([] as Incident[]);

  // Initialize services from data
  function initializeServices(data: any): ServiceStatus[] {
    if (data.services && data.services.length > 0) {
      return data.services.map((service: any) => ({
        name: service.name,
        status: service.status,
        description: service.description,
        lastUpdated: new Date(service.lastCheckedAt || Date.now()),
      }));
    } else if (data.serviceHealth) {
      // Use service health data if services are not available
      const serviceHealth = data.serviceHealth;
      return [
        {
          name: 'Matches',
          status: serviceHealth.api?.status || 'operational',
          description: 'Job matching and recommendations',
          lastUpdated: new Date(),
        },
        {
          name: 'Jobs',
          status: serviceHealth.api?.status || 'operational',
          description: 'Job search and listings',
          lastUpdated: new Date(),
        },
        {
          name: 'Tracker',
          status: serviceHealth.database?.status || 'operational',
          description: 'Application tracking',
          lastUpdated: new Date(),
        },
        {
          name: 'Documents',
          status: serviceHealth.database?.status || 'operational',
          description: 'Resume and document management',
          lastUpdated: new Date(),
        },
        {
          name: 'Automation',
          status: serviceHealth.worker?.status || 'operational',
          description: 'Automated job application tools',
          lastUpdated: new Date(),
        },
        {
          name: 'System',
          status: serviceHealth.database?.status || 'operational',
          description: 'Core system services',
          lastUpdated: new Date(),
        },
        {
          name: 'Website',
          status: serviceHealth.web?.status || 'operational',
          description: 'Website and user interface',
          lastUpdated: new Date(),
        },
      ];
    } else {
      return [
        { name: 'Matches', status: 'unknown', description: 'Job matching and recommendations' },
        { name: 'Jobs', status: 'unknown', description: 'Job search and listings' },
        { name: 'Tracker', status: 'unknown', description: 'Application tracking' },
        { name: 'Documents', status: 'unknown', description: 'Resume and document management' },
        { name: 'Automation', status: 'unknown', description: 'Automated job application tools' },
        { name: 'System', status: 'unknown', description: 'Core system services' },
        { name: 'Website', status: 'unknown', description: 'Website and user interface' },
      ];
    }
  }

  // Convert maintenance events to incidents
  function getIncidentsFromMaintenanceEvents(): Incident[] {
    if (!pageData.maintenance) return [];

    const allEvents = [
      ...(pageData.maintenance.inProgress || []),
      ...(pageData.maintenance.past || []),
      ...(pageData.maintenance.upcoming || []),
    ] as MaintenanceEvent[];

    return allEvents.map((event) => {
      // Map maintenance event status to incident status
      let status: 'investigating' | 'identified' | 'monitoring' | 'resolved';
      switch (event.status) {
        case 'in-progress':
          status = 'monitoring';
          break;
        case 'scheduled':
          status = 'identified';
          break;
        case 'completed':
          status = 'resolved';
          break;
        case 'cancelled':
          status = 'resolved';
          break;
        default:
          status = 'investigating';
      }

      // Create incident from maintenance event
      return {
        ...event,
        status,
        severity: event.severity || 'info',
        date: new Date(event.startTime),
        description:
          event.description +
          (event.affectedServices && event.affectedServices.length > 0
            ? `\nAffected services: ${event.affectedServices.join(', ')}`
            : ''),
        // Add progress for status bar
        progress: calculateProgress(event.startTime, event.endTime),
      };
    });
  }

  // Calculate progress for status bar
  function calculateProgress(startTime: string | Date, endTime: string | Date): number {
    const start = new Date(startTime).getTime();
    const end = new Date(endTime).getTime();
    const now = Date.now();

    if (now <= start) return 0;
    if (now >= end) return 100;

    return Math.round(((now - start) / (end - start)) * 100);
  }

  // Format date
  function formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  }

  // Fetch system status
  async function fetchSystemStatus() {
    if (isLoading) return; // Prevent concurrent fetches
    isLoading = true;

    try {
      // Make a direct fetch to the API health endpoint
      const healthResponse = await fetch('/api/health');
      if (!healthResponse.ok) {
        throw new Error(`Failed to fetch health data: ${healthResponse.status}`);
      }

      // Parse the health data
      const healthData = await healthResponse.json();

      // Use the current data as a base and update with fresh health data
      const freshData = {
        ...pageData,
        serviceHealth: healthData,
      };

      // Update services from the server data
      services = initializeServices(freshData);

      // Update metrics with real data
      metrics = {
        uptime: freshData.uptime || 99.9,
        emailDeliveryRate: freshData.email?.deliveryRate || 0,
        apiResponseTime: freshData.apiResponseTime || 250,
        jobSuccessRate: freshData.jobs?.successRate || 0,
      };

      lastUpdated = new Date();
    } catch (error) {
      console.error('Error fetching system status:', error);
      toast.error('Failed to fetch system status');
    } finally {
      isLoading = false;
    }
  }

  // Get service-specific metrics based on service name
  function getServiceMetrics(serviceName: string) {
    // Default metrics
    const defaultMetrics = {
      responseTime: 0,
      successRate: 0,
      requestCount: 0,
      errorRate: 0,
    };

    // Get service health data from the API response
    const serviceHealth = pageData.serviceHealth || ({} as Record<string, any>);

    // Map service names to their corresponding health data
    const serviceMapping: Record<string, any> = {
      Matches: { status: 'operational', details: {} },
      Jobs: { status: 'operational', details: {} },
      Tracker: { status: 'operational', details: {} },
      Documents: { status: 'operational', details: {} },
      Automation: { status: 'operational', details: {} },
      System: { status: 'operational', details: {} },
      Website: { status: 'operational', details: {} },
    };

    // Update with real data if available
    if (serviceHealth.web) {
      serviceMapping.Website = serviceHealth.web;
    }

    if (serviceHealth.api) {
      // API affects Jobs and Matches
      serviceMapping.Jobs = serviceHealth.api;
      serviceMapping.Matches = serviceHealth.api;
    }

    if (serviceHealth.worker) {
      serviceMapping.Automation = serviceHealth.worker;
    }

    if (serviceHealth.database) {
      serviceMapping.System = serviceHealth.database;
      // Database affects Tracker and Documents
      serviceMapping.Tracker = serviceHealth.database;
      serviceMapping.Documents = serviceHealth.database;
    }

    // Get health data for this service
    const serviceData = serviceMapping[serviceName] || {};

    // Extract details from the service data
    const details = serviceData.details || {};

    // Return metrics with real data if available
    return {
      responseTime: details.responseTime || defaultMetrics.responseTime,
      successRate:
        details.successRate ||
        (serviceName === 'Jobs'
          ? metrics.jobSuccessRate
          : serviceName === 'System'
            ? serviceHealth.database?.status === 'operational'
              ? 100
              : 80
            : serviceName === 'Website'
              ? serviceHealth.web?.status === 'operational'
                ? 100
                : 80
              : defaultMetrics.successRate),
      requestCount: details.requestCount || defaultMetrics.requestCount,
      errorRate:
        details.errorRate ||
        (serviceName === 'Jobs'
          ? 100 - metrics.jobSuccessRate
          : serviceName === 'System'
            ? serviceHealth.database?.status === 'operational'
              ? 0
              : 20
            : serviceName === 'Website'
              ? serviceHealth.web?.status === 'operational'
                ? 0
                : 20
              : defaultMetrics.errorRate),
      // Add additional metrics if available
      ...(details.queueSize !== undefined ? { queueSize: details.queueSize } : {}),
      ...(details.processingCount !== undefined
        ? { processingCount: details.processingCount }
        : {}),
      ...(details.memoryUsage !== undefined ? { memoryUsage: details.memoryUsage } : {}),
      ...(details.dbSizeMB !== undefined ? { dbSizeMB: details.dbSizeMB } : {}),
      ...(details.activeConnections !== undefined
        ? { activeConnections: details.activeConnections }
        : {}),
      ...(details.connectedClients !== undefined
        ? { connectedClients: details.connectedClients }
        : {}),
    };
  }

  // Component lifecycle
  import { onMount, onDestroy } from 'svelte';

  // Initialize data on component creation
  function initializeData() {
    // Initialize services from data
    services = initializeServices(pageData);

    // Initialize metrics
    metrics = {
      uptime: pageData.uptime || 99.9,
      emailDeliveryRate: pageData.email?.deliveryRate || 0,
      apiResponseTime: pageData.apiResponseTime || 250,
      jobSuccessRate: pageData.jobs?.successRate || 0,
    };

    // Set last updated time
    lastUpdated = new Date(pageData.lastUpdated || Date.now());

    // Get incidents from maintenance events
    recentIncidents = getIncidentsFromMaintenanceEvents();
  }

  // Set up auto-refresh interval
  let refreshInterval: ReturnType<typeof setInterval>;

  onMount(() => {
    // Initialize data
    initializeData();

    // Set up auto-refresh
    refreshInterval = setInterval(fetchSystemStatus, 60000);
  });

  onDestroy(() => {
    if (refreshInterval) clearInterval(refreshInterval);
  });
</script>

<div class="flex flex-col gap-4">
  <div class="border-border flex items-center justify-between border-b p-6">
    <div class="flex flex-col">
      <h1 class="text-3xl font-bold">System Status</h1>
      <p class="text-muted-foreground">Current status of Hirli services</p>
    </div>
    <div class="flex items-center gap-2 self-end">
      <p class="text-muted-foreground text-sm">Last updated: {formatDate(lastUpdated)}</p>
    </div>
  </div>

  <!-- System Metrics -->
  <SystemMetrics {metrics} />

  <!-- Service Status -->
  <Card.Root>
    <Card.Header>
      <Card.Title>Service Status</Card.Title>
      <Card.Description>Current status of core application services</Card.Description>
    </Card.Header>
    <Card.Content>
      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {#each services as service}
          <ServiceStatusCard
            name={service.name}
            status={service.status}
            description={service.description || ''}
            historyData={pageData.serviceHistory?.[service.name] || []}
            metrics={getServiceMetrics(service.name)} />
        {/each}
      </div>
    </Card.Content>
  </Card.Root>

  <!-- Recent Notices -->
  <MaintenanceEvents {recentIncidents} />
</div>
