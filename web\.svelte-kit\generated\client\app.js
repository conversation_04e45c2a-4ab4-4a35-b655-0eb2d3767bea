export { matchers } from './matchers.js';

export const nodes = [
	() => import('./nodes/0'),
	() => import('./nodes/1'),
	() => import('./nodes/2'),
	() => import('./nodes/3'),
	() => import('./nodes/4'),
	() => import('./nodes/5'),
	() => import('./nodes/6'),
	() => import('./nodes/7'),
	() => import('./nodes/8'),
	() => import('./nodes/9'),
	() => import('./nodes/10'),
	() => import('./nodes/11'),
	() => import('./nodes/12'),
	() => import('./nodes/13'),
	() => import('./nodes/14'),
	() => import('./nodes/15'),
	() => import('./nodes/16'),
	() => import('./nodes/17'),
	() => import('./nodes/18'),
	() => import('./nodes/19'),
	() => import('./nodes/20'),
	() => import('./nodes/21'),
	() => import('./nodes/22'),
	() => import('./nodes/23'),
	() => import('./nodes/24'),
	() => import('./nodes/25'),
	() => import('./nodes/26'),
	() => import('./nodes/27'),
	() => import('./nodes/28'),
	() => import('./nodes/29'),
	() => import('./nodes/30'),
	() => import('./nodes/31'),
	() => import('./nodes/32'),
	() => import('./nodes/33'),
	() => import('./nodes/34'),
	() => import('./nodes/35'),
	() => import('./nodes/36'),
	() => import('./nodes/37'),
	() => import('./nodes/38'),
	() => import('./nodes/39'),
	() => import('./nodes/40'),
	() => import('./nodes/41'),
	() => import('./nodes/42'),
	() => import('./nodes/43'),
	() => import('./nodes/44'),
	() => import('./nodes/45'),
	() => import('./nodes/46'),
	() => import('./nodes/47'),
	() => import('./nodes/48'),
	() => import('./nodes/49'),
	() => import('./nodes/50'),
	() => import('./nodes/51'),
	() => import('./nodes/52'),
	() => import('./nodes/53'),
	() => import('./nodes/54'),
	() => import('./nodes/55'),
	() => import('./nodes/56'),
	() => import('./nodes/57'),
	() => import('./nodes/58'),
	() => import('./nodes/59'),
	() => import('./nodes/60'),
	() => import('./nodes/61'),
	() => import('./nodes/62'),
	() => import('./nodes/63'),
	() => import('./nodes/64'),
	() => import('./nodes/65'),
	() => import('./nodes/66'),
	() => import('./nodes/67'),
	() => import('./nodes/68'),
	() => import('./nodes/69'),
	() => import('./nodes/70'),
	() => import('./nodes/71'),
	() => import('./nodes/72'),
	() => import('./nodes/73'),
	() => import('./nodes/74'),
	() => import('./nodes/75'),
	() => import('./nodes/76'),
	() => import('./nodes/77'),
	() => import('./nodes/78'),
	() => import('./nodes/79'),
	() => import('./nodes/80'),
	() => import('./nodes/81'),
	() => import('./nodes/82'),
	() => import('./nodes/83'),
	() => import('./nodes/84'),
	() => import('./nodes/85'),
	() => import('./nodes/86'),
	() => import('./nodes/87'),
	() => import('./nodes/88'),
	() => import('./nodes/89'),
	() => import('./nodes/90'),
	() => import('./nodes/91'),
	() => import('./nodes/92'),
	() => import('./nodes/93'),
	() => import('./nodes/94'),
	() => import('./nodes/95'),
	() => import('./nodes/96')
];

export const server_loads = [0,2,6,7,8];

export const dictionary = {
		"/": [~9],
		"/about": [~10],
		"/admin/features": [11],
		"/api/email/webhook/setup": [12],
		"/auth/forgot-password": [13,[2]],
		"/auth/google-callback": [14,[2]],
		"/auth/linkedin-callback": [15,[2]],
		"/auth/reset-password": [~16,[2]],
		"/auth/sign-in": [17,[2]],
		"/auth/sign-up": [~18,[2]],
		"/auth/verified": [19,[2]],
		"/auth/verify": [20,[2]],
		"/auto-apply": [21],
		"/blog": [~22],
		"/blog/[slug]": [~23],
		"/co-pilot": [24],
		"/contact": [~25],
		"/dashboard": [~26,[3]],
		"/dashboard/automation": [~27,[3]],
		"/dashboard/automation/[id]": [~28,[3]],
		"/dashboard/builder": [~29,[3]],
		"/dashboard/builder/superform/[id]": [~30,[3]],
		"/dashboard/builder/[id]": [~31,[3]],
		"/dashboard/documents": [~32,[3]],
		"/dashboard/documents/[id]": [~33,[3]],
		"/dashboard/documents/[id]/ats": [~34,[3]],
		"/dashboard/features": [35,[3]],
		"/dashboard/jobs": [~36,[3]],
		"/dashboard/jobs/[id]": [~37,[3]],
		"/dashboard/matches": [~38,[3]],
		"/dashboard/notifications": [~39,[3]],
		"/dashboard/resumes": [~40,[3]],
		"/dashboard/resumes/[id]": [~41,[3]],
		"/dashboard/resumes/[id]/optimize": [~42,[3]],
		"/dashboard/settings": [~43,[3,4]],
		"/dashboard/settings/account": [~44,[3,4]],
		"/dashboard/settings/admin": [~45,[3,4,5]],
		"/dashboard/settings/admin/email": [~46,[3,4,5,6]],
		"/dashboard/settings/admin/email/analytics": [47,[3,4,5,6]],
		"/dashboard/settings/admin/email/audiences": [~48,[3,4,5,6]],
		"/dashboard/settings/admin/email/broadcast": [49,[3,4,5,6]],
		"/dashboard/settings/admin/email/queue": [50,[3,4,5,6]],
		"/dashboard/settings/admin/feature-usage": [~51,[3,4,5]],
		"/dashboard/settings/admin/features": [52,[3,4,5]],
		"/dashboard/settings/admin/maintenance": [~53,[3,4,5]],
		"/dashboard/settings/admin/notifications": [~54,[3,4,5]],
		"/dashboard/settings/admin/plans": [~55,[3,4,5]],
		"/dashboard/settings/admin/seed-features": [56,[3,4,5]],
		"/dashboard/settings/admin/subscriptions": [~57,[3,4,5]],
		"/dashboard/settings/analysis": [~58,[3,4]],
		"/dashboard/settings/billing": [~59,[3,4]],
		"/dashboard/settings/email": [~60,[3,4]],
		"/dashboard/settings/general": [~61,[3,4]],
		"/dashboard/settings/interview-coach": [~62,[3,4]],
		"/dashboard/settings/make-admin": [63,[3,4]],
		"/dashboard/settings/notifications": [~64,[3,4]],
		"/dashboard/settings/profile": [~65,[3,4]],
		"/dashboard/settings/profile/[id]": [~66,[3,4]],
		"/dashboard/settings/referrals": [~67,[3,4]],
		"/dashboard/settings/security": [~68,[3,4]],
		"/dashboard/settings/team": [~69,[3,4]],
		"/dashboard/settings/usage": [~70,[3,4]],
		"/dashboard/tracker": [~71,[3]],
		"/dashboard/usage": [72,[3]],
		"/employers": [~73],
		"/help": [~74],
		"/help/category/[slug]": [~75],
		"/help/quick-start": [~76],
		"/help/search": [~77],
		"/help/[slug]": [~78],
		"/job-tracker": [79],
		"/jobs": [~80],
		"/legal": [~81,[7]],
		"/legal/[slug]": [~82,[7]],
		"/press": [~83,[8]],
		"/press/coverage": [~84,[8]],
		"/press/images": [~85,[8]],
		"/press/releases": [~86,[8]],
		"/press/releases/[slug]": [~87,[8]],
		"/pricing": [~88],
		"/profile/[id]": [~89],
		"/recruiters": [~90],
		"/resources": [~91],
		"/resources/[slug]": [~92],
		"/resume-builder": [93],
		"/studio": [~94],
		"/system-status": [~95],
		"/system-status/history": [~96]
	};

export const hooks = {
	handleError: (({ error }) => { console.error(error) }),
	
	reroute: (() => {}),
	transport: {}
};

export const decoders = Object.fromEntries(Object.entries(hooks.transport).map(([k, v]) => [k, v.decode]));

export const hash = false;

export const decode = (type, value) => decoders[type](value);

export { default as root } from '../root.js';