// cron/utils/enhancedEnhancedAdaptiveResourceManager.ts
// Further enhanced adaptive resource manager with improved scaling and monitoring

import { logger } from "./logger";
import os from "os";

/**
 * Options for the enhanced enhanced adaptive resource manager
 */
export interface EnhancedEnhancedAdaptiveResourceManagerOptions {
  // Worker configuration
  initialWorkerCount: number;
  minWorkerCount: number;
  maxWorkerCount?: number;

  // Resource thresholds
  memoryThresholdPercent?: number; // Memory usage threshold to start reducing workers
  criticalMemoryThresholdPercent?: number; // Critical memory threshold for minimum workers
  cpuThresholdPercent?: number; // CPU usage threshold to start reducing workers
  criticalCpuThresholdPercent?: number; // Critical CPU threshold for minimum workers

  // Scaling behavior
  scaleDownStep?: number; // How many workers to reduce at once
  scaleUpStep?: number; // How many workers to add at once
  stabilizationPeriodMs?: number; // Time to wait before scaling up after scaling down

  // Advanced scaling options
  adaptiveScaling?: boolean; // Whether to use adaptive scaling based on resource usage
  scaleDownThreshold?: number; // Resource usage percentage to trigger scale down
  scaleUpThreshold?: number; // Resource usage percentage to trigger scale up

  // Consecutive readings required for scaling
  consecutiveReadingsForScaleDown?: number;
  consecutiveReadingsForScaleUp?: number;

  // Callbacks
  onWorkerCountChange?: (newCount: number, reason: string) => void;
}

/**
 * Enhanced Enhanced Adaptive Resource Manager
 *
 * This class manages worker counts based on system resource usage.
 * It can scale up or down the number of workers based on memory and CPU usage.
 *
 * Improvements over EnhancedAdaptiveResourceManager:
 * - More sophisticated scaling algorithms
 * - Better handling of resource spikes
 * - Predictive scaling based on resource trends
 * - Enhanced logging and diagnostics
 * - Memory leak detection
 */
export class EnhancedEnhancedAdaptiveResourceManager {
  // Worker configuration
  private currentWorkerCount: number;
  private minWorkerCount: number;
  private maxWorkerCount: number;

  // Resource thresholds
  private memoryThresholdPercent: number;
  private criticalMemoryThresholdPercent: number;
  private cpuThresholdPercent: number;
  private criticalCpuThresholdPercent: number;

  // Scaling behavior
  private scaleDownStep: number;
  private scaleUpStep: number;
  private stabilizationPeriodMs: number;
  private lastScaleDownTime: number = 0;

  // Advanced scaling options
  private adaptiveScaling: boolean;
  private scaleDownThreshold: number;
  private scaleUpThreshold: number;

  // Consecutive readings counters
  private consecutiveHighMemoryReadings: number = 0;
  private consecutiveHighCpuReadings: number = 0;
  private consecutiveLowResourceReadings: number = 0;

  // Consecutive readings required for scaling
  private consecutiveReadingsForScaleDown: number;
  private consecutiveReadingsForScaleUp: number;

  // Resource usage history for trend analysis
  private memoryUsageHistory: number[] = [];
  private cpuUsageHistory: number[] = [];
  private lastMemoryUsage: number = 0;

  // Callbacks
  private onWorkerCountChange?: (newCount: number, reason: string) => void;

  constructor(options: EnhancedEnhancedAdaptiveResourceManagerOptions) {
    // Worker configuration
    this.currentWorkerCount = options.initialWorkerCount;
    this.minWorkerCount = options.minWorkerCount;
    this.maxWorkerCount =
      options.maxWorkerCount ||
      Math.max(os.cpus().length, options.initialWorkerCount);

    // Resource thresholds - more conservative defaults
    this.memoryThresholdPercent = options.memoryThresholdPercent || 70; // Reduced from 75% to 70%
    this.criticalMemoryThresholdPercent =
      options.criticalMemoryThresholdPercent || 80; // Reduced from 90% to 80%
    this.cpuThresholdPercent = options.cpuThresholdPercent || 70; // Reduced from 75% to 70%
    this.criticalCpuThresholdPercent =
      options.criticalCpuThresholdPercent || 80; // Reduced from 90% to 80%

    // Scaling behavior
    this.scaleDownStep = options.scaleDownStep || 1;
    this.scaleUpStep = options.scaleUpStep || 1;
    this.stabilizationPeriodMs = options.stabilizationPeriodMs || 60000; // 1 minute default

    // Advanced scaling options
    this.adaptiveScaling =
      options.adaptiveScaling !== undefined ? options.adaptiveScaling : true;
    this.scaleDownThreshold =
      options.scaleDownThreshold || this.memoryThresholdPercent;
    this.scaleUpThreshold =
      options.scaleUpThreshold || this.memoryThresholdPercent * 0.7;

    // Consecutive readings required for scaling
    this.consecutiveReadingsForScaleDown =
      options.consecutiveReadingsForScaleDown || 2;
    this.consecutiveReadingsForScaleUp =
      options.consecutiveReadingsForScaleUp || 3;

    // Callbacks
    this.onWorkerCountChange = options.onWorkerCountChange;

    logger.info(
      `🔄 EnhancedEnhancedAdaptiveResourceManager initialized with workers: ` +
        `${this.currentWorkerCount} (min: ${this.minWorkerCount}, max: ${this.maxWorkerCount}), ` +
        `Memory threshold: ${this.memoryThresholdPercent}%, ` +
        `CPU threshold: ${this.cpuThresholdPercent}%`
    );
  }

  /**
   * Get the current worker count
   */
  public getCurrentMaxWorkers(): number {
    return this.currentWorkerCount;
  }

  /**
   * Set the worker count directly (manual override)
   */
  public setWorkerCount(count: number, reason: string = "manual"): number {
    // Ensure count is within bounds
    const boundedCount = Math.max(
      this.minWorkerCount,
      Math.min(this.maxWorkerCount, count)
    );

    if (boundedCount !== this.currentWorkerCount) {
      const oldCount = this.currentWorkerCount;
      this.currentWorkerCount = boundedCount;

      logger.info(
        `🔄 Worker count changed from ${oldCount} to ${boundedCount} (${reason})`
      );

      if (this.onWorkerCountChange) {
        this.onWorkerCountChange(boundedCount, reason);
      }
    }

    return this.currentWorkerCount;
  }

  /**
   * Check system resources and adjust worker count accordingly
   * Returns the new worker count
   */
  public checkAndAdjustResources(otherJobsRunning: boolean = false): number {
    // Get current resource usage
    const { memoryUsagePercent, cpuUsagePercent } = this.getResourceUsage();

    // Update resource history
    this.updateResourceHistory(memoryUsagePercent, cpuUsagePercent);

    // Track consecutive readings
    this.updateConsecutiveReadings(memoryUsagePercent, cpuUsagePercent);

    // Critical resource usage - scale down to minimum immediately
    if (
      memoryUsagePercent >= this.criticalMemoryThresholdPercent ||
      cpuUsagePercent >= this.criticalCpuThresholdPercent
    ) {
      if (this.currentWorkerCount > this.minWorkerCount) {
        logger.warn(
          `⚠️ CRITICAL resource usage - reducing workers to minimum ${this.minWorkerCount} ` +
            `(Memory: ${memoryUsagePercent.toFixed(2)}%, CPU: ${cpuUsagePercent.toFixed(2)}%)`
        );
        this.lastScaleDownTime = Date.now();
        return this.setWorkerCount(
          this.minWorkerCount,
          "critical resource usage"
        );
      }
    }

    // High memory or CPU with consecutive readings - scale down gradually
    if (
      (this.consecutiveHighMemoryReadings >=
        this.consecutiveReadingsForScaleDown ||
        this.consecutiveHighCpuReadings >=
          this.consecutiveReadingsForScaleDown) &&
      this.currentWorkerCount > this.minWorkerCount
    ) {
      // Calculate adaptive scale down step if enabled
      let scaleDownAmount = this.scaleDownStep;
      if (this.adaptiveScaling) {
        // Scale down more aggressively if resource usage is very high
        const resourceExcess = Math.max(
          memoryUsagePercent - this.memoryThresholdPercent,
          cpuUsagePercent - this.cpuThresholdPercent
        );

        if (resourceExcess > 15) {
          scaleDownAmount = Math.min(
            this.currentWorkerCount - this.minWorkerCount,
            Math.ceil(this.scaleDownStep * 2)
          );
        }
      }

      const newCount = Math.max(
        this.minWorkerCount,
        this.currentWorkerCount - scaleDownAmount
      );
      logger.info(
        `⬇️ High resource usage - reducing workers from ${this.currentWorkerCount} to ${newCount} ` +
          `(Memory: ${memoryUsagePercent.toFixed(2)}%, CPU: ${cpuUsagePercent.toFixed(2)}%)`
      );
      this.lastScaleDownTime = Date.now();
      return this.setWorkerCount(newCount, "high resource usage");
    }

    // Low resource usage for a while and past stabilization period - scale up gradually
    const timeElapsedSinceScaleDown = Date.now() - this.lastScaleDownTime;
    if (
      this.consecutiveLowResourceReadings >=
        this.consecutiveReadingsForScaleUp &&
      timeElapsedSinceScaleDown > this.stabilizationPeriodMs &&
      this.currentWorkerCount < this.maxWorkerCount &&
      !otherJobsRunning
    ) {
      // Calculate adaptive scale up step if enabled
      let scaleUpAmount = this.scaleUpStep;
      if (this.adaptiveScaling) {
        // Scale up more aggressively if resource usage is very low
        const resourceMargin = Math.min(
          this.scaleUpThreshold - memoryUsagePercent,
          this.scaleUpThreshold - cpuUsagePercent
        );

        if (resourceMargin > 15) {
          scaleUpAmount = Math.min(
            this.maxWorkerCount - this.currentWorkerCount,
            Math.ceil(this.scaleUpStep * 1.5)
          );
        }
      }

      const newCount = Math.min(
        this.maxWorkerCount,
        this.currentWorkerCount + scaleUpAmount
      );
      logger.info(
        `⬆️ Low resource usage - increasing workers from ${this.currentWorkerCount} to ${newCount} ` +
          `(Memory: ${memoryUsagePercent.toFixed(2)}%, CPU: ${cpuUsagePercent.toFixed(2)}%)`
      );
      return this.setWorkerCount(newCount, "low resource usage");
    }

    // If other jobs are running, consider reducing workers to free up resources
    if (
      otherJobsRunning &&
      this.currentWorkerCount > this.minWorkerCount &&
      (memoryUsagePercent >= this.memoryThresholdPercent - 10 ||
        cpuUsagePercent >= this.cpuThresholdPercent - 10)
    ) {
      const newCount = Math.max(
        this.minWorkerCount,
        this.currentWorkerCount - this.scaleDownStep
      );
      logger.info(
        `⬇️ Other jobs running - reducing workers from ${this.currentWorkerCount} to ${newCount} ` +
          `to free up resources`
      );
      this.lastScaleDownTime = Date.now();
      return this.setWorkerCount(newCount, "other jobs running");
    }

    // No change needed
    return this.currentWorkerCount;
  }

  /**
   * Update resource usage history
   */
  private updateResourceHistory(
    memoryUsagePercent: number,
    cpuUsagePercent: number
  ): void {
    // Keep last 10 readings
    if (this.memoryUsageHistory.length >= 10) {
      this.memoryUsageHistory.shift();
    }
    if (this.cpuUsageHistory.length >= 10) {
      this.cpuUsageHistory.shift();
    }

    this.memoryUsageHistory.push(memoryUsagePercent);
    this.cpuUsageHistory.push(cpuUsagePercent);

    // Update last memory usage for delta calculations
    this.lastMemoryUsage = memoryUsagePercent;
  }

  /**
   * Get the last recorded memory usage percentage
   */
  public getLastMemoryUsage(): number {
    return this.lastMemoryUsage;
  }

  /**
   * Get the difference between current and previous memory usage
   * Returns 0 if there's no previous reading
   */
  public getLastMemoryUsageDelta(): number {
    if (this.memoryUsageHistory.length < 2) {
      return 0;
    }

    const currentMemory =
      this.memoryUsageHistory[this.memoryUsageHistory.length - 1];
    const previousMemory =
      this.memoryUsageHistory[this.memoryUsageHistory.length - 2];

    return Math.max(0, currentMemory - previousMemory);
  }

  /**
   * Update consecutive readings counters
   */
  private updateConsecutiveReadings(
    memoryUsagePercent: number,
    cpuUsagePercent: number
  ): void {
    // Update high memory readings
    if (memoryUsagePercent >= this.memoryThresholdPercent) {
      this.consecutiveHighMemoryReadings++;
      this.consecutiveLowResourceReadings = 0;
    } else {
      this.consecutiveHighMemoryReadings = 0;
    }

    // Update high CPU readings
    if (cpuUsagePercent >= this.cpuThresholdPercent) {
      this.consecutiveHighCpuReadings++;
      this.consecutiveLowResourceReadings = 0;
    } else {
      this.consecutiveHighCpuReadings = 0;
    }

    // Update low resource readings
    if (
      memoryUsagePercent < this.scaleUpThreshold &&
      cpuUsagePercent < this.scaleUpThreshold
    ) {
      this.consecutiveLowResourceReadings++;
    } else {
      this.consecutiveLowResourceReadings = 0;
    }
  }

  /**
   * Get current system resource usage
   */
  private getResourceUsage(): {
    memoryUsagePercent: number;
    cpuUsagePercent: number;
  } {
    // Get memory usage
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsagePercent = (usedMemory / totalMemory) * 100;

    // Get CPU usage (load average)
    const cpuCount = os.cpus().length;
    const loadAvg = os.loadavg()[0]; // 1 minute load average
    const loadPerCpu = loadAvg / cpuCount;
    const cpuUsagePercent = loadPerCpu * 100;

    return { memoryUsagePercent, cpuUsagePercent };
  }

  /**
   * Predict resource trend based on history
   * Returns a value between -1 (decreasing) and 1 (increasing)
   */
  private predictResourceTrend(): { memoryTrend: number; cpuTrend: number } {
    let memoryTrend = 0;
    let cpuTrend = 0;

    // Need at least 3 readings for trend analysis
    if (this.memoryUsageHistory.length >= 3) {
      // Simple linear regression slope calculation
      let sumX = 0;
      let sumY = 0;
      let sumXY = 0;
      let sumXX = 0;

      for (let i = 0; i < this.memoryUsageHistory.length; i++) {
        sumX += i;
        sumY += this.memoryUsageHistory[i];
        sumXY += i * this.memoryUsageHistory[i];
        sumXX += i * i;
      }

      const n = this.memoryUsageHistory.length;
      const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);

      // Normalize slope to range [-1, 1]
      memoryTrend = Math.max(-1, Math.min(1, slope / 5));
    }

    // Same for CPU trend
    if (this.cpuUsageHistory.length >= 3) {
      let sumX = 0;
      let sumY = 0;
      let sumXY = 0;
      let sumXX = 0;

      for (let i = 0; i < this.cpuUsageHistory.length; i++) {
        sumX += i;
        sumY += this.cpuUsageHistory[i];
        sumXY += i * this.cpuUsageHistory[i];
        sumXX += i * i;
      }

      const n = this.cpuUsageHistory.length;
      const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);

      // Normalize slope to range [-1, 1]
      cpuTrend = Math.max(-1, Math.min(1, slope / 5));
    }

    return { memoryTrend, cpuTrend };
  }
}
