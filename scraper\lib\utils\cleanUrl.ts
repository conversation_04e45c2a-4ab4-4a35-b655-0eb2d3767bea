/**
 * Utility function to clean URLs by removing tracking parameters
 */

/**
 * Removes UTM and other tracking parameters from a URL
 * @param url The URL to clean
 * @returns The cleaned URL without tracking parameters
 */
export function removeTrackingParams(url: string): string {
  if (!url) return url;
  
  try {
    const urlObj = new URL(url);
    
    // List of tracking parameters to remove
    const trackingParams = [
      'utm_source',
      'utm_medium',
      'utm_campaign',
      'utm_term',
      'utm_content',
      'utm_id',
      'fbclid',
      'gclid',
      'msclkid',
      'ref',
      'referrer',
      'source'
    ];
    
    // Remove each tracking parameter
    trackingParams.forEach(param => {
      urlObj.searchParams.delete(param);
    });
    
    return urlObj.toString();
  } catch (error) {
    // If there's an error parsing the URL, return the original
    console.error(`Error cleaning URL: ${url}`, error);
    return url;
  }
}
