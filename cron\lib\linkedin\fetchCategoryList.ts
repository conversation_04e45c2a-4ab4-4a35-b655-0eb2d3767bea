// cron/lib/linkedin/fetchCategoryList.ts

import { chromium } from "playwright";

export type LinkedInCollection = {
  name: string;
  slug: string;
};

export async function fetchJobCollections(): Promise<LinkedInCollection[]> {
  // Always use headless mode for this function, but add no-sandbox for Docker environments
  const browser = await chromium.launch({
    headless: true,
    args: ["--no-sandbox"],
  });
  const context = await browser.newContext({
    storageState: "./.auth/linkedin.json",
    userAgent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36",
    viewport: { width: 1280, height: 800 },
    locale: "en-US",
    extraHTTPHeaders: {
      "accept-language": "en-US,en;q=0.9",
      "sec-ch-ua":
        '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
      "sec-ch-ua-platform": '"Windows"',
      "sec-ch-ua-mobile": "?0",
    },
  });

  const page = await context.newPage();

  try {
    // Visit feed page first to load session properly
    await page.goto("https://www.linkedin.com/feed/", {
      waitUntil: "domcontentloaded",
    });
    await page.waitForTimeout(1500);

    // Then navigate to job collections page
    await page.goto("https://www.linkedin.com/jobs/collections/", {
      waitUntil: "domcontentloaded",
    });
    await page.waitForTimeout(1500);

    // Wait for job collection links
    await page.waitForSelector('a[href*="/jobs/collections/"]', {
      timeout: 45000,
    });

    // Optional: Preview debug HTML
    const html = await page.content();
    console.log("🧪 Preview:", html.slice(0, 1000));

    // Scrape job collections
    const collections = await page.$$eval(
      'a[href*="/jobs/collections/"]',
      (links) => {
        const seen = new Set();
        return links
          .map((link) => {
            const name = link.textContent?.trim() || "";
            const href = link.getAttribute("href") || "";
            const slugMatch = href.match(/\/jobs\/collections\/([^?\/#]+)/);
            const slug = slugMatch?.[1] ?? "";

            if (name && slug && !seen.has(slug)) {
              seen.add(slug);
              return { name, slug };
            }
            return null;
          })
          .filter((c): c is { name: string; slug: string } => c !== null);
      }
    );

    return collections;
  } catch (err) {
    console.error("❌ Failed to fetch LinkedIn job collections:", err);
    return [];
  } finally {
    await browser.close();
  }
}
